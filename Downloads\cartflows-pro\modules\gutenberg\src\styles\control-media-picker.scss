/* --------- Common Image and Icon Picker Styles --------- */

.spectra-media-control {

	// The Overall Wrapper.
	&__wrapper {
		height: $spectra-control-mediapicker-height;
		border-radius: $spectra-control-border-radius;
		border: 1px solid $spectra-color-border;
		position: relative;
		background-color: $spectra-color-plain-background;
		background-position: center;
		background-repeat: no-repeat;
		background-size: cover;
		-webkit-display: flex;
		-moz-display: flex;
		-ms-display: flex;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;

		&--small {
			height: $spectra-control-mediapicker-height-small;
		}
	}

	// The Round Buttons
	&__button {
		width: 18px;
		height: 18px;
		-webkit-display: flex;
		-moz-display: flex;
		-ms-display: flex;
		display: flex;
		-webkit-align-items: center;
		align-items: center;
		justify-content: center;
		border-radius: 50%;

		&--add {
			background-color: $spectra-color-icon-disabled;

			svg path {
				fill: $spectra-color-plain-background;
			}
		}

		&--close {
			background-color: $spectra-color-light-background;

			svg path {
				fill: $spectra-color-icon;
			}
		}
	}

	// The Clickable Divs
	&__clickable {
		cursor: pointer;
		border: unset;
		color: transparent;
		background-color: transparent;

		&--add {
			width: 100%;
			height: 100%;
			-webkit-display: flex;
			-moz-display: flex;
			-ms-display: flex;
			display: flex;
			-webkit-align-items: center;
			align-items: center;
			justify-content: center;
		}

		&--replace {
			width: 100%;
			height: 25px;
			color: $spectra-color-body;
			background-color: $spectra-color-light-background;
			position: absolute;
			bottom: -1px;
			-webkit-display: flex;
			-moz-display: flex;
			-ms-display: flex;
			display: flex;
			-webkit-align-items: center;
			align-items: center;
			justify-content: center;

			.uag-control-label {
				margin-bottom: 0;
			}
		}

		&--close {
			position: absolute;
			top: 10px;
			right: 10px;
		}
	}

	// The Icon Area Rendered when an Icon is Required
	&__icon {
		width: 100%;
		flex: 1;
		-webkit-display: flex;
		-moz-display: flex;
		-ms-display: flex;
		display: flex;
		-webkit-align-items: center;
		align-items: center;
		justify-content: center;

		svg {
			width: 30px;
			height: 30px;
		}

		&--stroke svg path {
			stroke: $spectra-color-border-hover;
		}

		&--fill svg path {
			fill: $spectra-color-border-hover;
		}

		& + .spectra-media-control__clickable--replace {
			position: relative;
		}
	}
}
