<?php
/**
 * Classe de integração com WooCommerce e CartFlows
 *
 * @package CF_Payment_Redirector
 */

// Sair se acessado diretamente.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe de integração.
 */
class CFPR_Integration {

    /**
     * Variável de instância.
     *
     * @var CFPR_Integration
     */
    private static $instance;

    /**
     * Inicializador.
     */
    public static function get_instance() {
        if ( ! isset( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Construtor.
     */
    public function __construct() {
        add_action( 'init', array( $this, 'init' ) );
    }

    /**
     * Inicializar.
     */
    public function init() {
        // Nada a fazer por enquanto
    }

    /**
     * Obter todos os métodos de pagamento do WooCommerce.
     *
     * @return array Array de métodos de pagamento.
     */
    public function get_available_payment_gateways() {
        $gateways = array();
        
        if ( function_exists( 'WC' ) ) {
            // Obter todos os gateways de pagamento disponíveis
            $available_gateways = WC()->payment_gateways->get_available_payment_gateways();
            
            if ( ! empty( $available_gateways ) ) {
                foreach ( $available_gateways as $gateway_id => $gateway ) {
                    if ( $gateway->enabled === 'yes' ) {
                        $gateways[ $gateway_id ] = $gateway->get_title();
                    }
                }
            }
        }
        
        return $gateways;
    }

    /**
     * Obter todos os funis do CartFlows.
     *
     * @return array Array com todos os funis.
     */
    public function get_flows() {
        $flows = array();
        
        // Verificar se o CartFlows está disponível
        if ( ! function_exists( 'wcf' ) ) {
            return $flows;
        }
        
        // Obter todos os funis
        $args = array(
            'post_type'      => 'cartflows_flow',
            'posts_per_page' => -1,
            'post_status'    => 'publish',
        );
        
        $flow_query = new WP_Query( $args );
        
        if ( $flow_query->have_posts() ) {
            while ( $flow_query->have_posts() ) {
                $flow_query->the_post();
                $flow_id = get_the_ID();
                $flows[ $flow_id ] = get_the_title();
            }
            wp_reset_postdata();
        }
        
        return $flows;
    }

    /**
     * Obter todos os passos de um funil.
     *
     * @param int $flow_id ID do funil.
     * @return array Array com todos os passos.
     */
    public function get_flow_steps( $flow_id ) {
        $steps = array();
        
        // Verificar se o CartFlows está disponível
        if ( ! function_exists( 'wcf' ) || empty( $flow_id ) ) {
            return $steps;
        }
        
        // Obter os passos do funil
        $flow_steps = get_post_meta( $flow_id, 'wcf-steps', true );
        
        if ( ! empty( $flow_steps ) ) {
            foreach ( $flow_steps as $step_data ) {
                $step_id = $step_data['id'];
                $step_title = get_the_title( $step_id );
                $step_type = get_post_meta( $step_id, 'wcf-step-type', true );
                
                // Adicionar apenas passos relevantes (thank-you, landing, etc.)
                if ( in_array( $step_type, array( 'landing', 'thankyou', 'upsell', 'downsell', 'optin' ), true ) ) {
                    $steps[ $step_id ] = $step_title . ' (' . $step_type . ')';
                }
            }
        }
        
        return $steps;
    }

    /**
     * Obter a URL de um passo específico.
     *
     * @param int $step_id ID do passo.
     * @return string URL do passo.
     */
    public function get_step_url( $step_id ) {
        if ( empty( $step_id ) ) {
            return '';
        }
        
        return get_permalink( $step_id );
    }

    /**
     * Obter método de pagamento usado no pedido.
     *
     * @param int $order_id ID do pedido.
     * @return string Método de pagamento.
     */
    public function get_order_payment_method( $order_id ) {
        if ( empty( $order_id ) ) {
            return '';
        }
        
        $order = wc_get_order( $order_id );
        
        if ( ! $order ) {
            return '';
        }
        
        return $order->get_payment_method();
    }
} 