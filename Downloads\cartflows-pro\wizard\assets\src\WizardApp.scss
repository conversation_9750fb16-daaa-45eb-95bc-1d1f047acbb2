body.cartflows-setup #wpadminbar {
	display: none !important;
}

.wcf-setup-wizard-page-wrapper {
	.wcf-field__data {
		display: flex;
		align-items: center;
		width: 100%;
		justify-content: space-between;

		.wcf-field__data--label {
			width: 40%;
		}
		.wcf-field__data--content {
			width: 60%;
		}
	}

	.wcf-content-blocked {
		pointer-events: none;
	}
	.wcf-content-blocked::before {
		content: "";
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		background-color: rgba( 247, 247, 247, 0.02 );
		left: 0;
	}

	.wcf-wizard--plugin-install {
		input[type="checkbox"]:checked::before {
			background-image: url( "../images/check-icon.svg" );
			content: "" !important;
			font-size: 18px;
			background-size: 15px;
			background-repeat: no-repeat;
			width: 100%;
			height: 100%;
			top: 4px;
			left: 5px;
			position: relative;
		}
	}
}
