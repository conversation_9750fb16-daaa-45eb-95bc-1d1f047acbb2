/* CSS Variables. */
:root {
	--wcf-primary-color: #f16334; /* Used. */
	--wcf-heading-color: #333; /* Used. */
	--wcf-btn-bg-color: var( --wcf-primary-color ); /* Used. */
	--wcf-btn-bg-hover-color: var( --wcf-primary-color ); /* Used. */
	--wcf-btn-text-color: #fff; /* Used. */
	--wcf-btn-hover-text-color: #fff; /* Used. */
	--wcf-text-color: #404040; /* Used. */
	--wcf-link-color: var( --wcf-primary-color ); /* Used. */
	--wcf-field-label-color: var( --wcf-text-color ); /* Used. */
	--wcf-field-bg-color: #fff; /* Used. */
	--wcf-field-border-color: #d4d4d4; /* Used */
	--wcf-field-text-color: #555; /* Used. */
	--wcf-field-error-label-color: #e11e00; /* Used */
	--wcf-field-error-border-color: #e11e00; /* Used */
	--wcf-field-error-color: #e2401c; /* Used. */
	--wcf-payment-section-label-color: var( --wcf-text-color );
	--wcf-payment-section-desc-text-color: #515151;
	--wcf-payment-section-desc-bg-color: #eaeaea;
	--wcf-payment-section-bg-color: #f7f7f7;
}

/******************
 * Login & Coupon Field Layout
 ****************/
.cartflows-container .wcf-embed-checkout-form,
.cartflows-container .wcf-embed-checkout-form * {
	box-sizing: border-box;
}

.wcf-embed-checkout-form .wc-backward {
	display: none !important;
}

.wcf-embed-checkout-form .woocommerce form .form-row-first,
.wcf-embed-checkout-form .woocommerce form .form-row-last,
.wcf-embed-checkout-form .woocommerce-page form .form-row-first,
.wcf-embed-checkout-form .woocommerce-page form .form-row-last {
	width: 50%;
	display: block;
}

.wcf-embed-checkout-form .woocommerce form .form-row-full,
.wcf-embed-checkout-form .woocommerce-page form .form-row-full {
	width: 100%;
	clear: both;
}

.wcf-embed-checkout-form form .form-row-wide {
	width: 100%;
}

/* To test divi and all */
.wcf-embed-checkout-form .woocommerce form .form-row-first,
.woocommerce-page form .form-row-first {
	float: left;
	clear: left;
}

.wcf-embed-checkout-form .woocommerce form .form-row-last,
.woocommerce-page form .form-row-last {
	float: right;
	clear: right;
}

.wcf-embed-checkout-form .woocommerce form .form-row .required {
	color: #e11e00;
	font-weight: 700;
	border: 0 !important;
	text-decoration: none;
}

.wcf-embed-checkout-form .woocommerce .wcf-product-image {
	height: auto;
	-js-display: flex;
	display: flex;
	vertical-align: middle;
	border-radius: 5px;
	align-items: center;
	width: 100%;
}

.wcf-embed-checkout-form .woocommerce .wcf-product-name {
	display: inline-block;
	width: 80%;
	margin-right: 10px;
}

.wcf-embed-checkout-form .woocommerce .wcf-product-image img {
	border-radius: 4px;
}

.wcf-embed-checkout-form .woocommerce .wcf-product-image .wcf-product-thumbnail {
	position: relative;
	border-radius: 4px;
	width: 20%;
	margin-right: 10px;
}

.wcf-embed-checkout-form .woocommerce .wcf-product-image .wcf-product-thumbnail .wcf-remove-product {
	position: absolute;
	left: -6px;
	top: -6px;
	padding: 0;
}

.wcf-embed-checkout-form .woocommerce .product-name .wcf-remove-product {
	display: inline-block;
	border-radius: 50px;
	font-size: 15px;
	padding: 1px;
	color: #4d5763 !important;
	line-height: 1;
	margin-right: 5px;
	margin-right: 10px;
	text-align: center;
	font-weight: 500;
	background-color: #fff;
}

.wcf-embed-checkout-form .woocommerce .product-name .wcf-remove-product:hover {
	color: var( --wcf-primary-color ) !important;
	background-color: #fff;
}

/* To test divi and all */

.wcf-embed-checkout-form form .form-row-first.form-row-one {
	clear: both;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login {
	display: none;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .form-row {
	margin-bottom: 15px;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .clear {
	clear: none;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .form-row label {
	margin: 0;
	line-height: 1em;
	padding-top: 0;
	position: relative;
	top: 0;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .login-form-actions {
	-js-display: flex;
	display: flex;
	margin-bottom: 0;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .remember_me,
.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .lost_password {
	flex: 1;
	font-family: inherit;
	font-size: 13px;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .form-row .remember_me label {
	margin-bottom: 0;
	vertical-align: text-top;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .form-row .remember_me input,
.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .form-row .remember_me span {
	vertical-align: middle;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .lost_password {
	text-align: right;
	margin-top: 0;
	margin-bottom: 0;
}

/**
 * **************
 * Common Classes
 * **************
 */

.wcf-embed-checkout-form .woocommerce .blockUI.blockOverlay {
	font-size: 15px;
}

.wcf-embed-checkout-form .woocommerce form .form-row.mt20 {
	margin-top: 20px;
}

.wcf-embed-checkout-form .woocommerce form .form-row.woocommerce-invalid label {
	color: var( --wcf-field-error-label-color ) !important;
}

.wcf-embed-checkout-form .woocommerce form .form-row.woocommerce-invalid .select2-container,
.wcf-embed-checkout-form .woocommerce form .form-row.woocommerce-invalid input.input-text,
.wcf-embed-checkout-form .woocommerce form .form-row.woocommerce-invalid select {
	border-width: 1px;
	border-color: var( --wcf-field-error-border-color );
}

.wcf-embed-checkout-form .woocommerce form .form-row.woocommerce-validated .select2-container,
.wcf-embed-checkout-form .woocommerce form .form-row.woocommerce-validated input.input-text,
.wcf-embed-checkout-form .woocommerce form .form-row.woocommerce-validated select {
	border-width: 1px;
	/*border-color: #69bf29;*/
}

.wcf-embed-checkout-form .select2-container--default.field-required .select2-selection--single,
.wcf-embed-checkout-form .woocommerce form .form-row input.input-text.field-required,
.wcf-embed-checkout-form .woocommerce form .form-row textarea.input-text.field-required,
.wcf-embed-checkout-form .woocommerce #order_review .input-text.field-required {
	border-color: var( --wcf-field-error-border-color );
}

.wcf-embed-checkout-form .woocommerce form .form-row input.field-validated {
	border-left-width: 2px;
	border-left-color: #69bf29;
}

/**
 * ************
 * Main Layout
 * ************
 */

.wcf-embed-checkout-form .wcf-checkout-header-image {
	text-align: center;
}

/**
 * ************************
 * Radio Button & Checkbox
 * ************************
 */

.wcf-embed-checkout-form .woocommerce #payment .wc-braintree-save-label {
	display: none;
}

.wcf-embed-checkout-form .woocommerce #payment [type="radio"]:checked + label,
.wcf-embed-checkout-form .woocommerce #payment [type="radio"]:not( :checked ) + label {
	color: var( --wcf-payment-section-label-color );
	padding: 0;
}

.wcf-embed-checkout-form .woocommerce .wcf-input-radio-field-wrapper .woocommerce-input-wrapper {
	display: block;
}

.wcf-embed-checkout-form .woocommerce .wcf-input-radio-field-wrapper input[type="radio"].input-radio {
	float: left;
	margin: 0 10px 0 0;
	cursor: pointer;
}

.wcf-embed-checkout-form .woocommerce .wcf-input-radio-field-wrapper label.radio.input-radio {
	margin: 15px 0;
	cursor: pointer;
}

.wcf-embed-checkout-form .woocommerce .wcf-col2-set input[type="radio"],
.wcf-embed-checkout-form .woocommerce #payment input[type="radio"],
.wcf-embed-checkout-form .woocommerce #order_review input[type="radio"] {
	border-radius: 50%;
	margin-right: 4px;
	line-height: 10px;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-checkout input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce .woocommerce-billing-fields [type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce #payment input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce .woocommerce-shipping-fields [type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce .woocommerce-account-fields input[type="checkbox"]:checked::before {
	content: "\e600";
	margin: 0;
	color: var( --wcf-primary-color );
}

.wcf-embed-checkout-form .woocommerce .woocommerce-checkout input[type="checkbox"],
.wcf-embed-checkout-form .woocommerce .woocommerce-billing-fields [type="checkbox"],
.wcf-embed-checkout-form .woocommerce #payment input[type="checkbox"],
.wcf-embed-checkout-form .woocommerce .woocommerce-shipping-fields [type="checkbox"],
.wcf-embed-checkout-form .woocommerce #payment input[type="radio"],
.wcf-embed-checkout-form .woocommerce .woocommerce-account-fields input[type="checkbox"],
.wcf-embed-checkout-form .woocommerce #order_review input[type="checkbox"],
.wcf-embed-checkout-form .woocommerce #order_review input[type="radio"],
.wcf-embed-checkout-form .woocommerce .wcf-col2-set input[type="radio"] {
	border: 1px solid #b4b9be;
	background: #fff;
	color: #555;
	clear: none;
	cursor: pointer;
	display: inline-block;
	line-height: 0;
	height: 16px;
	margin: -2px 4px 0 0;
	outline: 0;
	padding: 0 !important;
	text-align: center;
	vertical-align: middle;
	width: 16px;
	min-width: 16px;
	-webkit-appearance: none;
	box-shadow: inset 0 1px 2px rgba( 0, 0, 0, 0.1 );
	transition: 0.05s border-color ease-in-out;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-billing-fields [type="checkbox"]:focus,
.wcf-embed-checkout-form .woocommerce #payment input[type="checkbox"]:focus,
.wcf-embed-checkout-form .woocommerce .woocommerce-shipping-fields [type="checkbox"]:focus,
.wcf-embed-checkout-form .woocommerce #payment input[type="radio"]:focus,
.wcf-embed-checkout-form .woocommerce .woocommerce-account-fields input[type="checkbox"]:focus,
.wcf-embed-checkout-form .woocommerce .wcf-col2-set input[type="radio"]:focus,
.wcf-embed-checkout-form .woocommerce .wcf-product-option-wrap .wcf-qty-row div [type="radio"]:focus,
.wcf-embed-checkout-form .woocommerce .wcf-product-option-wrap .wcf-qty-row div [type="radio"]:not( :checked ):focus {
	border-color: var( --wcf-primary-color );
	box-shadow: 0 0 2px rgba( 241, 99, 52, 0.8 );
}

.wcf-embed-checkout-form .woocommerce #payment input[type="radio"]:checked::before,
.wcf-embed-checkout-form .woocommerce #order_review input[type="radio"]:checked::before,
.wcf-embed-checkout-form .woocommerce .wcf-col2-set input[type="radio"]:checked::before,
.wcf-embed-checkout-form .woocommerce .wcf-product-option-wrap .wcf-qty-row input[type="radio"]:checked::before {
	background-color: var( --wcf-primary-color );
	border-radius: 50px;
	content: "\2022";
	font-size: 24px;
	height: 6px;
	line-height: 16px;
	margin: 4px;
	text-indent: -9999px;
	width: 6px;
}
.wcf-embed-checkout-form .woocommerce .woocommerce-billing-fields [type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce #payment input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce .woocommerce-shipping-fields [type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce #payment input[type="radio"]:checked::before,
.wcf-embed-checkout-form .woocommerce .wcf-col2-set input[type="radio"]:checked::before,
.wcf-embed-checkout-form .woocommerce .woocommerce-account-fields input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce #order_review input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form .woocommerce #order_review input[type="radio"]:checked::before {
	display: inline-block;
	float: left;
	font: normal normal 400 15px/1 cartflows-icon;
	speak: none;
	vertical-align: middle;
	width: 6px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods li input[type="checkbox"] {
	min-width: 16px;
}

/* Added for pro support */
.wcf-embed-checkout-form .woocommerce .col2-set .col-1,
.wcf-embed-checkout-form .woocommerce .col2-set .col-2,
.wcf-embed-checkout-form .woocommerce-page .col2-set .col-1,
.wcf-embed-checkout-form .woocommerce-page .col2-set .col-2,
/* Added for pro support */
.wcf-embed-checkout-form .woocommerce .wcf-col2-set .wcf-col-1,
.wcf-embed-checkout-form .woocommerce .wcf-col2-set .wcf-col-2,
.wcf-embed-checkout-form .woocommerce-page .wcf-col2-set .wcf-col-1,
.wcf-embed-checkout-form .woocommerce-page .wcf-col2-set .wcf-col-2 {
	float: none;
	width: 100%;
	max-width: 100%;
	padding: 0;
	border-radius: 0;
	border: none;
	border-color: none;
	margin-bottom: 0;
}

.wcf-embed-checkout-form .woocommerce-billing-fields-custom > h3,
.wcf-embed-checkout-form .woocommerce-additional-fields > h3,
.wcf-embed-checkout-form .woocommerce-billing-fields > h3,
.wcf-embed-checkout-form #order_review_heading,
.wcf-embed-checkout-form .woocommerce #ship-to-different-address {
	font-family: inherit;
	font-weight: 600;
	font-size: 20px;
	/* text-transform: capitalize; */
	margin: 0 0 25px 0;
	padding: 0;
	width: 100%;
	color: var( --wcf-heading-color );
}

.wcf-embed-checkout-form .woocommerce-checkout #payment {
	background: unset;
	border-radius: 0;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .form-row {
	margin: 0;
	padding: 8px 0 0;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .form-row label.checkbox {
	display: block;
	margin-bottom: 1.1em;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .payment_method_paypal .about_paypal {
	float: none;
	margin-left: 10px;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .payment_method_woocommerce_payments .testmode-info {
	margin-bottom: 10px;
}
.wcf-embed-checkout-form .woocommerce-checkout #payment .payment_method_woocommerce_payments .testmode-info .js-woopayments-copy-test-number {
	margin-top: -2px;
	font-size: 14px !important;
	font-weight: 400 !important;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment div.payment_box .form-row {
	margin: 0 0 0.5em;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment fieldset .form-row .wcf-embed-checkout-form .woocommerce-checkout #payment fieldset p {
	margin-bottom: 10px;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment div.payment_box {
	background-color: var( --wcf-payment-section-desc-bg-color );
	color: var( --wcf-payment-section-desc-text-color );
	font-family: inherit;
	font-weight: inherit;
	margin-bottom: 0.5em;
}

.wcf-embed-checkout-form #add_payment_method #payment div.payment_box::before,
.wcf-embed-checkout-form .woocommerce-cart #payment div.payment_box::before,
.wcf-embed-checkout-form .woocommerce-checkout #payment div.payment_box::before {
	content: "";
	display: block;
	border: 1em solid;
	border-right-color: transparent;
	border-left-color: transparent;
	border-top-color: transparent;
	border-bottom-color: var( --wcf-payment-section-desc-bg-color );
	position: absolute;
	top: -0.75em;
	left: 0;
	margin: -1em 0 0 2em;
}

/* Fields */
.wcf-embed-checkout-form .select2-container--default .select2-selection--single {
	display: block;
	width: 100%;
	min-height: 34px;
	height: auto;
	padding: 11px 12px;
	font-size: 14px;
	line-height: 1.42857143 !important;
	color: var( --wcf-field-text-color );
	background-color: var( --wcf-field-bg-color );
	background-image: none;
	border: 1px solid;
	border-color: var( --wcf-field-border-color );
	border-radius: 3px;
	box-shadow: none;
	outline: none;
}

.wcf-embed-checkout-form .select2-container--default .select2-selection--single .select2-selection__rendered {
	color: var( --wcf-field-text-color );
	line-height: 21px;
	min-height: 20px;
	overflow: visible;
	padding: 0;
	-js-display: flex;
	display: flex;
	align-items: center;
}

.cartflows_step-template .select2-container--default .select2-results__option--highlighted[aria-selected],
.cartflows_step-template .select2-container--default .select2-results__option--highlighted[data-selected] {
	background-color: var( --wcf-primary-color );
}

.wcf-embed-checkout-form .select2-results__option[aria-selected],
.select2-results__option[data-selected] {
	font-size: 14px;
}

.wcf-embed-checkout-form .select2-container--default .select2-selection--single .select2-selection__arrow {
	height: 100%;
	top: 2px;
	right: 6px;
	transform: none;
}

.select2-dropdown {
	border: 1px solid var( --wcf-field-border-color );
}

.wcf-embed-checkout-form .woocommerce form .form-row input.input-text,
.wcf-embed-checkout-form .woocommerce form .form-row textarea,
.wcf-embed-checkout-form .woocommerce form .form-row select,
.wcf-embed-checkout-form .woocommerce #order_review .input-text {
	display: block;
	width: 100%;
	min-height: 34px;
	padding: 11px 12px;
	font-family: inherit;
	font-weight: inherit;
	font-size: 14px;
	line-height: 1.42857143 !important;
	color: var( --wcf-field-text-color );
	background-color: var( --wcf-field-bg-color );
	background-image: none;
	border: 1px solid;
	border-color: var( --wcf-field-border-color );
	border-radius: 4px;
	box-shadow: none;
	height: auto;
}

.wcf-embed-checkout-form .woocommerce form .form-row input.input-text::placeholder,
.wcf-embed-checkout-form .woocommerce form .form-row textarea::placeholder,
.wcf-embed-checkout-form .woocommerce form .form-row select::placeholder {
	font-size: 14px;
	line-height: 1.42857143 !important;
}

/* Below line is added because, 2022 theme adds the col2-set class in the order summary. */
.wcf-embed-checkout-form .woocommerce-checkout .wcf-order-wrap .col2-set,
.wcf-embed-checkout-form #order_review {
	padding: 3px;
	width: 100%;
}

.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-message,
.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-error {
	padding: 0 1.5em 1.2em 1.5em;
	background-color: transparent;
	border: none;
	margin: 0;
	width: 100%;
}

.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-error li {
	list-style: none !important;
	margin: 0;
}

.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-message::before,
.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-error::before {
	top: 0;
	left: 0;
}

.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-message::before {
	left: 0.1em;
	top: 1px;
	color: #52a400;
}

.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-error::before {
	top: 1px;
	content: "\e016";
}

.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field .woocommerce-error li {
	margin: 0 0 0 8px;
}

.wcf-embed-checkout-form .woocommerce .wcf-custom-coupon-field input[type="text"] {
	font-weight: 400;
}

.wcf-embed-checkout-form .wcf-custom-coupon-field {
	clear: left;
	-js-display: flex;
	display: flex;
	margin-bottom: 25px;
	margin-top: 25px;
	border-top: 1px solid #dcdcdc;
	border-bottom: 1px solid #dcdcdc;
	padding-top: 25px;
	padding-bottom: 25px;
	flex-wrap: wrap;
}

.wcf-embed-checkout-form .wcf-custom-coupon-field .wcf-coupon-col-1 {
	width: 70%;
	display: inline-block;
	padding-right: 20px;
}

.wcf-embed-checkout-form .wcf-custom-coupon-field .wcf-coupon-col-2 {
	width: 30%;
}

.wcf-embed-checkout-form input[type="text"]:focus,
.wcf-embed-checkout-form input[type="email"]:focus,
.wcf-embed-checkout-form input[type="password"]:focus,
.wcf-embed-checkout-form input[type="tel"]:focus,
.wcf-embed-checkout-form textarea:focus,
.wcf-embed-checkout-form .select2-container--open {
	border-color: #b3b3b3;
	box-shadow: none;
	outline: none;
}

.wcf-embed-checkout-form button:focus {
	outline: none;
	outline-color: none;
	outline-style: none;
	outline-width: none;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .form-row button,
.wcf-embed-checkout-form form.checkout_coupon .button {
	display: block;
	clear: both;
	border: none;
	font-size: 15px;
	font-family: inherit;
	font-weight: inherit;
	background-color: var( --wcf-btn-bg-color );
	color: var( --wcf-btn-text-color );
	line-height: 9px;
	padding: 18px;
	margin-bottom: 10px;
	width: 100%;
}

.wcf-embed-checkout-form .wcf-custom-coupon-field button.wcf-submit-coupon,
.wcf-embed-checkout-form .woocommerce #order_review button:not( .js-woopayments-copy-test-number ),
.wcf-embed-checkout-form .woocommerce #payment button:not( .js-woopayments-copy-test-number ),
.wcf-embed-checkout-form .woocommerce .wcf-customer-login-section__login-button {
	border: 1px solid;
	border-color: var( --wcf-btn-bg-color );
	background-color: var( --wcf-btn-bg-color );
	cursor: pointer;
	font-family: inherit;
	font-weight: inherit;
	letter-spacing: 0.5px;
	width: 100%;
	padding: 16px 24px;
	font-size: 16px;
	line-height: 1.5;
	border-radius: 4px;
	color: var( --wcf-btn-text-color );
}

.wcf-embed-checkout-form .woocommerce #order_review button.wcf-btn-small {
	padding: 10px 6px;
	height: 100%;
}

.wcf-embed-checkout-form .woocommerce #order_review button.wcf-btn-small:hover,
.wcf-embed-checkout-form .woocommerce #payment button:not( .js-woopayments-copy-test-number ):hover,
.wcf-embed-checkout-form .woocommerce .wcf-customer-login-section__login-button:hover {
	border-color: var( --wcf-btn-bg-hover-color );
	background-color: var( --wcf-btn-bg-hover-color );
	color: var( --wcf-btn-hover-text-color );
	opacity: 0.9;
}

/**
 * ***********************
 * New checkout style
 * ***********************
 */

.wcf-embed-checkout-form .woocommerce-checkout {
	color: var( --wcf-text-color );
	display: inline-block;
	text-align: inherit;
	width: 100%;
}

.wcf-embed-checkout-form .woocommerce-checkout .col2-set,
.wcf-embed-checkout-form .woocommerce-checkout .wcf-col2-set {
	display: inline-block;
	width: 55%;
	float: left;
	padding-right: 40px;
	margin: 20px 0 0;
}

/*Added for pro support */
.wcf-embed-checkout-form .woocommerce-checkout .col2-set .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper,
.wcf-embed-checkout-form .woocommerce-checkout .col2-set .woocommerce-shipping-fields .woocommerce-shipping-fields__field-wrapper,
/*Added for pro support */
.wcf-embed-checkout-form .woocommerce-checkout .wcf-col2-set .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper,
.wcf-embed-checkout-form .woocommerce-checkout .wcf-col2-set .woocommerce-shipping-fields .woocommerce-shipping-fields__field-wrapper {
	margin: 0 -7px;
	-js-display: flex;
	display: flex;
	display: -webkit-flex; /* Safari 6.1+, firefox, chrome */
	flex-wrap: wrap;
	-webkit-flex-wrap: wrap; /* Safari 6.1+, firefox, chrome*/
}

.wcf-embed-checkout-form .woocommerce-checkout .col2-set .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper,
.wcf-embed-checkout-form .woocommerce-checkout .wcf-col2-set .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper {
	margin: 0 -7px;
}

.wcf-embed-checkout-form-two-column .woocommerce-checkout .wcf-order-wrap {
	display: inline-block;
	float: none;
	width: 45%;
	border: none;
	background-color: inherit /*#F6F6F6*/;
	padding: 0 10px;
	border-radius: 3px;
}

.wcf-embed-checkout-form .woocommerce-checkout #order_review_heading {
	display: inline-block;
	font-family: inherit;
	font-weight: 600;
	width: 100%;
	margin: 20px 0 0;
	padding: 3px 3px 20px;
	border: none;
	border-bottom: none;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment {
	background-color: inherit;
	border: none;
	/*border-top: 1px solid;*/
	border-radius: 0;
	/*border-color: #dcdcdc;*/
	/*padding-top: 20px;*/
}

.wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
	margin: 1em 0 0;
	background-color: var( --wcf-payment-section-bg-color );
	padding: 15px;
}

.wcf-embed-checkout-form #add_payment_method #payment ul.payment_methods,
.wcf-embed-checkout-form .woocommerce-cart #payment ul.payment_methods,
.wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods {
	border: none;
}

.wcf-embed-checkout-form #payment ul.payment_methods li,
.wcf-embed-checkout-form #payment ul.payment_methods li:hover {
	background-color: transparent;
}

.wcf-embed-checkout-form .woocommerce form .form-row {
	display: block;
	margin: 0 0 1.1em;
	padding: 3px 7px;
	position: relative;
}

.wcf-embed-checkout-form .woocommerce form .woocommerce-account-fields .form-row {
	padding: 0;
}

.wcf-embed-checkout-form table.shop_table {
	border: none;
	border-bottom: 0;
	background-color: inherit;
	border-radius: 0;
	font-family: inherit;
	font-weight: inherit;
	font-size: 0.95em;
	margin: 0 0 25px 0;
	border-collapse: collapse;
	text-align: left;
}

.wcf-embed-checkout-form table.shop_table td strong.product-quantity {
	font-weight: 400;
}

.wcf-embed-checkout-form table.shop_table #shipping_method {
	margin: 0;
	padding: 0;
	list-style: none;
}
.wcf-embed-checkout-form table.shop_table #shipping_method .amount {
	font-weight: 400;
}
.wcf-embed-checkout-form table.shop_table th {
	color: var( --wcf-field-label-color );
	border: none;
	font-weight: 400;
	padding: 9px 0;
	line-height: 1.2em;
}
.wcf-embed-checkout-form table.shop_table tfoot tr:last-child .woocommerce-Price-amount {
	font-size: 1em;
}

.wcf-embed-checkout-form table.shop_table td,
.wcf-embed-checkout-form table.shop_table th,
.wcf-embed-checkout-form table.shop_table td dl dt,
.wcf-embed-checkout-form table.shop_table td dl dd {
	background-color: transparent;
	border: none;
	padding: 0.6em 0;
	line-height: 1.4em;
}
.wcf-embed-checkout-form table.shop_table td dl dd p {
	margin-top: 0;
}

.wcf-embed-checkout-form table.shop_table tbody th,
.wcf-embed-checkout-form table.shop_table tfoot td,
.wcf-embed-checkout-form table.shop_table tfoot th {
	border: none;
	display: table-cell;
	font-weight: 400;
	width: 50%;
}
.wcf-embed-checkout-form table.shop_table thead {
	background-color: transparent;
}
.wcf-embed-checkout-form table.shop_table tbody {
	border-top: 1px dashed #ccc;
	border-bottom: 1px dashed #ccc;
}
.wcf-embed-checkout-form table.shop_table th.product-name,
.wcf-embed-checkout-form table.shop_table th.product-total {
	font-weight: 600;
}
.wcf-embed-checkout-form table.shop_table tfoot tr.order-total:not( .recurring-total ) th,
.wcf-embed-checkout-form table.shop_table tfoot tr.order-total:not( .recurring-total ) td {
	font-weight: 600;
	border-top: 1px dashed #ccc;
}
.wcf-embed-checkout-form table.shop_table tfoot tr.recurring-totals th {
	padding-top: 1.8em;
	font-weight: 600;
}
.wcf-embed-checkout-form table.shop_table tfoot tr.recurring-totals th,
.wcf-embed-checkout-form table.shop_table tfoot tr.recurring-total th,
.wcf-embed-checkout-form table.shop_table tfoot tr.recurring-total td {
	vertical-align: top;
}

.wcf-embed-checkout-form table.shop_table thead tr th:nth-child( 1 ),
.wcf-embed-checkout-form table.shop_table tbody tr td:nth-child( 1 ),
.wcf-embed-checkout-form table.shop_table tfoot tr th:nth-child( 1 ) {
	width: 60%;
	padding-right: 0 !important;
}

.wcf-embed-checkout-form table.shop_table thead tr th:nth-child( 2 ),
.wcf-embed-checkout-form table.shop_table tbody tr td:nth-child( 2 ),
.wcf-embed-checkout-form table.shop_table tfoot tr td:nth-child( 2 ) {
	width: 30%;
	padding-left: 10px;
}

.wcf-embed-checkout-form table.shop_table tbody tr.cart_item td.product-name {
	-js-display: flex;
	display: flex;
	width: 100%;
	align-items: center;
}

.wcf-embed-checkout-form .woocommerce form.woocommerce-form-login .form-row label,
.wcf-embed-checkout-form .woocommerce-checkout .form-row label {
	color: var( --wcf-field-label-color );
	font-size: 13px;
	line-height: 1em;
	letter-spacing: 0.3px;
	font-family: inherit;
	font-weight: inherit;
	/* text-transform: capitalize; */
	margin-bottom: 8px;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options ul#shipping_method li label {
	color: var( --wcf-field-text-color ) !important;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-billing-fields label,
.wcf-embed-checkout-form .woocommerce .woocommerce-shipping-fields label {
	display: inherit;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-terms-and-conditions-wrapper .woocommerce-terms-and-conditions-checkbox-text {
	line-height: 20px;
	text-transform: none;
}

.wcf-embed-checkout-form #payment .woocommerce-privacy-policy-text p {
	font-family: inherit;
	font-weight: inherit;
	font-size: 11px;
	margin-top: 0;
	text-align: justify;
}

.wcf-embed-checkout-form #payment .wc_payment_methods .payment_box p {
	margin: 0;
}

.wcf-embed-checkout-form .woocommerce a {
	color: var( --wcf-link-color );
	background-color: transparent;
	text-decoration: none;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-info,
.wcf-embed-checkout-form .woocommerce .woocommerce-error,
.wcf-embed-checkout-form .woocommerce .woocommerce-message,
.wcf-embed-checkout-form .woocommerce .woocommerce-notices-wrapper .woocommerce-message,
.wcf-embed-checkout-form .woocommerce .woocommerce-NoticeGroup .woocommerce-message {
	padding: 1em 2em 0.4em 2em;
	border-top: none;
	background-color: inherit;
	font-size: 14px;
	font-weight: 500;
	text-align: left;
	margin: 0;
}

.wcf-embed-checkout-form .woocommerce-info::before,
.wcf-embed-checkout-form .woocommerce-message::before {
	left: 0.1em;
	color: var( --wcf-primary-color );
}

.wcf-embed-checkout-form .woocommerce .woocommerce-error,
.wcf-embed-checkout-form .woocommerce .woocommerce-NoticeGroup .woocommerce-error,
.wcf-embed-checkout-form .woocommerce .woocommerce-notices-wrapper .woocommerce-error,
.wcf-embed-checkout-form .wcf-customer-info .wcf-customer-info__notice.wcf-notice {
	background-color: #fff6f6;
	border: dashed 1px #a00;
	padding: 25px 25px 20px;
	color: #a00;
	margin: 1rem 0 1rem;
	font-size: 14px;
	width: 100%;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-error::before {
	content: "";
}

.wcf-embed-checkout-form .woocommerce-checkout #payment ul.payment_methods li:not( .woocommerce-notice )::before {
	display: inline;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-notices-wrapper .woocommerce-message {
	margin: 1em 0 1em;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-error li {
	list-style: disc inside !important;
	margin: 0 0 8px;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-error li strong {
	font-weight: 400;
}

.wcf-embed-checkout-form .woocommerce form.checkout_coupon {
	border: 1px solid #d3ced2;
	padding: 30px 20px 20px;
	margin: 2em 0;
	text-align: left;
	border-radius: 5px;
}

.wcf-embed-checkout-form .woocommerce .checkout_coupon p {
	font-size: 14px;
}

.wcf-embed-checkout-form .woocommerce .checkout_coupon p {
	font-family: inherit;
	font-weight: 500;
	margin-top: 0;
	margin-bottom: 10px;
}

/* Move Shipping methods to main column */

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-table .woocommerce-shipping-totals.shipping th {
	display: none;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-methods-wrapper {
	margin: 0 0 15px 0;
	width: 55%;
	float: left;
	padding-right: 40px;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-methods-wrapper table {
	margin: 0;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options #shipping_method {
	padding: 0;
	margin: 0 0 0.5em 0;
	border: 1px solid #d6d7db;
	border-radius: 4px;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options ul#shipping_method li {
	-js-display: flex;
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 12px;
	margin: 0;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options ul#shipping_method li label {
	color: #000;
	font-weight: 400;
	-js-display: flex;
	display: flex;
	align-items: center;
	gap: 10px;
	justify-content: space-between;
	width: 100%;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options ul#shipping_method li label .woocommerce-Price-amount {
	font-weight: 400;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options ul#shipping_method li:not( :last-child ) {
	border-bottom: 1px solid #d6d7db;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options ul#shipping_method li input {
	vertical-align: middle;
	height: 18px;
	width: 19px;
	cursor: pointer;
	margin: 0;
	border: 1px solid #d1d5db;
	border-radius: 50%;
	line-height: 0;
	box-shadow: inset 0 1px 2px rgb( 0 0 0 / 10% );
	transition: 0.05s border-color ease-in-out;
	-webkit-appearance: none;
	padding: 0;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-method-options ul#shipping_method li input:checked::before {
	background-color: var( --wcf-primary-color );
	border-radius: 50px;
	content: "\2022";
	text-indent: -9999px;
	display: inline-block;
	font: normal normal 400 15px/1 cartflows-icon;
	vertical-align: middle;
	-webkit-font-smoothing: antialiased;
	margin: 5px;
	width: 6px;
	height: 6px;
}

.wcf-embed-checkout-form .woocommerce #payment #place_order::before {
	display: inline-block !important;
}

.wcf-embed-checkout-form-two-step .woocommerce-checkout .col2-set {
	margin-bottom: 0 !important;
}

/* End Move Shipping methods to main column */

@media only screen and ( max-width: 768px ) {
	.wcf-embed-checkout-form .woocommerce form .wcf-column-33,
	.wcf-embed-checkout-form .woocommerce form .wcf-column-50 {
		width: 100%;
	}
	.wcf-embed-checkout-form .woocommerce-checkout #order_review_heading {
		width: 100%;
	}
	.wcf-embed-checkout-form .woocommerce-checkout #order_review {
		width: 100%;
	}
	.wcf-embed-checkout-form .woocommerce-checkout .wcf-order-wrap {
		width: 100%;
	}
	.wcf-embed-checkout-form .woocommerce-checkout {
		display: block;
	}
	.wcf-embed-checkout-form .woocommerce .wcf-product-image .wcf-product-thumbnail {
		width: 30%;
		margin-right: 10px;
	}
	.wcf-embed-checkout-form .woocommerce .wcf-product-name {
		width: 70%;
	}

	.wcf-embed-checkout-form .woocommerce .wcf-product-image img {
		width: 100%;
		height: 100%;
	}

	.wcf-embed-checkout-form table.shop_table td strong.product-quantity {
		margin-right: 10px;
	}

	.wcf-embed-checkout-form .woocommerce .product-name .wcf-remove-product {
		line-height: 1;
	}

	.wcf-embed-checkout-form .woocommerce-checkout .col2-set,
	.wcf-embed-checkout-form .woocommerce-checkout .wcf-col2-set {
		display: block;
		width: 100%;
		padding-right: 0;
		margin: 20px 0 0;
	}

	.wcf-embed-checkout-form form.checkout_coupon .button {
		font-size: 12px;
	}

	.wcf-bump-order-field-wrap .wcf-bump-order-label {
		font-size: 16px;
	}

	/* Added for pro support */
	.wcf-embed-checkout-form .woocommerce .col2-set .col-1,
	.wcf-embed-checkout-form .woocommerce .col2-set .col-2,
	.wcf-embed-checkout-form .woocommerce-page .col2-set .col-1,
	.wcf-embed-checkout-form .woocommerce-page .col2-set .col-2,
	/* Added for pro support */
	.wcf-embed-checkout-form .woocommerce .wcf-col2-set .wcf-col-1,
	.wcf-embed-checkout-form .woocommerce .wcf-col2-set .wcf-col-2,
	.wcf-embed-checkout-form .woocommerce-page .wcf-col2-set .wcf-col-1,
	.wcf-embed-checkout-form .woocommerce-page .wcf-col2-set .wcf-col-2,
	.wcf-embed-checkout-form .woocommerce .wcf-order-wrap,
	.wcf-embed-checkout-form .woocommerce-page .wcf-order-wrap {
		padding: 15px 18px;
	}

	.wcf-embed-checkout-form .woocommerce form .form-row-first,
	.wcf-embed-checkout-form .woocommerce form .form-row-last,
	.wcf-embed-checkout-form .woocommerce-page form .form-row-first,
	.wcf-embed-checkout-form .woocommerce-page form .form-row-last {
		width: 100%;
	}

	.wcf-embed-checkout-form .woocommerce-additional-fields > h3,
	.wcf-embed-checkout-form .woocommerce-billing-fields > h3,
	.wcf-embed-checkout-form #order_review_heading,
	.wcf-embed-checkout-form .woocommerce #ship-to-different-address,
	.wcf-embed-checkout-form .wcf-shipping-methods > h3 {
		font-size: 1em;
	}

	/* Move Shipping methods to main column */

	.wcf-customer-info-main-wrapper {
		width: 100% !important;
		padding-right: 0 !important;
	}

	.wcf-shipping-methods {
		display: block;
		padding: 15px 18px;
	}

	.wcf-modern-skin-two-column .wcf-shipping-methods,
	.wcf-embed-checkout-form-two-step .wcf-shipping-methods,
	.wcf-modern-skin-one-column .wcf-shipping-methods {
		padding: 0 !important;
		display: inline-block;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-two-column .woocommerce form .wcf-shipping-methods-wrapper {
		width: 100%;
		float: left;
		padding: 15px 18px;
	}
	/* End Move Shipping methods to main column */
}

/**
 * **********************
 * Divi Css Overrite
 * **********************
 */

#et-info-email::before,
#et-info-phone::before,
#et_search_icon::before,
.comment-reply-link::after,
.et-cart-info span::before,
.et-pb-arrow-next::before,
.et-pb-arrow-prev::before,
.et-social-icon a::before,
.et_audio_container .mejs-playpause-button button::before,
.et_audio_container .mejs-volume-button button::before,
.et_overlay::before,
.et_password_protected_form .et_submit_button::after,
.et_pb_button::after,
.et_pb_contact_reset::after,
.et_pb_contact_submit::after,
.et_pb_font_icon::before,
.et_pb_newsletter_button::after,
.et_pb_pricing_table_button::after,
.et_pb_promo_button::after,
.et_pb_social_icon a.icon::before,
.et_pb_testimonial::before,
.et_pb_toggle_title::before,
.form-submit .et_pb_button::after,
.mobile_menu_bar::before,
.woocommerce #content input.button.alt::after,
.woocommerce #content input.button::after,
.woocommerce #respond input#submit.alt::after,
.woocommerce #respond input#submit::after,
.woocommerce a.button.alt::after,
.woocommerce button.button.alt::after,
.woocommerce button.button::after,
.woocommerce button.single_add_to_cart_button.button::after,
.woocommerce input.button.alt::after,
.woocommerce input.button::after,
.woocommerce-page #content input.button.alt::after,
.woocommerce-page #content input.button::after,
.woocommerce-page #respond input#submit.alt::after,
.woocommerce-page #respond input#submit::after,
.woocommerce-page a.button.alt::after,
.woocommerce-page a.button::after,
.woocommerce-page button.button.alt::after,
.woocommerce-page button.button::after,
.woocommerce-page input.button.alt::after,
.woocommerce-page input.button::after,
a.et_pb_more_button::after {
	text-shadow: 0 0;
	font-family: none;
	font-weight: 400;
	font-style: normal;
	font-variant: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	line-height: 1;
	text-transform: none;
	speak: none;
}

.comment-reply-link:hover::after,
.woocommerce #content input.button.alt:hover::after,
.woocommerce #content input.button:hover::after,
.woocommerce #respond input#submit.alt:hover::after,
.woocommerce #respond input#submit:hover::after,
.woocommerce a.button.alt:hover::after,
.woocommerce a.button:hover::after,
.woocommerce button.button.alt:hover::after,
.woocommerce button.button:hover::after,
.woocommerce input.button.alt:hover::after,
.woocommerce input.button:hover::after,
.woocommerce-page #content input.button.alt:hover::after,
.woocommerce-page #content input.button:hover::after,
.woocommerce-page #respond input#submit.alt:hover::after,
.woocommerce-page #respond input#submit:hover::after,
.woocommerce-page a.button.alt:hover::after,
.woocommerce-page a.button:hover::after,
.woocommerce-page button.button.alt:hover::after,
.woocommerce-page button.button:hover::after,
.woocommerce-page input.button.alt:hover::after,
.woocommerce-page input.button:hover::after {
	opacity: 0;
}

/**
 * **************************
 * Thrive Compatibility css
 * **************************
 */

.thrv_wrapper .wcf-embed-checkout-form div {
	box-sizing: border-box;
}

/**
 * **************************************************************
 * Remove Product variaton CSS after few updates of CartFlows Pro
 * **************************************************************
 */

.wcf-embed-checkout-form-two-column .woocommerce .wcf-product-option-wrap.wcf-product-option-before-order {
	width: 45%;
}

.wcf-embed-checkout-form-two-step .woocommerce.step-one .wcf-product-option-wrap.wcf-product-option-before-order {
	display: none;
}

/**
 * **************************************************************
 * Remove Product variaton CSS after few updates of CartFlows Pro
 * **************************************************************/
/**

* ********************
* Fields Skins
* ********************
*/

.wcf-embed-checkout-form.wcf-field-style-one .mt20 {
	margin-top: 0;
}

.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-form-login .form-row label:not( .checkbox ).woocommerce-form__label-for-checkbox {
	position: relative;
	margin: 0;
}

.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields .form-row label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-shipping-fields .form-row label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-additional-fields .form-row label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-account-fields .form-row label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-form-login .form-row label:not( .checkbox ) {
	position: absolute;
	z-index: 999;
	margin-top: 19px;
	margin-left: 15px;
	transition: all 0.235s ease;
	overflow: hidden;
	white-space: nowrap;
}

.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields .form-row.wcf-anim-label label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-shipping-fields .form-row.wcf-anim-label label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-additional-fields .form-row.wcf-anim-label label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-account-fields .form-row.wcf-anim-label label:not( .checkbox ),
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-form-login .form-row.wcf-anim-label label:not( .checkbox ) {
	margin-top: 8px;
	font-size: 12px;
}

/* Compatibility for the Astra Labels as placeholder */
.ast-checkout-labels-as-placeholders .wcf-embed-checkout-form form #customer_details .form-row label {
	opacity: 1;
	position: relative;
	padding: 0;
	margin: 0 0 8px 0;
	line-height: 1em;
}

.ast-checkout-labels-as-placeholders .wcf-embed-checkout-form.wcf-field-style-one form #customer_details .form-row label {
	position: absolute;
	margin-top: 19px;
	padding: 0 1.02em;
	opacity: 1;
}

.ast-checkout-labels-as-placeholders .wcf-embed-checkout-form.wcf-field-style-one form #customer_details .form-row.wcf-anim-label label {
	margin-top: 8px;
	font-size: 12px;
}

.ast-checkout-labels-as-placeholders .wcf-embed-checkout-form.wcf-field-style-one form #customer_details .woocommerce-account-fields .form-row label.woocommerce-form__label-for-checkbox {
	position: relative;
	margin: 0;
	padding: 0;
}

/* Compatibility for the Astra Labels as placeholder */

.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="text"],
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="email"],
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="password"],
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="tel"],
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row textarea,
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row select,
.wcf-embed-checkout-form.wcf-field-style-one .select2-container--default .select2-selection--single {
	padding: 25px 12px 5px;
}

.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields .form-row.wcf-anim-label-fix label,
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-shipping-fields .form-row.wcf-anim-label-fix label,
.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-account-fields .form-row.wcf-anim-label-fix label {
	position: relative;
	margin: 0;
}

.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-account-fields .form-row label.woocommerce-form__label-for-checkbox {
	position: relative;
	margin: 0;
}

/**
* ************************
* Optimize Checkout fields
* ************************
*/

.wcf-embed-checkout-form .woocommerce form .form-row.wcf-hide-field {
	-js-display: flex !important;
	display: flex !important;
	align-items: center;
	font-size: 13px;
}

.wcf-embed-checkout-form .wcf-hide-field label,
.wcf-embed-checkout-form .wcf-hide-field span {
	display: none !important;
}

.wcf-embed-checkout-form .woocommerce .woocommerce-billing-fields .form-row.wcf-hide-field.mt20 {
	margin-top: 0;
}

/**
* ***********************************
* Custom Width Classes
* ***********************************
*/

.wcf-embed-checkout-form .woocommerce form .wcf-column-33 {
	width: 33.33%;
}
.wcf-embed-checkout-form .woocommerce form .wcf-column-50 {
	width: 50%;
}
.wcf-embed-checkout-form .woocommerce form .wcf-column-100 {
	width: 100%;
	margin-top: 0 !important;
}

/**
* **********************
* One Column Layout
* **********************
*/

.wcf-embed-checkout-form-one-column {
	background: inherit;
	width: 100%;
	margin: 0 auto;
}

.wcf-embed-checkout-form-one-column .wcf-bump-order-wrap {
	float: none;
}

.wcf-embed-checkout-form-one-column .woocommerce .col2-set,
.wcf-embed-checkout-form-one-column .woocommerce form .wcf-shipping-methods-wrapper {
	display: block;
	margin-top: 10px;
	width: 100%;
	padding-right: 0;
}

.wcf-embed-checkout-form-one-column .woocommerce .col2-set .col-1,
.wcf-embed-checkout-form-one-column .woocommerce .col2-set .col-2,
.wcf-embed-checkout-form-one-column .woocommerce-checkout .shop_table,
.wcf-embed-checkout-form-one-column .woocommerce-checkout #order_review_heading,
.wcf-embed-checkout-form-one-column .woocommerce-checkout #your_products_heading,
.wcf-embed-checkout-form-one-column .woocommerce-checkout #payment,
.wcf-embed-checkout-form-one-column .woocommerce form.checkout_coupon,
.wcf-embed-checkout-form-one-column .woocommerce .wcf-order-wrap {
	width: 100%;
}

.wcf-embed-checkout-form-one-column input[type="text"],
.wcf-embed-checkout-form-one-column input[type="email"],
.wcf-embed-checkout-form-one-column input[type="password"],
.wcf-embed-checkout-form-one-column input[type="tel"],
.wcf-embed-checkout-form-one-column textarea,
.wcf-embed-checkout-form-one-column select {
	display: block;
	width: 100%;
	min-height: 40px;
	padding: 11px 12px;
	font-family: inherit;
	font-weight: inherit;
	font-size: 14px;
	line-height: 1.42857143;
	color: var( --wcf-field-text-color );
	background-color: var( --wcf-field-bg-color );
	background-image: none;
	border: 1px solid var( --wcf-field-border-color );
	border-radius: 3px;
	box-shadow: none;
}

.wcf-embed-checkout-form-one-column .woocommerce-checkout #order_review_heading,
.wcf-embed-checkout-form-one-column .woocommerce-checkout #your_products_heading {
	margin: 20px 0 0;
}

.wcf-embed-checkout-form-one-column .woocommerce-checkout #your_products_heading {
	display: inline-block;
	font-family: inherit;
	font-weight: 600;
	font-size: 20px;
	width: 100%;
	padding: 0 5px 30px;
	border: none;
	border-bottom: none;
}

.wcf-embed-checkout-form-one-column .woocommerce-checkout #order_review {
	width: 100%;
}

.wcf-embed-checkout-form ::-webkit-input-placeholder {
	/* Chrome/Opera/Safari */
	color: var( --wcf-field-text-color );
}
.wcf-embed-checkout-form ::-moz-placeholder {
	/* Firefox 19+ */
	color: var( --wcf-field-text-color );
}
.wcf-embed-checkout-form :-ms-input-placeholder {
	/* IE 10+ */
	color: var( --wcf-field-text-color );
}
.wcf-embed-checkout-form :-moz-placeholder {
	/* Firefox 18- */
	color: var( --wcf-field-text-color );
}

/**
* **********************
* Modern Checkout Layout
* **********************
*/

.wcf-embed-checkout-form-modern-checkout {
	--wcf-primary-color: #f16334; /* Used. */
	--wcf-heading-color: #111; /* Used. */
	--wcf-btn-bg-color: var( --wcf-primary-color ); /* Used. */
	--wcf-btn-bg-hover-color: var( --wcf-primary-color ); /* Used. */
	--wcf-btn-text-color: #fff; /* Used. */
	--wcf-btn-hover-text-color: #fff; /* Used. */
	--wcf-text-color: #9ca3af; /* Used. */
	--wcf-link-color: var( --wcf-primary-color ); /* Used. */
	--wcf-field-label-color: var( --wcf-text-color ); /* Used. */
	--wcf-field-bg-color: #fff; /* Used. */
	--wcf-field-border-color: #d1d5db; /* Used */
	--wcf-field-text-color: #16110e; /* Used. */
	--wcf-field-error-label-color: #e11e00; /* Used */
	--wcf-field-error-border-color: #e11e00; /* Used */
	--wcf-field-error-color: #e2401c; /* Used. */
	--wcf-payment-section-label-color: #555;
	--wcf-payment-section-desc-text-color: #515151;
	--wcf-payment-section-desc-bg-color: #f9f9f9;
	--wcf-payment-section-bg-color: #fff;
}

/* New Conditional checkout style */
/* Customer Info, Billing, Shipping & Additional Fields. */

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout {
	-js-display: flex;
	display: flex;
	flex-wrap: wrap;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment .woocommerce-error,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .woocommerce-error,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .woocommerce-NoticeGroup .woocommerce-error,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .woocommerce-notices-wrapper .woocommerce-error,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-customer-info .wcf-customer-info__notice.wcf-notice {
	background-color: #fff5f5;
	border: solid 1px #f3d5d8;
	padding: 20px;
	color: #a10000;
	margin: 1rem 0 1rem;
	font-size: 14px;
	width: 100%;
	border-radius: 4px;
	font-weight: 400;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .wcf-custom-coupon-field .woocommerce-error {
	padding: 0 1.5em 1.2em 1.5em;
	background-color: transparent;
	border: none;
	margin: 0;
	width: 100%;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .woocommerce-error li {
	margin: 3px 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .woocommerce-NoticeGroup,
#cpsw-payment-request-wrapper {
	flex: 1 0 100%;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-customer-info-main-wrapper {
	width: 60%;
	padding: 0 45px 0 0;
}

/* Right Column with background color */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .wcf-order-wrap {
	display: inline-block;
	float: none;
	width: 40%;
	padding: 0 0 0 15px;
	vertical-align: top;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-cartflows-review-order-wrapper {
	display: none;
	margin: 0 -20px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-order-review-toggle {
	display: none;
	padding: 20px;
	background-color: #f9fafb;
	border: 1px solid #d6d7db;
	border-left: none;
	border-right: none;
	color: #111;
	font-weight: 500;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-order-review-toggle-button {
	margin-left: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-order-review-toggle-button-wrap,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-order-review-toggle-button {
	font-size: 15px;
	vertical-align: middle;
	line-height: 1.4em;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-order-review-total {
	font-size: 18px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-order-review-toggle .cartflows-cheveron-up {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column .woocommerce-checkout #order_review {
	position: sticky;
	top: 60px;
	padding: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .wcf-col2-set,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .wcf-customer-info,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .wcf-payment-option-heading,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .woocommerce-checkout-payment,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-shipping-methods-wrapper {
	display: inline-block;
	width: 100%;
	padding-right: 0;
	margin: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-billing-fields__customer-info-wrapper {
	margin: 0 -7px;
}

/* Headings */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column #order_review_heading {
	position: sticky;
	top: 15px;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout h3,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
	font-size: 20px;
	font-weight: 500;
	color: var( --wcf-heading-color );
	padding: 0;
	margin: 22px 0 15px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #ship-to-different-address {
	font-size: 15px;
	font-weight: 400;
}

/* .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #ship-to-different-address [type="checkbox"]:checked:before{
	font-size:13px;
} */

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-shipping-fields label.checkbox {
	margin: 0;
	text-transform: none;
}
/* Headings End*/

/* Form Field Styling. */

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row input.input-text,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row textarea,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row select,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .input-text,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .select2-container--default .select2-selection--single {
	color: var( --wcf-field-text-color );
	border-color: var( --wcf-field-border-color );
	border-radius: 4px;
	font-size: 14px;
	padding: 15px 12px;
	line-height: 28px;
	outline: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .select2-container--default.field-required .select2-selection--single,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row input.input-text.field-required,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row textarea.input-text.field-required,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .input-text.field-required {
	border-color: var( --wcf-field-error-border-color );
}
.wcf-embed-checkout-form .woocommerce .wcf-field-required-error {
	color: var( --wcf-field-error-color );
	font-size: 12px;
}

/* .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .select2-container--default .select2-selection--single .select2-selection__rendered {
	font-size: 15px;
} */

body.cartflows_step-template .select2-container--default .select2-search--dropdown .select2-search__field:focus {
	outline: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row input.input-text:focus,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row textarea:focus,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #order_review .wcf-custom-coupon-field input.input-text:focus {
	outline: none;
	border-color: var( --wcf-primary-color );
	box-shadow: 0 0 0 1px var( --wcf-primary-color );
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row {
	margin: 0 0 10px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form #billing_country_field {
	margin: 0 0 15px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-customer-login-section .show-password-input,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .create-account .show-password-input {
	top: 15px;
	right: 12px;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .create-account .show-password-input::after {
	margin: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row.notes {
	margin-bottom: 0;
}

.wcf-embed-checkout-form .woocommerce form .woocommerce-account-fields .form-row {
	padding: 3px 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout ::placeholder,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row label,
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row.address-field .select2-selection--single .select2-selection__placeholder {
	font-size: 13px;
	color: var( --wcf-field-label-color );
	letter-spacing: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .form-row input[type="text"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .form-row input[type="email"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .form-row input[type="password"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .form-row input[type="tel"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .form-row textarea,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .form-row select,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce form .form-row .selection .select2-selection,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .woocommerce-billing-fields-custom .form-row input[type="email"] {
	padding: 22px 15px 8px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-field-style-one .woocommerce .woocommerce-billing-fields-custom .form-row label:not( .checkbox ) {
	position: absolute;
	z-index: 999;
	margin-top: 19px;
	margin-left: 15px;
	transition: all 0.235s ease;
	overflow: hidden;
	white-space: nowrap;
}

.wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields-custom .form-row.wcf-anim-label label:not( .checkbox ) {
	margin-top: 8px;
	font-size: 12px;
}

/* Form Field Styling End. */

/* Ship to different address checkbox, Stripe save payment checkbox */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .wcf-create-account-section [type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .woocommerce-shipping-fields [type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box.payment_method_stripe [type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce input[type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #order_review input[type="checkbox"] {
	border-radius: 4px;
	height: 18px;
	margin: 0 5px 4px 0;
	padding: 0;
	width: 18px;
	min-width: 18px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .wcf-create-account-section [type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .woocommerce-shipping-fields [type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box.payment_method_stripe [type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #order_review input[type="checkbox"]:checked::before {
	display: inline-block;
	font: normal normal 400 15px/1 cartflows-icon;
	vertical-align: middle;
	width: 100%;
	content: "\e600";
	margin: 1px 0;
	color: var( --wcf-primary-color );
}

/* Ship to different address checkbox, Stripe save payment checkbox End. */

/* Order Table */

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table {
	margin-bottom: 0;
	padding: 15px 0 0 0;
	background-color: #fff;
	border: 1px solid #d4d4d4;
	border-collapse: collapse;
	border-radius: 4px;
	border-style: hidden;
	box-shadow: 0 0 0 1px #d6d7db;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tbody {
	border-top: 1px solid #e5e7eb;
	border-bottom: 1px solid #e5e7eb;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .order-total {
	font-size: 1.2em;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table th.product-name,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table th.product-total {
	padding: 1em 1.2em 1em 1.2em;
	color: #555;
	font-size: 15px;
	font-weight: 500;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .cart_item:not( :first-child ) .product-name,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .cart_item:not( :first-child ) .product-total {
	padding-top: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.order-total:not( .recurring-total ) th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.order-total:not( .recurring-total ) td {
	border-top: 1px solid #e5e7eb;
	font-size: large;
	/* padding: 1em 1em 1em 1.2em; */
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr:first-child td,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr:first-child th {
	padding: 1.5em 1.2em 1.5em 1.2em;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.cart-discount th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.cart-subtotal th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .woocommerce-shipping-totals th {
	font-weight: 500;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.cart-discount,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.cart-subtotal {
	border-bottom: 1px solid #e5e7eb;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table td,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table td dl dt,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table td dl dd {
	background-color: transparent;
	border: none;
	color: #555;
	padding: 1.5em 1.2em 1.5em 1.2em;
	line-height: 1.4em;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .woocommerce-shipping-totals th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .woocommerce-shipping-totals td {
	padding: 1.5em 1.2em 1.5em 1.2em;
	vertical-align: top;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .woocommerce-shipping-methods li {
	white-space: nowrap;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table ul#shipping_method li:last-child {
	margin-bottom: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .woocommerce-shipping-methods li input {
	margin-top: 5px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tr th:nth-child( 2 ),
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tr td:nth-child( 2 ) {
	text-align: right;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tr.woocommerce-shipping-totals td:nth-child( 2 ) {
	text-align: right;
	padding-left: 0;
}

.wcf-embed-checkout-form table.shop_table tr.woocommerce-shipping-totals .wcf-shipping-tooltip {
	position: relative;
}

.wcf-embed-checkout-form table.shop_table tr.woocommerce-shipping-totals .wcf-shipping-tooltip:hover .wcf-tooltip-msg {
	display: block;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-shipping-methods-wrapper .wcf-shipping-error-msg {
	background-color: #fff5f5;
	border: solid 1px #f3d5d8;
	padding: 20px;
	color: #a10000;
	margin: 1rem 0 1rem;
	font-size: 14px;
	width: 100%;
	border-radius: 4px;
	font-weight: 400;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .woocommerce-checkout-review-order-table .wcf-shipping-error-msg {
	color: #a10000;
}

.wcf-embed-checkout-form .woocommerce form .wcf-shipping-methods-wrapper .wcf-shipping-error-msg {
	display: block;
	background-color: #fff6f6;
	border: dashed 1px #a00;
	padding: 20px;
	color: #a00;
	margin: 1rem 0 1rem;
	font-size: 14px;
	font-weight: 500; /* Default Font Weight. */
	width: 100%;
}
.wcf-embed-checkout-form .woocommerce form .woocommerce-checkout-review-order-table .wcf-shipping-error-msg {
	color: #a00;
}

.wcf-embed-checkout-form table.shop_table tr.woocommerce-shipping-totals .wcf-tooltip-msg {
	display: none;
	position: absolute;
	background-color: #333;
	color: #fff;
	min-width: 260px;
	padding: 8px 10px;
	border-radius: 4px;
	font-size: 12px;
	right: -15px;
	top: 25px;
	text-align: left;
	z-index: 1;
}

.wcf-embed-checkout-form table.shop_table tr.woocommerce-shipping-totals .wcf-tooltip-msg::before {
	position: absolute;
	content: "";
	margin-left: -5px;
	border-width: 5px;
	border-style: solid;
	border-color: transparent transparent #444 transparent;
	top: -10px;
	right: 20px;
}

/* Remove the bottom margin of last element of order summary table */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column .woocommerce-checkout #order_review *:last-child {
	margin-bottom: 0;
}

/* Order Table End */

/* Coupon code section */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field {
	clear: left;
	-js-display: flex;
	display: flex;
	/* margin: 0; */
	border-top: none;
	border-bottom: none;
	padding: 0;
	flex-wrap: wrap;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field.wcf-coupon-applied {
	padding-bottom: 0.8em;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field.wcf-coupon-applied .wcf-coupon-col-1,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field.wcf-coupon-applied .wcf-coupon-col-2 {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field a#wcf_optimized_wcf_custom_coupon_field {
	width: 100%;
	text-align: left;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field a#wcf_optimized_wcf_custom_coupon_field .dashicons {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field .wcf-coupon-col-1 {
	padding-right: 15px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #order_review button.wcf-btn-small {
	transition: 0.1s ease-in-out;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field button.wcf-submit-coupon,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #order_review button.wcf-btn-small,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field input.input-text {
	height: 100%;
	width: 100%;
	padding: 11px;
	font-size: 15px;
	border-radius: 4px;
}
/* Coupon code section End. */

/* Payment Method */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods {
	background: var( --wcf-payment-section-bg-color );
	padding: 0;
	margin: 0 0 0.5em 0;
	border: 1px solid #d6d7db;
	border-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment .form-row {
	padding: 15px 0 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #payment .woocommerce-privacy-policy-text p {
	font-size: 12px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
	padding: 15px 12px 0 12px;
	min-height: 40px;
	border-bottom: 1px solid #d6d7db;
	display: inline-table;
	width: 100%;
	/* margin-bottom: 15px; */
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:first-child {
	border-radius: 4px 4px 0 0;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li:last-child {
	border-bottom: 0;
	border-radius: 0 0 4px 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment ul.payment_methods > li select {
	display: block;
	width: 100%;
	min-height: 34px;
	height: auto;
	padding: 11px 12px;
	font-size: 14px;
	line-height: 1.42857143 !important;
	color: var( --wcf-text-color );
	background-color: #fff;
	background-image: none;
	border: 1px solid;
	border-color: var( --wcf-field-border-color );
	border-radius: 4px;
	box-shadow: none;
	outline: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-bump-order-wrap.wcf-before-checkout .wcf-bump-order-field-wrap input[type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-bump-order-wrap.wcf-after-customer .wcf-bump-order-field-wrap input[type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-bump-order-wrap.wcf-after-order .wcf-bump-order-field-wrap input[type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-product-option-wrap .wcf-qty-row div [type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-product-option-wrap .wcf-qty-row input[type="radio"] {
	color: #d1d5db;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment .wc_payment_method > [type="radio"]:not( :checked ) {
	margin-bottom: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment .wc_payment_method > [type="radio"]:not( :checked ) + label {
	vertical-align: top;
	margin-bottom: 12px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment label {
	font-weight: 500;
	display: inline-block;
	width: calc( 100% - 10% );
	color: var( --wcf-payment-section-label-color );
	font-size: 15px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box {
	margin: 15px 0 0 -12px;
	border-top: 1px solid #e5e7eb;
	color: var( --wcf-payment-section-desc-text-color );
	background-color: var( --wcf-payment-section-desc-bg-color );
	padding: 20px;
	width: calc( 100% + 24px );
}

/* Stripe Gateway Fields CSS */
/* Inline form */
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-cc,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-number,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-expiry,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-cvc {
	display: block;
	border: 1px solid #d1d5db;
	margin: 10px 0;
	padding: 0;
	background: #fff;
	cursor: text;
	border-radius: 4px;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-cc .__PrivateStripeElement,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-number .__PrivateStripeElement,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-expiry .__PrivateStripeElement,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-cvc .__PrivateStripeElement {
	margin-top: 0 !important;
	padding: 0.8em !important;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-number,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-expiry,
.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-cvc {
	margin-top: 5px;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form span + br {
	display: none;
}

.wcf-embed-checkout-form .woocommerce-checkout #payment .cpsw-stripe-elements-form .cpsw-save-cards {
	margin-top: 0;
	padding: 8px 0;
}
/* Inline form */

/* Stripe Gateway Fields CSS */

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment .payment_box .clear {
	clear: both;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment .payment_box .clear::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment .payment_box .clear::after {
	content: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box ul {
	/* padding: 0; */
	margin: 10px 0;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box ul.woocommerce-SavedPaymentMethods:empty {
	display: none;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box .cpsw_stripe_element_payment_form {
	margin-top: -1rem;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment .payment_method_woocommerce_payments .testmode-info {
	padding: 0 7px;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box fieldset {
	border: none;
	padding: 0 !important;
	margin: 0;
}

#wc-stripe-upe-form iframe #card-panel .p-Label,
.p-Label {
	margin-bottom: 10px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wc-stripe-elements-field {
	min-height: 40px;
	padding: 12px 10px 8px 10px;
	border-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #stripe-exp-element {
	margin-right: 15px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment div.payment_box::before {
	content: "";
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #add_payment_method #payment .payment_method_paypal img,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-cart #payment .payment_method_paypal img,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment .payment_method_paypal img,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #payment .payment_method_paypal .about_paypal {
	max-height: 40px;
	float: none;
	line-height: 1.4em;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #order_review .place-order {
	padding: 15px 0 0;
}
/* Payment Method End. */

/* Checkbox and Radio button */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment input[type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment input[type="radio"] {
	height: 18px;
	margin: 0 7px 0 0;
	width: 18px;
	min-width: 18px;
	border-color: #d1d5db;
	vertical-align: text-top;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment .woocommerce-SavedPaymentMethods input[type="radio"] {
	margin-left: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment input[type="radio"]:checked::before {
	margin: 5px;
	width: 6px;
	height: 6px;
}
/* Checkbox and Radio button End. */

/* Order review buttons */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #payment button:not( .js-woopayments-copy-test-number ) {
	line-height: 1.5;
	width: 100%;
	font-size: 18px;
	font-weight: 600;
	letter-spacing: 0;
	border-radius: 4px;
	padding: 12px 5px;
	transition: 0.1s ease-in-out;
	-js-display: inline-flex;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

/* .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce #order_review button.wcf-btn-small {
	padding: 11px;
    font-size: 15px;
} */
/* Order review buttons End. */

/* Customer Info : Email */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .woocommerce-billing-fields-custom__field-wrapper p {
	padding: 0;
	margin-bottom: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-customer-login-section {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-customer-info .wcf-show {
	display: block;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .woocommerce-billing-fields__customer-login-label {
	font-size: 14px;
	text-transform: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .wcf-customer-login-actions {
	-js-display: flex;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 3px 7px;
	margin: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .wcf-customer-login-section__login-button {
	width: auto;
	padding: 8px 15px;
	color: #fff;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-customer-login-section__lost-password {
	padding: 5px 7px;
	margin-top: 3px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-login-section-message {
	padding: 10px 7px;
	margin: 0;
	font-size: 14px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-create-account-section {
	margin-top: 10px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-create-account-section .woocommerce-password-strength {
	font-weight: 400;
	color: #111;
	font-size: 14px;
	padding: 5px 0.5em;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-create-account-section .short {
	background-color: #ffdbdf;
	border-color: #dd5c5c;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .woocommerce-billing-fields-custom h3 {
	-js-display: flex;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-email-validation-block {
	font-size: 12px;
	display: block;
	margin-top: 5px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-email-validation-block.success {
	color: #008000;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-email-validation-block.error {
	color: #e11e00;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-logged-in-customer-info {
	padding: 5px 7px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .wcf-customer-info__notice a,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form #customer_details .woocommerce-account-fields {
	display: none;
}

/* Customer Info : Email End. */

/* Order Review End. */

/* Responsive */
@media only screen and ( max-width: 768px ) {
	/* Move Shipping methods to main column */
	.wcf-embed-checkout-form .woocommerce form .wcf-shipping-methods {
		padding: 15px 18px;
	}
	.wcf-embed-checkout-form .woocommerce form .wcf-customer-info-main-wrapper {
		width: 100% !important;
		padding-right: 0 !important;
	}
	/* End Move Shipping methods to main column */

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce {
		padding: 0 20px;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .wcf-col2-set,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-customer-info-main-wrapper,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .wcf-order-wrap {
		width: 100%;
		padding: 0;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .woocommerce-billing-fields-custom h3 {
		display: block;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .woocommerce-billing-fields__customer-login-label {
		font-size: 12px;
		text-transform: none;
		margin-top: 10px;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column .woocommerce-checkout {
		-js-display: flex;
		display: flex;
		flex-direction: column;
		text-align: inherit;
		width: 100%;
		padding: 0 0 18px;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column .woocommerce-NoticeGroup {
		order: 1;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column .wcf-order-wrap {
		order: 2;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column .wcf-customer-info-main-wrapper {
		order: 3;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review .wcf-custom-coupon-field,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review table.shop_table,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-cartflows-review-order-wrapper {
		display: none;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column #order_review .wcf-payment-option-heading,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-two-column #order_review #payment {
		display: block;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #order_review_heading {
		display: none;
		font-size: 21px;
		padding: 0;
		position: relative;
		top: 0;
		text-transform: capitalize;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table {
		margin-bottom: 0;
		padding: 15px 0 0 0;
		background-color: #f9fafb;
		border-collapse: collapse;
		border-radius: 0;
		box-shadow: none;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field input.input-text {
		width: 100%;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field input.input-text:focus {
		border-color: var( --wcf-primary-color );
		box-shadow: 0 0 0 1px var( --wcf-primary-color );
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table thead {
		visibility: hidden;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table th.product-name,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table th.product-total {
		padding: 0 !important;
		font-size: 0 !important;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tbody {
		border-top: none;
	}

	/* .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tbody tr.cart_item:first-child td,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.order-total:not( .recurring-total ) th {
		border-top: 1px solid #d6d7db;
	} */
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.cart-discount,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr.cart-subtotal {
		border-bottom: none;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr:first-child td,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table tfoot tr:first-child th {
		padding: 1.5em 1.2em 1em 1.2em;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .woocommerce-shipping-totals th,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .woocommerce-shipping-totals td {
		padding: 1em 1.2em 1.5em 1.2em;
		vertical-align: top;
	}
	/* .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-customer-info-main-wrapper h3{
        margin: 10px ​0 20px;
    } */

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout .wcf-col2-set {
		padding: 0;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-order-review-toggle {
		-js-display: flex;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 0 -20px;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-collapsed-order-review-section.wcf-show .wcf-cartflows-review-order-wrapper {
		display: block !important;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-collapsed-order-review-section.wcf-show .cartflows-cheveron-down {
		display: none;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-collapsed-order-review-section.wcf-show .cartflows-cheveron-up {
		display: inline;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .col2-set .col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .col2-set .col-2,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-page .col2-set .col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-page .col2-set .col-2,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .wcf-col2-set .wcf-col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce .wcf-col2-set .wcf-col-2,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-page .wcf-col2-set .wcf-col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-page .wcf-col2-set .wcf-col-2 {
		padding: 0;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout #stripe-exp-element {
		margin-right: 0;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout #your_products_heading {
		margin-top: 0;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce form .form-row.notes {
		margin-bottom: 0;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field {
		background-color: #f9fafb;
		margin: 0 0 15px;
		padding: 0 20px 20px;
	}
}

@media only screen and ( max-width: 480px ) {
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field .wcf-coupon-col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field .wcf-coupon-col-2 {
		width: 100%;
		padding-right: 0;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .wcf-custom-coupon-field .wcf-coupon-col-1 {
		margin-bottom: 15px;
	}
}
/* Responsive End. */
/* New Conditional checkout style End. */

/**
* *********************
* New loader for modern Checkout layout
* ********************* */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout table.shop_table .blockUI.blockOverlay {
	opacity: 1 !important;
	width: 99% !important;
	height: 99% !important;
	top: 2px !important;
	left: 2px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce table.shop_table .blockUI.blockOverlay::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout-payment .blockUI.blockOverlay::before {
	background-repeat: no-repeat;
	top: -4px;
	background-position: center;
	left: -15px;
	background-size: cover;
	animation: none;
	height: 100%;
	width: 100%;
	margin: 0;
	transform: scale( 0.92 );
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce table.shop_table .blockUI.blockOverlay::before {
	background: url( "../images/order-review-skeleton.svg" ) left top;
}

/* Payment loader */
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout-payment .blockUI.blockOverlay {
	opacity: 1 !important;
	border: 1px solid #ddd !important;
	border-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout-payment .blockUI.blockOverlay::before {
	background: url( "../images/cartflows-payment-section-loader.svg" ) left top;
	left: -5px;
}
/* Payment loader */

/**
* *********************
* New loader for modern Checkout layout
* ********************* */

/**
* **********************
* Modern Checkout Layout
* **********************
*/

/**
* **********************************
* Modern Checkout One-Column Layout
* **********************************
*/
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-one-column table.shop_table #shipping_method,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-one-column .wcf-customer-info-main-wrapper,
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-one-column .woocommerce-checkout .wcf-order-wrap {
	width: 100%;
	padding: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-one-column table.shop_table #shipping_method {
	text-align: right;
}

/* .wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-one-column .wcf-custom-coupon-field {
	padding: 0;
	margin-bottom: 0;
} */
/**
* **********************************
* Modern Checkout One-Column Layout
* **********************************
*/

:root > .wcf-field-modern-label {
	--wcf-text-color: #5f6061; /* Used. */
	--wcf-field-label-color: var( --wcf-text-color ); /* Used. */
}

/* Modern Floating Style */
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce #customer_details .form-row:not( .wcf-anim-hidden-label ).wcf-anim-label:has( .select2 .select2-selection__placeholder ) > label,
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce #customer_details .form-row:not( .wcf-anim-hidden-label ) label:not( .woocommerce-form__label-for-checkbox, .checkbox, .input-radio ) {
	-webkit-user-select: none;
	user-select: none;
	pointer-events: none;
	position: absolute;
	color: var( --wcf-field-label-color );
	transition: all 0.2s ease-out;
	font-size: 12px;
	font-weight: 300;
	margin: 8px 2px;
	padding: 0 11px;
	/* line-height: 28px; */
	opacity: 0;
	height: auto;
	width: auto;
	clip-path: none;
	clip: unset;
}

.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row input.input-text,
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row textarea,
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row select,
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce #order_review .input-text {
	min-height: 47px;
	padding: 15px 12px;
	transition-delay: 0s, 0s;
	transition-duration: 0.2s, 0s;
	transition-property: all, width;
	transition-timing-function: ease-out, ease;
}

.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce #customer_details .form-row.wcf-anim-label label:not( .woocommerce-form__label-for-checkbox ) {
	opacity: 1;
	z-index: 1000;
}

.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row.wcf-anim-label input.input-text,
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row.wcf-anim-label select,
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row.wcf-anim-label textarea {
	padding: 1.65em 0.8em 0.5em;
}

.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row.wcf-anim-label .select2-container--default .select2-selection--single:not( :has( .select2-selection__placeholder ) ) {
	padding: 1.6em 0.8em 0.5em;
}

.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row.address-field .select2-selection--single .select2-selection__rendered {
	padding: 0;
}

/* Placeholder */
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row [placeholder]:focus::-webkit-input-placeholder {
	opacity: 1;
	line-height: inherit;
}
/* Modern Floating Style */

/* Google Places Dropdown CSS */

body .pac-container {
	background-color: #fff;
	position: absolute !important;
	z-index: 1000;
	border-radius: 2px;
	border: 1px solid var( --wcf-field-border-color );
	border-top: none;
	font-family: inherit;
	box-shadow: none;
	box-sizing: border-box;
	overflow: hidden;
}

body .pac-logo::after,
body .hdpi.pac-logo::after {
	content: none;
	display: none;
	background-image: none;
}

body .pac-item {
	cursor: pointer;
	padding: 5px 15px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	line-height: 30px;
	text-align: left;
	border-top: 1px solid #e6e6e6;
	font-size: 11px;
	color: #515151;
}

body .pac-item:hover {
	background-color: #fafafa;
}
body .pac-item-selected,
body .pac-item-selected:hover {
	background-color: #ebf2fe;
}
body .pac-matched {
	font-weight: 700;
}
body .pac-item-query {
	font-size: 13px;
	padding-right: 3px;
	color: #000;
}

body .pac-icon,
body .hdpi .pac-icon {
	display: none;
	background-image: none;
	background-size: 0;
}

body .pac-placeholder {
	color: #808080;
}
/* Google Places Dropdown CSS */

/* Checkout Plugin - Stripe for WooCommerce Smart Button box CSS */
.wcf-embed-checkout-form #cpsw-payment-request-wrapper.checkout.cpsw-classic .cpsw-payment-request-button-wrapper {
	margin-top: 40px;
}

.wcf-embed-checkout-form-two-step #cpsw-payment-request-wrapper.checkout.cpsw-classic .cpsw-payment-request-button-wrapper {
	margin-top: 10px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout #cpsw-payment-request-wrapper.checkout.cpsw-classic .cpsw-payment-request-button-wrapper {
	margin-top: 0;
}

/* Checkout Plugin - Stripe for WooCommerce Smart Button box CSS */

/**
* Mainly for Official - Stripe for WooCommerce Smart Button box & for rest of the gateways.
*
* The selector -payment-request-wrapper & -payment-request-button-separator remains the same for.
* most of the payment gateways and they are pre-pended with payment gateway key.
*/
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout [id*="-payment-request-wrapper"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-modern-checkout .woocommerce-checkout [id*="-payment-request-button-separator"] {
	width: 100%;
}
/* Official - Stripe for WooCommerce Smart Button box CSS */

.shop_table tr.woocommerce-shipping-totals.shipping {
	display: none;
	visibility: hidden;
}

.wcf-embed-checkout-form table.shop_table .wc_shipping_dpd_terminals {
	display: none;
	visibility: hidden;
}

.wcf-embed-checkout-form-two-column .wcf-dpd-baltic-wrap {
	float: left;
}

.wcf-dpd-baltic-wrap {
	position: relative;
}

/**
* ***********************************
* Instant Checkout Layout Style Start
* ***********************************
*/

.wcf-embed-checkout-form-instant-checkout .wcf-after-customer {
	margin: 0 0 20px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout {
	--wcf-primary-color: #111;
	--wcf-text-color: #555;
	--wcf-heading-color: #111; /* Used. */
	--wcf-heading-font-weight: 500; /* Used. */
	--wcf-link-color: var( --wcf-primary-color ); /* Used. */

	--wcf-ic-section-border-color: #d6d7db; /* Used. */
	--wcf-ic-right-column-bg-color: #fff; /* Used. */
	--wcf-ic-left-column-bg-color: #fff; /* Used. */
	--wcf-ic-shipping-shadow-color: #d6d7db; /* Used. */
	/* --wcf-ic-button-hover-color: #777; */

	--wcf-btn-bg-color: var( --wcf-primary-color ); /* Used. */
	--wcf-btn-bg-hover-color: var( --wcf-primary-color ); /* Used. */
	--wcf-btn-text-color: #fff; /* Used. */
	--wcf-btn-hover-text-color: #fff; /* Used. */

	--wcf-field-label-color: #707070; /* Used. */
	--wcf-field-min-height: "48px"; /* Used. */
	--wcf-field-bg-color: #fff; /* Used. */
	--wcf-field-border-color: #dedede; /* Used */
	--wcf-field-text-color: #555; /* Used. */

	--wcf-field-error-label-color: #e11e00; /* Used */
	--wcf-field-error-border-color: #e11e00; /* Used */
	--wcf-field-error-color: #e2401c; /* Used. */

	--wcf-payment-section-bg-color: #fff;
	--wcf-payment-section-desc-bg-color: #f9fafb;
	--wcf-payment-section-desc-text-color: #111;
	--wcf-payment-section-label-color: var( --wcf-text-color );
}
/** Start Radio Button Style */

/* Empty Cart Block design*/

.wcf-empty-cart-notice-block {
	margin-top: 2rem;
}
.wcf-empty-cart-message-container {
	max-width: 1140px;
	width: 100%;
	border: 1px solid #d1d5db;
	border-radius: 16px;
	margin: 0 auto;
	padding: 40px;
}
.wcf-empty-cart-message-container .wcf-empty-cart-wrapper {
	-js-display: flex;
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	gap: 20px;
}
.wcf-empty-cart-message-container .wcf-empty-cart-content .wcf-empty-cart-heading,
.wcf-empty-cart-message-container .wcf-empty-cart-content .wcf-empty-cart-message,
.wcf-empty-cart-message-container .wcf-empty-cart-content .wcf-empty-cart-button {
	display: block;
	margin: 0 0 0 0;
}

.wcf-empty-cart-message-container .wcf-empty-cart-content .wcf-empty-cart-heading {
	color: #1f2937;
	font-size: 20px;
	font-weight: 600;
	line-height: 30px;
	text-align: center;
	margin-bottom: 6px;
}

.wcf-empty-cart-message-container .wcf-empty-cart-content .wcf-empty-cart-message {
	color: #6b7280;
	font-size: 14px;
	font-weight: 400;
	line-height: 20px;
	margin-bottom: 20px;
	text-align: center;
}

.wcf-empty-cart-message-container .wcf-empty-cart-content .wcf-empty-cart-button {
	background-color: var( --wcf-primary-color );
	color: #fff;
	padding: 9px 17px 9px 17px;
	border-radius: 6px;
	width: -moz-fit-content;
	width: fit-content;
	justify-content: center;
	margin: 15px auto 0 auto;
	text-decoration: none;
}

.wcf-empty-cart-message-container .wcf-empty-cart-content .wcf-empty-cart-button:hover {
	background-color: color-mix( in sRGB, var( --wcf-primary-color ) 80%, transparent );
}

.wcf-empty-cart-message-container .wcf-empty-cart-icon {
	background: #fff;
	border-radius: 50px;
	-js-display: flex;
	display: flex;
	align-items: center;
	padding: 16px;
}
.wcf-empty-cart-message-container .wcf-empty-cart-icon svg {
	height: 35px;
	width: 35px;
	color: var( --wcf-primary-color );
}
/* Empty Cart Block design*/

/* page's background color */

body.cartflows-instant-checkout {
	background-color: #f5f5f5;
}

body.cartflows-instant-checkout .main-header--wrapper {
	border-bottom: 1px solid #dedede;
	-js-display: flex;
	display: flex;
	justify-content: center;
	background-color: #fff;
}

body.cartflows-instant-checkout .main-header--content {
	max-width: 1140px;
	width: 100%;
	/* margin: 0; */
	padding: 0.704rem 0;
	align-items: center;
	/* justify-content: space-between; */
	display: grid;
	grid-template-columns: minmax( min-content, calc( 50% + calc( calc( 60rem - 52rem ) / 2 ) ) ) 1fr;
}

body.cartflows-instant-checkout .main-header--content .main-header--site-logo img {
	width: auto;
}

body.cartflows-instant-checkout .main-header--content .main-header--site-title {
	font-size: 21px;
	font-weight: 500;
	text-transform: capitalize;
	margin: 8px 0;
}

body.cartflows-instant-checkout .main-header--content .main-header--action {
	-js-display: flex;
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

body.cartflows-instant-checkout .main-header--content .main-header--action .main-header--action__cart-button {
	width: 24px;
	height: 24px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-instant-checkout-wrapper {
	display: grid;
	grid-template-columns: minmax( min-content, calc( 50% + calc( calc( 60rem - 52rem ) / 2 ) ) ) 1fr;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-order-wrap #order_review_heading {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-instant-checkout-wrapper .wcf-customer-info-main-wrapper {
	-js-display: flex;
	display: flex;
	justify-content: flex-end;
	background-color: var( --wcf-ic-left-column-bg-color );
	border-right: 1px solid #dedede;
	block-size: 100%;
	width: 100%;
	padding: 0;
	align-items: flex-start;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout .wcf-col2-set,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout .wcf-customer-info,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout .wcf-payment-option-heading,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout .woocommerce-checkout-payment {
	display: inline-block;
	width: 100%;
	padding-right: 0;
	margin: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-order-review-toggle {
	display: none;
	padding: 20px;
	background-color: #f9fafb;
	border: 1px solid #dedede;
	border-left: none;
	border-right: none;
	color: #111;
	font-weight: 500;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-order-review-toggle .cartflows-cheveron-up {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-order-review-toggle-button-wrap,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-order-review-toggle-button {
	font-size: 15px;
	vertical-align: middle;
	line-height: 1.4em;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-collapsed-order-review-section .wcf-order-review-total {
	font-size: 20px;
	font-weight: 500;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-order-review-toggle-button {
	margin-left: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-cartflows-review-order-wrapper {
	display: none;
	margin: 0 -20px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-instant-checkout-wrapper .wcf-ic-layout-right-column {
	display: block;
	background-color: var( --wcf-ic-right-column-bg-color );
	min-height: 100vh;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-create-account-section [type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .woocommerce-shipping-fields [type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment div.payment_box.payment_method_stripe [type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce input[type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #order_review input[type="checkbox"] {
	border-radius: 4px;
	height: 18px;
	margin: 0 5px 4px 0;
	padding: 0;
	width: 18px;
	min-width: 18px;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-create-account-section [type="checkbox"]:checked,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .woocommerce-shipping-fields [type="checkbox"]:checked,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment div.payment_box.payment_method_stripe [type="checkbox"]:checked,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce input[type="checkbox"]:checked,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #order_review input[type="checkbox"]:checked {
	background-color: var( --wcf-primary-color );
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-create-account-section [type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .woocommerce-shipping-fields [type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment div.payment_box.payment_method_stripe [type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #order_review input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-product-option-wrap .wcf-qty-row div [type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-bump-order-wrap.wcf-before-checkout .wcf-bump-order-field-wrap input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-bump-order-wrap.wcf-after-customer .wcf-bump-order-field-wrap input[type="checkbox"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-bump-order-wrap.wcf-after-order .wcf-bump-order-field-wrap input[type="checkbox"]:checked::before {
	color: #fff;
	font-size: 12px;
	width: auto;
	margin: 2px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-product-option-wrap .wcf-qty-row input[type="radio"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li input,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment input[type="checkbox"],
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment input[type="radio"] {
	height: 18px;
	margin: 2px 7px 0 0;
	width: 18px;
	min-width: 18px;
	border-color: #d1d5db;
	vertical-align: text-top;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment input[type="radio"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #wcf-ic-shipping input[type="radio"]:checked::before {
	margin: 5px !important;
}

/* Changed design of radio button only for instant checkout layout */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-product-option-wrap .wcf-qty-row input[type="radio"]:checked,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li input:checked,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment input[type="checkbox"]:checked,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment input[type="radio"]:checked {
	background-color: var( --wcf-primary-color );
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form #payment input[type="checkbox"]:checked::before {
	color: #fff;
	font-size: 12px;
	width: auto;
	margin: 2px;
}

body.cartflows-instant-checkout .select2-container--default .select2-results__option--highlighted[aria-selected],
body.cartflows-instant-checkout .select2-container--default .select2-results__option--highlighted[data-selected] {
	background-color: #111 !important;
}

/* Changed design of radio button only for instant checkout layout */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-product-option-wrap .wcf-qty-row input[type="radio"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li input:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment input[type="radio"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #wcf-shipping input[type="radio"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #order_review input[type="radio"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-col2-set input[type="radio"]:checked::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #wcf-ic-shipping input[type="radio"]:checked::before {
	background-color: #fff !important;
	border-radius: 50px;
	content: "\2022";
	font-size: 24px;
	height: 6px;
	line-height: 16px;
	margin: 5px;
	text-indent: -9999px;
	width: 6px;
}

/**
* *********************
* New loader for Instant Checkout layout
* ********************* */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout table.shop_table .blockUI.blockOverlay {
	opacity: 1 !important;
	width: 99% !important;
	height: 99% !important;
	top: 2px !important;
	left: 2px !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce table.shop_table .blockUI.blockOverlay::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout-payment .blockUI.blockOverlay::before {
	background-repeat: no-repeat;
	top: -4px;
	background-position: center;
	left: -15px;
	background-size: cover;
	animation: none;
	height: 100%;
	width: 100%;
	margin: 0;
	transform: scale( 0.92 );
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce table.shop_table .blockUI.blockOverlay::before {
	background: url( "../images/order-review-skeleton.svg" ) left top;
}

/* Payment loader */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout-payment .blockUI.blockOverlay {
	opacity: 1 !important;
	border: 1px solid #ddd !important;
	border-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout-payment .blockUI.blockOverlay::before {
	background: url( "../images/cartflows-payment-section-loader.svg" ) left top;
	left: -5px;
}
/* Payment loader */

/**
* *********************
* New loader for instant Checkout layout
* ********************* */

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods {
	background: var( --wcf-payment-section-bg-color );
	padding: 0;
	margin: 0 0 0.5em 0;
	/* border: 1px solid #d6d7db; */
	border-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:first-child,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout #wcf-ic-shipping ul.woocommerce-shipping-methods > li:first-child {
	border-radius: 4px 4px 0 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:first-child,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout #wcf-ic-shipping ul.woocommerce-shipping-methods > li:first-child,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ):has( input[type="radio"]:checked ):first-child {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:last-child,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout #wcf-ic-shipping ul.woocommerce-shipping-methods > li:last-child,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ):has( input[type="radio"]:checked ):last-child {
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ):has( input[type="radio"][name="payment_method"]:checked ) {
	border: 1px solid var( --wcf-primary-color );
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ):has( input[type="radio"]:checked ) div.payment_box {
	border-top: 1px solid var( --wcf-primary-color );
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	border-bottom-left-radius: 4px;
	border-bottom-right-radius: 4px;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods div.payment_box .wc-saved-payment-methods:not( :empty ) {
	padding-left: 0;
	margin-bottom: 10px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods > li:not( .woocommerce-info ) {
	padding: 12px 12px 0 12px;
	min-height: 40px;
	border: 1px solid #d6d7db;
	display: inline-table;
	width: 100%;
	/* margin-bottom: 15px; */
	border-bottom: none;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment ul.payment_methods .cpsw_stripe_icons {
	float: right !important;
	margin-top: 5px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment ul.payment_methods > li:last-child {
	border-bottom: 1px solid #d6d7db;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form #payment label {
	font-weight: 500;
	display: inline-block;
	width: calc( 100% - 10% );
	color: var( --wcf-payment-section-label-color );
	font-size: 14px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #payment .wc_payment_method > [type="radio"]:not( :checked ) + label,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #wcf-ic-shipping .woocommerce-shipping-methods > [type="radio"]:not( :checked ) + label {
	vertical-align: -webkit-baseline-middle;
	margin-bottom: 10px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment div.payment_box {
	margin: 10px 0 0 -12px;
	border-top: 1px solid #e5e7eb;
	color: var( --wcf-payment-section-desc-text-color );
	background-color: var( --wcf-payment-section-desc-bg-color );
	padding: 20px;
	width: calc( 100% + 24px );
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment div.payment_box::before {
	content: "";
	display: none;
}

/* Remove extra top spacing for CPSW Stripe element */
/* .wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment div.payment_box .cpsw_stripe_element_payment_form {
	margin-top: -1rem;
} */

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment .form-row {
	padding: 15px 0 0;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout #payment .woocommerce-privacy-policy-text p {
	font-size: 12px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form #payment button:not( .js-woopayments-copy-test-number ),
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.coupon-field .wcf-custom-coupon-field button {
	line-height: 1.5;
	width: 100%;
	font-size: 18px;
	font-weight: 600;
	letter-spacing: 0;
	border-radius: 4px;
	padding: 12px 5px;
	transition: 0.1s ease-in-out;
	-js-display: inline-flex;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	position: relative;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout:not( .wcf-field-default ) .woocommerce form #order_review table.shop_table tfoot tr.coupon-field .wcf-custom-coupon-field button.wcf-btn-small {
	padding: 13px 6px;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form #payment button#place_order::before {
	top: -1px;
}

/* Default border. */
body.cartflows-instant-checkout #ast-scroll-top {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form #ship-to-different-address,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form #order_review_heading {
	font-size: 20px;
	font-weight: var( --wcf-heading-font-weight );
	color: var( --wcf-heading-color );
	padding: 0;
	margin: 10px 0 25px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form #ship-to-different-address {
	font-size: 15px;
	font-weight: 400;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-shipping-fields label.checkbox {
	margin: 0;
	text-transform: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-methods-wrapper {
	width: 100%;
	padding: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options {
	padding: 0;
	background: #fff;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-methods-wrapper .wcf-shipping-error-msg {
	padding: 1em 1.5em 1em 1.5em;
	margin-top: 0;
	border: 1px solid var( --wcf-field-error-border-color );
	border-radius: 4px;
	color: var( --wcf-field-error-border-color );
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .woocommerce-checkout-review-order-table .wcf-shipping-error-msg {
	color: var( --wcf-field-error-border-color );
}

/* .wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout #payment ul.payment_methods li:has( input[type="radio"]:checked ), */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method {
	border: none;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:first-child {
	border-bottom: 0;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:not( :last-child ) {
	border: 1px solid var( --wcf-ic-section-border-color );
	border-bottom: 0;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:last-child {
	border: 1px solid var( --wcf-ic-section-border-color );
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:has( input[type="radio"]:checked ) {
	border: 1px solid var( --wcf-primary-color );
	box-shadow: none;
	background-color: color-mix( in sRGB, var( --wcf-primary-color ) 10%, transparent );
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:first-child,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:has( input[type="radio"]:checked ):first-child {
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:last-child,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:has( input[type="radio"]:checked ):last-child {
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
}

/* Remove the border if it has only one shipping method. */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method:has( > li:only-child ) {
	border: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li:only-child {
	outline: none;
	border: 1px solid var( --wcf-primary-color );
	border-radius: 5px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-shipping-method-options ul#shipping_method li label .woocommerce-Price-amount {
	font-weight: 400;
	color: #000;
}

/**End  Radio Button Style */

.wcf-instant-checkout-fees {
	padding: 10px;
	margin: 0 0 0.5em 0;
	border: 1px solid var( --wcf-ic-section-border-color );
	border-radius: 4px;
}

.wcf-ic-tax-name,
.wcf-ic-fees-name {
	font-weight: 500;
}

div.wcf-instant-checkout-fees > div {
	padding: 5px;
}

.wcf-embed-checkout-form-instant-checkout .woocommerce-NoticeGroup ul.woocommerce-error {
	padding: 2% 1% 1% 15%;
	background-color: #fff;
}

/* .wcf-embed-checkout-form-instant-checkout .checkout .woocommerce-NoticeGroup {
	padding-left: 2%;
	padding-right: 2%;
	display: none;
} */

button {
	cursor: pointer;
}

.instant-checkout-batch {
	position: absolute;
	top: -10px;
	right: -10px;
	background-color: var( --wcf-btn-bg-color );
	color: #fff;
	width: 22px;
	height: 22px;
	border-radius: 50%;
	font-size: 11px;
	font-weight: 700;
	-js-display: flex;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0;
	line-height: 1;
}

.wcf-embed-checkout-form-instant-checkout strong.product-quantity {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout.wcf-modern-skin-two-column .woocommerce-checkout #order_review {
	position: sticky;
	top: 91px;
	padding: 0;
}

.wcf-embed-checkout-form-instant-checkout .woocommerce {
	width: 100%;
	margin: 0 auto;
}

.wcf-embed-checkout-form-instant-checkout .woocommerce .woocommerce-form-login-toggle {
	display: none !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-ic-layout-left-column {
	display: grid;
	width: 42rem;
	padding: 2.5rem;
}

/* Notices CSS */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-ic-layout-left-column .woocommerce-notices-wrapper .woocommerce-message {
	padding: 1em 1.5em 1em 1.5em;
	margin-top: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-ic-layout-left-column .woocommerce-notices-wrapper .woocommerce-error,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-ic-layout-left-column .woocommerce-NoticeGroup .woocommerce-message {
	padding: 1em 1.5em 1em 1.5em;
	margin-top: 0;
	border: 1px solid var( --wcf-field-error-border-color );
	border-radius: 4px;
	color: var( --wcf-field-error-border-color );
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-ic-layout-left-column .woocommerce-error li:last-child {
	margin-bottom: 0;
}
/* Notices CSS */

/* Instant Checkout Fields CSS */

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-billing-fields__customer-info-wrapper {
	margin: 0 -7px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-email-validation-block {
	font-size: 12px;
	display: block;
	margin-top: 5px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-email-validation-block.success {
	color: #008000;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-email-validation-block.error {
	color: #e11e00;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-logged-in-customer-info {
	padding: 5px 7px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row {
	margin: 0 0 12px;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .woocommerce-additional-fields .form-row:last-child {
	margin-bottom: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row.woocommerce-invalid-required-field .input-text {
	border-color: var( --wcf-field-error-border-color );
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-customer-login-actions {
	-js-display: flex;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 3px 7px;
	margin: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-customer-login-actions .wcf-customer-login-lost-password-url {
	font-size: 14px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-customer-login-section__login-button {
	width: auto;
	padding: 8px 15px;
	color: #fff;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-customer-login-section .show-password-input,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .create-account .show-password-input {
	top: 20px;
	right: 12px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row input.input-text,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row textarea,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row select,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .input-text,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .select2-container--default .select2-selection--single {
	color: var( --wcf-field-text-color );
	border-color: var( --wcf-field-border-color );
	border-radius: 5px;
	font-size: 14px;
	padding: 15px 11px;
	min-height: var( --wcf-field-min-height );
	outline: none;
	text-overflow: ellipsis;
	line-height: 21px !important;
}
.wcf-embed-checkout-form.wcf-field-modern-label .woocommerce .form-row.wcf-anim-label .select2-container--default .select2-selection--single:not( :has( .select2-selection__placeholder ) ) {
	padding: 1.65em 0.8em 0.5em;
	-js-display: flex;
	display: flex;
	align-items: center;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row.wcf-anim-label input.input-text,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row.wcf-anim-label select,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row.wcf-anim-label textarea {
	padding: 22px 15px 8px 11px;
}

/* Hide the asterisk mark as a part of UI */
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row .required {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .select2-container--default .select2-selection--single .select2-selection__rendered {
	font-size: 14px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row input.input-text::placeholder,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row textarea::placeholder,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row select::placeholder {
	/* color: #707070; */
	color: var( --wcf-field-text-color );
}

/* Instant Checkout Fields CSS */

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-left-column .woocommerce-billing-fields-custom h3,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-left-column h3 {
	color: var( --wcf-heading-color );
	font-weight: var( --wcf-heading-font-weight );
	text-transform: capitalize;
	margin: 22px 0 15px;
	font-size: 20px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .woocommerce-billing-fields__customer-login-label {
	font-size: 14px;
	text-transform: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .woocommerce-billing-fields-custom h3 {
	-js-display: flex;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 500;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-ic-layout-left-column #customer_information_heading {
	margin-top: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-right-column .wcf-order-wrap {
	display: block;
	max-width: 34rem;
	width: 100%;
	padding: 1.5rem;
	min-height: 400px;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-ic-layout-right-column .wcf-order-wrap #order_review {
	padding: 0;
	min-height: inherit;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-ic-layout-right-column .wcf-order-wrap {
	position: sticky;
	top: 0;
	align-self: flex-start;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table {
	display: block;
	margin-bottom: 0;
	padding: 1rem;
	background-color: var( --wcf-ic-right-column-bg-color ) !important;
	border: none !important;
	border-collapse: collapse;
	border-radius: 4px;
	border-style: hidden;
	box-shadow: none !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-right-column .wcf-bump-order-grid-wrap.wcf-after-order:empty {
	margin: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-right-column .wcf-bump-order-grid-wrap.wcf-after-order:not( :empty ) {
	padding: 0 1rem;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table .blockUI.blockOverlay {
	width: 100% !important;
	height: 100% !important;
	top: 0 !important;
	left: 0 !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tbody tr.cart_item td,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.coupon-field td {
	padding: 0 0 2em 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tr th:nth-child( 2 ),
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tr td:nth-child( 2 ) {
	text-align: right;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tr.cart-shipping td label {
	white-space: nowrap;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.coupon-field .wcf-custom-coupon-field .wcf-optimized-coupon-field {
	width: 100%;
	color: var( --wcf-field-label-color );
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.coupon-field .wcf-custom-coupon-field {
	margin: 0;
	border: none;
	padding: 0;
	align-items: center;
	gap: 14px;
	flex-wrap: nowrap;
}
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-custom-coupon-field.wcf-coupon-applied .wcf-optimized-coupon-field,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-custom-coupon-field.wcf-coupon-applied .wcf-coupon-col-1,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-custom-coupon-field.wcf-coupon-applied .wcf-coupon-col-2 {
	display: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.coupon-field .wcf-custom-coupon-field .wcf-coupon-col-1 {
	padding: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr td {
	padding: 0 0 0.7rem 0.2rem;
	font-weight: 400;
	color: #000;
	width: auto;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.order-total:not( .recurring-total ) th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.order-total:not( .recurring-total ) td {
	font-size: 18px;
	font-weight: 500;
	padding: 0.5rem 0 0.7rem 0.2rem;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table tfoot tr.order-total:not( .recurring-total ) td strong {
	font-weight: 500;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout table.shop_table tbody tr.cart_item td.product-name {
	padding: 0;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce table.shop_table tbody tr.cart_item .wcf-product-image {
	height: auto;
	-js-display: flex;
	display: flex;
	vertical-align: middle;
	border-radius: 5px;
	align-items: center;
	gap: 0.8rem;
	width: 100%;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout table.shop_table tfoot tr.order-total:not( .recurring-total ) th,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout table.shop_table tfoot tr.order-total:not( .recurring-total ) td,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout table.shop_table tfoot tr.cart-discount,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout table.shop_table tfoot tr.cart-subtotal {
	border: none;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout table.shop_table tbody {
	border: none !important;
}

.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row input.input-text:focus,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row textarea:focus,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce #order_review .wcf-custom-coupon-field input.input-text:focus {
	outline: none;
	border-color: var( --wcf-primary-color );
	box-shadow: 0 0 0 1px var( --wcf-primary-color );
}

/*Link color*/
.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row.wcf-hide-field a {
	color: var( --wcf-link-color );
	background-color: transparent;
	font-weight: 400;
	text-decoration: none;
	-js-display: flex;
	display: flex;
	gap: 5px;
	align-items: center;
}
.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row.wcf-hide-field a .dashicons {
	height: auto;
	width: auto;
}
.wcf-embed-checkout-form-instant-checkout .woocommerce form .form-row.wcf-hide-field a .dashicons.dashicons-arrow-right::before {
	font-size: 13px;
	line-height: 1;
	border: 1px solid var( --wcf-link-color );
	border-radius: 50px;
}
/*Notice color*/
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-info::before,
.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-message::before {
	color: var( --wcf-primary-color );
}

/*Order review Image size*/
.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-product-image .wcf-product-thumbnail {
	position: relative;
	border-radius: 4px;
	width: 25%;
	margin: 0;
}

.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-product-image .wcf-product-thumbnail img {
	border: 1px solid #d6d6d6;
	object-fit: cover;
}

/* Title */
.wcf-ic-title h1 {
	color: var( --wcf-heading-color );
}
/* Title */

/* Breadcrumbs */

ul#wcf-ic-breadcrumbs {
	list-style: none;
	-js-display: flex;
	display: flex;
	padding-left: 0;
	margin: 0 0 2.5em 0;
}

#wcf-ic-breadcrumbs .wcf-ic-breadcrumb-item {
	margin-right: 5px;
	margin-left: 5px;
}

#wcf-ic-breadcrumbs .wcf-ic-breadcrumb-item a {
	text-decoration: none;
	color: var( --wcf-primary-color );
}

#wcf-ic-breadcrumbs .wcf-ic-breadcrumb-item a:hover {
	text-decoration: underline;
}
/* Breadcrumbs */

/* Responsive */

/* Modible devices and some smaller tablets */
@media only screen and ( max-width: 820px ) {
	.wcf-empty-cart-notice-block {
		padding: 0.704rem 1.5rem;
	}
	.wcf-empty-cart-message-container {
		padding: 30px;
	}
	.wcf-ic-layout-right-column .wcf-order-wrap {
		padding: 0;
	}

	.wcf-bump-order-wrap.wcf-ob-column-100 .wcf-bump-order-offer-content-left {
		max-width: 100% !important;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce {
		padding: 0 20px;
		background-color: var( --wcf-ic-left-column-bg-color );
		width: 100%;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-checkout .wcf-order-wrap {
		width: 100% !important;
		padding: 0;
	}

	/* Instant Checkout */

	body.cartflows-instant-checkout .main-header--content {
		max-width: 100%;
		width: 100%;
		padding: 0.704rem 2.4rem;
	}

	body.cartflows-instant-checkout .main-header--content .main-header--site-logo {
		max-height: 100%;
	}

	body.cartflows-instant-checkout .main-header--content .main-header--site-logo img {
		max-width: 45%;
		width: 100%;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-instant-checkout-wrapper {
		display: block;
		width: 100%;
		padding: 15px 0;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-instant-checkout-wrapper .wcf-customer-info-main-wrapper {
		border: none;
		display: block;
		block-size: auto;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-ic-layout-right-column {
		width: 100% !important;
		float: none;
		padding: 0 !important;
		min-height: max-content !important;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-right-column .wcf-order-wrap {
		padding: 0;
		min-height: auto;
		background-color: #fff;
		max-width: 100%;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-right-column .wcf-bump-order-grid-wrap.wcf-after-order:not( :empty ),
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form .wcf-ic-layout-right-column .wcf-bump-order-grid-wrap.wcf-after-order {
		margin-top: 0;
		padding: 0;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-ic-layout-left-column {
		display: block;
		border: none;
		width: 100% !important;
		padding: 15px 0;
		background-color: var( --wcf-ic-left-column-bg-color );
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-order-review-toggle {
		-js-display: flex;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 0 -20px;
		background-color: #f5f5f5;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-collapsed-order-review-section.wcf-show .cartflows-cheveron-down {
		display: none;
	}
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-collapsed-order-review-section.wcf-show .cartflows-cheveron-up {
		display: inline;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-collapsed-order-review-section.wcf-show .wcf-order-review-toggle {
		border-bottom: none;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-cartflows-review-order-wrapper .wcf-custom-coupon-field {
		background-color: var( --wcf-ic-right-column-bg-color );
		flex-direction: column;
		gap: 15px;
		margin: 0 0 0;
		padding: 0 20px 20px;
		border: none;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-collapsed-order-review-section .wcf-custom-coupon-field .wcf-coupon-col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-collapsed-order-review-section .wcf-custom-coupon-field .wcf-coupon-col-2 {
		width: 100%;
		padding-right: 0;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-collapsed-order-review-section .wcf-custom-coupon-field button.wcf-submit-coupon,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-collapsed-order-review-section .wcf-custom-coupon-field input.input-text {
		width: 100%;
		padding: 11px;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout #order_review .wcf-custom-coupon-field,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout #order_review table.shop_table,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-cartflows-review-order-wrapper {
		display: none;
		border-bottom: 1px solid #dedede;
		padding: 0;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce form table.shop_table {
		padding: 1.2rem;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .wcf-ic-layout-right-column .wcf-order-wrap #order_review {
		padding: 0;
		min-height: unset;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .col2-set .col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .col2-set .col-2,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-page .col2-set .col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-page .col2-set .col-2,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-col2-set .wcf-col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-col2-set .wcf-col-2,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-page .wcf-col2-set .wcf-col-1,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-page .wcf-col2-set .wcf-col-2,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .wcf-order-wrap,
	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce-page .wcf-order-wrap {
		padding: 0;
	}

	.wcf-embed-checkout-form.wcf-embed-checkout-form-instant-checkout .woocommerce .cpsw_stripe_icons {
		display: none;
	}

	/* Instant Checkout */
}

@media only screen and ( max-width: 480px ) {
	ul#wcf-ic-breadcrumbs {
		list-style: none;
		-js-display: flex;
		display: flex;
		padding-left: 0;
		margin: 0;
	}
}
/* Responsive End. */

.wcf-embed-checkout-form-instant-checkout #order_review_heading {
	display: none;
}
.wcf-embed-checkout-form-instant-checkout table.shop_table tbody {
	border-bottom: 1px solid #e5e7eb;
}
.wcf-embed-checkout-form-instant-checkout table.shop_table tfoot tr.cart-subtotal {
	border-bottom: 1px solid #e5e7eb;
}
.wcf-shipping-option-heading {
	width: 100%;
}
.wcf-embed-checkout-form-instant-checkout form.checkout.woocommerce-checkout {
	display: inline-block;
	margin: 0;
}
.wcf-shipping-option-heading {
	display: inline-block;
}
#wcf-ic-shipping ul#wcf-shipping_method {
	margin: 0;
}
.wcf-embed-checkout-form-instant-checkout table.shop_table thead {
	display: none;
}
h3#wcf_shipping_options_heading {
	margin-top: 0;
}

#wcf-ic-shipping span.wcf-ic-shipping-package-name {
	display: none;
}

.wcf-embed-checkout-form-instant-checkout.wcf-embed-checkout-form .woocommerce .wcf-product-image img {
	height: auto;
}

/**
* ***********************************
* Instant Checkout Layout Style End
* ***********************************
*/
