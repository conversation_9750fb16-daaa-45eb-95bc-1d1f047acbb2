<svg role="img" aria-labelledby="loading-aria" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="0" y="0" width="100%" height="100%" clip-path="url(#clip-path)" style='fill: url("#fill");' ></rect>
  <defs>
    <clipPath id="clip-path">
        <rect x="0" y="0" rx="0" ry="0" width="NaN" height="NaN" /> 
        <rect x="4" y="4" rx="3" ry="3" width="20" height="20" /> 
        <rect x="50" y="10" rx="3" ry="3" width="45%" height="10" /> 
        <rect x="370" y="10" rx="3" ry="3" width="15%" height="10" />
        <rect x="500" y="10" rx="3" ry="3" width="15%" height="10" />
    </clipPath>
    <linearGradient id="fill">
      <stop offset="0.599964" stop-color="#dddddd" stop-opacity="1" >
        <animate attributeName="offset" values="-2; -2; 1" keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite"></animate>
      </stop>
      <stop offset="1.59996" stop-color="#b0b0b0" stop-opacity="1">
        <animate attributeName="offset" values="-1; -1; 2" keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite" ></animate>
      </stop>
      <stop offset="2.59996" stop-color="#dddddd" stop-opacity="1" >
        <animate attributeName="offset" values="0; 0; 3" keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite" ></animate>
      </stop>
    </linearGradient>
  </defs>
</svg>