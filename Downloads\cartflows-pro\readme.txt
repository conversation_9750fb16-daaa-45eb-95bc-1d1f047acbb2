=== WooCommerce Checkout & Funnel Builder by CartFlows ===
Contributors: <PERSON><PERSON><PERSON><PERSON>, wpcrafter
Tags: woocommerce, funnel builder, sales funnels, custom checkout, WooCommerce checkout
Requires at least: 5.8
Tested up to: 6.8
Stable tag: 2.1.14
Requires PHP: 7.2
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

WooCommerce checkout & funnel builder to boost conversion and revenue with beautiful and optimized checkout pages & funnels.

== Description ==

**#1 WooCommerce Checkout & Funnel Builder by CartFlows – The Secret to High-Converting WooCommerce Stores** <br>
★★★★★<br>

[youtube https://www.youtube.com/watch?v=tCm60PWOd0M]

[What's New](https://cartflows.com/whats-new/?utm_source=WordPress&utm_medium=Referral&utm_campaign=CF_description) | [Docs](https://cartflows.com/docs/?utm_source=WordPress&utm_medium=Referral&utm_campaign=CF_description) | [Video Tutorials](https://www.youtube.com/@CartFlows/) | [Get Help](https://cartflows.com/support/open-a-ticket/?utm_source=WordPress&utm_medium=Referral&utm_campaign=CF_description) | [Community](https://www.facebook.com/groups/cartflows)

### The Best WooCommerce Checkout & Funnel Builder Ever⚡

Your WooCommerce checkout should do more than just work \- It should convert\!

As the **\#1 WooCommerce checkout plugin** and funnel builder, CartFlows is designed to transform your checkout process into a revenue-generating powerhouse.

It doesn’t just optimize your store \- it helps you sell more and boost **average order value (AOV)** with ease.

[Try the live demo of CartFlows](https://zipwp.org/plugins/cartflows/)

## Why WooCommerce Needs CartFlows

Let’s face it \- the default WooCommerce checkout is functional, but far from modern. It lacks the speed, personalization, and seamless experience customers expect.

A clunky checkout can hurt conversions and leave money on the table and nobody wants that\!

That’s where CartFlows steps in. To take WooCommerce to the next level.

With CartFlows, you can:

* Upgrade to a distraction-free, conversion-optimized checkout.
* Build high-conversion sales funnels with upsells, downsells, and dynamic order bumps.
* Simplify the customer journey with sleek, intuitive layouts that focus buyers on completing their purchase.

### What Makes CartFlows Unique?

CartFlows is \#1 for a good reason and that’s partly down to some unique features:

* **Built for WooCommerce**: CartFlows isn’t just another plugin \- it’s the ultimate checkout plugin designed to simplify checkout and drive conversion.
* **Seamless integration**: Works flawlessly within WordPress and leverages WooCommerce’s capabilities while adding advanced funnel builder tools.
* **Effortless setup**: Pre-designed templates and drag and drop editing make creating high-converting funnels a breeze.
* **Revenue boosters**: Upsells, downsells, and order bumps help maximize your store’s average order value (AOV).
* **Custom checkout experience**: Replace WooCommerce’s default checkout with sleek, branded layouts optimized for conversions.
* **Affordable solution**: Get advanced features without the hefty price tag of SaaS tools like ClickFunnels.

### How It Works

Working with CartFlows is easy\!

1. **Install CartFlows**: Activate the WooCommerce checkout plugin from your WordPress dashboard.
2. **Choose a template**: Pick a sales funnel and checkout template.
3. **Customize your funnel**: Use your favorite page builder to create a seamless shopping experience.
4. **Add dynamic offers**: Boost revenue with personalized upsells, downsells, and order bumps.
5. **Optimize with insights**: Use CartFlows’ analytics tools to refine your funnels and improve performance.

### POWERFUL FEATURES INCLUDE 💪

### CartFlows Free Features

#### 1. Instant Layouts

Want checkout pages that convert? **CartFlows plugin** makes it a breeze to create distraction-free, sleek checkout and thank you pages.

No coding or stress \- just high-converting layouts that keep your customers focused on buying.

In minutes, you’ll have a checkout that’s fast, modern, and optimized for sales.

#### 2. Multi-Page Builder Support

No matter your favorite page builder \- Spectra, Elementor, Bricks or Beaver \- CartFlows works seamlessly with them all.

Create designs you love without limits. Enjoy the flexibility to build beautiful, high-converting funnels your way.

#### 3. Automations with SureTriggers

Tired of repetitive tasks? Let **SureTriggers integration** handle them for you\!

From sending Thank You emails to managing leads, you can automate workflows and focus on growing your business.

It’s like having an assistant for your **WooCommerce checkout plugin**, saving you time and boosting efficiency.

#### 4. Social Media Pixel Tracking Support

Are your campaigns working? With Google, Facebook, Pinterest, Snapchat, and TikTok pixel integration, you can track customer actions and optimize your marketing efforts.

Get deeper insights, retarget smarter, and turn more clicks into conversions. It's a must-have for any WooCommerce funnel builder.

#### 5. Override Store Checkout

Replace that boring WooCommerce checkout with something that actually works for you\!

CartFlows lets you override the default checkout and build a streamlined, branded experience. Whether it’s single-step or multi-step, you’re in control with this powerful WooCommerce checkout plugin.

#### 6. Import Ready-Made Sales Funnels

Why start from scratch? Import pre-made funnels with just a click and start selling immediately.

The professionally designed templates are built to convert and are fully customizable. It's your shortcut to sales success.

#### 7. Checkout Form Field Editor

Tweak your checkout form to match your business needs. Add, remove, or rearrange fields to make the process smoother for your customers.

With this WooCommerce checkout plugin, you’re in charge of creating a checkout experience that converts.

### CartFlows Pro Features

#### 1. Advanced Analytics

Want to know what’s working and what isn’t? With CartFlows’ advanced analytics, you can track conversions, revenue, and customer behavior.

Use insights to tweak your funnels for maximum impact. It’s all about making data-driven decisions for better results.

#### 2. A/B Testing

Not sure which design works best? With A/B testing, you can test multiple layouts, headlines, or CTAs to see what converts.

Make smarter decisions and optimize every step of your sales funnel.

#### 3. Dynamic Sales Features

Want to boost your revenue per sale? CartFlows gives you personalized upsells, order bumps, and pre-checkout offers.

Built to increase your average order value (AOV). Add a personal touch that feels less like selling and more like solving your customers' needs.

#### 4. Lead Generation Tools

Build your email list while boosting sales. With CartFlows’ built-in opt-in features, you can create landing pages that convert like crazy.

Perfect for capturing leads and nurturing them into loyal customers.

#### 5. Enhanced Subscription Handling

Selling subscriptions? CartFlows makes it easy with auto-payment retries and proration for upgrades.
Manage everything seamlessly while keeping your subscribers happy. It’s the smart way to handle recurring revenue.

#### 6. Add Steps to Funnel

Take control of your customer journey\! Add upsells, downsells, thank-you pages, or any custom step to your funnel.

With CartFlows, you can create a tailored shopping experience that boosts conversions.

#### 7. Auto Apply Coupons

Stop making your customers hunt for discounts. With CartFlows, coupons are applied automatically during checkout. This makes the process smooth and keeps your buyers happy.

#### 8. Product Options

Offer personalized product choices right at checkout. Whether it’s sizes, colors, or additional services, CartFlows lets your customers choose what they need.

Personalization has never been so easy \- or effective\!

#### 9. Order Bumps

Boost revenue with targeted order bumps placed right on the checkout page.

These small, additional offers are easy to set up and can make a big impact on your bottom line.

#### 10. Dynamic Offers

Give customers the perfect offer at the perfect time. With CartFlows’ dynamic upsells and downsells, you can personalize the shopping experience and increase sales. It’s smarter selling made simple.

### WHO CAN BENEFIT FROM CARTFLOWS?

CartFlows is the perfect sales funnel builder for any store that uses WooCommerce.

Here are just a few example use cases:

✅ Ecommerce sellers
✅ Course creators
✅ Membership platforms
✅ Freelancers and consultants
✅ Marketing agencies
✅ Small business owners
✅ Service providers
✅ Event planners
✅ Digital product vendors
✅ Health and wellness professionals
✅ Nonprofits and charities
✅ Local businesses
✅ Dropshippers
✅ Niche sites
✅ Financial advisors
✅ Educational institutions
✅ Software companies
✅ Fashion and apparel brands
✅ Food and beverage services
✅ Real estate professionals
✅ Fitness coaches
✅ Creative artists and designers
✅ Authors and publishers
✅ Subscription box providers
✅ Professional public speakers
✅ Bloggers
✅ Personal brands
✅ And many more

### SUPPORTED THEMES, PLUGINS AND TOOLS

**Themes**
Any WooCommerce-compatible theme including:

➡️ Astra
➡️ Avada
➡️ Blocksy
➡️ Divi
➡️ Flatsome
➡️ GeneratePress
➡️ Hello Elementor
➡️ Hestia
➡️ Kadence
➡️ Neve
➡️ OceanWP
➡️ Phlox – by Averta
➡️  PopularFX – by Pagelayer
➡️ Storefront
➡️ Woostify
➡️ Zakra

**Page Builders**
CartFlows works with any page builder that is compatible with WooCommerce such as:

➡️ Spectra
➡️ Elementor
➡️ Divi Builder
➡️ Bricks Builder
➡️ Beaver Builder
➡️ Brizy Builder
➡️ Thrive Architect
➡️ Gutenberg
➡️ Oxygen Builder
➡️ And others

**Payment Plugins**
Any payment gateway that works with WooCommerce including:

➡️ Stripe Payments For WooCommerce by Checkout Plugins
➡️ PayPal Payments For WooCommerce by Checkout Plugins
➡️ WooCommerce Stripe Payment Gateway
➡️ PayPal Express Checkout
➡️ WooCommerce Payments
➡️ PayPal Payments
➡️ PayPal Standard
➡️ Mollie Credit Card & iDEAL
➡️ WooCommerce Square Payment Gateway
➡️ Direct Bank Transfer (BACS)

**Other Plugins**

➡️ WooCommerce Cart Abandonment Recovery
➡️ Variation Swatches for WooCommerce
➡️ SureMembers
➡️ LearnDash
➡️ TutorLMS
➡️ LifterLMS
➡️ MemberPress
➡️ LearnPress
➡️ Wishlist Member
➡️ FluentForms
➡️ AliDropship

**Integrations**
Integrates with 1,010 apps and services by using SureTriggers such as SureCart, Asana, AirTable and thousands of others.

### CONNECT WITH OUR TEAM AND COMMUNITY

* [**Facebook Group**](https://www.facebook.com/groups/cartflows/): Join our active community for tips, tricks, and updates.

== Installation ==

1. Upload `cartflows.zip` to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Make sure to disable caching on your checkout and thank you steps

== Frequently Asked Questions ==

= What is a sales funnel and a funnel builder? =
A sales funnel is a series of steps guiding buyers to purchase or provide a lead. A funnel builder like CartFlows makes it easy to create optimized funnels for WooCommerce.

= What makes CartFlows different from other plugins? =

CartFlows is your all-in-one solution to create, customize, and optimize high-converting sales funnels directly within WordPress.

Here’re a few unique features:

⏩ Replace the default WooCommerce checkout entirely or create unlimited personalized sales funnels for specific products or categories.
⏩ Create a complete sales funnel from start to finish without leaving WordPress.
⏩ Integrates effortlessly with popular page builders like Elementor, Bricks, Divi, Beaver Builder, and the block editor. Gives you the flexibility to design funnels exactly how you envision them.
⏩ Import professionally designed sales funnels in just a few clicks.
⏩ Add upsells, downsells, and order bumps, or personalize offers to match your goals. Rearrange funnel steps with simple drag-and-drop functionality.

= What is required to use CartFlows? =
CartFlows just requires WooCommerce installed on a WordPress website.

= Does CartFlows work with my page builder? =
Absolutely\! CartFlows integrates seamlessly with popular page builders like Spectra, Elementor, Divi, Bricks, Beaver Builder and the block editor.

= Which themes does CartFlows work with? =
CartFlows works with any WordPress theme. If you run into issues, our friendly support team is ready to help.

= What type of funnels can I create with CartFlows? =
CartFlows is super flexible and allows you to create any sales funnel you might need.

This includes funnels for:

* Lead generation
* Opt-in lead
* Squeeze page
* Quiz and survey
* Membership sales
* Subscription-based
* Product sales
* Cross-sell
* Dynamic WooCommerce
* Product launch
* Checkout optimization
* WooCommerce checkout replacement
* Tripwire sales
* Online course
* Free webinar
* Paid webinar
* Affiliate marketing
* Free consultation
* Social media hero
* Hybrid multi-step

= How many funnels can I create with CartFlows? =
With the free version of CartFlows, you can create multiple sales funnels to kickstart your journey. However, you’re limited to importing just one checkout step per funnel.
Want to unlock the full potential? Upgrade to CartFlows Pro for unlimited funnels and access to advanced features that take your WooCommerce store to the next level\!

= What is the Instant Checkout Layout feature? =
The Instant Checkout Layout helps you create distraction-free, conversion-optimized checkout and thank you pages. The sleek designs are perfect for keeping customers focused on completing transactions.

= Can I customize the checkout process? =
You can absolutely customize checkout\! With CartFlows, you can create sleek, branded checkouts tailored to your store’s needs.

= What are order bumps, and how do they work in CartFlows? =
Order bumps are additional offers made during checkout that boost average order value. CartFlows lets you create personalized, high-conversion order bumps with ease.

= Can CartFlows automate tasks? =
Yes, CartFlows can automate tasks. It integrates with SureTriggers to automate sending thank you emails, generating notifications, and managing leads.

= How does CartFlows handle abandoned carts? =
CartFlows offers a solution for handling abandoned carts through a separate plugin, [WooCommerce Cart Abandonment Recovery](https://wordpress.org/plugins/woo-cart-abandonment-recovery/), which sends automated email notifications to logged in users to help recover lost sales. It’s an effective way to maximize your revenue.

= Does CartFlows support A/B testing? =
Yes, CartFlows features A/B split testing. Compare different designs, identify what works best, and optimize your funnel for higher conversions.

= Does CartFlows provide analytics tools? =
CartFlows offers an enhanced analytics UI and Insights Dashboard to track conversions, revenue, and customer behavior. Use these tools to optimize underperforming funnels.

= How does CartFlows support social media pixel tracking? =
CartFlows integrates with Facebook, Google, Pinterest, Snapchat, and TikTok pixels. Track customer actions on checkout, upsell, and thank-you pages, helping improve retargeting campaigns.

= Can CartFlows handle flexible payment methods? =
Yes, CartFlows supports flexible payment options through Stripe, catering to global audiences with multiple methods. This reduces friction and boosts conversions.

= What payment gateways does CartFlows support? =
CartFlows works with any WooCommerce-compatible payment gateway, including Stripe, PayPal, Square, Mollie, and WooCommerce Payments.

= Does CartFlows support subscription payments? =
Yes, CartFlows is compatible with the WooCommerce Subscriptions extension and works seamlessly with subscription payment gateways.

= I’m new to CartFlows. Where can I find documentation? =
Welcome to CartFlows\! Click here to access our comprehensive documentation and learn how to build high-converting funnels effortlessly. Have questions or need help with a specific feature? Here’s how to connect with us:

* [Support](https://cartflows.com/support/)
* [Contact us](https://cartflows.com/contact/)
* [Join the CartFlows community](https://www.facebook.com/groups/cartflows/)


== Screenshots ==

1. Create a ready-made sales funnel.
2. Add a product to your sales funnel.
3. View Dashboard for quick updates.
4. Quickly re-arrange the steps.

== Changelog ==

= Version 2.1.14 - Thursday, 26th June 2025 =
* Improvement: Funnel import from a JSON file is now possible even when no funnels exist, as the Import button is displayed.
* Fix: Fixed the issue of image render on Instant Checkout Thank you page.

= Version 2.1.13 - Monday, 16th June 2025 =
* Improvement: Enhanced the Create New Product popup for a better user experience.
* Fix: Resolved an issue where the Order Bump stayed selected after the product was removed when cart editing on checkout was enabled.
* Fix: Exported funnel JSON files now include previously missing funnel settings data.

= Version 2.1.12 - Wednesday, 21th May 2025 =
* Improvement: Improved the toggle button with improved handling of disabled states, making the toggle button more intuitive and user-friendly.
* Improvement: Added compatibility for future release of CartFlows Pro.

= Version 2.1.11 - Tuesday, 13th May 2025 =
* New: Added option to upload a custom logo in the header of the Instant Layout style.
* Improvement: Enhanced tooltips across the interface for better clarity.
* Fix: Resolved a critical error caused by the latest update of the WooCommerce Stripe Gateway.
* Fix: Fixed an issue where the “What’s New” notification was not being displayed.
* Fix: Purchase event was not being tracked correctly for Facebook — now fixed.
* Fix: Resolved dropdown overlap issue with country and state field labels.
* Fix: Slide-out panel now closes properly when clicking outside the panel area.
* Fix: Fixed styling issues for navigation tabs in the Order Bump and Checkout Offer settings panels.
* Fix: Resolved a conflict with shipping methods when using the DPD Blastic shipping plugin on CartFlows Checkout.
* Fix: Fixed checkout field order conflict with the PostNL plugin in CartFlows.
* Fix: Shipping options now display correctly without needing to refresh the checkout page.
* Fix: Custom content was not appearing on the Instant Layout of the Thank You page — now resolved.

= Version 2.1.10 - Wednesday, 9th April 2025 =
* Improvement: SureTriggers is now OttoKit. We've updated the compatibility to keep everything working smoothly.
* Improvement: Instant Layout will be auto-enabled when users create a funnel from scratch. This will help users get a ready-made structure right away.
* Improvement: Improved design of Store Checkout pop-up

= Version 2.1.9 - Thursday, 3rd April 2025 =
* New: Introduced a new style for thank you page.
* Fix: Corrected the price display on the Instant Layout of the Thank You page when tax is enabled.
* Fix: Improved UX and mobile responsiveness on the step’s template library page.
* Fix: Fixed an error when adding an order bump product in step settings.
* Fix: Ensured billing and shipping details are displayed for all users on the Thank You page, not just logged-in users.
* Fix: Resolved an issue where the incorrect order number was shown on the Instant Layout of the Thank You page when using the Sequential Order Numbers plugin.

= Version 2.1.8 - Wednesday, 5th March 2025 =
* New: Introduced language files for multiple languages for quick translation of the plugin.
* Fix: Resolved issue preventing step pages from being edited with Oxygen Builder.
* Fix: Resolved the conflict between CartFlows and custom templates of Bricks Builder on Shop page.
* Fix: The funnel's GCP colors were not getting imported while importing the ready-made templates.
* Fix: Removed the extra spacing at the bottom of the page while using the instant checkout layout on mobile devices.
* Fix: The product image is not displaying properly for iOS mobile devices for instant checkout layout.
* Fix: Corrected the issue where the next step button was active in the page builder's editor view, causing redirect problems during page editing.
* Fix: Resolved UI/UX problems on the create new from scratch screen.
* Improvement: Introduced a setup status checklist feature on the CartFlows dashboard.
* Improvement: Displaying the selected product's information on the opt-in page consistently with the checkout page.
* Improvement: Enhanced the custom field creation process with inline error messages.
* Improvement: Enhanced the mobile experience of CartFlows Dashboard and Flow Listing page.
* Improvement: Improved the onboarding steps to let the user complete the required steps to help setup the CartFlows easily.

= Version 2.1.7 - Friday, 24th January 2025 =
* Fix: Resolved a critical issue caused by a missing compatibility class for the fix implemented for Oxygen Builder on the Thank You page.

= Version 2.1.6 - Thursday, 23rd January 2025 =
* New: Introduced the Rollback feature for CartFlows.
* Improvement: Enhanced block previews in Gutenberg for better clarity.
* Improvement: Removed the General Panel from Landing step settings for a cleaner interface.
* Improvement: Displayed product details in the Product Dropdown for easier selection.
* Improvement: Modified the "No Access" message on the global settings popup for better communication.
* Improvement: Updated the NPS Library for improved functionality.
* Improvement: Added CartFlows branding to page builder widgets/blocks.
* Fix: Resolved inconsistent style issues in the Store Checkout and Subscribe Tabs on onboarding pages.
* Fix: Fixed color customization inconsistencies for checkout form fields.
* Fix: Addressed a conflict with the Convert Pro plugin on the onboarding screen.
* Fix: Changed the date picker theme to Light Mode for consistency.
* Fix: Fixed display issues on the onboarding screen for the Automations page.
* Fix: Turned off the "Required" toggle when the "Enable" toggle is disabled in the Field Editor.
* Fix: Resolved CSS issues affecting the empty cart message block on the checkout page..
* Fix: Allowed decimal values in the discount field for Checkout, Upsell, and Downsell steps.
* Fix: Resolved an issue where SVG files failed to upload in Elementor templates, leading to blank image displays.
* Fix: Fixed responsive issues for Instant Checkout on slightly larger tablets.
* Fix: Addressed a CSS issue for Payment Options on Checkout Page with WooPayments.
* Fix: Resolved a conflict with the Speedy - WooCommerce Shipping Method plugin.

= Version 2.1.5 - Wednesday, 25th December 2024 =
* Fix: Resolved an issue where Instant Checkout design styles were not applying to the coupon button.
* Fix: Added onboarding steps to simplify selecting and importing the Instant Checkout layout.
* Fix: Updated the color picker component to enhance usability and address UX issues.
* Fix: Fixed the step settings slide-out panel unexpectedly closing when changing the step title or adding a custom field.
* Fix: Unable to able to select 'Less than or equal to' dropdown option in the dynamic offers.
* Fix: The product options does not get saved when it is collapsed or close the settings panel.

= Version 2.1.4 - Tuesday, 10th December 2024 =
* New: What’s New Feed: Added a feature to view the latest updates for the CartFlows plugin directly in the dashboard.
* Improvement: Updated the "Activate License" section to clearly display the license status.
* Improvement: Excluded "Out of Stock" products from the product list when selecting items for Checkout or Upsell steps.
* Fix: Resolved an issue where the saved checkmark remained after reopening the Settings popup.
* Fix: Fixed a problem where selecting inactive page builders redirected users to a blank screen.
* Fix: Adjusted the "Choose Step" dropdown width to prevent text overflow on smaller screens.
* Fix: Updated the origin column to show "Direct" for Upsell/Downsell orders instead of "Unknown."
* Fix: Fixed a UI issue where the border of the discount input field appeared broken.
* Fix: Resolved an issue where the Save button remained stuck in a loading state when the quantity field was set to 0.
* Fix: Prevented negative values from being accepted in the percentage discount field.
* Fix: Fixed a bug where changes were not saved after confirming "Yes" in the save changes popup.
* Fix: Improved the visibility of the active checkout template and added a clear "Back" button for better navigation.
* Fix: Resolved a problem where toggle fields did not update immediately without refreshing the page.
* Fix: Fixed the Scroll to Top icon issue for the Astra theme.
* Fix: Resolved an incorrect usage error for the _load_textdomain_just_in_time function.
* Fix: Resolved an issue where shipping options appeared multiple times in the multistep checkout layout.
* Fix: Fixed a bug preventing the CartFlows funnel landing page from being edited in Bricks Builder when set as the homepage.
* Fix: Resolved an issue where the NPS survey appeared on fresh installs without any funnels created.
* Fix: Fixed a spacing issue in the Global Settings popup that caused the Social Tracking accordion to display incorrectly.
* Deprecated: The Step Note field is now marked as soft-deprecated across all steps.

= Version 2.1.3 - Tuesday, 19th November 2024 =
* Improvement: Improved the UI of CartFlows in-plugin notices for better user experience.
* Fix: Resolved an issue where the order summary price failed to update on mobile after adding an Order Bump.
* Fix: Addressed a bug in the date filter that caused inaccuracies in analytics data.

= Version 2.1.2 - Wednesday, 13th November 2024 =
* Fix: Improved the display of express checkout block of Checkout Plugins - Stripe for WooCommerce for Instant Checkout layout.
* Fix: The next-step button UI of Two Step Checkout layout was not getting displayed properly after latest release of Elementor.

 = Version 2.1.1 - Wednesday, 13th November 2024 =
* New: Added new options in Funnel's setting to allow configurable logo width and height settings, conditionally applied for block (FSE) themes.
* Fix: Resolved an issue with logo display on Instant Checkout pages when using the Bricks theme.
* Fix: Resolved issue with shipping methods not refreshing correctly on the checkout page.
* Fix: Improved CSS for shipping error messages for better readability and consistent styling in RTL and standard templates.

= Version 2.1.0 - Thursday, 7th November 2024 =
* New: Instant Checkout & Thank You Layouts for distraction free and conversion focused checkout experience.
* New: Introduced Bricks Builder integration to easily design and build CartFlows steps.
* New: Added support for Dynamic Content Tags in CartFlows steps using Bricks Builder.
* New: SureTriggers Integration for adding workflows for each steps if required.
* New: Introduced native support for Pinterest Pixel to track activities on CartFlows Optin, Landing, Checkout, and Thank You pages.
* New: Introduced native support for Snapchat Pixel to track activities on CartFlows Optin, Landing, Checkout, and Thank You pages.
* Improvement: Enhanced user experience by moving the Shipping Methods section below the checkout fields.
* Improvement: Enhanced slide-in content display consistency across different funnel steps and tabs.
* Fix: Resolved the issue of knowledge base articles not being found when searching with lowercase terms.

The full changelog is available [here](https://cartflows.com/product/cartflows/).

== Upgrade Notice ==
