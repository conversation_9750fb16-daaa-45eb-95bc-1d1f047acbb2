Version 2.1.14 - Thursday, 26th June 2025
- Improvement: Funnel import from a JSON file is now possible even when no funnels exist, as the Import button is displayed.
- Fix: Fixed the issue of image render on Instant Checkout Thank you page.

Version 2.1.13 - Monday, 16th June 2025
- Improvement: Enhanced the Create New Product popup for a better user experience.
- Fix: Resolved an issue where the Order Bump stayed selected after the product was removed when cart editing on checkout was enabled.
- Fix: Exported funnel JSON files now include previously missing funnel settings data.

Version 2.1.12 - Wednesday, 21th May 2025
- Improvement: Improved the toggle button with improved handling of disabled states, making the toggle button more intuitive and user-friendly.
- Improvement: Added compatibility for future release of CartFlows Pro.

Version 2.1.11 - Tuesday, 13th May 2025
- New: Added option to upload a custom logo in the header of the Instant Layout style.
- Improvement: Enhanced tooltips across the interface for better clarity.
- Fix: Resolved a critical error caused by the latest update of the WooCommerce Stripe Gateway.
- Fix: Fixed an issue where the “What’s New” notification was not being displayed.
- Fix: Purchase event was not being tracked correctly for Facebook — now fixed.
- Fix: Resolved dropdown overlap issue with country and state field labels.
- Fix: Slide-out panel now closes properly when clicking outside the panel area.
- Fix: Fixed styling issues for navigation tabs in the Order Bump and Checkout Offer settings panels.
- Fix: Resolved a conflict with shipping methods when using the DPD Blastic shipping plugin on CartFlows Checkout.
- Fix: Fixed checkout field order conflict with the PostNL plugin in CartFlows.
- Fix: Shipping options now display correctly without needing to refresh the checkout page.
- Fix: Custom content was not appearing on the Instant Layout of the Thank You page — now resolved.

Version 2.1.10 - Wednesday, 9 April 2025
- Improvement: SureTriggers is now OttoKit. We've updated the compatibility to keep everything working smoothly.
- Improvement: Instant Layout will be auto-enabled when users create a funnel from scratch. This will help users get a ready-made structure right away.
- Improvement: Improved design of Store Checkout pop-up

Version 2.1.9 - Thursday, 3rd April 2025
- New: Introduced a new style for thank you page.
- Fix: Corrected the price display on the Instant Layout of the Thank You page when tax is enabled.
- Fix: Improved UX and mobile responsiveness on the step’s template library page.
- Fix: Fixed an error when adding an order bump product in step settings.
- Fix: Ensured billing and shipping details are displayed for all users on the Thank You page, not just logged-in users.
- Fix: Resolved an issue where the incorrect order number was shown on the Instant Layout of the Thank You page when using the Sequential Order Numbers plugin.

Version 2.1.8 - Wednesday, 5th March 2025
- New: Introduced language files for multiple languages for quick translation of the plugin.
- Fix: Resolved issue preventing step pages from being edited with Oxygen Builder.
- Fix: Resolved the conflict between CartFlows and custom templates of Bricks Builder on Shop page.
- Fix: The funnel's GCP colors were not getting imported while importing the ready-made templates.
- Fix: Removed the extra spacing at the bottom of the page while using the instant checkout layout on mobile devices.
- Fix: The product image is not displaying properly for iOS mobile devices for instant checkout layout.
- Fix: Corrected the issue where the next step button was active in the page builder's editor view, causing redirect problems during page editing.
- Fix: Resolved UI/UX problems on the create new from scratch screen.
- Improvement: Introduced a setup status checklist feature on the CartFlows dashboard.
- Improvement: Displaying the selected product's information on the opt-in page consistently with the checkout page.
- Improvement: Enhanced the custom field creation process with inline error messages.
- Improvement: Enhanced the mobile experience of CartFlows Dashboard and Flow Listing page.
- Improvement: Improved the onboarding steps to let the user complete the required steps to help setup the CartFlows easily.

Version 2.1.7 - Friday, 24th January 2025
- Fix: Resolved a critical issue caused by a missing compatibility class for the fix implemented for Oxygen Builder on the Thank You page.

Version 2.1.6 - Thursday, 23rd January 2025
- New: Introduced the Rollback feature for CartFlows.
- Improvement: Enhanced block previews in Gutenberg for better clarity.
- Improvement: Removed the General Panel from Landing step settings for a cleaner interface.
- Improvement: Displayed product details in the Product Dropdown for easier selection.
- Improvement: Modified the "No Access" message on the global settings popup for better communication.
- Improvement: Updated the NPS Library for improved functionality.
- Improvement: Added CartFlows branding to page builder widgets/blocks.
- Fix: Resolved inconsistent style issues in the Store Checkout and Subscribe Tabs on onboarding pages.
- Fix: Fixed color customization inconsistencies for checkout form fields.
- Fix: Addressed a conflict with the Convert Pro plugin on the onboarding screen.
- Fix: Changed the date picker theme to Light Mode for consistency.
- Fix: Fixed display issues on the onboarding screen for the Automations page.
- Fix: Turned off the "Required" toggle when the "Enable" toggle is disabled in the Field Editor.
- Fix: Resolved CSS issues affecting the empty cart message block on the checkout page..
- Fix: Allowed decimal values in the discount field for Checkout, Upsell, and Downsell steps.
- Fix: Resolved an issue where SVG files failed to upload in Elementor templates, leading to blank image displays.
- Fix: Fixed responsive issues for Instant Checkout on slightly larger tablets.
- Fix: Addressed a CSS issue for Payment Options on Checkout Page with WooPayments.
- Fix: Resolved a conflict with the Speedy - WooCommerce Shipping Method plugin.

Version 2.1.5 - Wednesday, 25th December 2024
- Fix: Resolved an issue where Instant Checkout design styles were not applying to the coupon button.
- Fix: Added onboarding steps to simplify selecting and importing the Instant Checkout layout.
- Fix: Updated the color picker component to enhance usability and address UX issues.
- Fix: Fixed the step settings slide-out panel unexpectedly closing when changing the step title or adding a custom field.
- Fix: Unable to able to select 'Less than or equal to' dropdown option in the dynamic offers.
- Fix: The product options does not get saved when it is collapsed or close the settings panel.

Version 2.1.4 - Tuesday, 10th December 2024
- New: What’s New Feed: Added a feature to view the latest updates for the CartFlows plugin directly in the dashboard.
- Improvement: Updated the "Activate License" section to clearly display the license status.
- Improvement: Excluded "Out of Stock" products from the product list when selecting items for Checkout or Upsell steps.
- Fix: Resolved an issue where the saved checkmark remained after reopening the Settings popup.
- Fix: Fixed a problem where selecting inactive page builders redirected users to a blank screen.
- Fix: Adjusted the "Choose Step" dropdown width to prevent text overflow on smaller screens.
- Fix: Updated the origin column to show "Direct" for Upsell/Downsell orders instead of "Unknown."
- Fix: Fixed a UI issue where the border of the discount input field appeared broken.
- Fix: Resolved an issue where the Save button remained stuck in a loading state when the quantity field was set to 0.
- Fix: Prevented negative values from being accepted in the percentage discount field.
- Fix: Fixed a bug where changes were not saved after confirming "Yes" in the save changes popup.
- Fix: Improved the visibility of the active checkout template and added a clear "Back" button for better navigation.
- Fix: Resolved a problem where toggle fields did not update immediately without refreshing the page.
- Fix: Fixed the Scroll to Top icon issue for the Astra theme.
- Fix: Resolved an incorrect usage error for the _load_textdomain_just_in_time function.
- Fix: Resolved an issue where shipping options appeared multiple times in the multistep checkout layout.
- Fix: Fixed a bug preventing the CartFlows funnel landing page from being edited in Bricks Builder when set as the homepage.
- Fix: Resolved an issue where the NPS survey appeared on fresh installs without any funnels created.
- Fix: Fixed a spacing issue in the Global Settings popup that caused the Social Tracking accordion to display incorrectly.
- Deprecated: The Step Note field is now marked as soft-deprecated across all steps.

Version 2.1.3 - Tuesday, 19th November 2024
- New: Added NPS Survey to gather your valuable feedback!
- Improvement: Improved the UI of CartFlows in-plugin notices for better user experience.
- Fix: Resolved an issue where the order summary price failed to update on mobile after adding an Order Bump.
- Fix: Addressed a bug in the date filter that caused inaccuracies in analytics data.

Version 2.1.2 - Wednesday, 13th November 2024
- Fix: Improved the display of express checkout block of Checkout Plugins - Stripe for WooCommerce for Instant Checkout layout.
- Fix: The next-step button UI of Two Step Checkout layout was not getting displayed properly after latest release of Elementor.

Version 2.1.1 - Wednesday, 13th November 2024
- New: Added new options in Funnel's setting to allow configurable logo width and height settings, conditionally applied for block (FSE) themes.
- Fix: Resolved an issue with logo display on Instant Checkout pages when using the Bricks theme.
- Fix: Resolved issue with shipping methods not refreshing correctly on the checkout page.
- Fix: Improved CSS for shipping error messages for better readability and consistent styling in RTL and standard templates.

Version 2.1.0 - Thursday, 7th November 2024
- New: Instant Checkout & Thank You Layouts for distraction free and conversion focused checkout experience.
- New: Introduced Bricks Builder integration to easily design and build CartFlows steps.
- New: Added support for Dynamic Content Tags in CartFlows steps using Bricks Builder.
- New: SureTriggers Integration for adding workflows for each steps if required.
- New: Introduced native support for Pinterest Pixel to track activities on CartFlows Optin, Landing, Checkout, and Thank You pages.
- New: Introduced native support for Snapchat Pixel to track activities on CartFlows Optin, Landing, Checkout, and Thank You pages.
- Improvement: Enhanced user experience by moving the Shipping Methods section below the checkout fields.
- Improvement: Enhanced slide-in content display consistency across different funnel steps and tabs.
- Fix: Resolved the issue of knowledge base articles not being found when searching with lowercase terms.

Version 2.0.12 - Tuesday, 8th October 2024
- New: Introduced native support for Google Ads to track activities on CartFlows Optin, Landing, Checkout, and Thank You pages.
- Improvement: Users are now automatically redirected to the default editor if the selected builder plugin is missing.
- Fix: Addressed an issue where the radio button field wasn't appearing in Optin Forms.
- Fix: Fixed the loading issue with search functionality and pagination on the CartFlows funnel page (backend).

Version 2.0.11 - Monday, 16th September 2024
- New: Introduced native support for TikTok Pixel to track activities on CartFlows Optin, Landing, Checkout, and Thank You pages.
- Improvement: Enhanced the checkout fields UI to resolve spacing inconsistencies.
- Fix: Addressed an issue where outdated templates occasionally appeared for supported page builders.

Version 2.0.10 - Tuesday, 27th August 2024
- New: Added a setting in the backend to modify the "no shipping" message displayed during checkout.
- New: Introducing recommended plugins page, that suggests useful plugins to enhance your experience.
- Fix: Addressed a bug where WooCommerce coupons restricted to specific emails were causing an invalid email error during checkout.

Version 2.0.9 - Thursday, 11th July 2024
- Fix: The redirect from product page was showing 404 page if the selected flow is deleted or trashed.
- Fix: Fixed the character encoding issue while importing the ready-made flow and step templates.
- Fix: Resolved the conflict on checkout fields with CartFlows and FluentCRM plugin.

Version 2.0.8 - Thursday, 16th May 2024
- Fix: A blank question mark was getting added in the URL of next-step button.
- Security Fix: Hardened the security of the plugin suggested by Wordfence.

Version 2.0.7 - Tuesday, 23nd April 2024
- Fix: Unable to display the 100% discount applied on product tab for Checkout page and Order Bump backend setting.
- Fix: Added the missing coupon option in order bump product tab.
- Fix: Option added enable/disable toggle for subtitle option for next-step button block of Gutenberg of landing page.
- Fix: Resolved the deprecated filter sanitize string notice for PHP 8.2 version.

Version 2.0.6 - Thursday, 28th March 2024
- Improvement: Improved the CSS for 404 page for the admin interface.
- Fix: Fixed the selection issue with set default product option on the checkout page.

Version 2.0.5 - Monday, 11th March 2024
- Improvement: Added the product names on the hover of the product tag in the dynamic conditions dropdown option
- Fix: DIVI flow was not getting imported via JSON file.
- Fix: In permalink, the Default option was not getting displayed as selected.
- Fix: The edit flow button's position in edit page for block editor was not aligned properly.
- Fix: Flow's list was not getting displayed if all of the flows are moved to trashed.
- Fix: Flow's bulk actions were not working in some cases.
- Fix: Step's slug was not showing the updated slug value.

Version 2.0.4 - Thursday, 15th February 2024
- Fix: Flows exported via bulk functionality was not getting importing properly
- Fix: Fixed the clone, delete ajax calls were returning 400 error in some case.

Version 2.0.3 - Thursday, 25th January 2024
- Improvement: Modified the location of Change Template button for store checkout steps. It is now available in the individual step's setting panel.
- Fix: Fixed province field issue not showing selected value with certain countries for Google address autocomplete.
- Fix: The URL parameters was not getting updated if the same parameters are present in the URL.
- Fix: Google autocomplete address settings was not getting saved properly on multi-site setup.
- Fix: Default value for email field was not getting populated after logged-in from the Checkout page using the Modern Checkout Layout.
- Fix: Long label text was getting displayed for custom fields in the backend settings.
- Fix: Minor UI improvement in dynamic offers for dropdown select field display.
- Fix: Fixed the `null` parameter deprecation warning for strpos function.
- Fix: Resolved typography inconsistency between input's text and it's placeholder for checkout fields.
- Fix: The step setting's slide-out popup was getting closed when another popup OR alert window is opened.

Version 2.0.2 - Monday, 4th December 2023
- Security Fix: Hardened the security of the plugin suggested by PatchStack.
- Fix: Unable to add the product on the upsell/downsell step after they are cloned.
- Fix: Responsive issue for width of first and last name fields.
- Fix: Flow template from exported JSON file was not getting imported.
- Fix: Dynamic Conditions Rules of checkout step were not getting re-ordered.
- Fix: Radio button for product options selection was not working when set to single selection.

Version 2.0.1 - Friday, 10th November 2023
- New: Introduced the option to enable the Global Color Pallet of CartFlows for individual flows.
- New: Introduced the option to edit the order bump title.
- Fix: Fixed deprecated notices for Elementor displayed on the landing step for next step button.
- Fix: Fixed the array to string conversion warning while using the Block Builder.

Version 2.0.0 - Tuesday, 7th November 2023
- New: Revamped the whole CartFlows user-interface to add a user-friendly touch.
- New: Introduced a do_action `cartflows_store_checkout_before_configure_cart` for store checkout.
- New: Added a way to carry forward the URL parameters to the next step.
- Fix: Fixed the flow import/export issue with multi-language in which special characters are used.
- Fix: Fixed Modern Cart Plugin compatibility for dynamic flow feature of CartFlows.
- Fix: Fixed the deprecation notices for the latest update of Elementor.
- Fix: Fixed the checkout field mobile responsive issue for two-step checkout layout.

Version 1.11.15 - Monday, 14th August 2023
- Fix: CartFlows blocks were not working with the latest version of Spectra.
- Fix: The test mode was not working on the thank you page with the restrict thank you page feature.

Version 1.11.14 - Tuesday, 1st August 2023
- Improvement: The Redirect after thank you URL option, can be also used to redirect another domains.
- Fix: The email address was not getting stored in order when the order was placed from express checkout.
- Fix: The forward slash was getting added in the add to cart button string if apostrophe is added in the string.

Version 1.11.13 - Tuesday, 6th June 2023
- Improvement: Additional checks added to validate data in flow & step operations.

Version 1.11.12 - Friday, 2nd June 2023
- Fix: Updated post operation methods for better security enforcement.

Version 1.11.11 - Thursday, 25th May 2023
- Fix: Fixed the reloading of the checkout page when clicking on the log-in link while in an incognito/private browser window.
- Fix: Raw HTML was displayed for shipping methods in order summary in mobile devices.
- Fix: Showing raw HTML in weekly report email.

Version 1.11.10 - Monday, 24th April 2023
- Improvement: Restructured the code to enhance the security of the plugin.
- Fix: Corrected an issue where Google AutoAddress was not functioning properly in some situations.
- Fix: Fixed a problem where the modern checkout was displaying duplicate email fields in some situations.
- Fix: Resolved an error that occurred when attempting to edit a step using Gutenberg without having WooCommerce installed or activated.
- Fix: Google AutoAddress was not working correctly in some cases.
- Fix: Showing duplicate email field for modern checkout in some cases.
- New: Introduced a new action `cartflows_save_ab_test_setting` for A/B test save setting.

Version 1.11.9 - Tuesday, 7th February 2023
- Improvement: Improved the preview of checkout offer feature.
- Fix: Fixed the conflict between "Snapmint" local payment gateway and "Buy Now for WooCommerce" by wpismylife plugin.
- Fix: Product image was getting replaced on the checkout page.
- Fix: Recurring totals section was not displaying in the mobile view.
- Fix: Fixed elementor deprecated action notices.
- Fix: Primary color was not getting applied to the radio button while using Gutenberg as a page builder.

Version 1.11.8 - Tuesday, 20th December 2022
- New: Added edit flow option on the admin bar for easy access to the flow.
- Improvement: Minimum versions of PHP and WordPress are increased to 7.2 & 5.8 respectively.
- Improvement: CartFlows logger file improved. Each log will be auto-deleted after one month to save some storage on the server.
- Improvement: Added typography options for CartFlows Gutenberg blocks.
- Fix: Few settings of the CartFlows Gutenberg block were not working.
- Fix: Function deprecated notices for PHP 8.1 version.
- Fix: Elementor functions deprecation notices from elementor.
- Fix: Duplicate product image on the flyout cart section.
- Fix: Custom field was not getting added for the optin step.
- Fix: Redirection issue when multiple checkout steps are added.
- Fix: The billing email field was not getting pre-filled for modern checkout.
- Fix: PHP warnings when a radio custom field is added to optin.
- Dev: Minor structural changes for better optimizations.

Version 1.11.7 - Thursday, 1st December 2022
- Fix: Weekly report emails were not sending in some cases.

Version 1.11.6 - Tuesday, 22nd November 2022
- New: Added option to display inline field validation message for the field validation.
- Improvement: Added new CSS customizations options for CartFlows Gutenberg Blocks.
- Fix: The icon on the order review toggle button was not visible in the mobile view.
- Fix: Weekly email reports were not working.

Version 1.11.5 - Wednesday, 16th November 2022
- Fix: Product quantity was getting misaligned if the product name is large in order review section.
- Fix: Order Bump description editor not working if edited in text mode.

Version 1.11.4 - Monday, 7th November 2022
- Improvement: Revamped CartFlows Gutenberg blocks.
- Improvement: Added WooCommerce HPOS compatibility.

Version 1.11.3 - Friday, 4th November 2022
- Fix: Flow templates were not importing while using WordPress Version 6.1.

Version 1.11.2 - Thursday, 13th October 2022
- New: Added support of multiple thank you pages in store checkout flow.

Version 1.11.1 - Thursday, 15th September 2022
- Fix: Discounted price was not getting displayed in the backend.

Version 1.11.0 - Thursday, 8th September 2022
- Improvement: New designing options introduced for order bump customizations.
- Improvement: Improved the error handling for importing the ready-made templates.
- Fix: Undefined constant notice was getting displayed on the onboarding screen.

Version 1.10.4 - Tuesday, 23rd August 2022
- New: Added an option to change the checkout heading texts.
- New: Added compatibility for a future release of CartFlows Pro.
- Fix: Conflict/errors with LearnDash LMS - Course Grid plugin.
- Fix: Store Checkout page was not displaying on multi-site setup in rare cases.

Version 1.10.3 - Tuesday, 26th July 2022
- Fix: The product tab content was not showing for offer steps of store checkout.
- Fix: CSS conflict with the Kadence theme for RTL view.
- Fix: Onboarding dashboard widget was showing for non-admin users.

Version 1.10.2 - Thursday, 21st July 2022
- Improvement: Added filter `cartflows_show_store_checkout_product_tab` to show the product tab for store checkout.

Version 1.10.1 - Thursday, 7th July 2022
- Fix: CartFlows Setup dashboard widget was not getting hidden if hidden from the screen option.
- Fix: Fixed console error on store checkout tab.

Version 1.10.0 - Wednesday, 6th July 2022
- New: Added Store checkout aka Global Checkout tab with improved functionalities.
- New: Added lead event to the Facebook pixel & google analytics for optin pages.
- New: Added weekly report emails for revenue regenerated by Store.
- New: Revamped the onboarding UI.
- New: Added setup menu to complete the incomplete onboarding.
- Improvement: Updated the permalink base slug for CartFlows Pages (New users).
- Fix: Label field was getting overlapped if the "sell to a specific country" option was set in the WooCommerce setting while using modern labels.

Version 1.9.2 - Tuesday, 31st May 2022
- New: Introduced user role management.
- New: Introduced dynamic JS variables to use in a custom script on CartFlows pages.
- Fix: Coupon field in order review was not getting hidden in mobile view for modern checkout layout
- Fix: The email address was missing in order details when the order was placed through express checkout.

Version 1.9.1 - Thursday, 31st March 2022
- Fix: Optin was not working if the same email exists.
- Fix: Fixed deprecated function notices of Elementor.
- Fix: Field setting to hide/show order sections on thank you page was not working while using shortcode.
- Fix: Console error on the flow analytics page when CartFlows Pro was not installed.

Version 1.9.0 - Wednesday, 16th March 2022
- New: Google address autocomplete feature.
- New: Added new style for input labels.
- New: Added a new modern checkout one-column & two-column style layouts.
- New: The thank you page content will not be displayed if no valid order is found & non logged users.
- New: Added a new viewContent event in FB pixel.
- New: Added one-column checkout layout from the pro version to free version.
- New: Added custom field editor setting of checkout fields from the pro version to free version.
- Improvement: A notice will be displayed if Gutenberg plugin is activated.
- Improvement: Added compatibility for CPSW express checkout buttons position.
- Improvement: Added RTL support for the Canvas.
- Improvement: Hard deprecated the "Floating Labels" style of fields and introduced new style "Modern Labels".
- Fix: Fixed some issues in Gutenberg modules where some settings were not working.
- Fix: Only fifteen flows were getting listed in the templates menu.
- Fix: SVGs available on the ready-made templates were not getting downloaded while importing the templates.

Version 1.8.2 - Friday, 28th January 2022
- Improvement: WooCommerce templates updated.
- Improvement: Removed deprecated function of LearnDash.
- Fix: The global checkout tag wasn't displaying for the checkout step.
- Fix: The menu settings dropdown was not working.

Version 1.8.1 - Tuesday, 25th January 2022
- Security Fix: Hardened the security of the plugin as suggested by Wordfence.
- Fix: Showing incorrect product price in GA event in some cases.

Version 1.8.0 - Tuesday, 28th December 2021
- New: Introduced Canvas 1.0 for a better overview of the flow.
- New: Introduced a new option to override the global checkout page.
- New: Added compatibility of "Dynamic Offers" aka "Rule Engine" for conditional redirects.
- New: Added compatibility of the Dynamic Order Bumps.
- Improvement: Hard deprecated the "cartflows_allow_persistace" filter, use "cartflows_allow_persistence" instead.
- Improvement: Fixed deprecated notices for Elementor.
- Fix: Fatal error while editing the elementor saved template if the checkout shortcode is present.

Version 1.7.3 - Tuesday, 21th December 2021
- Improvement: Improved Starter Templates compatibility.

Version 1.7.2 - Tuesday, 14th December 2021
- Improvement: Starter templates compatibility added.

Version 1.7.1 - Monday, 20th September 2021
- Fix: Vertical navigation in step and flow setting was not working for RTL and translation.
- Fix: Showing blank screen if the step title is blank.
- Fix: Input field style was not getting displayed for other checkout layouts in CartFlows-Gutenberg Blocks.
- Fix: Input field required style not working in CartFlows-Gutenberg Block.
- Fix: Fixed some notices which were displayed for PHP version 8.0

Version 1.7.0 - Wednesday, 21th July 2021
- New: Multiple Order Bumps - Ability to display multiple order bumps on the checkout page with new styles.
- Fix: Wrong products appearing when multiple checkouts are opened simultaneously.
- Deprecated: Legacy Admin UI has been removed.
- Deprecated: Order bump settings have been removed from all page builder settings.

Version 1.6.15 - Tuesday, 22nd June 2021
- Improvement: Revamped the Facebook Pixel Tracking integration.
- Improvement: Revamped the Google Analytics integration and GA-4 support added.
- Improvement: Added the styling option for the CartFlows order overview module of Beaver Builder.

Version 1.6.14 - Wednesday, 12th May 2021
- Fix: If one of the variations is out of stock on checkout, it will be skipped, and the next will get added.

Version 1.6.13 - Thursday, 29th April 2021
- Improvement: Hardened the security of the plugin.

Version 1.6.12 - Thursday, 22nd April 2021
- New: Added the regenerate CSS option to regenerate the step's dynamic CSS for shortcodes.
- New: Added an option to overwrite the global disallow indexing option for flow.
- Improvement: Added the placeholder images for the checkout products.
- Improvement: Added the quick view button for the step.
- Fix: Multiple flows and steps were getting imported if clicked multiple times on the import button.
- Fix: Long flow and step name length reduced.
- Fix: Font weight field was getting set to default after refresh.
- Fix: Font family field was not searchable.
- Fix: PHP error while searching the products.
- Fix: Showing no product selected notice in the editor for global checkout page.

Version 1.6.11 - Wednesday, 7th April 2021
- New: Added an option to delete plugin data on plugin deletion.
- Improvement: In test mode, it will add selected products to checkout instead of random if test mode is enabled.
- Improvement: Latest Google fonts updated.
- Fix: Critical error if product variations not found while searching for the product.
- Fix: No product assigned badge for steps in some cases.
- Fix: Color field's reset button was not working.

Version 1.6.10 - Wednesday, 24th March 2021
- New: Added the custom script option for the flows.
- Fix: Step was not getting deleted when the A/B test was enabled while cloning the flow.
- Fix: Offer step badges were not updating while reordering the steps.
- Fix: Showing error too few arguments to function redirect_to_next_flow_step.
- Fix: The default page builder option of the setup wizard was not working for Gutenberg.

Version 1.6.9 - Wednesday, 10th March 2021
- Improvement: WordPress 5.7 Compatibility added.
- Fix: Showing regular price even if the sale price is set in the product settings.
- Fix: Float values were getting converted in Integer format in order bump product discount settings.
- Fix: Updated step title was not getting displayed in the flow analytics.
- Fix: Unable to distinguish between the checkout style free and pro options.

Version 1.6.8 - Thursday, 25th February 2021
- New: Dynamic Flow. Now you can start your flow from the product's single page too.
- New: Default product selection option in product's options on checkout setting.
- Improvement: Added an option in the new UI to publish the private flow.
- Fix: Error while auto-renewal of subscription product when Google Analytics is enabled.

Version 1.6.7 - Thursday, 18th February 2021
- Improvement: WooCommerce templates updated.
- Fix: Broken menu was getting displayed in the footer for Astra Theme.

Version 1.6.6 - Thursday, 11th February 2021
- Fix: The single exported flow was not getting imported.
- Fix: CartFlows modules were not getting displayed on the Beaver Builder's Module Setting page.
- Fix: The product's tab was not displaying after importing the JSON flow.

Version 1.6.5 - Thursday, 4th February 2021
- New: Added the support for the prefill checkout fields using query parameters.
- Fix: Showing an activate license notice while creating a flow from scratch.
- Fix: A/B Split testing setting was not getting saved.
- Fix: Request timeout error while importing a template.
- Fix: Checkout was not working when the optin step was set as a home page.

Version 1.6.4 - Thursday, 21st January 2021
- Fix: Admin dashboard was not loading or showing 500 error on few servers.
- Fix: Showing wrong product name in product settings after reload.
- Fix: Bulk import flows were not working.

Version 1.6.3 - Monday, 18th January 2021
- Fix: Checkout steps backend was not working when a variation product was selected.

Version 1.6.2 - Monday, 18th January 2021
- Improvement: New UI RTL support added.
- Fix: Checkout product selection settings saving issue when empty.
- Fix: Thank you page meta settings saving issue.
- Fix: Reset Analytics button was not working.

Version 1.6.1 - Thursday, 7th January 2021
- Fix: Global settings field dependency logic.

Version 1.6.0 - Tuesday, 5th January 2021
- New: Introduced new admin UI.
- Fix: Templates page was not displaying.
- Fix: ActiveMember360 redirect issue while login on the CartFlows checkout page.
- Fix: Error in Google Analytics if the WooCommerce is not installed & activated.
- Fix: No products selected error message was displayed for CartFlows-Beaver Builder module.

Version 1.5.22 - Friday, 6th November 2020
- Fix: LearnDash and Tutor LMS permalink conflict.

Version 1.5.21 - Thursday, 5th November 2020
- Fix: Pages was showing 404 error due to permalink issue.

Version 1.5.20 - Thursday, 5th November 2020
- New: Introduced Gutenberg Blocks.

Version 1.5.19 - Tuesday, 13th October 2020
- New: Introduced Beaver Builder widgets.
- Fix: Facebook Pixel tracking events were not getting triggered when WooCommerce was not activated.

Version 1.5.18 - Tuesday, 29th September 2020
- New: Introduced Elementor widgets.
- Improvement: Added split test compatibility for a global checkout.
- Improvement: Added compatibility for a future release of CartFlows Pro.
- Fix: Place order button text option was not working for the optin page when the optin page was set as homepage.

Version 1.5.17 - Tuesday, 8th September 2020
- New: Added Braintree subscription product compatibility. Now, you can select it on the checkout page meta setting.
- Improvement: Added compatibility for a future release of CartFlows Pro.
- Fix: CartFlows meta boxes were stuck in the side panel.
- Fix: Groups Menu was getting redirected to the home page in BuddyBoss Theme.
- Fix: Checkout page was not displaying the rest of the product if one product goes out of stock.

Version 1.5.16 - Tuesday, 25th August 2020
- Improvement: Hardened the security of the plugin.
- Fix: User's account was not getting created from the Optin step.
- Fix: Import notice was not displaying if the import step limit is over.
- Fix: Facebook Pixel and Google Analytics tracking events were not getting triggered in some cases.

Version 1.5.15 - Wednesday, 12th August 2020
- Improvement: Added Split Testing compatibility for a future release of CartFlows Pro.
- Improvement: Added WordPress 5.5 Compatibility.
- Fix: Product discount price & percentage options were not working if the full discount is applied.
- Fix: Account already exists error message on the Optin step.

Version 1.5.14 - Thursday, 30th July 2020
- Fix: Not able to search & select the course type of product.

Version 1.5.13 - Tuesday, 21st July 2020
- Improvement: Improved the product search query of meta options.
- Improvement: Added compatibility for a future release of CartFlows Pro.

Version 1.5.12 - Wednesday, 1st July 2020
- Fix: PHP error if WooCommerce was not installed and the Google Analytics Purchase event is enabled.
- Fix: Flows & Steps ready-made templates were unable to import due to the X-CSRF-TOKEN JS error.

Version 1.5.11 - Tuesday, 16th June 2020
- New: Users can now share [non-personal usage data] to help us test and develop better products. ( https://my.cartflows.com/usage-tracking/ )
- Fix: CartFlows setup wizard.
- Fix: Showing notice 'Remove selected product from checkout' even there is no product selected in Global checkout.
- Fix: Redirected to the cart when the order canceled by a user for PayPal checkout gateway.

Version 1.5.10 - Wednesday, 3rd June 2020
- New: Gutenberg templates added.
- New: Import/Export content support added.
- Fix: Step slug was not editable in some permalink cases.
- Improvement: Trashed flows will not be consider for flow restriction.

Version 1.5.9 - Thursday, 14th May 2020
- New: Added quantity and discount options for the product.
- Fix: Google Analytics JS error.
- Fix: Astra theme PHP notice when WooCommerce was not activated.

Version 1.5.8 - Saturday, 9th May 2020
- Fix: Incorrect required field error messages with PayPal.

Version 1.5.7 - Monday, 4th May 2020
- New: Gutenberg support added for steps.
- Fix: Astra's mini cart icon display issue on the CartFlows steps.

Version 1.5.6 - Thursday, 23rd April 2020
- Fix: Checkout two-step type CSS conflict with the latest version of The-7 theme.
- Fix: Optin step CSS conflict with the Astra theme.

Version 1.5.5 - Tuesday, 14th April 2020
- Fix: CSS conflict with the latest release of Astra theme.

Version 1.5.4 - Tuesday, 7th April 2020
- Improvement: Hardened the security of the plugin.
- Fix: Older flows were not displaying in the library.

Version 1.5.3 - Wednesday, 1st April 2020
- Improvement: Divi CSS support added for Optin step type.
- Fix: FB Pixel php notice issue & conflict with Beaver Builder.

Version 1.5.2 - Wednesday, 18th March 2020
- New: Added an option to enable Facebook pixel's Pageview event on the whole site.
- Improvement: Added compatibility for the Pro theme.
- Improvement: Added compatibility for the Kallyas theme.
- Improvement: Added compatibility for a future release of CartFlows Pro.
- Fix: Dynamic CSS was not working properly in some DIVI cases.

Version 1.5.1 - Thursday, 12th March 2020
- Improvement: WooCommerce version 4.0.0 compatibility tested.

Version 1.5.0 - Wednesday, 4th March 2020
- New: Native Google Analytics support added.
- Fix: PHP error in Elementor preview if WooCommerce was not installed and the Facebook Pixel setting was enabled.

Version 1.4.2 - Wednesday, 19th February 2020
- New: Added option to change default order confirmations text on thank you page.
- New: Added ability to set optin step type as a home page.
- New: Create WooCommerce's product directly from the checkout step.
- Improvement: Optin step type is included in LearnDash LMS template settings.

Version 1.4.1 - Thursday, 13th February 2020
- Fix: Optin step was redirecting to the global checkout.

Version 1.4.0 - Tuesday, 11th February 2020
- New: Introduced a new optin step type for lead generation.
- Improvement: Added compatibility for a future release of CartFlows Pro.
- Fix: Fatal Error while editing the page in elementor preview mode if the WooCommerce is not installed.

Version 1.3.5 - Tuesday, 28th January 2020
- New: Option added for thank you page redirection.
- Fix: Pro version's two-step CSS conflict with the DIVI, the second tab was not displaying.
- Fix: Checkbox icon for terms & conditions was not displaying for DIVI Builder & Theme.

Version 1.3.4 - Tuesday, 14th January 2020
- New: Option added to change order button text for each flow.
- Improvement: WooCommerce Google Analytics Integration plugin's compatibility added.
- Fix: LearnDash deprecated function notice.

Version 1.3.3 - Thursday, 26th December 2019
- Improvement: Added filter `cartflows_global_checkout_url` for global checkout.
- Fix: Thrive's split test option was not displaying in some cases.
- Fix: Klarna's payment fields were not displaying due to the wc-ajax endpoint.

Version 1.3.2 - Wednesday, 11th December 2019
- New: Option added to remove products on the checkout page.
- Fix: Checkout page's login form text was not able to translate.
- Fix: 2Checkout & PayPlus payment gateway's compatibility issue with global checkout.

Version 1.3.1 - Wednesday, 6th November 2019
- Improvement: Added compatibility for a future release of CartFlows Pro.
- Fix: Security issue fix & added user capability check.

Version 1.3.0 - Tuesday, 15th October 2019
- New: Native Facebook pixel support added.
- Fix: Fallback added for mb_strpos function fatal error for older PHP versions.
- Fix: DIVI two step unused CSS removed.

Version 1.2.6 - Tuesday, 27th August 2019
- New: 'wp_body_open' action support added in templates.
- Improvement: Added compatibility for a future release of CartFlows Pro.
- Improvement: Checkout step type is included in LearnDash LMS template settings.
- Fix: Setting options were not working properly on a multisite setup.
- Fix: Blank menu was getting added for the setup wizard.

Version 1.2.5 - Tuesday, 13th August 2019
- New: Introduced Permalink structure.
- Improvement: Filter 'cartflows_coupon_field_text' introduced to change the coupon field text.
- Improvement: Normalize CSS issues when the default template was set.

Version 1.2.4 - Wednesday, 31st July 2019
- Improvement: Added compatibility for a future release of CartFlows Pro's Pre-Checkout Upsell.
- Fix: Account field CSS updated.

Version 1.2.3 - Thursday, 11th July 2019
- New: Permalink option added in settings to change the "cartflows_step" post-type slug.
- New: LearnDash Compatibility added for custom templates.
- Improvement: The hook 'cartflows_checkout_aftet_configure_cart' deprecated.
- Improvement: The 'Logo' tab removed from the checkout step.
- Improvement: Optimized the backward-compatibility database query.

Version 1.2.2 - Thursday, 27th June 2019
- Fix: Vivawallet Gateway issue fixed for global checkout.
- Fix: Two column css issue on mobile.

Version 1.2.1 - Thursday, 27th June 2019
- Fix: Display post states issue.

Version 1.2.0 - Wednesday, 26th June 2019
- New: Header, Footer default template added.
- Improvement: If the cart is empty, then now it will show empty cart notice on global checkout.
- Fix: Next step link `Open in new tab` issue.

Version 1.1.22 - Monday, 17th June 2019
- Improvement: Added "Skip Setup" button for onboarding wizard.
- Improvement: Added link in test mode notice to disable test mode from the frontend.
- Fix: Paypal cancel return redirect was not redirecting to the checkout page.
- Fix: Test mode box was not visible.

Version 1.1.21 - Wednesday, 12th June 2019
- Fix: Thank you page was redirecting to checkout page when global checkout was set.

Version 1.1.20 - Wednesday, 12th June 2019
- New: WooCommerce dependency removed. Now, you can use CartFlows without WooCommerce.
- Fix: Analytics was not showing.
- Fix: Bank account details were not showing on the Thank You page.
- Fix: Download details were not showing on the Thank You page for PayPal.
- Fix: Variation issue fixed when the WordPress folder path is different.

Version 1.1.19 - Tuesday, 4th June 2019
- Improvement: Extra theme compatibility added.
- Improvement: Oxygen builder compatibility added.
- Fix: Floating Label issue with account fields. CSS updated.
- Fix: Remove product icon not visible in the admin area for entre theme.

Version 1.1.18 - Friday, 10th May 2019
- Fix: Sometimes, Next step link was not working.

Version 1.1.17 - Tuesday, 30th April 2019
- Fix: Database connectivity error while cloning the flow.

Version 1.1.16 - Thursday, 25th April 2019
- Improvement: Added compatibility for the Cartflows Pro plugin update for an older versions.

Version 1.1.15 - Wednesday, 24th April 2019
- Fix: WooCommerce dependency issue.

Version 1.1.14 - Wednesday, 24th April 2019
- Improvement: Minify CSS and Js files filter added.
- Improvement: Inline coupon message. Now coupon message will show above coupon field.
- Fix: Divi compatibility CSS updated.
- Fix: Lost your password link was unable to click on the checkout page.

Version 1.1.13 - Tuesday, 9th April 2019
- Improvement: Added compatibility for a Two-Step checkout style of CartFlows Pro.
- Fix: CSS conflict with the Divi theme.
- Fix: Checkout field validation issue.
- Fix: Conflict with the Norebro theme.
- Fix: Redirection issue while paying through a Swish payment gateway.

Version 1.1.12 - Friday, 22nd March 2019
- New: Added ability to set checkout page as a home page.
- Fix: Thrive Architect CSS conflict.

Version 1.1.11 - Tuesday, 19th March 2019
- Fix: Checkbox tick on checkout was not visible in some cases.
- Fix: Width issue of order review.
- Fix: Selling the wrong main product in firefox.

Version 1.1.10 - Wednesday, 6th March 2019
- Fix: Divi builder next step link issue.

Version 1.1.9 - Tuesday, 5th March 2019
- Fix: Divi builder fails to open if the page is set as home page.
- Fix: Checkout field's width issue on mobile devices in Divi.

Version 1.1.8 - Wednesday, 27th February 2019
- New: Custom script support added for Landing, Checkout and Thank You page type.
- Fix: Added compatibility for pre-applied coupon field fix.
- Fix: Divi flow importer style tag issue.

Version 1.1.7 - Monday, 25th February 2019
- New: Divi templates.
- Fix: Shipping fields label issue.

Version 1.1.6 - Thursday, 07th February 2019
- Improvement: Added compatibility for a future release of CartFlows Pro's checkout field editor.
- Fix: Permalink conflict with listing theme.
- Fix: Thrive architect template popup conflict.
- Fix: Coupon code not applied in case of multiple products selection option is selected.

Version 1.1.5 - Wednesday, 23rd January 2019
- Fix: Beaver Builder Pro version conflict while installing templates.
- Fix: Elementor builder white screen while editing checkout page and cart is empty.

Version 1.1.4 - Tuesday, 22nd January 2019
- New: Beaver Builder templates.
- Improvement: Better error handling on the checkout page.
- Fix: Product variants were not getting aligned properly in the order summary.
- Fix: Divi builder conflict.

Version 1.1.3 - Tuesday, 15th January 2019
- New: Import / Export the flows
- Improvement: Pixel Caffeine plugin pixel compatibility added.
- Improvement: Filter introduced to change the coupon field placeholder & button text.
- Fix: Clone option conflict with Duplicate post and Duplicate page plugin.
- Fix: Cart is not empty after purchase.

Version 1.1.2 - Friday, 4th January 2019
- Fix: Import Cloud Template option not visible in thrive visual editor.
- Fix: Oceanwp css conflict with checkout layout.
- Fix: WC error notice in elementor preview mode when Elementor pro is not active/installed.

Version 1.1.1 - Wednesday, 2nd January 2019
- Improvement: Added compatibility for a future release of CartFlows Pro.
- Improvement: Minor CSS and HTML changes.
- Fix: Flatsome UX builder compatibility added.
- Fix: OceanWP CSS overwrite issue.
- Fix: Divi CSS issue.
- Fix: Other minor bugs.

Version 1.1.0.1 - Friday, 7th December 2018
- Fix: Checkout breaking issue.

Version 1.1.0 - Wednesday, 5th December 2018
- New: Ready for Order Bump Positions.
- Improvement: PixelYourSite compatibility added.
- Fix: Elementor preview not working for some permalinks.
- Fix: Checkout field label alignment issue.
- Fix: Long field text overlap on mobile.

Version 1.0.4 - Friday, 23rd November 2018
- Fix: Thank you page style meta option issue.

Version 1.0.3 - Friday, 23rd November 2018
- Improvement: Optimized flow and step import.
- Fix: Elementor page preview not working if landing page is set as homepage.
- Fix: Landing page issue when set as home-page. Used Home url instead of Site url.

Version 1.0.2 - Wednesday, 21st November 2018
- Fix: Address fields misalignment on checkout page.

Version 1.0.1 - Wednesday, 21st November 2018
- New: Checkout persistence remember data
- Fix: Link issue in Beaver Builder
- Fix: Unwanted fields on checkout page.

Version 1.0.0 - Monday, 19th November 2018
- Initial Release
