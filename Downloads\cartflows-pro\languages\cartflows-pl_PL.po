msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: gpt-po v1.1.1\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-04T15:33:29+00:00\n"
"PO-Revision-Date: 2025-02-04T15:33:29+00:00\n"
"Language: \n"

#: cartflows.php
#: classes/class-cartflows-admin-notices.php:217
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:62
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:71
#. Plugin Name of the plugin
msgid "CartFlows"
msgstr "CartFlows"

#: cartflows.php
#. Plugin URI of the plugin
msgid "https://cartflows.com/"
msgstr "https://cartflows.com/"

#: cartflows.php
#. Description of the plugin
msgid "Create beautiful checkout pages & sales flows for WooCommerce."
msgstr "Twórz piękne strony kasowe i procesy sprzedaży dla WooCommerce."

#: admin-core/ajax/ab-steps.php:89
#. translators: %s step id
msgid "Can't create a variation for this step - %s, Invalid Step ID."
msgstr "Nie można utworzyć wariantu dla tego kroku - %s, nieprawidłowy identyfikator kroku."

#: admin-core/ajax/ab-steps.php:105
#. translators: %s flow id
msgid "Step successfully hidden - %s"
msgstr "Krok pomyślnie ukryty - %s"

#: admin-core/ajax/ab-steps.php:140
#. translators: %s step id
msgid "Can't delete a variation for this step - %s, Invalid Step Id or Funnel Id."
msgstr "Nie można usunąć wariantu dla tego kroku - %s, nieprawidłowy identyfikator kroku lub leja."

#: admin-core/ajax/ab-steps.php:188
#. translators: %s flow id
msgid "Step deleted - %s"
msgstr "Krok usunięty - %s"

#: admin-core/ajax/ab-steps.php:223
#. translators: %s step id
msgid "Can't create a variation for this step - %s"
msgstr "Nie można utworzyć wariantu dla tego kroku - %s"

#: admin-core/ajax/ab-steps.php:279
#. translators: %s step id
msgid "A/B test settings updated for this step - %s"
msgstr "Ustawienia testu A/B zaktualizowane dla tego kroku - %s"

#: admin-core/ajax/ajax-errors.php:59
#: wizard/ajax/ajax-errors.php:59
msgid "Sorry, you are not allowed to do this operation."
msgstr "Przepraszam, nie masz uprawnień do wykonania tej operacji."

#: admin-core/ajax/ajax-errors.php:60
#: admin-core/ajax/common-settings.php:217
#: admin-core/ajax/common-settings.php:279
#: admin-core/ajax/common-settings.php:385
#: admin-core/ajax/common-settings.php:418
#: admin-core/inc/meta-ops.php:32
#: modules/checkout/classes/class-cartflows-checkout-ajax.php:110
#: wizard/ajax/ajax-errors.php:60
msgid "Nonce validation failed"
msgstr "Walidacja nonce nie powiodła się"

#: admin-core/ajax/ajax-errors.php:61
#: wizard/ajax/ajax-errors.php:61
msgid "Sorry, something went wrong."
msgstr "Przepraszam, coś poszło nie tak."

#: admin-core/ajax/ajax-errors.php:62
msgid "Required parameter is missing from the posted data."
msgstr "Brakuje wymaganego parametru w przesłanych danych."

#: admin-core/ajax/common-settings.php:85
msgid "Successfully deleted the dynamic CSS keys!"
msgstr "Pomyślnie usunięto dynamiczne klucze CSS!"

#: admin-core/ajax/common-settings.php:105
msgid "No post data found!"
msgstr "Nie znaleziono danych do przesłania!"

#: admin-core/ajax/common-settings.php:152
msgid "Successfully saved data!"
msgstr "Pomyślnie zapisano dane!"

#: admin-core/ajax/debugger.php:82
#: admin-core/ajax/debugger.php:133
#: admin-core/ajax/debugger.php:157
msgid "You don't have permission to perform this action."
msgstr "Nie masz uprawnień do wykonania tej czynności."

#: admin-core/ajax/debugger.php:91
msgid "Sync Success."
msgstr "Synchronizacja zakończona pomyślnie."

#: admin-core/ajax/debugger.php:105
#: admin-core/inc/log-status.php:79
msgid "You don't have permission to view this page."
msgstr "Nie masz uprawnień do przeglądania tej strony."

#: admin-core/ajax/debugger.php:139
#: admin-core/inc/log-status.php:175
msgid "Filename is empty. Please refresh the page and retry."
msgstr "Nazwa pliku jest pusta. Odśwież stronę i spróbuj ponownie."

#: admin-core/ajax/debugger.php:174
#: admin-core/inc/log-status.php:210
msgid "Invalid file."
msgstr "Nieprawidłowy plik."

#: admin-core/ajax/debugger.php:181
msgid "Export logs successfully"
msgstr "Pomyślnie wyeksportowano logi"

#: admin-core/ajax/flows.php:97
msgid "No Funnel IDs has been supplied to export!"
msgstr "Nie podano żadnych identyfikatorów lejków do eksportu!"

#: admin-core/ajax/flows.php:110
#: admin-core/ajax/importer.php:109
#: admin-core/ajax/importer.php:212
msgid "Funnel exported successfully"
msgstr "Lejek został pomyślnie wyeksportowany"

#: admin-core/ajax/flows.php:142
msgid "Can't update the flow data"
msgstr "Nie można zaktualizować danych przepływu"

#: admin-core/ajax/flows.php:159
#: admin-core/ajax/steps.php:415
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "(no title)"
msgstr "(brak tytułu)"

#: admin-core/ajax/flows.php:184
msgid "Successfully saved the flow data!"
msgstr "Pomyślnie zapisano dane przepływu!"

#: admin-core/ajax/flows.php:246
msgid "Successfully deleted the Funnels!"
msgstr "Pomyślnie usunięto lejki!"

#: admin-core/ajax/flows.php:274
#: admin-core/ajax/flows.php:352
#: admin-core/ajax/flows.php:988
msgid "No Funnel IDs has been supplied to delete!"
msgstr "Nie podano żadnych identyfikatorów lejków do usunięcia!"

#: admin-core/ajax/flows.php:323
#: admin-core/ajax/flows.php:391
msgid "Successfully trashed the Funnels!"
msgstr "Pomyślnie usunięto lejki!"

#: admin-core/ajax/flows.php:422
msgid "Invalid Funnel ID has been supplied to update title."
msgstr "Podano nieprawidłowy identyfikator lejka do aktualizacji tytułu."

#: admin-core/ajax/flows.php:427
msgid "Can't update the flow title"
msgstr "Nie można zaktualizować tytułu przepływu"

#: admin-core/ajax/flows.php:443
#. translators: %s flow id
msgid "Funnel title updated - %s"
msgstr "Tytuł lejka zaktualizowany - %s"

#: admin-core/ajax/flows.php:468
msgid "Invalid Funnel ID has been supplied to clone!"
msgstr "Podano nieprawidłowy identyfikator lejka do sklonowania!"

#: admin-core/ajax/flows.php:502
msgid "Invalid Funnel ID has been supplied to duplicate!"
msgstr "Podano nieprawidłowy identyfikator lejka do zduplikowania!"

#: admin-core/ajax/flows.php:679
msgid "Successfully cloned the Funnel!"
msgstr "Pomyślnie sklonowano lejek!"

#: admin-core/ajax/flows.php:708
msgid "Invalid Funnel ID has been supplied to restore!"
msgstr "Podano nieprawidłowy identyfikator lejka do przywrócenia!"

#: admin-core/ajax/flows.php:748
msgid "Successfully restored the Funnel!"
msgstr "Lejek został pomyślnie przywrócony!"

#: admin-core/ajax/flows.php:775
msgid "Invalid Funnel ID has been supplied to trash!"
msgstr "Podano nieprawidłowy identyfikator lejka do kosza!"

#: admin-core/ajax/flows.php:814
msgid "Successfully trashed the Funnel!"
msgstr "Lejek został pomyślnie usunięty do kosza!"

#: admin-core/ajax/flows.php:841
msgid "Invalid Funnel ID has been supplied to delete!"
msgstr "Podano nieprawidłowy identyfikator lejka do usunięcia!"

#: admin-core/ajax/flows.php:882
msgid "Successfully deleted the Funnel!"
msgstr "Pomyślnie usunięto lejek!"

#: admin-core/ajax/flows.php:909
msgid "Invalid Funnel IDs has been supplied to update status!"
msgstr "Podano nieprawidłowe identyfikatory lejków do aktualizacji statusu!"

#: admin-core/ajax/flows.php:958
#: admin-core/ajax/flows.php:1017
msgid "Successfully updated the Funnel status!"
msgstr "Pomyślnie zaktualizowano status lejka!"

#: admin-core/ajax/flows.php:1057
msgid "Invalid flow ID has been provided."
msgstr "Podano nieprawidłowy identyfikator przepływu."

#: admin-core/ajax/flows.php:1073
#. translators: %s flow id
msgid "Steps not sorted for flow - %s"
msgstr "Kroki nie są posortowane dla przepływu - %s"

#: admin-core/ajax/flows.php:1113
#. translators: %s flow id
msgid "Steps sorted for flow - %s"
msgstr "Kroki posortowane dla przepływu - %s"

#: admin-core/ajax/flows.php:1146
msgid "No Funnel ID is been supplied"
msgstr "Nie podano ID lejka"

#: admin-core/ajax/flows.php:1159
#. translators: %s flow id
msgid "Notice Dismissed"
msgstr "Powiadomienie odrzucone"

#: admin-core/ajax/importer.php:116
msgid "No Funnels to export"
msgstr "Brak lejków do eksportu"

#: admin-core/ajax/importer.php:205
msgid "Invalid flow ID."
msgstr "Nieprawidłowy identyfikator przepływu."

#: admin-core/ajax/importer.php:392
msgid "Invalid Funnel Id has been provided."
msgstr "Podano nieprawidłowy identyfikator lejka."

#: admin-core/ajax/importer.php:407
#. translators: %s: step ID
msgid "Invalid step id %1$s."
msgstr "Nieprawidłowy identyfikator kroku %1$s."

#: admin-core/ajax/importer.php:414
msgid "Successfully created the step!"
msgstr "Pomyślnie utworzono krok!"

#: admin-core/ajax/importer.php:516
msgid "Theme Activated"
msgstr "Motyw aktywowany"

#: admin-core/ajax/importer.php:575
#: admin-core/ajax/importer.php:590
#: modules/flow/classes/class-cartflows-step-post-type.php:262
#: wizard/ajax/wizard.php:717
msgid "Checkout"
msgstr "Kasa"

#: admin-core/ajax/importer.php:579
#: admin-core/ajax/importer.php:594
#: admin-core/ajax/importer.php:606
#: modules/flow/classes/class-cartflows-step-post-type.php:269
#: wizard/ajax/wizard.php:721
#: wizard/ajax/wizard.php:732
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Thank You"
msgstr "Dziękuję"

#: admin-core/ajax/importer.php:586
msgid "Sales Landing"
msgstr "Strona docelowa sprzedaży"

#: admin-core/ajax/importer.php:602
#: modules/flow/classes/class-cartflows-step-post-type.php:248
#: wizard/ajax/wizard.php:728
msgid "Landing"
msgstr "Lądowanie"

#: admin-core/ajax/importer.php:661
#: wizard/ajax/wizard.php:805
msgid "Successfully created the Funnel!"
msgstr "Pomyślnie utworzono lejek!"

#: admin-core/ajax/importer.php:719
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "Wymagany CartFlows Pro! %1$sUaktualnij do CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:721
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$supgrade to CartFlows Pro%2$s."
msgstr "Aby zaimportować przepływ premium, %1$szaktualizuj do CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:724
#: wizard/ajax/wizard.php:544
#. translators: %1$s: link html start, %2$s: link html end
msgid "Activate the CartFlows Pro to import the flow! %1$sActivate CartFlows Pro%2$s"
msgstr "Aktywuj CartFlows Pro, aby zaimportować przepływ! %1$sAktywuj CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:726
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate Cartflows Pro%2$s and validate the license key."
msgstr "Aby zaimportować przepływ premium, %1$saktywuj Cartflows Pro%2$s i zweryfikuj klucz licencyjny."

#: admin-core/ajax/importer.php:729
#. translators: %1$s: link html start, %2$s: link html end
msgid "Invalid License Key! %1$sActivate CartFlows Pro%2$s"
msgstr "Nieprawidłowy klucz licencyjny! %1$sAktywuj CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:731
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate CartFlows Pro%2$s."
msgstr "Aby zaimportować przepływ premium, %1$saktywuj CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:744
#: admin-core/ajax/importer.php:1056
msgid "Funnel data not found."
msgstr "Nie znaleziono danych lejka."

#: admin-core/ajax/importer.php:791
msgid "Steps not found."
msgstr "Nie znaleziono kroków."

#: admin-core/ajax/importer.php:824
#: wizard/ajax/wizard.php:642
msgid "Successfully imported the Flow!"
msgstr "Pomyślnie zaimportowano przepływ!"

#: admin-core/ajax/importer.php:873
msgid "Step data ID not found for import."
msgstr "Nie znaleziono identyfikatora danych kroku do importu."

#: admin-core/ajax/importer.php:885
msgid "Funnel ID not found in the request."
msgstr "Identyfikator lejka nie został znaleziony w żądaniu."

#: admin-core/inc/admin-helper.php:1127
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$1sTo resolve this issue, please check this %2$2sarticle%3$3s."
msgstr ""
"Błąd limitu czasu żądania. Proszę sprawdzić, czy zapora sieciowa lub jakakolwiek wtyczka zabezpieczająca nie blokuje "
"wychodzących żądań HTTP/HTTPS do templates.cartflows.com. %1$1sAby rozwiązać ten problem, proszę sprawdzić ten "
"%2$2sartykuł%3$3s."

#: admin-core/ajax/importer.php:915
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sUpgrade to CartFlows Pro.%2$s"
msgstr "%1$sUlepsz do CartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:916
msgid "To import the premium step, please upgrade to CartFlows Pro"
msgstr "Aby zaimportować krok premium, proszę uaktualnić do CartFlows Pro"

#: admin-core/ajax/importer.php:919
#: admin-core/ajax/importer.php:1041
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro%2$s"
msgstr "%1$sAktywuj CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:920
msgid "To import the premium step activate Cartflows Pro and validate the license key."
msgstr "Aby zaimportować krok premium, aktywuj Cartflows Pro i zweryfikuj klucz licencyjny."

#: admin-core/ajax/importer.php:923
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro License %2$s"
msgstr "%1$sAktywuj licencję CartFlows Pro %2$s"

#: admin-core/ajax/importer.php:924
msgid "To import the premium step activate the CartFlows Pro."
msgstr "Aby zaimportować krok premium, aktywuj CartFlows Pro."

#: admin-core/ajax/importer.php:959
#: admin-core/ajax/importer.php:1080
msgid "Step data not found."
msgstr "Nie znaleziono danych kroków."

#: admin-core/ajax/importer.php:967
#: admin-core/ajax/importer.php:1088
msgid "Successfully imported the Step!"
msgstr "Pomyślnie zaimportowano krok!"

#: admin-core/ajax/importer.php:1038
#. translators: %1$s: link html start, %2$s: link html end
msgid "Upgrade to %1$sCartFlows Pro.%2$s"
msgstr "Zaktualizuj do %1$sCartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:1044
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro license is not active. Activate %1$sCartFlows Pro License %2$s"
msgstr "Licencja CartFlows Pro nie jest aktywna. Aktywuj %1$sLicencję CartFlows Pro %2$s"

#: admin-core/ajax/importer.php:1112
#: admin-core/ajax/importer.php:1198
#. translators: %s: step ID
msgid "Invalid step id %1$s or post id %2$s."
msgstr "Nieprawidłowy identyfikator kroku %1$s lub identyfikator posta %2$s."

#: admin-core/ajax/importer.php:1175
#: admin-core/inc/admin-menu.php:1194
#: admin-core/inc/store-checkout.php:110
msgid "Nonce verification failed."
msgstr "Weryfikacja nonce nie powiodła się."

#: admin-core/ajax/importer.php:1454
#: wizard/ajax/wizard.php:388
msgid "Successful!"
msgstr "Sukces!"

#: admin-core/ajax/meta-data.php:143
#. Translators: %d stock amount
msgid "Stock: %d"
msgstr "Stan: %d"

#: admin-core/ajax/meta-data.php:271
msgid "On backorder"
msgstr "W realizacji zamówienia"

#: admin-core/ajax/meta-data.php:274
msgid "In stock"
msgstr "W magazynie"

#: admin-core/ajax/meta-data.php:277
msgid "Out of stock"
msgstr "Brak w magazynie"

#: admin-core/ajax/setup-page.php:84
msgid "Setup page dismissed successfully."
msgstr "Strona konfiguracji została pomyślnie zamknięta."

#: admin-core/ajax/steps.php:91
msgid "Can't update the step title"
msgstr "Nie można zaktualizować tytułu kroku"

#: admin-core/ajax/steps.php:112
#. translators: %s flow id
msgid "Step title updated - %s"
msgstr "Tytuł kroku zaktualizowany - %s"

#: admin-core/ajax/steps.php:148
#. translators: %s flow id
msgid "Can't clone this step - %1$s. Flow - %2$s"
msgstr "Nie można sklonować tego kroku - %1$s. Przepływ - %2$s"

#: admin-core/ajax/steps.php:267
#. translators: %s flow id
msgid "Step - %1$s cloned. Flow - %2$s"
msgstr "Krok - %1$s sklonowany. Przepływ - %2$s"

#: admin-core/ajax/steps.php:315
#. translators: %s flow id
msgid "Step not deleted for flow - %s"
msgstr "Krok nie został usunięty dla przepływu - %s"

#: admin-core/ajax/steps.php:358
#. translators: %s flow id
msgid "Step deleted for flow - %s"
msgstr "Krok usunięty dla przepływu - %s"

#: admin-core/ajax/steps.php:367
#. translators: %s flow id
msgid "This step can not be deleted."
msgstr "Nie można usunąć tego kroku."

#: admin-core/ajax/steps.php:400
#. translators: %s flow id
msgid "Invalid Step Id has been provided."
msgstr "Podano nieprawidłowy identyfikator kroku."

#: admin-core/ajax/steps.php:451
#. translators: %s flow id
msgid "Data saved successfully for step id %s"
msgstr "Dane zostały pomyślnie zapisane dla identyfikatora kroku %s"

#: admin-core/api/common-settings.php:129
#: admin-core/api/flow-data.php:139
#: admin-core/api/flows.php:287
#: admin-core/api/home-page.php:172
#: admin-core/api/product/product-data.php:121
#: admin-core/api/step-data.php:144
msgid "Sorry, you cannot list resources."
msgstr "Przepraszam, nie możesz wymieniać zasobów."

#: admin-core/api/flow-data.php:70
msgid "Flow ID."
msgstr "ID przepływu."

#: admin-core/api/flows.php:194
#: modules/flow/classes/class-cartflows-flow-post-type.php:229
#: admin-core/assets/build/settings-app.js:80
msgid "View"
msgstr "Widok"

#: admin-core/api/flows.php:202
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Edit"
msgstr "Edytuj"

#: admin-core/api/flows.php:210
#: admin-core/inc/admin-helper.php:763
#: admin-core/inc/admin-helper.php:845
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate"
msgstr "Duplikat"

#: admin-core/api/flows.php:217
#: admin-core/assets/build/settings-app.js:32
msgid "Export"
msgstr "Eksport"

#: admin-core/api/flows.php:224
#: admin-core/inc/admin-helper.php:780
#: admin-core/inc/admin-helper.php:854
#: admin-core/inc/admin-helper.php:922
#: admin-core/inc/admin-helper.php:942
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Delete"
msgstr "Usuń"

#: admin-core/api/product/product-data.php:68
#: admin-core/api/step-data.php:69
msgid "Step ID."
msgstr "ID kroku."

#: admin-core/inc/admin-helper.php:580
#: admin-core/inc/flow-meta.php:262
#: classes/class-cartflows-helper.php:1412
#: classes/class-cartflows-helper.php:1429
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:167
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:194
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:96
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1211
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:103
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:442
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:228
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:143
#: modules/optin/classes/class-cartflows-optin-meta-data.php:305
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:50
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Default"
msgstr "Domyślny"

#: admin-core/inc/admin-helper.php:597
msgid "System Fonts"
msgstr "Czcionki systemowe"

#: admin-core/inc/admin-helper.php:615
msgid "Google Fonts"
msgstr "Google Fonts"

#: admin-core/inc/admin-helper.php:772
msgid "A/B Test"
msgstr "Test A/B"

#: admin-core/inc/admin-helper.php:797
msgid "Automation"
msgstr "Automatyzacja"

#: admin-core/inc/admin-helper.php:800
msgid "(Connect)"
msgstr "(Połącz)"

#: admin-core/inc/admin-helper.php:862
msgid "Archive"
msgstr "Archiwum"

#: admin-core/inc/admin-helper.php:869
msgid "Declare as Winner"
msgstr "Ogłoś jako zwycięzcę"

#: admin-core/inc/admin-helper.php:913
msgid "Deleted variation can't be restored."
msgstr "Usuniętej wariacji nie można przywrócić."

#: admin-core/inc/admin-helper.php:914
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:188
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:200
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:212
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:224
msgid "Hide"
msgstr "Ukryj"

#: admin-core/inc/admin-helper.php:934
#: admin-core/assets/build/settings-app.js:32
msgid "Restore"
msgstr "Przywróć"

#: admin-core/inc/admin-helper.php:1115
msgid "Ooops! Something went wrong. Please open a support ticket from the website."
msgstr "Ups! Coś poszło nie tak. Proszę otworzyć zgłoszenie do pomocy technicznej na stronie internetowej."

#: admin-core/inc/admin-helper.php:1116
msgid "No error found."
msgstr "Nie znaleziono błędu."

#: admin-core/inc/admin-helper.php:1141
#. translators: %1$s: HTML, %2$s: HTML, %3$s: HTML
msgid ""
"Sorry for the inconvenience, but your website seems to be having trouble connecting to our server. %1$s Please open a "
"technical %2$ssupport ticket%3$s and share the server's outgoing IP address."
msgstr ""
"Przepraszamy za niedogodności, ale Twoja strona internetowa wydaje się mieć problemy z połączeniem z naszym serwerem. "
"%1$s Proszę otworzyć zgłoszenie do działu %2$swsparcia technicznego%3$s i podać wychodzący adres IP serwera."

#: admin-core/inc/admin-helper.php:1143
msgid "Server's outgoing IP address: "
msgstr "Adres IP wychodzący serwera:"

#: admin-core/inc/admin-menu.php:123
#: admin-core/inc/admin-menu.php:174
#: classes/class-cartflows-flow-frontend.php:70
#: admin-core/assets/build/settings-app.js:32
msgid "Edit Funnel"
msgstr "Edytuj lejek"

#: admin-core/inc/admin-menu.php:210
msgid "Go to Funnel Editing"
msgstr "Przejdź do edycji lejka"

#: admin-core/inc/admin-menu.php:258
#: admin-core/inc/admin-menu.php:259
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:25
msgid "Funnels"
msgstr "Lejki"

#: admin-core/inc/admin-menu.php:269
#: admin-core/inc/admin-menu.php:270
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: wizard/assets/build/wizard-app.js:1
msgid "Store Checkout"
msgstr "Kasa sklepowa"

#: admin-core/inc/admin-menu.php:278
#: admin-core/inc/admin-menu.php:280
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Automations"
msgstr "Automatyzacje"

#: admin-core/inc/admin-menu.php:280
#: admin-core/inc/admin-menu.php:290
msgid "New"
msgstr "Nowy"

#: admin-core/inc/admin-menu.php:298
#: admin-core/inc/admin-menu.php:299
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Add-ons"
msgstr "Dodatki"

#: admin-core/inc/admin-menu.php:308
#: admin-core/inc/admin-menu.php:309
msgid "Setup"
msgstr "Ustawienia"

#: admin-core/inc/admin-menu.php:328
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Dashboard"
msgstr "Pulpit"

#: admin-core/inc/admin-menu.php:541
msgid "Thin 100"
msgstr "Cien cienki"

#: admin-core/inc/admin-menu.php:542
msgid "Extra-Light 200"
msgstr "Extra-Light 200"

#: admin-core/inc/admin-menu.php:543
msgid "Light 300"
msgstr "Światło 300"

#: admin-core/inc/admin-menu.php:544
msgid "Normal 400"
msgstr "Normalny 400"

#: admin-core/inc/admin-menu.php:545
msgid "Medium 500"
msgstr "Średni 500"

#: admin-core/inc/admin-menu.php:546
msgid "Semi-Bold 600"
msgstr "Półgruby 600"

#: admin-core/inc/admin-menu.php:547
msgid "Bold 700"
msgstr "Pogrubienie 700"

#: admin-core/inc/admin-menu.php:548
msgid "Extra-Bold 800"
msgstr "Extra-Bold 800"

#: admin-core/inc/admin-menu.php:549
msgid "Ultra-Bold 900"
msgstr "Ultra-Bold 900"

#: admin-core/inc/admin-menu.php:634
#. Translators: %1$s is the required page builder title, %2$s is the opening anchor tag to plugins.php, %3$s is the closing anchor tag, %4$s is the plugin title.
msgid "The default page builder is set to %1$s. Please %2$sinstall & activate%3$s the %4$s to start editing the steps."
msgstr ""
"Domyślny kreator stron jest ustawiony na %1$s. Proszę %2$szainstalować i aktywować%3$s %4$s, aby rozpocząć edycję "
"kroków."

#: admin-core/inc/admin-menu.php:1002
msgid "Stripe Payments For WooCommerce"
msgstr "Płatności Stripe dla WooCommerce"

#: admin-core/inc/admin-menu.php:1003
msgid "Accept credit card payments in your store with Stripe for WooCommerce."
msgstr "Akceptuj płatności kartą kredytową w swoim sklepie za pomocą Stripe dla WooCommerce."

#: admin-core/inc/admin-menu.php:1014
msgid "PayPal Payments For WooCommerce"
msgstr "Płatności PayPal dla WooCommerce"

#: admin-core/inc/admin-menu.php:1015
msgid "Accept payments in your store with PayPal for WooCommerce."
msgstr "Akceptuj płatności w swoim sklepie za pomocą PayPal dla WooCommerce."

#: admin-core/inc/admin-menu.php:1026
msgid "WooCommerce"
msgstr "WooCommerce"

#: admin-core/inc/admin-menu.php:1027
msgid "WooCommerce is a customizable, open-source ecommerce platform built on WordPress."
msgstr "WooCommerce to konfigurowalna, otwartoźródłowa platforma e-commerce zbudowana na WordPressie."

#: admin-core/inc/admin-menu.php:1051
msgid "SureMembers"
msgstr "SureMembers"

#: admin-core/inc/admin-menu.php:1065
msgid "Transform your WordPress form-building experience with stunning designs, ai integration, and no-code flexibility."
msgstr ""
"Przekształć swoje doświadczenie w tworzeniu formularzy WordPress dzięki oszałamiającym projektom, integracji z AI i "
"elastyczności bez kodu."

#: admin-core/inc/admin-menu.php:1064
msgid "SureForms"
msgstr "SureForms"

#: admin-core/inc/admin-menu.php:1076
#: wizard/assets/build/wizard-app.js:1
msgid "Spectra"
msgstr "Widma"

#: admin-core/inc/admin-menu.php:1077
msgid "Power-up the Gutenberg editor with advanced and powerful blocks."
msgstr "Wzmocnij edytor Gutenberg zaawansowanymi i potężnymi blokami."

#: admin-core/inc/admin-menu.php:1108
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Cart Abandonment"
msgstr "Porzucenie koszyka"

#: admin-core/inc/admin-menu.php:1109
msgid "Recover abandonded carts with ease in less than 10 minutes."
msgstr "Odzyskaj porzucone koszyki z łatwością w mniej niż 10 minut."

#: admin-core/inc/admin-menu.php:1120
msgid "Variation Swatches for WooCommerce"
msgstr "Próbki wariantów dla WooCommerce"

#: admin-core/inc/admin-menu.php:1121
msgid "Convert dropdown boxes into highly engaging variation swatches."
msgstr "Przekształć pola rozwijane w wysoce angażujące próbki wariantów."

#: admin-core/inc/admin-menu.php:1137
msgid "Astra"
msgstr "Astra"

#: admin-core/inc/admin-menu.php:1138
msgid ""
"Astra is fast, fully customizable & beautiful WordPress theme suitable for blog, personal portfolio, business website "
"and WooCommerce storefront."
msgstr ""
"Astra to szybki, w pełni konfigurowalny i piękny motyw WordPress, odpowiedni dla bloga, osobistego portfolio, strony "
"biznesowej i sklepu WooCommerce."

#: admin-core/inc/admin-menu.php:1148
msgid "Spectra One"
msgstr "Spectra One"

#: admin-core/inc/admin-menu.php:1149
msgid ""
"Spectra One is a beautiful and modern WordPress theme built with the Full Site Editing (FSE) feature. It's a versatile "
"theme that can be used for blogs, portfolios, businesses, and more."
msgstr ""
"Spectra One to piękny i nowoczesny motyw WordPress zbudowany z funkcją pełnej edycji witryny (FSE). To wszechstronny "
"motyw, który można wykorzystać do blogów, portfolio, firm i nie tylko."

#: admin-core/inc/flow-meta.php:54
msgid "Instant Layout "
msgstr "Układ natychmiastowy"

#: admin-core/inc/flow-meta.php:73
msgid "Logo"
msgstr "Logo"

#: admin-core/inc/flow-meta.php:108
msgid "Width (In px)"
msgstr "Szerokość (w pikselach)"

#: admin-core/inc/flow-meta.php:125
msgid "Height (In px)"
msgstr "Wysokość (w px)"

#: admin-core/inc/flow-meta.php:159
msgid "Global Styling"
msgstr "Stylizacja globalna"

#: admin-core/inc/flow-meta.php:164
msgid "Enable Global Styling"
msgstr "Włącz globalne stylizowanie"

#: admin-core/inc/flow-meta.php:172
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:211
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:119
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:119
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:291
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:297
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:168
#: modules/optin/classes/class-cartflows-optin-meta-data.php:277
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:125
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Primary Color"
msgstr "Kolor podstawowy"

#: admin-core/inc/flow-meta.php:187
msgid "Secondary Color"
msgstr "Kolor dodatkowy"

#: admin-core/inc/flow-meta.php:202
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:219
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:247
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:441
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:674
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:861
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:397
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:222
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:250
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:325
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:390
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:456
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:522
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1052
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:172
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:251
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:303
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:329
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:592
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:690
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:763
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:361
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:350
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:410
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:334
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:383
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:413
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:478
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:541
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:572
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:633
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:663
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:722
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:752
#: modules/optin/classes/class-cartflows-optin-meta-data.php:500
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Color"
msgstr "Kolor tekstu"

#: admin-core/inc/flow-meta.php:217
msgid "Heading/Accent Color"
msgstr "Kolor nagłówka/akcentu"

#: admin-core/inc/flow-meta.php:233
msgid "General "
msgstr "Ogólny"

#: admin-core/inc/flow-meta.php:239
#: admin-core/inc/global-settings.php:149
msgid "Funnel Slug"
msgstr "Ślimak lejka"

#: admin-core/inc/flow-meta.php:245
msgid "Enable Test Mode"
msgstr "Włącz tryb testowy"

#: admin-core/inc/flow-meta.php:266
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:139
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:148
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:157
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:166
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "Yes"
msgstr "Tak"

#: admin-core/inc/flow-meta.php:270
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:140
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:149
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:158
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:167
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "No"
msgstr "Nie"

#: admin-core/inc/flow-meta.php:277
msgid "Funnel Custom Script"
msgstr "Skrypt niestandardowy lejka"

#: admin-core/inc/global-settings.php:45
msgid "No Access"
msgstr "Brak dostępu"

#: admin-core/inc/global-settings.php:49
msgid "Full Access"
msgstr "Pełny dostęp"

#: admin-core/inc/global-settings.php:54
msgid "Limited Access"
msgstr "Dostęp ograniczony"

#: admin-core/inc/global-settings.php:71
msgid "Show Ready Templates for"
msgstr "Pokaż gotowe szablony dla"

#: admin-core/inc/global-settings.php:73
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"Please choose your preferred page builder from the list so you will only see templates that are made using that page "
"builder. %1$sLearn More >>%2$s"
msgstr ""
"Proszę wybrać preferowany kreator stron z listy, aby zobaczyć tylko szablony stworzone za pomocą tego kreatora stron. "
"%1$sDowiedz się więcej >>%2$s"

#: admin-core/inc/global-settings.php:77
msgid "Block Editor"
msgstr "Edytor bloków"

#: admin-core/inc/global-settings.php:82
msgid "Elementor"
msgstr "Elementor"

#: admin-core/inc/global-settings.php:87
msgid "Bricks"
msgstr "Cegły"

#: admin-core/inc/global-settings.php:92
msgid "Beaver"
msgstr "Bóbr"

#: admin-core/inc/global-settings.php:97
msgid "Other"
msgstr "Inne"

#: admin-core/inc/global-settings.php:110
msgid "Override Store Checkout"
msgstr "Zastąp finalizację zakupu w sklepie"

#: admin-core/inc/global-settings.php:112
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the Store Checkout settings please %1$sClick here%2$s."
msgstr "Aby uzyskać więcej informacji na temat ustawień kasy sklepu, proszę %1$sKliknij tutaj%2$s."

#: admin-core/inc/global-settings.php:120
msgid "Disallow search engine from indexing funnels."
msgstr "Nie zezwalaj wyszukiwarkom na indeksowanie lejków."

#: admin-core/inc/global-settings.php:122
msgid "Prevent search engines from including funnels in their search results."
msgstr "Zapobiegaj uwzględnianiu lejków przez wyszukiwarki w wynikach wyszukiwania."

#: admin-core/inc/global-settings.php:139
msgid "Default Permalinks"
msgstr "Domyślne linki bezpośrednie"

#: admin-core/inc/global-settings.php:140
msgid "Default WordPress Permalink"
msgstr "Domyślny odnośnik WordPress"

#: admin-core/inc/global-settings.php:144
msgid "Funnel and Step Slug"
msgstr "Lejek i Krok Slug"

#: admin-core/inc/global-settings.php:154
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1019
#: modules/landing/classes/class-cartflows-landing-meta-data.php:113
#: modules/optin/classes/class-cartflows-optin-meta-data.php:568
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:396
msgid "Step Slug"
msgstr "Slug kroku"

#: admin-core/inc/global-settings.php:164
msgid "Post Type Permalink Base"
msgstr "Podstawa linku bezpośredniego typu wpisu"

#: admin-core/inc/global-settings.php:168
msgid "Step Base"
msgstr "Podstawa kroku"

#: admin-core/inc/global-settings.php:174
msgid "Funnel Base"
msgstr "Podstawa lejka"

#: admin-core/inc/global-settings.php:185
#: admin-core/inc/global-settings.php:376
#: admin-core/inc/global-settings.php:582
#: admin-core/inc/global-settings.php:956
msgid "Enable For CartFlows Pages"
msgstr "Włącz dla stron CartFlows"

#: admin-core/inc/global-settings.php:203
#: admin-core/inc/global-settings.php:394
#: admin-core/inc/global-settings.php:600
#: admin-core/inc/global-settings.php:788
#: admin-core/inc/global-settings.php:974
#: admin-core/inc/global-settings.php:1180
msgid "Enable for the whole site"
msgstr "Włącz dla całej witryny"

#: admin-core/inc/global-settings.php:205
msgid "If checked, page view and view content event will also be triggered for other pages/posts of site."
msgstr ""
"Jeśli zaznaczone, zdarzenie wyświetlenia strony i wyświetlenia treści zostanie również uruchomione dla innych "
"stron/postów witryny."

#: admin-core/inc/global-settings.php:231
msgid "Enter Facebook pixel ID"
msgstr "Wprowadź identyfikator piksela Facebooka"

#: admin-core/inc/global-settings.php:258
msgid "Facebook Pixel Events"
msgstr "Zdarzenia piksela Facebooka"

#: admin-core/inc/global-settings.php:272
#: admin-core/inc/global-settings.php:701
#: admin-core/inc/global-settings.php:888
#: admin-core/inc/global-settings.php:1093
msgid "View Content"
msgstr "Wyświetl zawartość"

#: admin-core/inc/global-settings.php:288
msgid "Initiate Checkout"
msgstr "Rozpocznij realizację zamówienia"

#: admin-core/inc/global-settings.php:304
#: admin-core/inc/global-settings.php:495
#: admin-core/inc/global-settings.php:716
#: admin-core/inc/global-settings.php:1108
#: admin-core/inc/global-settings.php:1309
msgid "Add Payment Info"
msgstr "Dodaj informacje o płatności"

#: admin-core/inc/global-settings.php:321
msgid "Purchase Complete"
msgstr "Zakup zakończony"

#: admin-core/inc/global-settings.php:337
#: admin-core/inc/global-settings.php:527
#: admin-core/inc/global-settings.php:748
#: admin-core/inc/global-settings.php:934
#: admin-core/inc/global-settings.php:1140
#: admin-core/inc/global-settings.php:1357
msgid "Optin Lead"
msgstr "Potencjalny klient opt-in"

#: admin-core/inc/global-settings.php:358
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Facebook Pixel not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Facebook Pixel nie działa poprawnie? %1$1s Kliknij tutaj %2$2s, aby dowiedzieć się więcej."

#: admin-core/inc/global-settings.php:396
msgid "If checked, page view event will also be triggered for other pages/posts of site."
msgstr "Jeśli zaznaczone, zdarzenie wyświetlenia strony zostanie również uruchomione dla innych stron/postów witryny."

#: admin-core/inc/global-settings.php:422
msgid "Enter Google Analytics ID"
msgstr "Wprowadź identyfikator Google Analytics"

#: admin-core/inc/global-settings.php:426
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s google analytics account %2$2s to find your ID. e.g. G-XXXXX or UA-XXXXX-X"
msgstr "Zaloguj się na swoje %1$1s konto Google Analytics %2$2s, aby znaleźć swój identyfikator. np. G-XXXXX lub UA-XXXXX-X"

#: admin-core/inc/global-settings.php:451
msgid "Google Analytics Events"
msgstr "Zdarzenia Google Analytics"

#: admin-core/inc/global-settings.php:464
#: admin-core/inc/global-settings.php:670
#: admin-core/inc/global-settings.php:858
#: admin-core/inc/global-settings.php:1062
#: admin-core/inc/global-settings.php:1279
msgid "Begin Checkout"
msgstr "Rozpocznij realizację zamówienia"

#: admin-core/inc/global-settings.php:480
#: admin-core/inc/global-settings.php:686
#: admin-core/inc/global-settings.php:873
#: admin-core/inc/global-settings.php:1078
#: admin-core/inc/global-settings.php:1294
msgid "Add To Cart"
msgstr "Dodaj do koszyka"

#: admin-core/inc/global-settings.php:511
#: admin-core/inc/global-settings.php:732
#: admin-core/inc/global-settings.php:903
#: admin-core/inc/global-settings.php:1124
#: admin-core/inc/global-settings.php:1325
msgid "Purchase"
msgstr "Zakup"

#: admin-core/inc/global-settings.php:548
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Google Analytics not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Google Analytics nie działa poprawnie? %1$1s Kliknij tutaj %2$2s, aby dowiedzieć się więcej."

#: admin-core/inc/global-settings.php:566
msgid "Enter Google Map API key"
msgstr "Wprowadź klucz API Google Map"

#: admin-core/inc/global-settings.php:573
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Check this %1$1s article %2$2s to setup and find an API key."
msgstr "Sprawdź ten %1$1s artykuł %2$2s, aby skonfigurować i znaleźć klucz API."

#: admin-core/inc/global-settings.php:602
#: admin-core/inc/global-settings.php:790
#: admin-core/inc/global-settings.php:976
msgid "If checked, PageView event will also be triggered for other pages/posts of site."
msgstr "Jeśli zaznaczone, zdarzenie PageView zostanie również wywołane dla innych stron/postów witryny."

#: admin-core/inc/global-settings.php:628
msgid "Enter TikTok ID"
msgstr "Wprowadź identyfikator TikTok"

#: admin-core/inc/global-settings.php:632
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s TikTok business account %2$2s to find your ID."
msgstr "Zaloguj się do swojego %1$1s konta biznesowego TikTok %2$2s, aby znaleźć swój identyfikator."

#: admin-core/inc/global-settings.php:657
msgid "TikTok Events"
msgstr "Wydarzenia TikTok"

#: admin-core/inc/global-settings.php:760
#: admin-core/inc/global-settings.php:946
#: admin-core/inc/global-settings.php:1152
#: admin-core/inc/global-settings.php:1369
msgid "Optin Lead event will be triggered for optin page."
msgstr "Zdarzenie Optin Lead zostanie wywołane dla strony optin."

#: admin-core/inc/global-settings.php:770
#: admin-core/inc/global-settings.php:1162
msgid "Enable for CartFlows pages"
msgstr "Włącz dla stron CartFlows"

#: admin-core/inc/global-settings.php:816
msgid "Enter Snapchat pixel ID"
msgstr "Wprowadź identyfikator piksela Snapchat"

#: admin-core/inc/global-settings.php:820
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Snapchat business account %2$2s to find your ID."
msgstr "Zaloguj się do swojego %1$1s konta firmowego Snapchat %2$2s, aby znaleźć swój identyfikator."

#: admin-core/inc/global-settings.php:845
msgid "Snapchat Events"
msgstr "Wydarzenia na Snapchacie"

#: admin-core/inc/global-settings.php:918
#: wizard/assets/build/wizard-app.js:1
msgid "Subscribe"
msgstr "Subskrybuj"

#: admin-core/inc/global-settings.php:1002
msgid "Enter Google Ads Conversion ID"
msgstr "Wprowadź identyfikator konwersji Google Ads"

#: admin-core/inc/global-settings.php:1006
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion ID."
msgstr "Zaloguj się do swojego %1$1s konta Google Ads %2$2s, aby znaleźć swój identyfikator konwersji."

#: admin-core/inc/global-settings.php:1019
msgid "Enter Google Ads Conversion Label"
msgstr "Wprowadź etykietę konwersji Google Ads"

#: admin-core/inc/global-settings.php:1023
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion label."
msgstr "Zaloguj się do swojego %1$1s konta Google Ads %2$2s, aby znaleźć swoją etykietę konwersji."

#: admin-core/inc/global-settings.php:1049
msgid "Google Ads Events"
msgstr "Wydarzenia Google Ads"

#: admin-core/inc/global-settings.php:1182
msgid "If checked, PageVisit event will also be triggered for other pages/posts of site."
msgstr "Jeśli zaznaczone, zdarzenie PageVisit zostanie również wywołane dla innych stron/postów witryny."

#: admin-core/inc/global-settings.php:1208
msgid "Enter Pinterest Tag ID"
msgstr "Wprowadź identyfikator tagu Pinterest"

#: admin-core/inc/global-settings.php:1212
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Pinterest business account %2$2s to find your ID."
msgstr "Zaloguj się na swoje %1$1s konto firmowe na Pintereście %2$2s, aby znaleźć swój identyfikator."

#: admin-core/inc/global-settings.php:1237
msgid "Enable Pinterest tag tracking consent notice"
msgstr "Włącz powiadomienie o zgodzie na śledzenie tagów Pinterest"

#: admin-core/inc/global-settings.php:1250
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"This setting enables a consent notice for Pinterest Tag tracking on your website. For more information check "
"%1$1sPinterest documentation%2$2s."
msgstr ""
"To ustawienie umożliwia wyświetlanie powiadomienia o zgodzie na śledzenie za pomocą Pinterest Tag na Twojej stronie "
"internetowej. Aby uzyskać więcej informacji, sprawdź %1$1sdokumentację Pinterest%2$2s."

#: admin-core/inc/global-settings.php:1266
msgid "Pinterest Events"
msgstr "Wydarzenia Pinterest"

#: admin-core/inc/global-settings.php:1340
msgid "Signup"
msgstr "Zarejestruj się"

#: admin-core/inc/global-settings.php:1352
msgid "Signup event will be triggered for optin page."
msgstr "Zdarzenie rejestracji zostanie uruchomione dla strony optin."

#: admin-core/inc/global-settings.php:1382
msgid "Store Revenue Report Emails"
msgstr "E-maile z raportem przychodów sklepu"

#: admin-core/inc/global-settings.php:1387
msgid "Enable Store Report Email."
msgstr "Włącz e-mail z raportem sklepu."

#: admin-core/inc/global-settings.php:1390
#. translators: %1$1s: link html start, %2$12: link html end
msgid "If enabled, you will receive the weekly report emails of your store for the revenue stats generated by CartFlows."
msgstr ""
"Jeśli włączone, będziesz otrzymywać cotygodniowe raporty e-mailowe swojego sklepu dotyczące statystyk przychodów "
"generowanych przez CartFlows."

#: admin-core/inc/global-settings.php:1397
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:577
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:579
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:174
msgid "Email Address"
msgstr "Adres e-mail"

#: admin-core/inc/global-settings.php:1398
msgid "Email address to receive the weekly sales report emails. For multiple emails, add each email address per line."
msgstr ""
"Adres e-mail do otrzymywania cotygodniowych raportów sprzedaży. W przypadku wielu adresów e-mail, dodaj każdy adres "
"e-mail w osobnej linii."

#: admin-core/inc/global-settings.php:1425
msgid "Delete plugin data on plugin deletion"
msgstr "Usuń dane wtyczki przy usuwaniu wtyczki"

#: admin-core/inc/global-settings.php:1430
msgid "Are you sure? Do you want to delete plugin data while deleting the plugin? Type \"DELETE\" to confirm!"
msgstr "Czy jesteś pewien? Czy chcesz usunąć dane wtyczki podczas usuwania wtyczki? Wpisz \"DELETE\", aby potwierdzić!"

#: admin-core/inc/global-settings.php:1433
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"This option will delete all the CartFlows options data on plugin deletion. If you enable this and deletes the plugin, "
"you can't restore your saved data. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Ta opcja usunie wszystkie dane opcji CartFlows podczas usuwania wtyczki. Jeśli to włączysz i usuniesz wtyczkę, nie "
"będziesz mógł przywrócić zapisanych danych. Aby dowiedzieć się więcej, %1$1s Kliknij tutaj %2$2s."

#: admin-core/inc/log-status.php:108
msgid "Log deleted successfully!"
msgstr "Dziennik został pomyślnie usunięty!"

#: admin-core/inc/log-status.php:171
#: admin-core/inc/log-status.php:195
msgid "Nonce verification failed. Please refresh the page and retry."
msgstr "Weryfikacja nonce nie powiodła się. Odśwież stronę i spróbuj ponownie."

#: admin-core/inc/store-checkout.php:63
msgid "Checkout (Store)"
msgstr "Kasa (Sklep)"

#: admin-core/inc/store-checkout.php:67
msgid "Thank You (Store)"
msgstr "Dziękujemy (Sklep)"

#: admin-core/views/404-error.php:36
msgid "404 ERROR"
msgstr "Błąd 404"

#: admin-core/views/404-error.php:37
msgid "Page Not Found."
msgstr "Strona nie znaleziona."

#: admin-core/views/404-error.php:38
msgid "Sorry, we couldn’t find the page you’re looking for."
msgstr "Przepraszamy, nie mogliśmy znaleźć strony, której szukasz."

#: admin-core/views/404-error.php:39
msgid "Go back home"
msgstr "Wróć do domu"

#: admin-core/views/header.php:22
msgid "Generate More Leads & More Sales"
msgstr "Generuj więcej leadów i więcej sprzedaży"

#: classes/class-cartflows-admin-notices.php:88
#. translators: %1$s Software Title, %2$s Plugin, %3$s Anchor opening tag, %4$s Anchor closing tag, %5$s Software Title.
msgid ""
"%1$sCartFlows:%2$s We just introduced an awesome new feature, weekly store revenue reports via email. Now you can see "
"how many revenue we are generating for your store each week, without having to log into your website. You can set the "
"email address for these email from %3$shere.%4$s"
msgstr ""
"%1$sCartFlows:%2$s Właśnie wprowadziliśmy świetną nową funkcję, cotygodniowe raporty przychodów sklepu wysyłane "
"e-mailem. Teraz możesz zobaczyć, ile przychodów generujemy dla Twojego sklepu każdego tygodnia, bez konieczności "
"logowania się na Twoją stronę internetową. Możesz ustawić adres e-mail dla tych wiadomości %3$stutaj.%4$s"

#: classes/class-cartflows-admin-notices.php:218
msgid "How likely are you to recommend #pluginname to your friends or colleagues?"
msgstr "Jak bardzo prawdopodobne jest, że polecisz #pluginname swoim znajomym lub kolegom?"

#: classes/class-cartflows-admin-notices.php:221
msgid ""
"Could you please do us a favor and give us a 5-star rating on WordPress? It would help others choose CartFlows with "
"confidence. Thank you!"
msgstr ""
"Czy mógłbyś zrobić nam przysługę i dać nam 5-gwiazdkową ocenę na WordPressie? To pomoże innym wybrać CartFlows z "
"pewnością. Dziękujemy!"

#: classes/class-cartflows-admin-notices.php:225
msgid "Thank you for your feedback"
msgstr "Dziękuję za Twoją opinię"

#: classes/class-cartflows-admin-notices.php:226
msgid "We value your input. How can we improve your experience?"
msgstr "Cenimy sobie Twoją opinię. Jak możemy poprawić Twoje doświadczenie?"

#: classes/class-cartflows-admin-notices.php:249
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Heads up! The Gutenberg plugin is not recommended on production sites as it may contain non-final features that cause "
"compatibility issues with CartFlows and other plugins. %1$s Please deactivate the Gutenberg plugin %2$s to ensure the "
"proper functioning of your website."
msgstr ""
"Uwaga! Wtyczka Gutenberg nie jest zalecana na stronach produkcyjnych, ponieważ może zawierać nieostateczne funkcje, "
"które powodują problemy z kompatybilnością z CartFlows i innymi wtyczkami. %1$s Proszę dezaktywować wtyczkę Gutenberg "
"%2$s, aby zapewnić prawidłowe działanie Twojej strony internetowej."

#: classes/class-cartflows-admin.php:122
#: wizard/views/wizard-base.php:19
msgid "CartFlows Setup"
msgstr "Konfiguracja CartFlows"

#: classes/class-cartflows-admin.php:167
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "Step"
msgstr "Krok"

#: classes/class-cartflows-admin.php:167
msgid "of"
msgstr "z"

#: classes/class-cartflows-admin.php:173
msgid "You're almost there! Once you complete CartFlows setup you can start receiving orders from flows."
msgstr ""
"Jesteś prawie na miejscu! Gdy zakończysz konfigurację CartFlows, będziesz mógł zacząć przyjmować zamówienia z "
"przepływów."

#: classes/class-cartflows-admin.php:175
#: admin-core/assets/build/settings-app.js:34
msgid "Complete Setup"
msgstr "Zakończ konfigurację"

#: classes/class-cartflows-admin.php:233
msgid "Docs"
msgstr "Dokumenty"

#: classes/class-cartflows-admin.php:246
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:162
#: modules/landing/classes/class-cartflows-landing-meta-data.php:57
#: modules/optin/classes/class-cartflows-optin-meta-data.php:188
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:58
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Settings"
msgstr "Ustawienia"

#: classes/class-cartflows-admin.php:362
msgid "You do not have permission to access this page."
msgstr "Nie masz uprawnień do dostępu do tej strony."

#: classes/class-cartflows-admin.php:363
#: classes/class-cartflows-admin.php:394
#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:11
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback to Previous Version"
msgstr "Przywróć poprzednią wersję"

#: classes/class-cartflows-admin.php:376
msgid "Error occurred, The version selected is invalid. Try selecting different version."
msgstr "Wystąpił błąd, wybrana wersja jest nieprawidłowa. Spróbuj wybrać inną wersję."

#: classes/class-cartflows-default-meta.php:163
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1880
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1889
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1223
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:501
#: modules/checkout/templates/checkout/shipping-methods.php:69
msgid ""
"There are no shipping options available. Please ensure that your address has been entered correctly, or contact us if "
"you need any help."
msgstr ""
"Nie ma dostępnych opcji wysyłki. Upewnij się, że Twój adres został wprowadzony poprawnie, lub skontaktuj się z nami, "
"jeśli potrzebujesz pomocy."

#: classes/class-cartflows-default-meta.php:176
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1345
msgid "Place Order"
msgstr "Złóż zamówienie"

#: classes/class-cartflows-default-meta.php:367
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1320
msgid "is required"
msgstr "jest wymagane"

#: classes/class-cartflows-default-meta.php:629
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:126
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:143
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:174
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:484
#: modules/thankyou/templates/instant-thankyou.php:76
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank you. Your order has been received."
msgstr "Dziękuję. Twoje zamówienie zostało przyjęte."

#: classes/class-cartflows-default-meta.php:820
#: modules/optin/classes/class-cartflows-optin-meta-data.php:593
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit"
msgstr "Prześlij"

#: classes/class-cartflows-flow-frontend.php:284
msgid "Edit Design"
msgstr "Edytuj projekt"

#: classes/class-cartflows-functions.php:595
#. translators: %1$s page builder name "string"
msgid ""
"We have introduced %1$1s widgets for CartFlows shortcodes. Now, you can add/change/update design settings directly from "
"the page builder as well."
msgstr ""
"Wprowadziliśmy widżety %1$1s dla shortcode'ów CartFlows. Teraz możesz również dodawać/zmieniać/aktualizować ustawienia "
"projektowe bezpośrednio z poziomu kreatora stron."

#: classes/class-cartflows-functions.php:596
msgid "Learn More »"
msgstr "Dowiedz się więcej »"

#: classes/class-cartflows-helper.php:568
msgid "First name"
msgstr "Imię"

#: classes/class-cartflows-helper.php:577
msgid "Last name"
msgstr "Nazwisko"

#: classes/class-cartflows-helper.php:586
#: wizard/assets/build/wizard-app.js:3
msgid "Email address"
msgstr "Adres e-mail"

#: classes/class-cartflows-helper.php:1370
msgid "Enable Field"
msgstr "Włącz pole"

#: classes/class-cartflows-helper.php:1376
msgid "Field Width"
msgstr "Szerokość pola"

#: classes/class-cartflows-helper.php:1382
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "33%"
msgstr "33%"

#: classes/class-cartflows-helper.php:1386
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "50%"
msgstr "50%"

#: classes/class-cartflows-helper.php:1390
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "100%"
msgstr "100%"

#: classes/class-cartflows-helper.php:1397
msgid "Field Label"
msgstr "Etykieta pola"

#: classes/class-cartflows-helper.php:1402
#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
msgid "Field ID"
msgstr "ID pola"

#: classes/class-cartflows-helper.php:1406
msgid "Copy this field id to use in Order Custom Field rule of dynamic offers."
msgstr "Skopiuj ten identyfikator pola, aby użyć go w regule niestandardowego pola zamówienia dla ofert dynamicznych."

#: classes/class-cartflows-helper.php:1418
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Checked"
msgstr "Sprawdzone"

#: classes/class-cartflows-helper.php:1422
msgid "Un-Checked"
msgstr "Odznaczone"

#: classes/class-cartflows-helper.php:1439
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:411
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Options"
msgstr "Opcje"

#: classes/class-cartflows-helper.php:1463
msgid "Min Date"
msgstr "Min data"

#: classes/class-cartflows-helper.php:1470
msgid "Max Date"
msgstr "Maksymalna data"

#: classes/class-cartflows-helper.php:1482
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Placeholder"
msgstr "Symbol zastępczy"

#: classes/class-cartflows-helper.php:1491
msgid "Min Number"
msgstr "Min liczba"

#: classes/class-cartflows-helper.php:1497
msgid "Max Number"
msgstr "Maksymalna liczba"

#: classes/class-cartflows-helper.php:1506
msgid "Show In Email"
msgstr "Pokaż w e-mailu"

#: classes/class-cartflows-helper.php:1513
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Required"
msgstr "Wymagane"

#: classes/class-cartflows-helper.php:1521
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Collapsible"
msgstr "Składany"

#: classes/class-cartflows-helper.php:1571
msgid "CartFlows Primary Color"
msgstr "Podstawowy kolor CartFlows"

#: classes/class-cartflows-helper.php:1572
msgid "CartFlows Secondary Color"
msgstr "Kolor dodatkowy CartFlows"

#: classes/class-cartflows-helper.php:1573
msgid "CartFlows Text Color"
msgstr "Kolor tekstu CartFlows"

#: classes/class-cartflows-helper.php:1574
msgid "CartFlows Heading/Accent Color"
msgstr "Kolor nagłówka/akcentu CartFlows"

#: classes/class-cartflows-loader.php:292
#. translators: %s: html tags
msgid ""
"The new version of  %1$s%3$s%2$s is released. Please download the latest zip to install the new updates. Click here to "
"%4$sdownload%5$s."
msgstr ""
"Nowa wersja %1$s%3$s%2$s została wydana. Proszę pobrać najnowszy plik zip, aby zainstalować nowe aktualizacje. Kliknij "
"tutaj, aby %4$spobrać%5$s."

#: classes/class-cartflows-loader.php:309
#. translators: %s: html tags
msgid "You are using an older version of %1$s%3$s%2$s. Please update %1$s%3$s%2$s plugin to version %1$s%4$s%2$s or higher."
msgstr "Używasz starszej wersji %1$s%3$s%2$s. Proszę zaktualizować wtyczkę %1$s%3$s%2$s do wersji %1$s%4$s%2$s lub nowszej."

#: classes/class-cartflows-loader.php:612
#. translators: %s: html tags
msgid "This %1$sCartFlows%2$s page requires %1$sWooCommerce%2$s plugin installed & activated."
msgstr "Ta strona %1$sCartFlows%2$s wymaga zainstalowanego i aktywowanego wtyczki %1$sWooCommerce%2$s."

#: classes/class-cartflows-loader.php:622
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
msgid "Activate WooCommerce"
msgstr "Aktywuj WooCommerce"

#: classes/class-cartflows-loader.php:629
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Install WooCommerce"
msgstr "Zainstaluj WooCommerce"

#: classes/class-cartflows-rollback.php:167
msgid "CartFlows <p>Rollback to Previous Version</p>"
msgstr "CartFlows <p>Cofnij do poprzedniej wersji</p>"

#: classes/class-cartflows-tracking.php:1396
msgid "We use Pinterest tags to improve your experience. Do you consent to our use of Pinterest tags?"
msgstr "Używamy tagów Pinterest, aby poprawić Twoje doświadczenia. Czy wyrażasz zgodę na użycie przez nas tagów Pinterest?"

#: classes/class-cartflows-tracking.php:1397
msgid "Accept"
msgstr "Akceptuj"

#: classes/class-cartflows-tracking.php:1398
msgid "Decline"
msgstr "Odrzuć"

#: classes/class-cartflows-tracking.php:1403
msgid "Pinterest Consent"
msgstr "Zgoda na Pinterest"

#: classes/importer/batch-process/class-cartflows-batch-process.php:482
msgid ""
"ERROR! Cron schedules are disabled by setting constant DISABLE_WP_CRON to true.<br/>To start the import process please "
"enable the cron by setting the constant to false. E.g. define( 'DISABLE_WP_CRON', false );"
msgstr ""
"BŁĄD! Harmonogramy Cron są wyłączone przez ustawienie stałej DISABLE_WP_CRON na true.<br/>Aby rozpocząć proces importu, "
"proszę włączyć cron, ustawiając stałą na false. Np. define( 'DISABLE_WP_CRON', false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:486
msgid ""
"ERROR! Cron schedules are disabled by setting constant ALTERNATE_WP_CRON to true.<br/>To start the import process "
"please enable the cron by setting the constant to false. E.g. define( 'ALTERNATE_WP_CRON', false );"
msgstr ""
"BŁĄD! Harmonogramy Cron są wyłączone przez ustawienie stałej ALTERNATE_WP_CRON na true.<br/>Aby rozpocząć proces "
"importu, proszę włączyć cron, ustawiając stałą na false. Np. define( 'ALTERNATE_WP_CRON', false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:522
#. translators: 1: The HTTP response code.
msgid "Unexpected HTTP response code: %s"
msgstr "Nieoczekiwany kod odpowiedzi HTTP: %s"

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:46
msgid "(✕) Empty content."
msgstr "(✕) Pusta zawartość."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:51
msgid "(✕) Invalid content."
msgstr "(✕) Nieprawidłowa zawartość."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:62
msgid "Invalid content. Expected an array."
msgstr "Nieprawidłowa zawartość. Oczekiwano tablicy."

#: classes/importer/batch-process/helpers/class-wp-background-process-cartflows-sync-library.php:69
msgid "All processes are complete"
msgstr "Wszystkie procesy zostały zakończone"

#: classes/importer/batch-process/helpers/class-wp-background-process.php:440
#. Translators: %d: interval
msgid "Every %d Minutes"
msgstr "Co %d minut"

#: classes/importer/class-cartflows-api.php:428
msgid "Request successfully processed!"
msgstr "Żądanie zostało pomyślnie przetworzone!"

#: classes/logger/class-cartflows-log-handler-file.php:355
#: classes/logger/class-cartflows-log-handler-file.php:375
msgid "This method should not be called before plugins_loaded."
msgstr "Ta metoda nie powinna być wywoływana przed załadowaniem wtyczek."

#: classes/logger/class-cartflows-wc-logger.php:58
#. translators: 1: class name 2: Cartflows_Log_Handler_Interface
msgid "The provided handler %1$s does not implement %2$s."
msgstr "Podany obsługujący %1$s nie implementuje %2$s."

#: classes/logger/class-cartflows-wc-logger.php:136
#. translators: 1: Cartflows_WC_Logger::log 2: level
msgid "%1$s was called with an invalid level \"%2$s\"."
msgstr "%1$s został wywołany z nieprawidłowym poziomem \"%2$s\"."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:85
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:354
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:546
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:220
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:249
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:202
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:348
#: modules/gutenberg/dist/blocks.build.js:1
msgid "None"
msgstr "Brak"

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:112
#. translators: 1: anchor start, 2: anchor close
msgid ""
"Non-enrolled students will redirect to the selected CartFlows template. If you have not created any Flow already, add "
"new Flow from %1$shere%2$s."
msgstr ""
"Studenci niezapisani zostaną przekierowani do wybranego szablonu CartFlows. Jeśli nie utworzyłeś jeszcze żadnego Flow, "
"dodaj nowy Flow z %1$stąd%2$s."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:118
msgid "Select CartFlows Template for this Course"
msgstr "Wybierz szablon CartFlows dla tego kursu"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:44
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:150
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:373
#: modules/gutenberg/build/blocks-placeholder.js:9
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Checkout Form"
msgstr "Formularz zamówienia"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:34
msgid "Checkout Form."
msgstr "Formularz zamówienia."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:35
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:36
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:34
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:36
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:34
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:35
msgid "Cartflows Modules"
msgstr "Moduły Cartflows"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:137
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:146
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:59
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:68
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:251
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:198
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:207
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Checkout"
msgstr "Nowoczesna kasa"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:138
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:60
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:69
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:255
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:199
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:208
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern One Column"
msgstr "Nowoczesna jedna kolumna"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:139
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:149
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:61
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:70
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:263
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:200
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:209
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "One Column"
msgstr "Jedna kolumna"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:140
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:150
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:62
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:71
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:267
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:210
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Column"
msgstr "Dwie kolumny"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:141
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:64
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:203
msgid "MultiStep Checkout ( PRO )"
msgstr "Wieloetapowa kasa ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:142
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:63
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:202
msgid "Two Step ( PRO )"
msgstr "Two Step ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:148
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:73
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:212
#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout"
msgstr "Wieloetapowa kasa"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:151
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:271
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:211
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step"
msgstr "Dwa kroki"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:168
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1212
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:446
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:229
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Labels"
msgstr "Nowoczesne etykiety"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:132
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:109
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:68
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:82
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1012
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:157
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:294
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:160
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:152
#: modules/landing/classes/class-cartflows-landing-meta-data.php:106
#: modules/optin/classes/class-cartflows-optin-meta-data.php:561
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:389
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "General"
msgstr "Ogólne"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:190
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:253
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Layout"
msgstr "Wybierz układ"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:192
#. translators: %s: link
msgid "The PRO layout options are available in the CartFlows Pro. %1$s  Upgrade Now! %2$s"
msgstr "Opcje układu PRO są dostępne w CartFlows Pro. %1$s  Uaktualnij teraz! %2$s"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:204
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:277
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:186
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:112
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:141
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:390
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:164
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:414
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:207
#: modules/optin/classes/class-cartflows-optin-meta-data.php:298
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Style"
msgstr "Styl"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:207
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:115
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1226
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:70
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:289
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Global"
msgstr "Globalny"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:233
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:262
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:341
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:529
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:458
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:473
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:127
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:189
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:334
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:236
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:264
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:310
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:346
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:399
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:689
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:228
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:347
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Typography"
msgstr "Typografia"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:243
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:218
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1234
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:78
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:362
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:301
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:523
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:616
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:705
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:168
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading"
msgstr "Nagłówek"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:273
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:137
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1230
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:421
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:406
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:199
#: modules/optin/classes/class-cartflows-optin-meta-data.php:292
msgid "Input Fields"
msgstr "Pola wejściowe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:287
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:460
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:913
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1114
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:443
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:878
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:226
#: modules/optin/classes/class-cartflows-optin-meta-data.php:375
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label Color"
msgstr "Kolor etykiety"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:301
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:161
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:477
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:188
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:572
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:454
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:237
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Background Color"
msgstr "Kolor tła pola"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:320
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:503
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:465
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:248
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Text / Placeholder Color"
msgstr "Kolor tekstu wejściowego / symbol zastępczy"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:350
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:542
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:198
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:344
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:533
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:476
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:259
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Style"
msgstr "Styl obramowania"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:352
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:544
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:200
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:346
msgid "The type of border to use. Double borders must have a width of at least 3px to render properly."
msgstr "Rodzaj obramowania do użycia. Podwójne obramowania muszą mieć szerokość co najmniej 3px, aby poprawnie się wyświetlały."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:355
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:547
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:203
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:349
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:539
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:482
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:265
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Solid"
msgstr "Solidny"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:356
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:548
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:204
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:350
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:542
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:485
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:268
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dashed"
msgstr "Przerywana"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:357
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:549
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:205
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:351
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:541
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:484
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:267
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dotted"
msgstr "Kropkowany"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:358
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:550
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:206
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:352
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:540
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:483
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:266
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Double"
msgstr "Podwójny"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:377
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:582
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:230
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:377
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:575
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:500
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:278
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width"
msgstr "Szerokość obramowania"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:398
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:605
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:840
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:245
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:393
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:610
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1024
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:517
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:940
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:290
#: modules/optin/classes/class-cartflows-optin-meta-data.php:396
#: modules/optin/classes/class-cartflows-optin-meta-data.php:528
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Color"
msgstr "Kolor obramowania"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:416
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:646
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:752
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:258
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:421
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius"
msgstr "Promień obramowania"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:437
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:274
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:556
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:287
#: modules/gutenberg/build/blocks.js:11
msgid "Buttons"
msgstr "Przyciski"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:465
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:411
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:292
#: modules/optin/classes/class-cartflows-optin-meta-data.php:507
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Hover Color"
msgstr "Kolor tekstu po najechaniu"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:485
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:824
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:874
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:425
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:306
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:293
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:339
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:404
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:470
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:536
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:743
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1002
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1070
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:264
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:329
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:240
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:263
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:360
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:411
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:459
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:505
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:607
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:724
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:927
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:989
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:374
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:363
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:433
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:437
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:585
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:676
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:765
#: modules/optin/classes/class-cartflows-optin-meta-data.php:389
#: modules/optin/classes/class-cartflows-optin-meta-data.php:514
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Color"
msgstr "Kolor tła"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:509
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:433
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:320
#: modules/optin/classes/class-cartflows-optin-meta-data.php:521
#: modules/gutenberg/build/blocks.js:11
msgid "Background Hover Color"
msgstr "Kolor tła po najechaniu"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:626
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:331
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:407
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:316
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:703
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:456
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:421
#: modules/optin/classes/class-cartflows-optin-meta-data.php:535
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Hover Color"
msgstr "Kolor obramowania po najechaniu"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:670
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1250
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:756
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Payment Section"
msgstr "Sekcja płatności"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:688
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1131
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:776
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Description Color"
msgstr "Opis Kolor"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:702
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:802
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Information Background Color"
msgstr "Kolor tła informacji"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:710
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1143
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:829
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:789
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:256
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Background Color"
msgstr "Kolor tła sekcji"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:724
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1161
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:814
msgid "Section Padding"
msgstr "Odstępy sekcji"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:738
#: modules/gutenberg/build/blocks.js:11
msgid "Margin"
msgstr "Margines"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:768
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1246
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:861
msgid "Field Validation & Error Messages"
msgstr "Walidacja pól i komunikaty o błędach"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:772
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:602
msgid "Field Label Color"
msgstr "Kolor etykiety pola"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:788
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:936
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:587
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:890
msgid "Field Border Color"
msgstr "Kolor obramowania pola"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:808
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:980
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:913
msgid "Error Message Color"
msgstr "Kolor komunikatu o błędzie"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:857
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1242
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:962
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Review"
msgstr "Przegląd zamówienia"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:32
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:70
#: modules/gutenberg/classes/class-cartflows-block-config.php:54
#: modules/gutenberg/build/blocks-placeholder.js:10
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button"
msgstr "Przycisk Następny Krok"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:33
msgid "A simple next step button."
msgstr "Prosty przycisk \"Dalej\"."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:139
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:192
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text"
msgstr "Tekst"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:140
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:61
msgid "Next Step"
msgstr "Następny krok"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:149
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon"
msgstr "Ikona"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:160
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:195
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Position"
msgstr "Pozycja ikony"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:163
msgid "Before Text"
msgstr "Przed tekstem"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:164
msgid "After Text"
msgstr "Po tekście"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:172
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:240
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Spacing"
msgstr "Odstępy między ikonami"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:190
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Type"
msgstr "Rodzaj"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:195
msgid "Flat"
msgstr "Płaski"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:196
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Gradient"
msgstr "Gradient"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:197
msgid "Transparent"
msgstr "Przezroczysty"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:198
msgid "3D"
msgstr "3D"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:208
msgid "Border Size"
msgstr "Rozmiar obramowania"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:217
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:233
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:246
msgid "Hover Styles"
msgstr "Style najechania"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:221
msgid "Fade Background"
msgstr "Zanikanie tła"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:222
msgid "Fill Background From Top"
msgstr "Wypełnij tło od góry"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:223
msgid "Fill Background From Bottom"
msgstr "Wypełnij tło od dołu"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:224
msgid "Fill Background From Left"
msgstr "Wypełnij tło od lewej"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:225
msgid "Fill Background From Right"
msgstr "Wypełnij tło od prawej"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:226
msgid "Fill Background Vertical"
msgstr "Wypełnij tło pionowo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:227
msgid "Fill Background Diagonal"
msgstr "Wypełnij tło ukośnie"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:228
msgid "Fill Background Horizontal"
msgstr "Wypełnij tło poziomo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:236
msgid "Move Down"
msgstr "Przesuń w dół"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:237
msgid "Move Up"
msgstr "Przesuń w górę"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:238
msgid "Move Left"
msgstr "Przesuń w lewo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:239
msgid "Move Right"
msgstr "Przesuń w prawo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:240
msgid "Animate Top"
msgstr "Animuj górę"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:241
msgid "Animate Bottom"
msgstr "Animuj od dołu"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:250
msgid "Appear Icon From Right"
msgstr "Pojaw ikonę z prawej"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:251
msgid "Appear Icon From Left"
msgstr "Pojaw ikonę z lewej"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:252
msgid "Appear Icon From Top"
msgstr "Pojaw ikonę z góry"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:253
msgid "Appear Icon From Bottom"
msgstr "Pojaw ikonę od dołu"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:259
msgid "Structure"
msgstr "Struktura"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:263
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Width"
msgstr "Szerokość"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:267
#: modules/gutenberg/build/blocks.js:11
msgid "Full Width"
msgstr "Pełna szerokość"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:268
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:509
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:680
#: modules/optin/classes/class-cartflows-optin-meta-data.php:355
#: modules/optin/classes/class-cartflows-optin-meta-data.php:459
msgid "Custom"
msgstr "Niestandardowy"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:284
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:302
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:309
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Alignment"
msgstr "Wyrównanie"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:287
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:297
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:310
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:507
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:490
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center"
msgstr "Centrum"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:288
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:298
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:306
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:503
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:313
#: modules/optin/classes/class-cartflows-optin-meta-data.php:486
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Left"
msgstr "Lewo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:289
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:299
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:314
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:511
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:321
#: modules/optin/classes/class-cartflows-optin-meta-data.php:494
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Right"
msgstr "Prawo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:294
msgid "Mobile Alignment"
msgstr "Wyrównanie mobilne"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:304
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:340
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Padding"
msgstr "Wypełnienie"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:318
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:447
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:792
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:215
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:277
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:626
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:395
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:373
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border"
msgstr "Granica"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:342
msgid "Custom Width"
msgstr "Szerokość niestandardowa"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:351
msgid "Custom Height"
msgstr "Wysokość niestandardowa"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:360
msgid "Padding Top/Bottom"
msgstr "Wypełnienie Góra/Dół"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:369
msgid "Padding Left/Right"
msgstr "Dopełnienie z lewej/prawej"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:378
msgid "Round Corners"
msgstr "Zaokrąglone rogi"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:393
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Colors"
msgstr "Kolory"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:444
msgid "Apply Hover Color To"
msgstr "Zastosuj kolor po najechaniu do"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:448
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:322
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
msgid "Background"
msgstr "Tło"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:461
msgid "Button Settings"
msgstr "Ustawienia przycisku"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:465
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:260
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Size"
msgstr "Rozmiar ikony"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:46
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:578
#: modules/optin/classes/class-cartflows-optin-meta-data.php:181
#: modules/optin/classes/class-cartflows-optin-meta-data.php:260
#: modules/gutenberg/build/blocks-placeholder.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Optin Form"
msgstr "Formularz zgody"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:34
msgid "Optin Form."
msgstr "Formularz zgody."

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:97
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:104
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:144
#: modules/optin/classes/class-cartflows-optin-meta-data.php:309
msgid "Floating Labels"
msgstr "Etykiety pływające"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:32
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:46
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:65
#: modules/gutenberg/classes/class-cartflows-block-config.php:157
msgid "Order Details Form"
msgstr "Formularz szczegółów zamówienia"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:33
msgid "Order Details Form."
msgstr "Formularz szczegółów zamówienia."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:125
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:141
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:172
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank You Text"
msgstr "Tekst podziękowania"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:136
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:321
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:86
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:150
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:458
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Overview"
msgstr "Przegląd zamówienia"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:145
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:428
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:94
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:157
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:605
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:99
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Details"
msgstr "Szczegóły zamówienia"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:154
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:164
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:209
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Billing Address"
msgstr "Adres rozliczeniowy"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:163
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:171
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:221
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Shipping Address"
msgstr "Adres wysyłki"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:178
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:74
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:245
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing"
msgstr "Odstępy"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:182
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:253
msgid "Heading Bottom Spacing"
msgstr "Odstęp dolny nagłówka"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:197
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:269
msgid "Spacing Between Sections"
msgstr "Odstępy między sekcjami"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:375
msgid "Sections Heading"
msgstr "Nagłówki sekcji"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:274
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:404
msgid "Sections Content"
msgstr "Zawartość sekcji"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:353
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:418
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:484
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:550
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:398
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:446
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:492
msgid "Text Typography"
msgstr "Typografia tekstu"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:363
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:90
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:515
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Downloads"
msgstr "Pobrane"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:367
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:432
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:498
#: modules/gutenberg/build/blocks.js:11
msgid "Heading Color"
msgstr "Kolor nagłówka"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:381
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:447
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:513
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:381
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:432
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:479
msgid "Heading Typography"
msgstr "Typografia nagłówka"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:494
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:98
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:697
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Customer Details"
msgstr "Szczegóły klienta"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:116
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:117
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:128
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:159
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Layout"
msgstr "Układ"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:134
#. translators: %s is the URL for upgrading
msgid "This feature is available in the CartFlows higher plan. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">%2$s</a>."
msgstr "Ta funkcja jest dostępna w wyższym planie CartFlows. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">%2$s</a>."

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:136
msgid "Upgrade Now!"
msgstr "Zaktualizuj teraz!"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:154
msgid " Global Text Typography"
msgstr "Globalna typografia tekstu"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:252
msgid " Global Primary Color"
msgstr "Globalny kolor podstawowy"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:538
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:481
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:264
msgid "Inherit"
msgstr "Dziedziczyć"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:645
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:528
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:643
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:403
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:301
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:381
msgid "Rounded Corners"
msgstr "Zaokrąglone rogi"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:841
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:290
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Box Shadow"
msgstr "Cień pudełka"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:906
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:869
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Validation"
msgstr "Walidacja pola"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:904
msgid "Error Messages"
msgstr "Komunikaty o błędach"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1174
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:827
msgid "Section Margin"
msgstr "Margines sekcji"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1238
msgid "Buttons (Normal)"
msgstr "Przyciski (Normalne)"

#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:76
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button"
msgstr "Przycisk"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:276
msgid "Sections Heading Typography"
msgstr "Typografia Nagłówków Sekcji"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:288
msgid "Sections Text Typography"
msgstr "Sekcje Tekst Typografia"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:304
msgid "Sections Background Color"
msgstr "Kolor tła sekcji"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:145
msgid "Sorry there was a problem removing this coupon."
msgstr "Przepraszamy, wystąpił problem z usunięciem tego kuponu."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:148
msgid "Coupon has been removed."
msgstr "Kupon został usunięty."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:166
msgid "Sorry there was a problem removing "
msgstr "Przepraszamy, wystąpił problem z usunięciem"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:169
msgid " has been removed."
msgstr "został usunięty."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email Exist."
msgstr "Email istnieje."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email not exist"
msgstr "Email nie istnieje"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:126
#. Translators: %1$s & %2$s is replaced with Field Name
msgid "%1$s Add %2$s"
msgstr "%1$s Dodaj %2$s"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:137
#. Translators: %s is replaced with Field Icon
msgid "%s Have a coupon?"
msgstr "%s Masz kupon?"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:146
#. Translators: %s is replaced with Field Icon
msgid "%s Add Order Notes"
msgstr "%s Dodaj notatki do zamówienia"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:474
#: modules/optin/classes/class-cartflows-optin-markup.php:228
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:232
msgid "WooCommerce functions do not exist. If you are in an IFrame, please reload it."
msgstr "Funkcje WooCommerce nie istnieją. Jeśli jesteś w IFrame, proszę przeładuj go."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:475
#: modules/optin/classes/class-cartflows-optin-markup.php:229
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:233
msgid "Click Here to Reload"
msgstr "Kliknij tutaj, aby przeładować"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:503
msgid "Checkout ID not found"
msgstr "Identyfikator transakcji nie znaleziony"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:506
#. translators: %1$1s, %2$2s Link to article
msgid ""
"It seems that this is not the CartFlows Checkout page where you have added this shortcode. Please refer to this "
"%1$1sarticle%2$2s to know more."
msgstr ""
"Wygląda na to, że to nie jest strona realizacji zamówienia CartFlows, na której dodałeś ten shortcode. Proszę zapoznać "
"się z tym %1$1sartykułem%2$2s, aby dowiedzieć się więcej."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:576
#: modules/checkout/templates/embed/checkout-template-simple.php:48
#: modules/checkout/templates/wcf-template.php:40
#: modules/optin/templates/optin-template-simple.php:29
msgid "Your cart is currently empty."
msgstr "Twój koszyk jest obecnie pusty."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:616
#. translators: %1$1s, %2$2s Link to meta
msgid "No product is selected. Please select products from the %1$1scheckout meta settings%2$2s to continue."
msgstr "Nie wybrano produktu. Proszę wybrać produkty z %1$1sustawień meta kasy%2$2s, aby kontynuować."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:724
msgid "Variations Not set"
msgstr "Wariacje Nie ustawione"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:735
msgid "This product can't be purchased"
msgstr "Nie można kupić tego produktu"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1467
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1511
#: modules/checkout/templates/checkout/collapsed-order-summary.php:46
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:82
msgid "Coupon Code"
msgstr "Kod kuponu"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1468
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1520
#: modules/checkout/templates/checkout/collapsed-order-summary.php:51
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:87
msgid "Apply"
msgstr "Zastosuj"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1544
msgid "Entered email address is not a valid email."
msgstr "Wprowadzony adres e-mail nie jest prawidłowym adresem e-mail."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1545
msgid "This email is already registered. Please enter the password to continue."
msgstr "Ten adres e-mail jest już zarejestrowany. Proszę wprowadzić hasło, aby kontynuować."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1548
msgid "Value must be between "
msgstr "Wartość musi być pomiędzy"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1752
msgid "Show Order Summary"
msgstr "Pokaż podsumowanie zamówienia"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1753
msgid "Hide Order Summary"
msgstr "Ukryj podsumowanie zamówienia"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1876
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:498
#: modules/checkout/templates/checkout/shipping-methods.php:66
msgid "Enter your address to view shipping options."
msgstr "Wprowadź swój adres, aby zobaczyć opcje wysyłki."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1885
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:405
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:407
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:796
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:110
#: modules/thankyou/templates/instant-thankyou-order-details.php:107
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping"
msgstr "Wysyłka"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:138
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:943
#: modules/optin/classes/class-cartflows-optin-meta-data.php:174
msgid "Products"
msgstr "Produkty"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:144
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Order Bumps"
msgstr "Dodatki do zamówienia"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:156
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Offers"
msgstr "Oferty dynamiczne"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:172
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:982
msgid "Checkout Offer"
msgstr "Oferta przy kasie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:205
msgid "Two Step (Available in higher plan) "
msgstr "Dwuetapowy (Dostępny w wyższym planie)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:206
msgid "Multistep Checkout (Available in higher plan) "
msgstr "Wieloetapowe zakupy (Dostępne w wyższym planie)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:213
#: modules/landing/classes/class-cartflows-landing-meta-data.php:91
#: modules/optin/classes/class-cartflows-optin-meta-data.php:253
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:92
msgid "Shortcode"
msgstr "Kod krótki"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:230
msgid "CartFlows Checkout"
msgstr "Kasa CartFlows"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:232
msgid "Add this shortcode to your checkout page"
msgstr "Dodaj ten shortcode do swojej strony realizacji zakupu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:239
msgid "Checkout Design"
msgstr "Projekt kasy"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:245
msgid "Checkout Skin"
msgstr "Sprawdź skórkę"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:259
#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout"
msgstr "Wieloetapowe zakupy"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:296
#: modules/optin/classes/class-cartflows-optin-meta-data.php:284
#: modules/optin/classes/class-cartflows-optin-meta-data.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:420
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:181
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:205
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:306
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Family"
msgstr "Rodzina czcionek"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:304
msgid "Instant Checkout"
msgstr "Szybkie zakupy"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:319
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:328
msgid "Left Column Background Color"
msgstr "Kolor tła lewej kolumny"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:336
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:344
msgid "Right Column Background Color"
msgstr "Kolor tła prawej kolumny"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:355
msgid "Checkout Texts & Buttons"
msgstr "Teksty i przyciski do kasy"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:362
msgid "Enable Advance Options"
msgstr "Włącz opcje zaawansowane"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:370
msgid "Heading Font"
msgstr "Czcionka nagłówka"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:384
#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text Color"
msgstr "Kolor tekstu nagłówka"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:400
msgid "Heading Font Family"
msgstr "Czcionka nagłówka"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:436
msgid "Input Field Style"
msgstr "Styl pola wejściowego"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:463
msgid "Input Field Font Family"
msgstr "Czcionka pola wejściowego"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:483
msgid "Field Size"
msgstr "Rozmiar pola"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:489
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:660
#: modules/optin/classes/class-cartflows-optin-meta-data.php:335
#: modules/optin/classes/class-cartflows-optin-meta-data.php:439
msgid "Extra Small"
msgstr "Bardzo mały"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:493
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:664
#: modules/optin/classes/class-cartflows-optin-meta-data.php:339
#: modules/optin/classes/class-cartflows-optin-meta-data.php:443
msgid "Small"
msgstr "Mały"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:497
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:668
#: modules/optin/classes/class-cartflows-optin-meta-data.php:343
#: modules/optin/classes/class-cartflows-optin-meta-data.php:447
msgid "Medium"
msgstr "Średni"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:501
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:672
#: modules/optin/classes/class-cartflows-optin-meta-data.php:347
#: modules/optin/classes/class-cartflows-optin-meta-data.php:451
msgid "Large"
msgstr "Duży"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:505
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:676
#: modules/optin/classes/class-cartflows-optin-meta-data.php:351
#: modules/optin/classes/class-cartflows-optin-meta-data.php:455
msgid "Extra Large"
msgstr "Bardzo duży"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:525
msgid "Field Top-Bottom Spacing"
msgstr "Odstęp pola góra-dół"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:541
msgid "Field Left-Right Spacing"
msgstr "Odstęp pola lewo-prawo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:557
msgid "Field Text / Placeholder Color"
msgstr "Kolor tekstu pola / zastępczego"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:618
msgid "Button Fields"
msgstr "Pola przycisków"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:634
msgid "Button Font Family"
msgstr "Czcionka przycisku"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:654
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:330
msgid "Button Size"
msgstr "Rozmiar przycisku"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:696
msgid "Button Top-Bottom Spacing"
msgstr "Odstęp przycisku góra-dół"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:717
msgid "Button Left-Right Spacing"
msgstr "Odstęp przycisku lewo-prawo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:738
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:294
msgid "Button Text Color"
msgstr "Kolor tekstu przycisku"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:753
msgid "Button Text Hover Color"
msgstr "Kolor tekstu przycisku po najechaniu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:768
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:300
msgid "Button Background Color"
msgstr "Kolor tła przycisku"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:783
msgid "Button Background Hover Color"
msgstr "Kolor tła przycisku po najechaniu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:798
msgid "Button Border Color"
msgstr "Kolor obramowania przycisku"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:813
msgid "Button Border Hover Color"
msgstr "Kolor obramowania przycisku po najechaniu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:861
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:865
msgid "Enable Product Options"
msgstr "Włącz opcje produktu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:870
msgid "Enable Conditions"
msgstr "Włącz warunki"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:874
msgid "Restrict user to purchase all products"
msgstr "Ogranicz użytkownikowi możliwość zakupu wszystkich produktów"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:878
msgid "Let user select one product from all options"
msgstr "Pozwól użytkownikowi wybrać jeden produkt spośród wszystkich opcji"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:882
msgid "Let user select multiple products from all options"
msgstr "Pozwól użytkownikowi wybrać wiele produktów ze wszystkich opcji"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:888
msgid "Enable Variations"
msgstr "Włącz warianty"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:897
msgid "Show variations inline"
msgstr "Pokaż warianty w tekście"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:901
msgid "Show variations in popup"
msgstr "Pokaż warianty w okienku"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:907
msgid "Enable Quantity"
msgstr "Włącz ilość"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:920
msgid "Select Coupon"
msgstr "Wybierz kupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:921
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a coupon"
msgstr "Wyszukaj kupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:925
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows coupon please %1$1s Click here.%2$2s"
msgstr "Aby uzyskać więcej informacji o kuponie CartFlows, proszę %1$1s kliknij tutaj.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:951
msgid "Select Product"
msgstr "Wybierz produkt"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:952
msgid "Search for a product..."
msgstr "Wyszukaj produkt..."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:962
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the checkout product settings please %1$1s Click here.%2$2s"
msgstr "Aby uzyskać więcej informacji o ustawieniach produktu w kasie, proszę %1$1s kliknij tutaj.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:968
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Auto Apply Coupon"
msgstr "Automatyczne zastosowanie kuponu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:975
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Product Options"
msgstr "Opcje produktu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1026
#: modules/flow/classes/class-cartflows-step-meta-base.php:80
#: modules/landing/classes/class-cartflows-landing-meta-data.php:119
#: modules/optin/classes/class-cartflows-optin-meta-data.php:574
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:402
msgid "Custom Script"
msgstr "Skrypt niestandardowy"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1035
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:475
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Advanced"
msgstr "Zaawansowane"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1041
msgid "Display product images"
msgstr "Wyświetl obrazy produktów"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1049
msgid "Enable cart editing on checkout"
msgstr "Włącz edytowanie koszyka przy kasie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1064
#: modules/landing/classes/class-cartflows-landing-meta-data.php:134
#: modules/optin/classes/class-cartflows-optin-meta-data.php:634
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:523
msgid "Step Note"
msgstr "Krok Notatka"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1148
msgid "Form Settings"
msgstr "Ustawienia formularza"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1153
msgid "Enable Coupon Field"
msgstr "Włącz pole kuponu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1159
msgid "Collapsible Coupon Field"
msgstr "Składane pole kuponu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1175
msgid "Enable Additional Field"
msgstr "Włącz dodatkowe pole"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1181
msgid "Collapsible Additional Field"
msgstr "Składane dodatkowe pole"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1197
msgid "Enable Ship To Different Address"
msgstr "Włącz wysyłkę na inny adres"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1204
msgid "Enable Google Address Autocomplete"
msgstr "Włącz autouzupełnianie adresów Google"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1207
#. translators: %1$s: link html start, %2$s: link html end
msgid "Before enabling this option, make sure that you have added API key in Google Address Autocomplete Settings."
msgstr "Zanim włączysz tę opcję, upewnij się, że dodałeś klucz API w ustawieniach autouzupełniania adresów Google."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1212
msgid "Enable Custom Shipping Message"
msgstr "Włącz niestandardową wiadomość o wysyłce"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1220
msgid "Shipping Message"
msgstr "Wiadomość o wysyłce"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1224
msgid "This message will be displayed when no shipping method is available."
msgstr "Ten komunikat zostanie wyświetlony, gdy żadna metoda wysyłki nie będzie dostępna."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1241
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:446
msgid "Order Summary Position"
msgstr "Pozycja podsumowania zamówienia"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1248
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:219
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:453
msgid "Top"
msgstr "Góra"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1252
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:457
msgid "Bottom"
msgstr "Dół"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1266
msgid "Form Headings"
msgstr "Nagłówki formularza"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1273
msgid "Billing Details"
msgstr "Szczegóły rozliczeń"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1275
msgid "Billing details"
msgstr "Szczegóły rozliczeń"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1280
msgid "Shipping Details"
msgstr "Szczegóły wysyłki"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1282
msgid "Ship to a different address?"
msgstr "Wysłać na inny adres?"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1287
#: modules/thankyou/templates/instant-thankyou-your-product.php:23
msgid "Your Order"
msgstr "Twoje zamówienie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1289
msgid "Your order"
msgstr "Twoje zamówienie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1294
msgid "Customer Information"
msgstr "Informacje o kliencie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1296
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:159
msgid "Customer information"
msgstr "Informacje o kliencie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1302
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1304
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:823
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:276
#: modules/thankyou/templates/instant-thankyou-order-details.php:127
msgid "Payment"
msgstr "Płatność"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1310
msgid "Enable Field validation error message"
msgstr "Włącz komunikat o błędzie walidacji pola"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1318
msgid "Validation error message"
msgstr "Komunikat o błędzie walidacji"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1336
msgid "Place Order Button"
msgstr "Przycisk Zamów"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1342
#: modules/optin/classes/class-cartflows-optin-meta-data.php:590
#: modules/gutenberg/build/blocks.js:11
msgid "Button Text"
msgstr "Tekst przycisku"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1351
msgid "Enable Lock Icon"
msgstr "Włącz ikonę kłódki"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1360
msgid "Enable Price Display"
msgstr "Włącz wyświetlanie cen"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:260
msgid "Home"
msgstr "Dom"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:496
#: modules/checkout/templates/checkout/shipping-methods.php:64
msgid "Shipping costs are calculated during checkout."
msgstr "Koszty wysyłki są obliczane podczas realizacji zamówienia."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:515
#: modules/checkout/templates/checkout/shipping-methods.php:72
#. Translators: $s shipping destination.
msgid "No shipping options were found for %s."
msgstr "Nie znaleziono opcji wysyłki dla %s."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:519
#: modules/checkout/templates/checkout/shipping-methods.php:73
#. Translators: $s shipping destination.
msgid "Enter a different address"
msgstr "Wprowadź inny adres"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:562
#: modules/thankyou/templates/instant-thankyou-order-details.php:67
msgid "Contact"
msgstr "Kontakt"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:564
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "%1$1s Log in%2$2s"
msgstr "%1$1s Zaloguj się%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:594
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:192
msgid "Password"
msgstr "Hasło"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:596
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:194
#. translators: %s: asterisk mark
msgid "Password %s"
msgstr "Hasło %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:602
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:200
msgid "Login"
msgstr "Zaloguj się"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:603
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:201
msgid "Lost your password?"
msgstr "Zgubiłeś hasło?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:608
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:206
msgid "Login is optional, you can continue with your order below."
msgstr "Logowanie jest opcjonalne, możesz kontynuować swoje zamówienie poniżej."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:620
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:218
msgid "Create an account?"
msgstr "Utworzyć konto?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:635
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:233
msgid "Account username"
msgstr "Nazwa użytkownika konta"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:637
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:235
#. translators: %s: asterisk mark
msgid "Account username %s"
msgstr "Nazwa użytkownika konta %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:649
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:247
msgid "Create account password"
msgstr "Utwórz hasło do konta"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:651
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:249
#. translators: %s: asterisk mark
msgid "Create account password %s"
msgstr "Utwórz hasło do konta %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:660
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:258
#. translators: %1$s: username, %2$s emailid
msgid " Welcome Back %1$s ( %2$s )"
msgstr "Witamy z powrotem %1$s ( %2$s )"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:915
msgid "Looks like you haven't added any items to cart yet — start shopping to fill it up!"
msgstr "Wygląda na to, że nie dodałeś jeszcze żadnych przedmiotów do koszyka — zacznij zakupy, aby go zapełnić!"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:927
msgid "Your Cart is Currently Empty."
msgstr "Twój koszyk jest obecnie pusty."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:929
msgid "Start Shopping"
msgstr "Rozpocznij zakupy"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:161
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "Already have an account? %1$1s Log in%2$2s"
msgstr "Masz już konto? %1$1s Zaloguj się%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:177
#. translators: %s: asterisk mark
msgid "Email Address %s"
msgstr "Adres e-mail %s"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:69
msgid "Coupon code applied successfully."
msgstr "Kod kuponu został pomyślnie zastosowany."

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:76
msgid "Have a coupon?"
msgstr "Masz kupon?"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:95
#: modules/checkout/templates/checkout/order-review-table.php:17
#: modules/checkout/templates/checkout/order-review-table.php:43
#: modules/thankyou/templates/instant-thankyou-your-product.php:118
msgid "Subtotal"
msgstr "Suma częściowa"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:147
#: modules/checkout/templates/checkout/order-review-table.php:79
#: modules/thankyou/templates/instant-thankyou-your-product.php:148
msgid "Total"
msgstr "Razem"

#: modules/checkout/templates/checkout/order-review-table.php:16
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Product"
msgstr "Produkt"

#: modules/checkout/templates/checkout/shipping-methods.php:53
#. Translators: $s shipping destination.
msgid "Shipping to %s."
msgstr "Wysyłka do %s."

#: modules/checkout/templates/checkout/shipping-methods.php:54
#. Translators: $s shipping destination.
msgid "Change address"
msgstr "Zmień adres"

#: modules/checkout/templates/checkout/shipping-methods.php:56
msgid "Shipping options will be updated during checkout."
msgstr "Opcje wysyłki zostaną zaktualizowane podczas finalizacji zamówienia."

#: modules/checkout/templates/wcf-template.php:51
msgid "Copyright &copy;"
msgstr "Prawa autorskie &copy;"

#: modules/checkout/templates/wcf-template.php:56
msgid "All Rights Reserved"
msgstr "Wszelkie prawa zastrzeżone"

#: modules/elementor/class-cartflows-el-widgets-loader.php:177
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:588
msgid "Cartflows"
msgstr "Cartflows"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:267
#. translators: %s admin link
msgid ""
"This feature is available in the CartFlows higher plan. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Upgrade "
"Now!</a>."
msgstr ""
"Ta funkcja jest dostępna w wyższym planie CartFlows. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Ulepsz "
"teraz!</a>."

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:370
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:175
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color"
msgstr "Kolor"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:585
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:354
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:343
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Normal"
msgstr "Normalny"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:683
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:417
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:403
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover"
msgstr "Najechać"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:839
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Rounded Corners"
msgstr "Sekcja Zaokrąglone Rogi"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:164
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title"
msgstr "Tytuł"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:166
msgid "BUY NOW"
msgstr "KUP TERAZ"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:176
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sub Title"
msgstr "Podtytuł"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title"
msgstr "Przed tytułem"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:200
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title"
msgstr "Po tytule"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:201
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title & Sub Title"
msgstr "Przed tytułem i podtytułem"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:202
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title & Sub Title"
msgstr "Po Tytule i Podtytule"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:213
msgid "Icon Vertical Alignment"
msgstr "Wyrównanie pionowe ikony"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:223
msgid "Middle"
msgstr "Środek"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:318
msgid "Justify"
msgstr "Uzasadnij"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:424
msgid "Hover Text Color"
msgstr "Kolor tekstu po najechaniu"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:437
msgid "Hover Background Color"
msgstr "Kolor tła po najechaniu"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:467
msgid "Hover Animation"
msgstr "Animacja po najechaniu"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:554
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:645
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:734
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Content"
msgstr "Zawartość"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:499
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Alignment"
msgstr "Wyrównanie tekstu"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:556
msgid "Title and Sub Title Spacing"
msgstr "Odstępy między tytułem a podtytułem"

#: modules/elementor/widgets/class-cartflows-el-optin-form.php:324
#: modules/optin/classes/class-cartflows-optin-meta-data.php:406
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button"
msgstr "Przycisk Prześlij"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:187
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:199
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:211
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:223
msgid "Show"
msgstr "Pokaż"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:367
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sections"
msgstr "Sekcje"

#: modules/email-report/class-cartflows-admin-report-emails.php:104
msgid "There"
msgstr "Tam"

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "You have successfully unsubscribed from our weekly emails list."
msgstr "Pomyślnie wypisałeś się z naszej listy cotygodniowych e-maili."

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "Unsubscribed"
msgstr "Wypisano z subskrypcji"

#: modules/email-report/class-cartflows-admin-report-emails.php:174
msgid "Here’s how your store performed last week!"
msgstr "Oto jak Twój sklep wypadł w zeszłym tygodniu!"

#: modules/email-report/templates/email-body.php:17
msgid "CartFlows Weekly Report"
msgstr "Tygodniowy raport CartFlows"

#: modules/email-report/templates/email-cf-pro-block.php:26
msgid "CartFlows Pro can help you to increase conversion and maximize profits."
msgstr "CartFlows Pro może pomóc Ci zwiększyć konwersję i maksymalizować zyski."

#: modules/email-report/templates/email-cf-pro-block.php:43
msgid ""
"Want to earn 30% more store revenue on autopilot? CartFlows order bumps and upsells help you do just that. Try "
"CartFlows Pro risk-free for 30 days!"
msgstr ""
"Chcesz zwiększyć przychody sklepu o 30% na autopilocie? Dodatki do zamówień i upselling w CartFlows pomogą Ci to "
"osiągnąć. Wypróbuj CartFlows Pro bez ryzyka przez 30 dni!"

#: modules/email-report/templates/email-cf-pro-block.php:62
msgid "GET CARTFLOWS NOW"
msgstr "POBIERZ CARTFLOWS TERAZ"

#: modules/email-report/templates/email-content-section.php:27
#. translators: %s user name
msgid "Hey %s!"
msgstr "Cześć %s!"

#: modules/email-report/templates/email-content-section.php:42
#. translators: %1$s: store name, %2$s: total revenue.  %3$s: total revenue
msgid ""
"%1$s has earned a total %2$s in revenue last week by using CartFlows to power your store! And in the last month, it "
"earned %3$s"
msgstr ""
"%1$s zarobił w zeszłym tygodniu %2$s przychodów, korzystając z CartFlows do zasilania swojego sklepu! A w ostatnim "
"miesiącu zarobił %3$s"

#: modules/email-report/templates/email-content-section.php:79
msgid "(Last 7 days)"
msgstr "(Ostatnie 7 dni)"

#: modules/email-report/templates/email-content-section.php:93
msgid "(Last 30 days)"
msgstr "(Ostatnie 30 dni)"

#: modules/email-report/templates/email-footer.php:63
#. translators: %1$s - link to a site;
msgid "This email was auto-generated and sent from %1$s."
msgstr "Ten e-mail został wygenerowany automatycznie i wysłany z %1$s."

#: modules/email-report/templates/email-footer.php:70
msgid "Unsubscribe"
msgstr "Anuluj subskrypcję"

#: modules/email-report/templates/email-header.php:27
msgid "Your weekly summary from CartFlows."
msgstr "Twoje cotygodniowe podsumowanie z CartFlows."

#: modules/email-report/templates/email-other-product-block.php:26
msgid "Would you like to try our other products that help WooCommerce stores sell more?"
msgstr "Czy chciałbyś wypróbować nasze inne produkty, które pomagają sklepom WooCommerce sprzedawać więcej?"

#: modules/email-report/templates/email-other-product-block.php:41
msgid "TRY OUR OTHER PRODUCTS"
msgstr "WYPRÓBUJ NASZE INNE PRODUKTY"

#: modules/email-report/templates/email-stat-content.php:26
msgid "Full Overview"
msgstr "Pełny przegląd"

#: modules/email-report/templates/email-stat-content.php:82
#: modules/email-report/templates/email-stat-content.php:220
msgid "Order Placed"
msgstr "Zamówienie złożone"

#: modules/email-report/templates/email-stat-content.php:112
#: modules/email-report/templates/email-stat-content.php:247
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total Visits"
msgstr "Łączna liczba wizyt"

#: modules/email-report/templates/email-stat-content.php:142
#: modules/email-report/templates/email-stat-content.php:274
msgid "Order Bumps Revenue"
msgstr "Przychody z dodatkowych zamówień"

#: modules/email-report/templates/email-stat-content.php:172
#: modules/email-report/templates/email-stat-content.php:304
msgid "Offers Revenue"
msgstr "Przychody z ofert"

#: modules/email-report/templates/email-stat-content.php:250
#: modules/email-report/templates/email-stat-content.php:282
#: modules/email-report/templates/email-stat-content.php:307
msgid "CartFlows Pro"
msgstr "CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Flow: "
msgstr "Przepływ:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Name: "
msgstr "Imię:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:105
msgid "Search Flows"
msgstr "Wyszukaj przepływy"

#: modules/flow/classes/class-cartflows-flow-post-type.php:106
msgid "All Flows"
msgstr "Wszystkie przepływy"

#: modules/flow/classes/class-cartflows-flow-post-type.php:107
msgid "Edit Flow"
msgstr "Edytuj przepływ"

#: modules/flow/classes/class-cartflows-flow-post-type.php:108
msgid "View Flow"
msgstr "Wyświetl przepływ"

#: modules/flow/classes/class-cartflows-flow-post-type.php:109
#: modules/flow/classes/class-cartflows-flow-post-type.php:111
#: modules/flow/classes/class-cartflows-step-post-type.php:176
#: modules/flow/classes/class-cartflows-step-post-type.php:178
#: admin-core/assets/build/settings-app.js:25
msgid "Add New"
msgstr "Dodaj nowy"

#: modules/flow/classes/class-cartflows-flow-post-type.php:110
msgid "Update Flow"
msgstr "Aktualizuj przepływ"

#: modules/flow/classes/class-cartflows-flow-post-type.php:112
msgid "New Flow Name"
msgstr "Nowa nazwa przepływu"

#: modules/flow/classes/class-cartflows-flow-post-type.php:194
#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Upgrade to CartFlows Pro"
msgstr "Uaktualnij do CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:213
msgid "Slug"
msgstr "Ślimak"

#: modules/flow/classes/class-cartflows-flow-post-type.php:332
#: modules/flow/classes/class-cartflows-flow-post-type.php:338
#: modules/flow/classes/class-cartflows-step-post-type.php:410
#: modules/flow/classes/class-cartflows-step-post-type.php:416
#. translators: %s: singular custom post type name
msgid "%s updated."
msgstr "%s zaktualizowano."

#: modules/flow/classes/class-cartflows-flow-post-type.php:334
#: modules/flow/classes/class-cartflows-step-post-type.php:412
#. translators: %s: singular custom post type name
msgid "Custom %s updated."
msgstr "Zaktualizowano niestandardowy %s."

#: modules/flow/classes/class-cartflows-flow-post-type.php:336
#: modules/flow/classes/class-cartflows-step-post-type.php:414
#. translators: %s: singular custom post type name
msgid "Custom %s deleted."
msgstr "Usunięto niestandardowy %s."

#: modules/flow/classes/class-cartflows-flow-post-type.php:340
#: modules/flow/classes/class-cartflows-step-post-type.php:418
#. translators: %1$s: singular custom post type name ,%2$s: date and time of the revision
msgid "%1$s restored to revision from %2$s"
msgstr "%1$s przywrócono do wersji z %2$s"

#: modules/flow/classes/class-cartflows-flow-post-type.php:342
#: modules/flow/classes/class-cartflows-step-post-type.php:420
#. translators: %s: singular custom post type name
msgid "%s published."
msgstr "%s opublikowano."

#: modules/flow/classes/class-cartflows-flow-post-type.php:344
#: modules/flow/classes/class-cartflows-step-post-type.php:422
#. translators: %s: singular custom post type name
msgid "%s saved."
msgstr "%s zapisano."

#: modules/flow/classes/class-cartflows-flow-post-type.php:346
#: modules/flow/classes/class-cartflows-step-post-type.php:424
#. translators: %s: singular custom post type name
msgid "%s submitted."
msgstr "%s przesłano."

#: modules/flow/classes/class-cartflows-flow-post-type.php:348
#: modules/flow/classes/class-cartflows-step-post-type.php:426
#. translators: %s: singular custom post type name
msgid "%s scheduled for."
msgstr "%s zaplanowano na."

#: modules/flow/classes/class-cartflows-flow-post-type.php:350
#: modules/flow/classes/class-cartflows-step-post-type.php:428
#. translators: %s: singular custom post type name
msgid "%s draft updated."
msgstr "Zaktualizowano wersję roboczą %s."

#: modules/flow/classes/class-cartflows-step-meta-base.php:58
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:118
msgid "Design"
msgstr "Projekt"

#: modules/flow/classes/class-cartflows-step-meta-base.php:82
msgid "Custom script lets you add your own custom script on front end of this flow page."
msgstr "Niestandardowy skrypt pozwala dodać własny skrypt na froncie tej strony przepływu."

#: modules/flow/classes/class-cartflows-step-post-type.php:172
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Search Steps"
msgstr "Kroki wyszukiwania"

#: modules/flow/classes/class-cartflows-step-post-type.php:173
msgid "All Steps"
msgstr "Wszystkie kroki"

#: modules/flow/classes/class-cartflows-step-post-type.php:174
msgid "Edit Step"
msgstr "Edytuj krok"

#: modules/flow/classes/class-cartflows-step-post-type.php:175
msgid "View Step"
msgstr "Zobacz krok"

#: modules/flow/classes/class-cartflows-step-post-type.php:177
msgid "Update Step"
msgstr "Krok aktualizacji"

#: modules/flow/classes/class-cartflows-step-post-type.php:179
msgid "New Step Name"
msgstr "Nowa nazwa kroku"

#: modules/flow/classes/class-cartflows-step-post-type.php:220
msgid "Step Type"
msgstr "Typ kroku"

#: modules/flow/classes/class-cartflows-step-post-type.php:230
msgid "Step Flow"
msgstr "Przepływ kroków"

#: modules/flow/classes/class-cartflows-step-post-type.php:255
msgid "Optin"
msgstr "Zgoda"

#: modules/flow/classes/class-cartflows-step-post-type.php:276
msgid "Upsell"
msgstr "Sprzedaż dodatkowa"

#: modules/flow/classes/class-cartflows-step-post-type.php:283
msgid "Downsell"
msgstr "Sprzedaż dodatkowa"

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:146
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:201
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:311
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:86
msgid "Permission denied."
msgstr "Odmowa dostępu."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:225
msgid "No product is selected. Please select products from the checkout meta settings to continue."
msgstr "Nie wybrano żadnego produktu. Proszę wybrać produkty z ustawień meta kasy, aby kontynuować."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:328
#: modules/optin/classes/class-cartflows-optin-markup.php:321
msgid "No product is selected. Please select a Simple, Virtual and Free product from the meta settings."
msgstr "Nie wybrano produktu. Proszę wybrać produkt prosty, wirtualny i darmowy z ustawień meta."

#: modules/landing/classes/class-cartflows-landing-meta-data.php:98
msgid "Next Step Link"
msgstr "Link do następnego kroku"

#: modules/optin/classes/class-cartflows-optin-markup.php:261
msgid "Please place shortcode on Optin step-type only."
msgstr "Proszę umieścić shortcode tylko na kroku typu Optin."

#: modules/optin/classes/class-cartflows-optin-markup.php:338
msgid "Please update the selected product's price to zero (0)."
msgstr "Proszę zaktualizować cenę wybranego produktu na zero (0)."

#: modules/optin/classes/class-cartflows-optin-markup.php:347
#: modules/optin/classes/class-cartflows-optin-markup.php:351
msgid "Please select a Simple, Virtual and Free product."
msgstr "Proszę wybrać produkt prosty, wirtualny i darmowy."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:76
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enable Custom Field Editor"
msgstr "Włącz Edytor Pola Niestandardowego"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a Product"
msgstr "Wyszukaj produkt"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:229
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows Optin step please %1$sClick here.%2$s"
msgstr "Aby uzyskać więcej informacji o kroku Optin w CartFlows, %1$skliknij tutaj.%2$s"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:262
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:101
msgid "Add this shortcode to your optin page"
msgstr "Dodaj ten krótki kod do swojej strony optin"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:270
msgid "Global Settings"
msgstr "Ustawienia globalne"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:328
#: modules/optin/classes/class-cartflows-optin-meta-data.php:432
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size"
msgstr "Rozmiar"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:361
#: modules/optin/classes/class-cartflows-optin-meta-data.php:465
msgid "Top Bottom Spacing"
msgstr "Odstępy górne i dolne"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:368
#: modules/optin/classes/class-cartflows-optin-meta-data.php:472
msgid "Left Right Spacing"
msgstr "Odstępy z lewej i prawej strony"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:382
msgid "Text / Placeholder Color"
msgstr "Kolor tekstu / symbolu zastępczego"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:412
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Size"
msgstr "Rozmiar czcionki"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:479
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Position"
msgstr "Pozycja"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:584
msgid "Optin Settings"
msgstr "Ustawienia subskrypcji"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:598
msgid "Pass Fields as URL Parameters"
msgstr "Przekaż pola jako parametry URL"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:601
msgid "You can pass specific fields from the form to next step as URL query parameters."
msgstr "Możesz przekazać określone pola z formularza do następnego kroku jako parametry zapytania URL."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:606
msgid "Enter form field"
msgstr "Wprowadź pole formularza"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:609
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:492
msgid "Enter comma seprated field name. E.g. first_name, last_name"
msgstr "Wprowadź nazwy pól oddzielone przecinkami. Np. first_name, last_name"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:610
msgid "Fields to pass, separated by commas"
msgstr "Pola do przekazania, oddzielone przecinkami"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:612
#. translators: %s: link
msgid "You can pass field value as a URL parameter to the next step. %1$sLearn More >>%2$s"
msgstr "Możesz przekazać wartość pola jako parametr URL do następnego kroku. %1$sDowiedz się więcej >>%2$s"

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:180
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:183
msgid "We can't seem to find an order for you."
msgstr "Nie możemy znaleźć dla Ciebie zamówienia."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:272
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:662
msgid "No completed or processing order found to show the order details form demo."
msgstr "Nie znaleziono ukończonego ani przetwarzanego zamówienia, aby pokazać formularz szczegółów zamówienia demo."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:280
msgid "Order not found. You cannot access this page directly."
msgstr "Nie znaleziono zamówienia. Nie możesz uzyskać dostępu do tej strony bezpośrednio."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:673
msgid "Order Details Not Found."
msgstr "Nie znaleziono szczegółów zamówienia."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:675
msgid "Return to Shopping"
msgstr "Wróć do zakupów"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:212
msgid "Font Size (In px)"
msgstr "Rozmiar czcionki (w px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:221
msgid "Advanced Options"
msgstr "Zaawansowane opcje"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:227
msgid "Enable Advanced Options"
msgstr "Włącz opcje zaawansowane"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:234
msgid "Container Width (In px)"
msgstr "Szerokość kontenera (w px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:331
msgid "Background color of left side column for Instant Thank You Layout."
msgstr "Kolor tła lewej kolumny dla układu natychmiastowego podziękowania."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:347
msgid "Background color of right side column for Instant Thank You Layout."
msgstr "Kolor tła prawej kolumny dla układu Instant Thank You."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:417
msgid "Enable Order Overview"
msgstr "Włącz podgląd zamówienia"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:424
msgid "Enable Order Details"
msgstr "Włącz szczegóły zamówienia"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:431
msgid "Enable Billing Details"
msgstr "Włącz szczegóły rozliczeń"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:438
msgid "Enable Shipping Details"
msgstr "Włącz szczegóły wysyłki"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:481
msgid "Thank You Page Text"
msgstr "Tekst strony podziękowania"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:489
msgid "Redirect After Purchase"
msgstr "Przekierowanie po zakupie"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:497
msgid "Redirect Link"
msgstr "Przekieruj link"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:500
msgid "https://"
msgstr "https://"

#: modules/thankyou/templates/instant-thankyou-order-details.php:41
#. Translators: First name.
msgid "Thank you, %s!"
msgstr "Dziękuję, %s!"

#: modules/thankyou/templates/instant-thankyou-order-details.php:58
msgid "Order Updates"
msgstr "Aktualizacje zamówienia"

#: modules/thankyou/templates/instant-thankyou-order-details.php:59
msgid "You will receive order and shipping updates via email."
msgstr "Otrzymasz aktualizacje dotyczące zamówienia i wysyłki za pośrednictwem e-maila."

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
msgid "Address"
msgstr "Adres"

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Billing"
msgstr "Rozliczenia"

#: modules/thankyou/templates/instant-thankyou-order-details.php:155
msgid "Continue Shopping"
msgstr "Kontynuuj zakupy"

#: modules/thankyou/templates/instant-thankyou.php:37
msgid ""
"Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please "
"attempt your purchase again."
msgstr ""
"Niestety, nie można zrealizować Twojego zamówienia, ponieważ bank/handlowiec odrzucił Twoją transakcję. Proszę "
"spróbować ponownie dokonać zakupu."

#: modules/thankyou/templates/instant-thankyou.php:41
msgid "Pay"
msgstr "Zapłać"

#: modules/thankyou/templates/instant-thankyou.php:43
msgid "My account"
msgstr "Moje konto"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:137
msgid "Select the Flow"
msgstr "Wybierz przepływ"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:147
msgid "Add to Cart text"
msgstr "Dodaj do koszyka tekst"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:149
msgid "Add to cart"
msgstr "Dodaj do koszyka"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:154
#. translators: %1$s,%2$s HTML content
msgid ""
"If you want to start the flow from the product page, select the appropriate flow & button text field if required. Refer "
"%1$sthis article%2$s for more information."
msgstr ""
"Jeśli chcesz rozpocząć proces z strony produktu, wybierz odpowiedni przepływ i pole tekstowe przycisku, jeśli jest to "
"wymagane. Zobacz %1$sten artykuł%2$s, aby uzyskać więcej informacji."

#: wizard/ajax/wizard.php:207
msgid "Please enter your email ID."
msgstr "Proszę wprowadzić swój identyfikator e-mail."

#: wizard/ajax/wizard.php:262
msgid "Oops! Something went wrong. Please refresh the page and try again."
msgstr "Ups! Coś poszło nie tak. Odśwież stronę i spróbuj ponownie."

#: wizard/ajax/wizard.php:363
msgid "Please select any of the page builder to display the ready templates."
msgstr "Proszę wybrać dowolny kreator stron, aby wyświetlić gotowe szablony."

#: wizard/ajax/wizard.php:502
msgid "No flow ID found. Please select atleast one flow to import."
msgstr "Nie znaleziono identyfikatora przepływu. Proszę wybrać co najmniej jeden przepływ do zaimportowania."

#: admin-core/ajax/importer.php:893
#: wizard/ajax/wizard.php:516
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$sarticle%3$s."
msgstr ""
"Błąd przekroczenia czasu żądania. Proszę sprawdzić, czy zapora sieciowa lub jakakolwiek wtyczka zabezpieczająca nie "
"blokuje wychodzących żądań HTTP/HTTPS do templates.cartflows.com. %1$sAby rozwiązać ten problem, proszę sprawdzić ten "
"%2$sartykuł%3$s."

#: wizard/ajax/wizard.php:539
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import this template, CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "Aby zaimportować ten szablon, wymagany jest CartFlows Pro! %1$sUaktualnij do CartFlows Pro%2$s"

#: wizard/ajax/wizard.php:540
#: wizard/ajax/wizard.php:542
msgid "CartFlows Pro Required"
msgstr "Wymagany CartFlows Pro"

#: wizard/ajax/wizard.php:546
msgid "Invalid License Key"
msgstr "Nieprawidłowy klucz licencyjny"

#: wizard/ajax/wizard.php:548
#. translators: %1$s: link html start, %2$s: link html end
msgid "No valid license key found! %1$sActivate license%2$s"
msgstr "Nie znaleziono prawidłowego klucza licencyjnego! %1$sAktywuj licencję%2$s"

#: wizard/inc/wizard-core.php:174
msgid "Thanks for installing and using CartFlows!"
msgstr "Dziękujemy za zainstalowanie i korzystanie z CartFlows!"

#: wizard/inc/wizard-core.php:175
msgid "It is easy to use the CartFlows. Please use the setup wizard to quick start setup."
msgstr "Korzystanie z CartFlows jest łatwe. Proszę użyć kreatora konfiguracji, aby szybko rozpocząć konfigurację."

#: wizard/inc/wizard-core.php:177
msgid "Start Wizard"
msgstr "Uruchom kreatora"

#: wizard/inc/wizard-core.php:178
msgid "Skip Setup"
msgstr "Pomiń konfigurację"

#: wizard/inc/wizard-core.php:394
msgid "Oops!! Unexpected error occoured"
msgstr "Ups!! Wystąpił nieoczekiwany błąd"

#: wizard/inc/wizard-core.php:395
msgid "Import template API call failed. Please reload the page and try again!"
msgstr "Importowanie szablonu API nie powiodło się. Proszę odświeżyć stronę i spróbować ponownie!"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Settings Saved"
msgstr "Ustawienia zapisane"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:53
msgid "Saving…"
msgstr "Zapisywanie…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:42
msgid "Save Settings"
msgstr "Zapisz ustawienia"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Regular Price of the product"
msgstr "Regularna cena produktu"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Price after discount."
msgstr "Cena po rabacie."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Product Name"
msgstr "Nazwa produktu"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{product_name}} and {{quantity}} to dynamically fetch respective product details."
msgstr "Użyj {{product_name}} i {{quantity}}, aby dynamicznie pobrać odpowiednie szczegóły produktu."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Subtext"
msgstr "Podtekst"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{quantity}}, {{discount_value}}, {{discount_percent}} to dynamically fetch respective product details."
msgstr "Użyj {{quantity}}, {{discount_value}}, {{discount_percent}}, aby dynamicznie pobrać odpowiednie szczegóły produktu."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Enable Highlight"
msgstr "Włącz podświetlenie"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Highlight Text"
msgstr "Podświetl tekst"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Create New Product"
msgstr "Utwórz nowy produkt"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add"
msgstr "Dodaj"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Once you have selected products, they will be displayed here."
msgstr "Gdy wybierzesz produkty, zostaną one wyświetlone tutaj."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Items"
msgstr "Przedmioty"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Quantity"
msgstr "Ilość"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Discount"
msgstr "Zniżka"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Adding…"
msgstr "Dodawanie…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Please search and select at-lease one product to add."
msgstr "Proszę wyszukać i wybrać co najmniej jeden produkt do dodania."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
#: wizard/assets/build/wizard-app.js:1
msgid "Reset"
msgstr "Resetuj"

#: admin-core/assets/build/editor-app.js:1
msgid "Image Preview"
msgstr "Podgląd obrazu"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "Upload a file"
msgstr "Prześlij plik"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "or drag and drop"
msgstr "lub przeciągnij i upuść"

#: admin-core/assets/build/editor-app.js:1
msgid "PNG, JPG, GIF up to 10MB"
msgstr "PNG, JPG, GIF do 10MB"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Select Dates"
msgstr "Wybierz daty"

#: admin-core/assets/build/editor-app.js:4
#: admin-core/assets/build/settings-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Pro %2$s to use %3$s feature."
msgstr "Proszę uaktualnić do %1$s CartFlows Pro %2$s, aby używać funkcji %3$s."

#: admin-core/assets/build/editor-app.js:7
#: admin-core/assets/build/settings-app.js:7
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Higher Plan %2$s to use %3$s feature."
msgstr "Proszę uaktualnić do %1$s wyższego planu CartFlows %2$s, aby używać funkcji %3$s."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
#. translators: %s is replaced with feature name
msgid "Please upgrade to the CartFlows Higher Plan to use the %s feature."
msgstr "Proszę uaktualnić do wyższego planu CartFlows, aby używać funkcji %s."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Role"
msgstr "Rola"

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Access"
msgstr "Dostęp"

#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/settings-app.js:11
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the user role management please %1$sClick here.%2$s"
msgstr "Aby uzyskać więcej informacji na temat zarządzania rolami użytkowników, proszę %1$sKliknij tutaj.%2$s"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
#. translators: %1$s is the selected product of CartFlows, %2$s is the selected version of CartFlows.
msgid "Are you sure you want to rollback to %1$s v%2$s?"
msgstr "Czy na pewno chcesz przywrócić do %1$s w wersji %2$s?"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback"
msgstr "Cofnij"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:14
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Cancel"
msgstr "Anuluj"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid ""
"Experiencing an issue with the current version of CartFlows? Roll back to a previous version to help troubleshoot the "
"problem."
msgstr "Masz problem z obecną wersją CartFlows? Cofnij się do poprzedniej wersji, aby pomóc w rozwiązaniu problemu."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Now"
msgstr "Regeneruj teraz"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:19
msgid "Reset Permalinks Settings"
msgstr "Zresetuj ustawienia bezpośrednich odnośników"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate License"
msgstr "Aktywuj licencję"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Deactivate License"
msgstr "Dezaktywuj licencję"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Please enter a valid license key!"
msgstr "Proszę wprowadzić prawidłowy klucz licencyjny!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
#: wizard/assets/build/wizard-app.js:4
msgid "Processing"
msgstr "Przetwarzanie"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unknown error occurred while activating the license."
msgstr "Wystąpił nieznany błąd podczas aktywacji licencji."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Facebook Pixel"
msgstr "Facebook Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Analytics Pixel"
msgstr "Pixel Google Analytics"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Ads Pixel"
msgstr "Pixel Google Ads"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Tiktok Pixel"
msgstr "Piksel Tiktok"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Pinterest Tag"
msgstr "Tag Pinteresta"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Snapchat Pixel"
msgstr "Snapchat Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Auto Address"
msgstr "Automatyczne adresowanie Google"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Inline CSS"
msgstr "Regeneruj wbudowany CSS"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid " Regenerating…."
msgstr "Ponowne generowanie…."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerated"
msgstr "Zregenerowany"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"If you are using the CartFlows Shortcode and using the Design Settings, then this option will regenerate the steps "
"inline CSS. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Jeśli używasz shortcode'u CartFlows i ustawień projektowania, ta opcja wygeneruje na nowo kroki w stylu CSS. Aby "
"dowiedzieć się więcej, %1$1s kliknij tutaj %2$2s."

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Updating"
msgstr "Aktualizowanie"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Permalinks reset successfully"
msgstr "Linki bezpośrednie zostały pomyślnie zresetowane"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the CartFlows Permalink settings please %1$sClick here.%2$s"
msgstr "Aby uzyskać więcej informacji na temat ustawień Permalink w CartFlows, %1$skliknij tutaj.%2$s"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Oops! You don't have access to this page."
msgstr "Ups! Nie masz dostępu do tej strony."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You don't have permission to access this page. Please reach out to your admin for help."
msgstr "Nie masz uprawnień do dostępu do tej strony. Skontaktuj się z administratorem, aby uzyskać pomoc."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Back to Dashboard"
msgstr "Powrót do pulpitu"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Integrations"
msgstr "Integracje"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "User Role Manager"
msgstr "Menedżer ról użytkownika"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Permalink"
msgstr "Stały link"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Version Control"
msgstr "Kontrola wersji"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Order"
msgstr "Zamówienie"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "License"
msgstr "Licencja"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Sync Knowledge Base"
msgstr "Synchronizuj bazę wiedzy"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Knowledge Base"
msgstr "Baza wiedzy"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Search knowledge base"
msgstr "Przeszukaj bazę wiedzy"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "No Docs Founds"
msgstr "Nie znaleziono dokumentów"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Please try syncing the docs library"
msgstr "Proszę spróbować zsynchronizować bibliotekę dokumentów"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Syncing…"
msgstr "Synchronizowanie…"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Synced. Reloading.."
msgstr "Zsynchronizowano. Ponowne ładowanie.."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Need Help?"
msgstr "Potrzebujesz pomocy?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "We aim to answer all priority support requests within 2-3 hours."
msgstr "Naszym celem jest odpowiadanie na wszystkie priorytetowe zgłoszenia wsparcia w ciągu 2-3 godzin."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Get Support"
msgstr "Uzyskaj wsparcie"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "All Documentation"
msgstr "Cała dokumentacja"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse documentation, reference material, and tutorials for CartFlows."
msgstr "Przeglądaj dokumentację, materiały referencyjne i samouczki dla CartFlows."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "View documentation"
msgstr "Wyświetl dokumentację"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Videos"
msgstr "Filmy"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse tutorial videos on our YouTube channel."
msgstr "Przeglądaj filmy instruktażowe na naszym kanale YouTube."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Youtube Channel"
msgstr "Kanał YouTube"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Support"
msgstr "Wsparcie"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "What's New?"
msgstr "Co nowego?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Unlicensed"
msgstr "Bez licencji"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Licensed"
msgstr "Licencjonowany"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Open Global Settings"
msgstr "Otwórz ustawienia globalne"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Tutorial Videos"
msgstr "Filmy instruktażowe"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:35
msgid "More Options"
msgstr "Więcej opcji"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:35
msgid "Control"
msgstr "Kontrola"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
#. translators: %d is replaced with the count
msgid "Variation-%d"
msgstr "Wariacja-%d"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "No Product Assigned"
msgstr "Brak przypisanego produktu"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Store Checkout - Remove selected checkout product"
msgstr "Kasa sklepu - Usuń wybrany produkt z kasy"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "PRO"
msgstr "PRO"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Accepted"
msgstr "Oferta zaakceptowana"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Rejected"
msgstr "Oferta odrzucona"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Invalid Position"
msgstr "Nieprawidłowa pozycja"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Views"
msgstr "Widoki"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Conversions"
msgstr "Konwersje"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Revenue"
msgstr "Przychody"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "Upgrade to Pro"
msgstr "Uaktualnij do wersji Pro"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:39
#: admin-core/assets/build/settings-app.js:42
#. translators: %s: step slug
msgid "%s Step"
msgstr "Krok %s"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid "Step Editing is Disabled"
msgstr "Edycja kroków jest wyłączona"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Open Settings"
msgstr "Otwórz Ustawienia"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Duplicate Step"
msgstr "Powtórz krok"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to duplicate this step? Are you sure?"
msgstr "Czy chcesz powielić ten krok? Czy jesteś pewien?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Delete Step"
msgstr "Usuń krok"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to delete this step? Are you sure?"
msgstr "Czy chcesz usunąć ten krok? Czy jesteś pewien?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Declare Winner"
msgstr "Ogłoś zwycięzcę"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to declare this step as winner? Are you sure?"
msgstr "Czy chcesz ogłosić ten krok jako zwycięzcę? Czy jesteś pewien?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Archive Step"
msgstr "Archiwizuj krok"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to archive this step? Are you sure?"
msgstr "Czy chcesz zarchiwizować ten krok? Czy jesteś pewien?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Saving.."
msgstr "Zapisywanie.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:53
msgid "Saved"
msgstr "Zapisano"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading.."
msgstr "Ponowne ładowanie.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Testing"
msgstr "Testowanie testu podziału"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stop Split Test"
msgstr "Zatrzymaj test podziału"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Start Split Test"
msgstr "Rozpocznij test podziału"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test"
msgstr "Test A/B"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stopping…"
msgstr "Zatrzymywanie…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Starting…"
msgstr "Rozpoczynanie…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading…"
msgstr "Przeładowywanie…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Settings"
msgstr "Ustawienia testu A/B"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Restore Archived Variation"
msgstr "Przywróć zarchiwizowaną wariację"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to restore this archived variation? Are you sure?"
msgstr "Czy chcesz przywrócić tę zarchiwizowaną wersję? Czy jesteś pewien?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:44
msgid "Trash Archived Variation"
msgstr "Wariant zarchiwowanych śmieci"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will trash this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Ta akcja trwale usunie tę zarchiwizowaną wariację i jej dane analityczne. %1$s Czy chcesz usunąć tę zarchiwizowaną "
"wariację?"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
msgid "Hide Archived Variation"
msgstr "Ukryj zarchiwizowaną wariację"

#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:44
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will hide this archived variation from the list of steps, but its analytics will be visible. %1$s Do you "
"want to hide this archived variation?"
msgstr ""
"Ta akcja ukryje tę zarchiwizowaną wariację z listy kroków, ale jej analizy będą widoczne. %1$s Czy chcesz ukryć tę "
"zarchiwizowaną wariację?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will delete this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Ta akcja trwale usunie tę zarchiwizowaną wariację i jej dane analityczne. %1$s Czy chcesz usunąć tę zarchiwizowaną "
"wariację?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Deleted On: "
msgstr "Usunięto dnia:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived On: "
msgstr "Zarchiwizowano dnia:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived Steps"
msgstr "Zarchiwizowane kroki"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Step Name"
msgstr "Edytuj nazwę kroku"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "Save"
msgstr "Zapisz"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Update Template"
msgstr "Zaktualizuj szablon"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Changing the template will permanently delete the current design in this step. Would you still like to proceed?"
msgstr "Zmiana szablonu spowoduje trwałe usunięcie bieżącego projektu na tym etapie. Czy nadal chcesz kontynuować?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Change Template"
msgstr "Zmień szablon"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If you are using shortcodes, enable this design settings to apply styles."
msgstr "Jeśli używasz skrótów, włącz te ustawienia projektowania, aby zastosować style."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Design Settings"
msgstr "Włącz ustawienia projektowania"

#: admin-core/assets/build/editor-app.js:15
#: admin-core/assets/build/settings-app.js:15
#. translators: %s is replaced with plugin name
msgid "Activate %s"
msgstr "Aktywuj %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
#. translators: %s is replaced with plugin name
msgid "Activating %s"
msgstr "Aktywowanie %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Activated!"
msgstr "Pomyślnie aktywowano!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Failed! Activation!"
msgstr "Niepowodzenie! Aktywacja!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Upgrade to Cartflows Pro"
msgstr "Uaktualnij do Cartflows Pro"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Activate the License"
msgstr "Aktywuj licencję"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Step Type: "
msgstr "Typ kroku:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "License is required!"
msgstr "Wymagana jest licencja!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Activate the license to modify this offer step's settings"
msgstr "Aktywuj licencję, aby zmodyfikować ustawienia tego kroku oferty"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product Selected"
msgstr "Nie wybrano produktu"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you select the product, they will be displayed here."
msgstr "Po wybraniu produktu, zostaną one wyświetlone tutaj."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping Rate"
msgstr "Stawka za wysyłkę"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you have add product, it will be displayed here."
msgstr "Gdy dodasz produkt, zostanie on wyświetlony tutaj."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Coupon will apply on checkout page"
msgstr "Kupon zostanie zastosowany na stronie kasy"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "AND"
msgstr "I"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the conditional order bump feature."
msgstr "Zaktualizuj CartFlows Pro do najnowszej wersji, aby móc korzystać z funkcji warunkowego zwiększenia zamówienia."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable conditional order bump "
msgstr "Włącz warunkowy dodatek do zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "By enabling this option, you can create the conditions to display the order bump."
msgstr "Włączając tę opcję, możesz stworzyć warunki do wyświetlenia dodatkowej oferty przy zamówieniu."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show this order bump if following conditions are true"
msgstr "Pokaż ten dodatek do zamówienia, jeśli spełnione są następujące warunki"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Condition"
msgstr "Dodaj warunek"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:33
#: admin-core/assets/build/settings-app.js:48
msgid "OR"
msgstr "LUB"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Conditions Group"
msgstr "Dodaj grupę warunków"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Bump Product Image"
msgstr "Obraz produktu dodatkowego zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Styles"
msgstr "Style"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Conditions"
msgstr "Warunki"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Save Changes"
msgstr "Zapisz zmiany"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "You have made changes. Do you want to save the changes?"
msgstr "Dokonałeś zmian. Czy chcesz zapisać zmiany?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product"
msgstr "Brak produktu"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Duplicate Order Bump"
msgstr "Zduplikuj Dodatek do Zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Order Bump"
msgstr "Edytuj Dodatek do Zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Trash Order Bump"
msgstr "Śmieciowy Dodatek do Zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Do you really want to trash this order bump permanently?"
msgstr "Czy na pewno chcesz trwale usunąć ten dodatek do zamówienia?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Delete Order Bump"
msgstr "Usuń dodatek do zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
msgid "Status"
msgstr "Status"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Actions"
msgstr "Akcje"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Create an order bump."
msgstr "Utwórz dodatkową ofertę przy zamówieniu."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enter order bump name"
msgstr "Wprowadź nazwę dodatkowej oferty"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please enter the order bump title"
msgstr "Proszę wprowadzić tytuł dodatkowego zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Preview"
msgstr "Podgląd"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "View in Full Screen"
msgstr "Wyświetl na pełnym ekranie"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Exit Full Screen"
msgstr "Zakończ tryb pełnoekranowy"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Submitted"
msgstr "Zamówienie złożone"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Special Offer"
msgstr "Oferta specjalna"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Receipt"
msgstr "Potwierdzenie zamówienia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Checkout Offer"
msgstr "Włącz ofertę przy kasie"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Dynamic Conditions"
msgstr "Dynamiczne warunki"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the dynamic offers feature."
msgstr "Zaktualizuj CartFlows Pro do najnowszej wersji, aby korzystać z funkcji dynamicznych ofert."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Dynamic Offers"
msgstr "Włącz oferty dynamiczne"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Redirect to: "
msgstr "Przekieruj do:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for step…"
msgstr "Szukaj kroku…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If the following conditions are true"
msgstr "Jeśli następujące warunki są prawdziwe"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Dynamic Offer"
msgstr "Dodaj ofertę dynamiczną"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for default step…"
msgstr "Szukaj domyślnego kroku…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If all of the above conditions failed."
msgstr "Jeśli wszystkie powyższe warunki zawiodły."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label"
msgstr "Etykieta"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "ID"
msgstr "ID"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable"
msgstr "Włącz"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add New Field"
msgstr "Dodaj nowe pole"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Date & Time"
msgstr "Data i czas"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Custom Field"
msgstr "Dodaj pole niestandardowe"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "TextArea"
msgstr "Pole tekstowe"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Number"
msgstr "Numer"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Checkbox"
msgstr "Pole wyboru"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Radio"
msgstr "Radio"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Select"
msgstr "Wybierz"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Hidden"
msgstr "Ukryty"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add To"
msgstr "Dodaj do"

#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
#. translators: %$s is replaced with the HTML tag
msgid "Label %1$s*%2$s"
msgstr "Etykieta %1$s*%2$s"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#. translators: %$s is replaced with the HTML tag
msgid ""
"Field value will store in this meta key. Add field id without prefix like \"billing_\" or \"shipping_\". %s Use \"_\" "
"instead of spaces."
msgstr ""
"Wartość pola zostanie zapisana w tym kluczu meta. Dodaj identyfikator pola bez prefiksu, takiego jak \"billing_\" lub "
"\"shipping_\". %s Użyj \"_\" zamiast spacji."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min Value"
msgstr "Wartość minimalna"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max Value"
msgstr "Maksymalna wartość"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enter your options separated by (|)."
msgstr "Wprowadź swoje opcje oddzielone znakiem (|)."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "UnChecked"
msgstr "Niezaznaczone"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Input Type"
msgstr "Typ pola wejściowego"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:50
msgid "Date"
msgstr "Data"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Time"
msgstr "Czas"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min "
msgstr "Min"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max "
msgstr "Max"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Show in Email"
msgstr "Pokaż w e-mailu"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Billing Fields"
msgstr "Pola rozliczeniowe"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Shipping Fields"
msgstr "Pola wysyłki"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Delete Field"
msgstr "Usuń pole"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Are you really want to delete field?"
msgstr "Czy na pewno chcesz usunąć pole?"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Editor"
msgstr "Edytor pól"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Label is required field"
msgstr "Etykieta jest polem wymaganym"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "WooCommerce is Required!"
msgstr "Wymagany jest WooCommerce!"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
#. translators: %s: step type
msgid "To modify the %s step options, please install and activate the WooCommerce plugin."
msgstr "Aby zmodyfikować opcje kroku %s, zainstaluj i aktywuj wtyczkę WooCommerce."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activating…"
msgstr "Aktywacja…"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activated"
msgstr "Aktywowany"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Funnel Settings"
msgstr "Ustawienia lejka"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Save Setting"
msgstr "Zapisz ustawienie"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Disable Store Checkout"
msgstr "Wyłącz kasę sklepową"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Enable Store Checkout"
msgstr "Włącz kasę sklepową"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Edit Title"
msgstr "Edytuj tytuł"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Publish or Draft the Funnel"
msgstr "Opublikuj lub zapisz szkic lejka"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Publish"
msgstr "Opublikuj"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to publish this funnel?"
msgstr "Czy na pewno chcesz opublikować ten lejek?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Draft Funnel"
msgstr "Szkic lejka"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to draft this funnel?"
msgstr "Czy na pewno chcesz zaprojektować ten lejek?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Unique Visits"
msgstr "Unikalne wizyty"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Conversion Rate"
msgstr "Wskaźnik konwersji"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total number of orders."
msgstr "Łączna liczba zamówień."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all orders."
msgstr "Łączna suma wszystkich zamówień."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Average total of every order."
msgstr "Średnia całkowita każdego zamówienia."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all order bumps."
msgstr "Łączna suma wszystkich dodatkowych zamówień."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Export Flow"
msgstr "Eksportuj przepływ"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "View Funnel"
msgstr "Wyświetl lejek"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Open Funnel Settings"
msgstr "Otwórz ustawienia lejka"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Delete Store Checkout"
msgstr "Usuń kasę sklepu"

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
#. translators: %s new line break
msgid "Do you really want to delete store checkout?%1$1sNOTE: This action cannot be reversed."
msgstr "Czy na pewno chcesz usunąć kasę sklepu?%1$1sUWAGA: Tej akcji nie można cofnąć."

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "archived_date"
msgstr "archived_date"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
#. translators: %1$s: html tag, %2$s: html tag
msgid "%1$sNote:%2$s The orders which are placed by the admins are not considered while calculating the analytics."
msgstr "%1$sUwaga:%2$s Zamówienia składane przez administratorów nie są uwzględniane przy obliczaniu analityki."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Reset Analytics"
msgstr "Zresetuj analitykę"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Are you really want to reset funnel analytics?"
msgstr "Czy na pewno chcesz zresetować analitykę lejka?"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Resetting"
msgstr "Resetowanie"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Automation for"
msgstr "Automatyzacja dla"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Create a distraction free, high-converting checkout experience without needing a page builder."
msgstr "Stwórz bezrozpraszające, wysoko konwertujące doświadczenie kasowe bez potrzeby korzystania z kreatora stron."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Funnel Steps"
msgstr "Kroki lejka"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add New Step"
msgstr "Dodaj nowy krok"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "No Steps Added."
msgstr "Nie dodano kroków."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Seems like there are no steps created or added in this flow"
msgstr "Wygląda na to, że w tym przepływie nie utworzono ani nie dodano żadnych kroków"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add new step"
msgstr "Dodaj nowy krok"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Select Step Type"
msgstr "Wybierz typ kroku"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Activating WooCommerce.."
msgstr "Aktywowanie WooCommerce.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installing WooCommerce.."
msgstr "Instalowanie WooCommerce.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Installed!"
msgstr "Pomyślnie zainstalowano!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installation Failed!"
msgstr "Instalacja nie powiodła się!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Step"
msgstr "Krok importu"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
msgid "Add multiple steps to your flows today with an upgraded CartFlows plan."
msgstr "Dodaj wiele kroków do swoich przepływów już dziś dzięki ulepszonemu planowi CartFlows."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
msgid "Get CartFlows Higher Plan"
msgstr "Uzyskaj wyższy plan CartFlows"

#: admin-core/assets/build/editor-app.js:44
#: admin-core/assets/build/editor-app.js:45
#: admin-core/assets/build/editor-app.js:54
#: admin-core/assets/build/editor-app.js:55
#: admin-core/assets/build/editor-app.js:59
#: admin-core/assets/build/editor-app.js:63
#: admin-core/assets/build/editor-app.js:64
#: admin-core/assets/build/settings-app.js:56
#: admin-core/assets/build/settings-app.js:57
#: admin-core/assets/build/settings-app.js:66
#: admin-core/assets/build/settings-app.js:67
#: admin-core/assets/build/settings-app.js:70
#: admin-core/assets/build/settings-app.js:74
#: admin-core/assets/build/settings-app.js:75
#. translators: %s is replaced with plugin name
msgid "Add multiple steps to your flows by activating %s."
msgstr "Dodaj wiele kroków do swoich przepływów, aktywując %s."

#: admin-core/assets/build/editor-app.js:46
#: admin-core/assets/build/editor-app.js:47
#: admin-core/assets/build/editor-app.js:56
#: admin-core/assets/build/editor-app.js:60
#: admin-core/assets/build/editor-app.js:65
#: admin-core/assets/build/settings-app.js:58
#: admin-core/assets/build/settings-app.js:59
#: admin-core/assets/build/settings-app.js:68
#: admin-core/assets/build/settings-app.js:71
#: admin-core/assets/build/settings-app.js:76
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating %2$s."
msgstr "Dodaj krok %1$s do swoich przepływów, aktywując %2$s."

#: admin-core/assets/build/editor-app.js:48
#: admin-core/assets/build/editor-app.js:49
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:60
#: admin-core/assets/build/settings-app.js:61
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click %s to your flows when you upgrade to our CartFlows Higher plan today."
msgstr ""
"Dodaj nieograniczoną liczbę jednorazowych %s zwiększających dochody do swoich przepływów, gdy dziś uaktualnisz do "
"naszego planu CartFlows Higher."

#: admin-core/assets/build/editor-app.js:50
#: admin-core/assets/build/settings-app.js:62
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flow by activating CartFlows license."
msgstr "Dodaj krok %1$s do swojego przepływu, aktywując licencję CartFlows."

#: admin-core/assets/build/editor-app.js:51
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:63
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s."
msgstr "Uzyskaj dostęp do wszystkich naszych profesjonalnych szablonów, aktywując %s."

#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Access all of our pro templates when you upgrade your plan to CartFlows Pro today."
msgstr "Uzyskaj dostęp do wszystkich naszych profesjonalnych szablonów, gdy dziś zaktualizujesz swój plan do CartFlows Pro."

#: admin-core/inc/admin-menu.php:318
#: admin-core/inc/admin-menu.php:319
#: classes/class-cartflows-admin.php:250
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Get CartFlows Pro"
msgstr "Pobierz CartFlows Pro"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating license."
msgstr "Dodaj krok %1$s do swoich przepływów, aktywując licencję."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:78
msgid "You need WooCommerce plugin installed and activated to import this step."
msgstr "Musisz mieć zainstalowaną i aktywowaną wtyczkę WooCommerce, aby zaimportować ten krok."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Imported! Redirecting…"
msgstr "Zaimportowano! Przekierowywanie…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid " Please sync the library and try importing the template again."
msgstr "Proszę zsynchronizować bibliotekę i spróbować ponownie zaimportować szablon."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Failed! Try again."
msgstr "Importowanie nie powiodło się! Spróbuj ponownie."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create Step"
msgstr "Utwórz krok"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Creating Step.."
msgstr "Tworzenie kroku.."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Step Created! Redirecting…"
msgstr "Krok utworzony! Przekierowywanie…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
msgid "Failed to Create Step!"
msgstr "Nie udało się utworzyć kroku!"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate license for adding more steps and other features."
msgstr "Aktywuj licencję, aby dodać więcej kroków i inne funkcje."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Close the window"
msgstr "Zamknij okno"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Name Your Step"
msgstr "Nazwij swój krok"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Step Name"
msgstr "Nazwa kroku"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Enter Step Name"
msgstr "Wprowadź nazwę kroku"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Learn How"
msgstr "Dowiedz się jak"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create from Scratch"
msgstr "Utwórz od podstaw"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Step thumbnail image"
msgstr "Miniatura obrazu kroku"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Import"
msgstr "Import"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
#: wizard/assets/build/wizard-app.js:1
msgid "Back"
msgstr "Wstecz"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/settings-app.js:34
msgid "Sync Library"
msgstr "Synchronizuj bibliotekę"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
#. translators: %d is replaced with the condition number
msgid "Importing page %d"
msgstr "Importowanie strony %d"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Sync Complete"
msgstr "Synchronizacja zakończona"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Syncing Library…"
msgstr "Synchronizowanie biblioteki…"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Library Synced"
msgstr "Biblioteka zsynchronizowana"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Steps Library"
msgstr "Biblioteka kroków"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:69
msgid "Get CartFlows Higher plan"
msgstr "Uzyskaj wyższy plan CartFlows"

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Activate license for adding more flows and other features."
msgstr "Aktywuj licencję, aby dodać więcej przepływów i inne funkcje."

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Importing Step.."
msgstr "Importowanie kroku.."

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Close"
msgstr "Zamknij"

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Error"
msgstr "Błąd"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click upsells to your flows by activating %s"
msgstr "Dodaj nieograniczoną liczbę jednorazowych upselli zwiększających dochody do swoich przepływów, aktywując %s"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
msgid "Add unlimited income boosting one-click upsells to your flows when you upgrade to our CartFlows Plus or Pro plan today."
msgstr ""
"Dodaj nieograniczoną liczbę jednorazowych upselli zwiększających dochody do swoich przepływów, gdy dziś zaktualizujesz "
"plan do CartFlows Plus lub Pro."

#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:79
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s"
msgstr "Uzyskaj dostęp do wszystkich naszych profesjonalnych szablonów, aktywując %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:80
#. translators: %s is replaced with the step title
msgid "Templates for %s"
msgstr "Szablony dla %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait…"
msgstr "Proszę czekać…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating funnel…"
msgstr "Proszę czekać. Duplikowanie lejka…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Drafting funnel…"
msgstr "Proszę czekać. Tworzenie lejka..."

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting funnel…"
msgstr "Proszę czekać. Usuwanie lejka…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Restoring funnel…"
msgstr "Proszę czekać. Przywracanie lejka…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Exporting…"
msgstr "Proszę czekać. Eksportowanie…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating step…"
msgstr "Proszę czekać. Powielanie kroku…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting step…"
msgstr "Proszę czekać. Usuwanie kroku…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Creating variation…"
msgstr "Proszę czekać. Tworzenie wariacji…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Archiving variation…"
msgstr "Proszę czekać. Archiwizowanie wariantu…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Declaring winner…"
msgstr "Proszę czekać. Ogłaszanie zwycięzcy…"

#: admin-core/assets/build/settings-app.js:23
msgid "Getting Started"
msgstr "Rozpoczęcie"

#: admin-core/assets/build/settings-app.js:23
msgid "Introduction to CartFlows"
msgstr "Wprowadzenie do CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Modernizing WordPress eCommerce!"
msgstr "Modernizacja eCommerce na WordPress!"

#: admin-core/assets/build/settings-app.js:23
msgid "Create Your First Flow"
msgstr "Utwórz swój pierwszy przepływ"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Setup Wizard"
msgstr "Przejdź do kreatora konfiguracji"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:34
msgid "Import Funnel"
msgstr "Importuj lejek"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Click for more info"
msgstr "Kliknij, aby uzyskać więcej informacji"

#: admin-core/assets/build/settings-app.js:23
msgid "You need WooCommerce plugin installed and activated to import this funnel."
msgstr "Musisz mieć zainstalowaną i aktywowaną wtyczkę WooCommerce, aby zaimportować ten lejek."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates by activating CartFlows Pro."
msgstr "Uzyskaj dostęp do wszystkich naszych profesjonalnych szablonów, aktywując CartFlows Pro."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates when you activate CartFlows Pro license."
msgstr "Uzyskaj dostęp do wszystkich naszych profesjonalnych szablonów po aktywacji licencji CartFlows Pro."

#: admin-core/assets/build/settings-app.js:23
msgid "Importing Complete Funnel.."
msgstr "Importowanie kompletnego lejka.."

#: admin-core/assets/build/settings-app.js:23
msgid "Design Your Funnel"
msgstr "Zaprojektuj swój lejek"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Created! Redirecting…"
msgstr "Utworzono! Przekierowywanie…"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Failed to Create Flow!"
msgstr "Nie udało się utworzyć przepływu!"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To CartFlows Pro"
msgstr "Ulepsz do CartFlows Pro"

#: admin-core/assets/build/settings-app.js:23
msgid "Name Your Funnel"
msgstr "Nazwij swój lejek"

#: admin-core/assets/build/settings-app.js:23
msgid "Funnel Name"
msgstr "Nazwa lejka"

#: admin-core/assets/build/settings-app.js:23
msgid "Enter Funnel Name"
msgstr "Wprowadź nazwę lejka"

#: admin-core/assets/build/settings-app.js:24
msgid "Welcome to CartFlows "
msgstr "Witamy w CartFlows"

#: admin-core/assets/build/settings-app.js:24
msgid "Sales funnel builder turns your WordPress website into an optimized selling machine."
msgstr "Kreator lejków sprzedażowych zamienia Twoją stronę WordPress w zoptymalizowaną maszynę sprzedażową."

#: admin-core/assets/build/settings-app.js:24
msgid "Create Your First Funnel"
msgstr "Utwórz swój pierwszy lejek"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"A sales funnel is the sequence of steps a buyer takes to make a purchase. CartFlows helps optimize funnels to turn "
"visitors into customers."
msgstr ""
"Lejek sprzedażowy to sekwencja kroków, które podejmuje kupujący, aby dokonać zakupu. CartFlows pomaga optymalizować "
"lejki, aby zamieniać odwiedzających w klientów."

#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
msgid "Create New Funnel"
msgstr "Utwórz nowy lejek"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views"
msgstr "Łączna liczba wyświetleń strony"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Revenue"
msgstr "Całkowity przychód"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Orders"
msgstr "Łączna liczba zamówień"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue"
msgstr "Przychody z oferty"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Views"
msgstr "Łączna liczba wyświetleń"

#: admin-core/assets/build/settings-app.js:24
msgid "Overview"
msgstr "Przegląd"

#: admin-core/assets/build/settings-app.js:24
msgid "WooCommerce plugin is required."
msgstr "Wtyczka WooCommerce jest wymagana."

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the overview"
msgstr "Musisz mieć zainstalowaną i aktywowaną wtyczkę WooCommerce, aby zobaczyć przegląd"

#: admin-core/assets/build/settings-app.js:24
msgid "Recent Orders"
msgstr "Ostatnie zamówienia"

#: admin-core/assets/build/settings-app.js:24
msgid "View All"
msgstr "Zobacz wszystkie"

#: admin-core/assets/build/settings-app.js:24
msgid "Customer"
msgstr "Klient"

#: admin-core/assets/build/settings-app.js:24
msgid "Payment Method"
msgstr "Metoda płatności"

#: admin-core/assets/build/settings-app.js:24
msgid "Value"
msgstr "Wartość"

#: admin-core/assets/build/settings-app.js:24
msgid "at"
msgstr "przy"

#: admin-core/assets/build/settings-app.js:24
msgid "Find recent order here"
msgstr "Znajdź ostatnie zamówienie tutaj"

#: admin-core/assets/build/settings-app.js:24
msgid "Once you have received orders, come back here to find it again easily"
msgstr "Gdy otrzymasz zamówienia, wróć tutaj, aby łatwo je znaleźć ponownie"

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the recent orders"
msgstr "Musisz mieć zainstalowaną i aktywowaną wtyczkę WooCommerce, aby wyświetlić ostatnie zamówienia"

#: admin-core/assets/build/settings-app.js:24
msgid "Quick Actions"
msgstr "Szybkie akcje"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Funnel"
msgstr "Utwórz lejek"

#: admin-core/assets/build/settings-app.js:80
msgid "Analytics"
msgstr "Analityka"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Product"
msgstr "Utwórz produkt"

#: admin-core/assets/build/settings-app.js:24
msgid "Create new Product"
msgstr "Utwórz nowy produkt"

#: admin-core/assets/build/settings-app.js:24
msgid "All Funnels"
msgstr "Wszystkie lejki"

#: admin-core/assets/build/settings-app.js:24
msgid "View all funnels"
msgstr "Zobacz wszystkie leje"

#: admin-core/assets/build/settings-app.js:24
msgid "Previous"
msgstr "Poprzedni"

#: admin-core/assets/build/settings-app.js:24
#: wizard/assets/build/wizard-app.js:1
msgid "Next"
msgstr "Dalej"

#: admin-core/assets/build/settings-app.js:24
msgid "First"
msgstr "Pierwszy"

#: admin-core/assets/build/settings-app.js:24
msgid "Last"
msgstr "Ostatni"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"You can specify a file to import by either dragging it into the drag and drop area.(Maximum file size of 5MB; .json "
"file extensions only.)"
msgstr ""
"Możesz określić plik do importu, przeciągając go do obszaru przeciągania i upuszczania. (Maksymalny rozmiar pliku to 5 "
"MB; tylko rozszerzenia plików .json.)"

#: admin-core/assets/build/settings-app.js:24
msgid "Change a file"
msgstr "Zmień plik"

#: admin-core/assets/build/settings-app.js:24
msgid "JSON file up to 5MB"
msgstr "Plik JSON do 5MB"

#: admin-core/assets/build/settings-app.js:25
#. translators: %s is replaced with the file name.
msgid "File Selected: %s"
msgstr "Wybrany plik: %s"

#: admin-core/assets/build/settings-app.js:25
msgid "Please select the valid json file."
msgstr "Proszę wybrać prawidłowy plik json."

#: admin-core/assets/build/settings-app.js:25
#: wizard/assets/build/wizard-app.js:5
msgid "Importing.."
msgstr "Importowanie.."

#: admin-core/assets/build/settings-app.js:25
msgid "Export All"
msgstr "Eksportuj wszystko"

#: admin-core/assets/build/settings-app.js:25
msgid "Exporting…"
msgstr "Eksportowanie…"

#: admin-core/assets/build/settings-app.js:25
msgid "Search Funnels"
msgstr "Lejki wyszukiwania"

#: admin-core/assets/build/settings-app.js:25
msgid "Filter Funnels by Date"
msgstr "Filtruj lejki według daty"

#: admin-core/assets/build/settings-app.js:25
msgid "Publish "
msgstr "Opublikuj"

#: admin-core/assets/build/settings-app.js:25
msgid "Draft "
msgstr "Szkic"

#: admin-core/assets/build/settings-app.js:25
msgid "Trash "
msgstr "Śmieci"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:33
msgid "Mode"
msgstr "Tryb"

#: admin-core/assets/build/settings-app.js:25
msgid "All"
msgstr "Wszystko"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
msgid "Live"
msgstr "Na żywo"

#: admin-core/assets/build/settings-app.js:25
msgid "SandBox"
msgstr "Piaskownica"

#: admin-core/assets/build/settings-app.js:25
msgid "Reset Filters"
msgstr "Resetuj filtry"

#: admin-core/assets/build/settings-app.js:28
#. translators: %s: action name.
msgid "%s This Flow"
msgstr "%s Ten przepływ"

#: admin-core/assets/build/settings-app.js:31
#. translators: %s: action status name.
msgid "Do you want to %s this flow? Are you sure?"
msgstr "Czy chcesz %s ten przepływ? Czy jesteś pewien?"

#: admin-core/assets/build/settings-app.js:31
msgid "items selected"
msgstr "wybrane elementy"

#: admin-core/assets/build/settings-app.js:31
msgid "Applying changes…"
msgstr "Trwa stosowanie zmian…"

#: admin-core/assets/build/settings-app.js:31
msgid " Published "
msgstr "Opublikowano"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate Funnel"
msgstr "Zduplikuj lejek"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to duplicate this funnel?"
msgstr "Czy na pewno chcesz zduplikować ten lejek?"

#: admin-core/assets/build/settings-app.js:31
msgid "Trash Funnel"
msgstr "Lejek na śmieci"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this funnel?"
msgstr "Czy na pewno chcesz usunąć ten lejek?"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this Funnel?"
msgstr "Czy na pewno chcesz usunąć ten lejek?"

#: admin-core/assets/build/settings-app.js:31
msgid "Restore Funnel"
msgstr "Przywróć lejek"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to restore this funnel?"
msgstr "Czy na pewno chcesz przywrócić ten lejek?"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Draft"
msgstr "Szkic"

#: admin-core/assets/build/settings-app.js:32
#. translators: %s date
msgid "Last Modified: %s"
msgstr "Ostatnia modyfikacja: %s"

#: admin-core/assets/build/settings-app.js:32
msgid "Updated "
msgstr "Zaktualizowano"

#: admin-core/assets/build/settings-app.js:32
msgid "Sandbox"
msgstr "Piaskownica"

#: admin-core/assets/build/settings-app.js:32
msgid "WooCommerce Required to display the revenue."
msgstr "WooCommerce jest wymagany do wyświetlania przychodów."

#: admin-core/assets/build/settings-app.js:32
msgid "Restore Flow"
msgstr "Przywróć przepływ"

#: admin-core/assets/build/settings-app.js:32
msgid "Delete Flow"
msgstr "Usuń przepływ"

#: admin-core/assets/build/settings-app.js:32
msgid "Upgrade to Pro for this feature."
msgstr "Ulepsz do wersji Pro, aby uzyskać tę funkcję."

#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate (Pro)"
msgstr "Duplikat (Pro)"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash Flow"
msgstr "Przepływ odpadów"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash"
msgstr "Śmieci"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Name"
msgstr "Nazwa"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Sales"
msgstr "Sprzedaż"

#: admin-core/assets/build/settings-app.js:32
msgid "Move to Trash"
msgstr "Przenieś do kosza"

#: admin-core/assets/build/settings-app.js:33
#. translators: %d Search term
msgid "No matching results found for the search term \"%s\"."
msgstr "Nie znaleziono pasujących wyników dla wyszukiwanego hasła \"%s\"."

#: admin-core/assets/build/settings-app.js:33
msgid "No flows found for the selected filter."
msgstr "Nie znaleziono przepływów dla wybranego filtra."

#: admin-core/assets/build/settings-app.js:33
msgid "Please try using different keywords, date range, or filters to refine your results."
msgstr "Spróbuj użyć innych słów kluczowych, zakresu dat lub filtrów, aby doprecyzować wyniki."

#: admin-core/assets/build/settings-app.js:33
msgid "Create New"
msgstr "Utwórz nowy"

#: admin-core/assets/build/settings-app.js:34
#. translators: %d flow count
msgid " %d items"
msgstr "%d przedmioty"

#: admin-core/assets/build/settings-app.js:34
msgid "Create your first funnel"
msgstr "Utwórz swój pierwszy lejek"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Build a sales funnel with everything you need to generate leads and grow sales."
msgstr "Zbuduj lej sprzedażowy ze wszystkim, czego potrzebujesz, aby generować leady i zwiększać sprzedaż."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "One Click Upsells"
msgstr "Jednoklikowe upselle"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "A/B Split Testing"
msgstr "Testowanie A/B"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Conversion Templates"
msgstr "Szablony konwersji"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Editor"
msgstr "Edytor kasy"

#: admin-core/assets/build/settings-app.js:34
msgid "Insights"
msgstr "Spostrzeżenia"

#: admin-core/assets/build/settings-app.js:34
msgid "Create Funnel"
msgstr "Utwórz lejek"

#: admin-core/assets/build/settings-app.js:34
msgid "Plugin Required"
msgstr "Wtyczka wymagana"

#: admin-core/assets/build/settings-app.js:34
msgid "You need WooCommerce plugin installed and activated to access this page."
msgstr "Musisz mieć zainstalowaną i aktywowaną wtyczkę WooCommerce, aby uzyskać dostęp do tej strony."

#: admin-core/assets/build/settings-app.js:34
msgid "Installing"
msgstr "Instalowanie"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
msgid "Activating"
msgstr "Aktywacja"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Failed"
msgstr "Niepowodzenie"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Redirecting"
msgstr "Przekierowywanie"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Create Store Checkout"
msgstr "Utwórz kasę sklepową"

#: admin-core/assets/build/settings-app.js:34
msgid "Name Your Store Checkout"
msgstr "Nazwij swój sklep Kasa"

#: admin-core/assets/build/settings-app.js:34
msgid "You can't create more than 3 flows in free version. Upgrade to CartFlows Pro for adding more flows and other features."
msgstr ""
"Nie możesz utworzyć więcej niż 3 przepływy w wersji darmowej. Uaktualnij do CartFlows Pro, aby dodać więcej przepływów "
"i inne funkcje."

#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To Cartflows Pro"
msgstr "Ulepsz do Cartflows Pro"

#: admin-core/assets/build/settings-app.js:34
msgid "Store Checkout Name"
msgstr "Nazwa kasy sklepowej"

#: admin-core/assets/build/settings-app.js:34
msgid "Enter Store Checkout Name"
msgstr "Wprowadź nazwę kasy sklepowej"

#: admin-core/assets/build/settings-app.js:34
msgid "Create a global store checkout"
msgstr "Utwórz globalną kasę sklepową"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"A well-designed checkout page can help streamline the checkout process, reduce cart abandonment rates and increase "
"conversions."
msgstr ""
"Dobrze zaprojektowana strona kasy może pomóc usprawnić proces realizacji zamówienia, zmniejszyć wskaźniki porzucania "
"koszyka i zwiększyć konwersje."

#: admin-core/assets/build/settings-app.js:34
msgid "Improved user experience"
msgstr "Ulepszona obsługa użytkownika"

#: admin-core/assets/build/settings-app.js:34
msgid "Brand consistency"
msgstr "Spójność marki"

#: admin-core/assets/build/settings-app.js:34
msgid "Increased trust and credibility"
msgstr "Zwiększone zaufanie i wiarygodność"

#: admin-core/assets/build/settings-app.js:34
msgid "Flexibility and customization"
msgstr "Elastyczność i dostosowanie"

#: admin-core/assets/build/settings-app.js:34
msgid "Competitive advantage"
msgstr "Przewaga konkurencyjna"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"By setting up the store checkout, your default checkout page will be replaced by the CartFlows modern checkout which "
"will lead to more conversion and leads."
msgstr ""
"Konfigurując kasę sklepu, Twoja domyślna strona kasy zostanie zastąpiona nowoczesną kasą CartFlows, co doprowadzi do "
"większej liczby konwersji i leadów."

#: admin-core/assets/build/settings-app.js:34
msgid "Get Started"
msgstr "Rozpocznij"

#: admin-core/assets/build/settings-app.js:34
msgid "Connect a Payment Gateway"
msgstr "Połącz bramkę płatności"

#: admin-core/assets/build/settings-app.js:34
msgid "Stripe for WooCommerce delivers a simple, secure way to accept credit card payments in your WooCommerce store."
msgstr ""
"Stripe dla WooCommerce zapewnia prosty i bezpieczny sposób akceptowania płatności kartą kredytową w Twoim sklepie "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:34
msgid "Connect with Stripe"
msgstr "Połącz z Stripe"

#: admin-core/assets/build/settings-app.js:34
msgid "Setting up…"
msgstr "Trwa konfigurowanie…"

#: admin-core/assets/build/settings-app.js:34
msgid "Recover Abandoned Carts"
msgstr "Odzyskaj porzucone koszyki"

#: admin-core/assets/build/settings-app.js:34
msgid "Use our cart abandonment plugin and automatically recover your lost revenue absolutely free."
msgstr "Użyj naszego wtyczki do porzucania koszyka i automatycznie odzyskaj utracone przychody całkowicie za darmo."

#: admin-core/assets/build/settings-app.js:34
msgid "Finishing…"
msgstr "Kończenie…"

#: admin-core/assets/build/settings-app.js:34
msgid "Setup Email Reports"
msgstr "Skonfiguruj raporty e-mailowe"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send you an email report with "
"key metrics and insights."
msgstr ""
"Pozwól CartFlows wyeliminować zgadywanie z wyników twojego procesu zakupu. Każdego tygodnia twój sklep wyśle ci raport "
"e-mailowy z kluczowymi wskaźnikami i spostrzeżeniami."

#: admin-core/assets/build/settings-app.js:34
msgid "Add Email Address"
msgstr "Dodaj adres e-mail"

#: admin-core/assets/build/settings-app.js:34
msgid "Dismiss Setup"
msgstr "Zamknij konfigurację"

#: admin-core/assets/build/settings-app.js:34
msgid "Active"
msgstr "Aktywny"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Activate"
msgstr "Aktywuj"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Install"
msgstr "Zainstaluj"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Installing…"
msgstr "Instalowanie…"

#: admin-core/assets/build/settings-app.js:34
msgid "Installed"
msgstr "Zainstalowany"

#: admin-core/assets/build/settings-app.js:34
msgid "Let's Go"
msgstr "Chodźmy"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Plugins"
msgstr "Zalecane wtyczki"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Themes"
msgstr "Zalecane motywy"

#: admin-core/assets/build/settings-app.js:34
msgid "View All Steps"
msgstr "Zobacz wszystkie kroki"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel thumbnail image"
msgstr "Miniatura obrazu lejka"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel Preview"
msgstr "Podgląd lejka"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Live Preview"
msgstr "Podgląd na żywo"

#: admin-core/assets/build/settings-app.js:34
msgid "Funnel Templates"
msgstr "Szablony lejków"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Start from scratch"
msgstr "Zacznij od zera"

#: admin-core/assets/build/settings-app.js:34
msgid "It seems that the page builder you selected is inactive."
msgstr "Wygląda na to, że wybrany przez Ciebie kreator stron jest nieaktywny."

#: admin-core/assets/build/settings-app.js:34
msgid " to see CartFlows templates. If you prefer another page builder tool, you can "
msgstr "aby zobaczyć szablony CartFlows. Jeśli wolisz inne narzędzie do tworzenia stron, możesz"

#: admin-core/assets/build/settings-app.js:34
msgid "select it here"
msgstr "wybierz to tutaj"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using any other page builder? No worries. CartFlows works well with every other page builder. Right now we do "
"not have ready templates for every page builder but we are planning to add it very soon."
msgstr ""
"Czy używasz innego kreatora stron? Nie martw się. CartFlows działa dobrze z każdym innym kreatorem stron. Obecnie nie "
"mamy gotowych szablonów dla każdego kreatora stron, ale planujemy je dodać wkrótce."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Learn How "
msgstr "Dowiedz się jak"

#: admin-core/assets/build/settings-app.js:35
msgid "No Results Found."
msgstr "Nie znaleziono wyników."

#: admin-core/assets/build/settings-app.js:35
msgid "Don't see a funnel that you would like to import?"
msgstr "Nie widzisz lejka, który chciałbyś zaimportować?"

#: admin-core/assets/build/settings-app.js:35
msgid "Please suggest us "
msgstr "Proszę zasugeruj nam"

#: admin-core/assets/build/settings-app.js:35
msgid "Choose a Funnel Templates"
msgstr "Wybierz szablony lejka"

#: admin-core/assets/build/settings-app.js:35
msgid "Search Templates"
msgstr "Wyszukaj szablony"

#: admin-core/assets/build/settings-app.js:35
msgid "Start from Scratch"
msgstr "Zacznij od zera"

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the Bricks Builder."
msgstr "Wygląda na to, że używasz Bricks Builder."

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the page builder other than Elementor, Beaver Builder, Block Builder."
msgstr "Wygląda na to, że używasz kreatora stron innego niż Elementor, Beaver Builder, Block Builder."

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using Bricks Builder? No worries. CartFlows works well with Bricks Builder. Right now we do not have ready "
"templates for Bricks Builder but we are planning to add it very soon."
msgstr ""
"Czy używasz Bricks Builder? Nie martw się. CartFlows działa dobrze z Bricks Builder. Obecnie nie mamy gotowych "
"szablonów dla Bricks Builder, ale planujemy je dodać wkrótce."

#: admin-core/assets/build/settings-app.js:35
msgid "Checkout Page"
msgstr "Strona kasy"

#: admin-core/assets/build/settings-app.js:35
msgid "Oops!!! No template Found."
msgstr "Ups!!! Nie znaleziono szablonu."

#: admin-core/assets/build/settings-app.js:35
msgid "Seems like no template is available for chosen editor."
msgstr "Wygląda na to, że dla wybranego edytora nie ma dostępnego szablonu."

#: admin-core/assets/build/settings-app.js:35
msgid "Store Checkout Templates"
msgstr "Szablony kasy sklepowej"

#: admin-core/assets/build/settings-app.js:80
msgid "No CartFlows Logs Found."
msgstr "Nie znaleziono logów CartFlows."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Logs"
msgstr "Dzienniki CartFlows"

#: admin-core/assets/build/settings-app.js:80
msgid "Copied"
msgstr "Skopiowano"

#: admin-core/assets/build/settings-app.js:80
msgid "Copy"
msgstr "Kopiuj"

#: admin-core/assets/build/settings-app.js:80
msgid "Downloading"
msgstr "Pobieranie"

#: admin-core/assets/build/settings-app.js:80
msgid "Download"
msgstr "Pobierz"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleting"
msgstr "Usuwanie"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleted"
msgstr "Usunięto"

#: admin-core/assets/build/settings-app.js:80
msgid "Email Marketing Automation"
msgstr "Automatyzacja e-mail marketingu"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate email marketing campaigns based on customer actions, such as abandoned carts or completed purchases in "
"WooCommerce."
msgstr ""
"Zautomatyzuj kampanie e-mailowe na podstawie działań klientów, takich jak porzucone koszyki lub zrealizowane zakupy w "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Customer Birthday Campaigns"
msgstr "Kampanie urodzinowe dla klientów"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automatically send personalized birthday offers or discounts to customers based on their birthdate stored in "
"WooCommerce."
msgstr ""
"Automatycznie wysyłaj spersonalizowane oferty urodzinowe lub zniżki do klientów na podstawie ich daty urodzenia "
"zapisanej w WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Order Notification"
msgstr "Powiadomienie o zamówieniu"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Receive instant notifications via SMS, Slack, WhatsApp, or messaging apps when new orders are placed in your "
"WooCommerce store."
msgstr ""
"Otrzymuj natychmiastowe powiadomienia przez SMS, Slack, WhatsApp lub aplikacje do przesyłania wiadomości, gdy w Twoim "
"sklepie WooCommerce zostaną złożone nowe zamówienia."

#: admin-core/assets/build/settings-app.js:80
msgid "Payment and Accounting Integration"
msgstr "Integracja płatności i księgowości"

#: admin-core/assets/build/settings-app.js:80
msgid "Sync WooCommerce sales data with your accounting software for streamlined financial management."
msgstr "Synchronizuj dane sprzedaży WooCommerce z oprogramowaniem księgowym, aby usprawnić zarządzanie finansami."

#: admin-core/assets/build/settings-app.js:80
msgid "Coupon Code Marketing"
msgstr "Marketing kodów rabatowych"

#: admin-core/assets/build/settings-app.js:80
msgid "Automate the creation and distribution of coupon codes based on specific conditions or customer actions in WooCommerce."
msgstr "Zautomatyzuj tworzenie i dystrybucję kodów kuponów na podstawie określonych warunków lub działań klientów w WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Upsell and Cross-sell Campaigns"
msgstr "Kampanie upsellingowe i cross-sellingowe"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate targeted upsell and cross-sell offers based on customers' purchase history or product interactions in "
"WooCommerce."
msgstr ""
"Zautomatyzuj ukierunkowane oferty upsell i cross-sell na podstawie historii zakupów klientów lub interakcji z "
"produktami w WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Connect Your Website"
msgstr "Połącz swoją stronę internetową"

#: admin-core/assets/build/settings-app.js:80
msgid "Reloading"
msgstr "Przeładowanie"

#: admin-core/assets/build/settings-app.js:80
msgid "Connecting"
msgstr "Łączenie"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate WooCommerce and CartFlows with Anything"
msgstr "Zintegruj WooCommerce i CartFlows z czymkolwiek"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate all your apps, plugins, and services to automate repetitive tasks."
msgstr "Zintegruj wszystkie swoje aplikacje, wtyczki i usługi, aby zautomatyzować powtarzalne zadania."

#: admin-core/assets/build/settings-app.js:80
msgid "These are just some examples. The possibilities are truly endless!"
msgstr "To tylko kilka przykładów. Możliwości są naprawdę nieograniczone!"

#: admin-core/assets/build/settings-app.js:80
msgid "Trusted by World's Top Brands to Connect Their Apps"
msgstr "Zaufany przez najlepsze światowe marki do łączenia ich aplikacji"

#: admin-core/assets/build/settings-app.js:80
msgid "Connect your apps and automate your business."
msgstr "Połącz swoje aplikacje i zautomatyzuj swój biznes."

#: modules/gutenberg/build/blocks-placeholder.js:12
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail Form"
msgstr "Formularz szczegółów zamówienia"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Overview."
msgstr "Ukryj/Pokaż podsumowanie zamówienia."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail"
msgstr "Szczegóły zamówienia"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Detail."
msgstr "Ukryj/Pokaż szczegóły zamówienia."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Billing Address."
msgstr "Ukryj/Pokaż adres rozliczeniowy."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Shipping Address."
msgstr "Ukryj/Pokaż adres wysyłki."

#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text"
msgstr "Tekst nagłówka"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading Bottom Spacing(px)"
msgstr "Odstęp dolny nagłówka (px)"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Opacity"
msgstr "Nieprzezroczystość"

#: modules/gutenberg/build/blocks.js:7
msgid "Section Spacing"
msgstr "Odstępy sekcji"

#: modules/gutenberg/build/blocks.js:7
msgid "Download Details"
msgstr "Szczegóły pobierania"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Loading"
msgstr "Ładowanie"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Order Detail Form Block"
msgstr "Blok formularza szczegółów zamówienia CartFlows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cartflows"
msgstr "cartflows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "order detail form"
msgstr "formularz szczegółów zamówienia"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cf"
msgstr "cf"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Color"
msgstr "Kolor ikony"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Hover Color"
msgstr "Kolor ikony po najechaniu"

#: modules/gutenberg/build/blocks.js:11
msgid "Gap Between Icon And Text"
msgstr "Odstęp między ikoną a tekstem"

#: modules/gutenberg/build/blocks.js:11
msgid "Subtitle"
msgstr "Podtytuł"

#: modules/gutenberg/build/blocks.js:11
msgid "Enable Subtitle"
msgstr "Włącz napisy"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Bottom Spacing"
msgstr "Odstęp dolny tytułu"

#: modules/gutenberg/build/blocks.js:11
msgid "SubTitle"
msgstr "Podtytuł"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Add text…"
msgstr "Dodaj tekst…"

#: modules/gutenberg/build/blocks.js:11
msgid "CartFlows Next Step Button Block."
msgstr "Blok przycisku następnego kroku CartFlows."

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "next step button"
msgstr "przycisk następny krok"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step ( Pro )"
msgstr "Two Step ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout ( Pro )"
msgstr "Wieloetapowa realizacja zamówienia ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This feature is available in the CartFlows higher plan. Upgrade Now!."
msgstr "Uwaga: Ta funkcja jest dostępna w wyższym planie CartFlows. Ulepsz teraz!."

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Skin"
msgstr "Skórka pola wejściowego"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Field"
msgstr "Pole wejściowe"

#: modules/gutenberg/build/blocks.js:11
msgid "Field Text Color"
msgstr "Kolor tekstu pola"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Validation"
msgstr "Walidacja pola wejściowego"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This styling can be only seen at frontend"
msgstr "Uwaga: Ten styl można zobaczyć tylko na froncie"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Horizontal"
msgstr "Poziomy"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Vertical"
msgstr "Pionowy"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Blur"
msgstr "Rozmycie"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spread"
msgstr "Rozprzestrzeniaj"

#: modules/gutenberg/build/blocks.js:11
msgid "Buttons Text"
msgstr "Tekst przycisków"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Color"
msgstr "Kolor tytułu"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Background Color"
msgstr "Kolor tła tytułu"

#: modules/gutenberg/build/blocks.js:11
msgid "Desc Background Color"
msgstr "Kolor tła opisu"

#: modules/gutenberg/build/blocks.js:11
msgid "Success/Error Message"
msgstr "Komunikat sukcesu/błędu"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Message Color"
msgstr "Kolor wiadomości"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label"
msgstr "Etykieta pływająca"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Skin"
msgstr "Skóra wejściowa"

#: modules/gutenberg/build/blocks.js:11
msgid "Skin"
msgstr "Skóra"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide Advanced"
msgstr "Ukryj zaawansowane"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Line Height"
msgstr "Wysokość linii"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color Settings"
msgstr "Ustawienia kolorów"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Overlay Color"
msgstr "Kolor nakładki"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Inset"
msgstr "Wstawka"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Outset"
msgstr "Początek"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing Between Sections(px)"
msgstr "Odstępy między sekcjami (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Type"
msgstr "Typ tła"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image"
msgstr "Obraz"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Image"
msgstr "Obraz tła"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Background Image"
msgstr "Wybierz obraz tła"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Replace image"
msgstr "Zastąp obraz"

#: admin-core/assets/build/editor-app.js:1
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Remove Image"
msgstr "Usuń obraz"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image Position"
msgstr "Pozycja obrazu"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Left"
msgstr "Lewy górny"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Center"
msgstr "Góra środek"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Right"
msgstr "Prawy górny"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Left"
msgstr "Centrum Lewo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Center"
msgstr "Centrum Centrum"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Right"
msgstr "Centrum Prawica"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Left"
msgstr "Dolny lewy"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Center"
msgstr "Dół środek"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Right"
msgstr "Dolny prawy"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Attachment"
msgstr "Załącznik"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Fixed"
msgstr "Naprawione"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Scroll"
msgstr "Przewiń"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat"
msgstr "Powtórz"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "No Repeat"
msgstr "Bez powtórki"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-x"
msgstr "Powtórz-x"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-y"
msgstr "Powtórz-y"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Auto"
msgstr "Samochód"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Cover"
msgstr "Okładka"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Contain"
msgstr "Zawierać"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Weight"
msgstr "Grubość czcionki"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Subset"
msgstr "Podzbiór czcionek"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "px"
msgstr "px"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "em"
msgstr "em"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size Type"
msgstr "Typ rozmiaru"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button Block"
msgstr "Blok przycisku Następny krok"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Icon"
msgstr "Wybierz ikonę"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button Hover Color"
msgstr "Kolor przycisku po najechaniu"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Groove"
msgstr "Rytm"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Ridge"
msgstr "Grzbiet"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Bottom Spacing (px)"
msgstr "Odstęp dolny tytułu (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover Color"
msgstr "Kolor najechania"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Transform"
msgstr "Przekształcanie tekstu"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Capitalize"
msgstr "Kapitalizować"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Uppercase"
msgstr "Wielkie litery"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Lowercase"
msgstr "małe litery"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Letter Spacing (px)"
msgstr "Odstępy między literami (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Checkout Block"
msgstr "Blok kasy CartFlows"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "checkout form"
msgstr "formularz zamówienia"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout ( Pro )"
msgstr "Wieloetapowa kasa ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width (px)"
msgstr "Szerokość obramowania (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Note: This feature is available in the CartFlows Pro. Upgrade Now!."
msgstr "Uwaga: Ta funkcja jest dostępna w CartFlows Pro. Uaktualnij teraz!."

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input/Text Placeholder Color"
msgstr "Kolor zastępczy tekstu/wejścia"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius (px)"
msgstr "Promień obramowania (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Padding (px)"
msgstr "Odstępy sekcji (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Margin (px)"
msgstr "Margines sekcji (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Success / Error Message"
msgstr "Komunikat o sukcesie / błędzie"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Form success / Error validation"
msgstr "Sukces formularza / Walidacja błędów"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Error Message"
msgstr "Komunikat o błędzie"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Optin Form Block"
msgstr "Blok formularza zgody CartFlows"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "optin form"
msgstr "formularz zgody"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label ( Pro )"
msgstr "Etykieta pływająca ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button Text"
msgstr "Tekst przycisku wysyłania"

#: wizard/assets/build/wizard-app.js:1
msgid "Let's Start"
msgstr "Zacznijmy"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 1 of 6"
msgstr "Krok 1 z 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome to CartFlows"
msgstr "Witamy w CartFlows"

#: wizard/assets/build/wizard-app.js:1
msgid ""
"You're only minutes away from having a more profitable WooCommerce store! This short setup wizard will help you get "
"started with CartFlows."
msgstr ""
"Jesteś tylko kilka minut od posiadania bardziej dochodowego sklepu WooCommerce! Ten krótki kreator konfiguracji pomoże "
"Ci rozpocząć pracę z CartFlows."

#: wizard/assets/build/wizard-app.js:1
msgid "Save & Continue"
msgstr "Zapisz i kontynuuj"

#: wizard/assets/build/wizard-app.js:1
msgid "Saving"
msgstr "Zapisywanie"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 2 of 6"
msgstr "Krok 2 z 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Hi there! Tell us which page builder you use."
msgstr "Cześć! Powiedz nam, którego kreatora stron używasz."

#: wizard/assets/build/wizard-app.js:1
msgid "CartFlows works with all page builders, so don't worry if your page builder is not in the list. "
msgstr ""
"CartFlows współpracuje ze wszystkimi kreatorami stron, więc nie martw się, jeśli Twojego kreatora stron nie ma na "
"liście."

#: wizard/assets/build/wizard-app.js:1
msgid "Install & Activate"
msgstr "Zainstaluj i aktywuj"

#: wizard/assets/build/wizard-app.js:1
msgid "Installing Required Plugins"
msgstr "Instalowanie wymaganych wtyczek"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 3 of 6"
msgstr "Krok 3 z 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Great job!"
msgstr "Świetna robota!"

#: wizard/assets/build/wizard-app.js:1
msgid "Now let's install some required plugins."
msgstr "Teraz zainstalujmy kilka wymaganych wtyczek."

#: wizard/assets/build/wizard-app.js:1
msgid ""
"Since CartFlows uses WooCommerce, we'll set it up for you along with Cart Abandonment and Stripe Payments so you can "
"recover abandoned orders and easily accept payments."
msgstr ""
"Ponieważ CartFlows korzysta z WooCommerce, skonfigurujemy go dla Ciebie wraz z Porzuceniem Koszyka i Płatnościami "
"Stripe, abyś mógł odzyskiwać porzucone zamówienia i łatwo akceptować płatności."

#: wizard/assets/build/wizard-app.js:1
msgid "The following plugins will be installed and activated for you:"
msgstr "Następujące wtyczki zostaną zainstalowane i aktywowane dla Ciebie:"

#: wizard/assets/build/wizard-app.js:1
msgid "Continuing…"
msgstr "Kontynuując…"

#: wizard/assets/build/wizard-app.js:1
msgid "Continue"
msgstr "Kontynuuj"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 5 of 6"
msgstr "Krok 5 z 6"

#: wizard/assets/build/wizard-app.js:2
#. translators: %s: html tag
msgid "One last step. %s Let's setup email reports on how your store is doing."
msgstr "Jeszcze jeden krok. %s Skonfigurujmy raporty e-mailowe dotyczące wyników Twojego sklepu."

#: wizard/assets/build/wizard-app.js:3
#. translators: %1$s: html tag, %2$s: html tag
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send %1$s you an email report "
"with key metrics and insights. You also will receive emails from us to %2$s help your store sell more."
msgstr ""
"Pozwól CartFlows wyeliminować zgadywanie z wyników twojego procesu realizacji zamówienia. Każdego tygodnia twój sklep "
"wyśle %1$s ci raport e-mailowy z kluczowymi wskaźnikami i spostrzeżeniami. Otrzymasz również od nas e-maile, aby %2$s "
"pomóc twojemu sklepowi sprzedawać więcej."

#: wizard/assets/build/wizard-app.js:3
msgid "First Name"
msgstr "Imię"

#: wizard/assets/build/wizard-app.js:3
msgid "Please enter your name"
msgstr "Proszę wprowadzić swoje imię"

#: wizard/assets/build/wizard-app.js:3
msgid "Enter Your Email"
msgstr "Wprowadź swój e-mail"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Name"
msgstr "Proszę wprowadzić imię"

#: wizard/assets/build/wizard-app.js:4
msgid "Entered email address is not a valid email"
msgstr "Wprowadzony adres e-mail nie jest prawidłowym adresem e-mail"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Email ID"
msgstr "Proszę wprowadzić identyfikator e-mail"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome"
msgstr "Witamy"

#: wizard/assets/build/wizard-app.js:1
msgid "Page Builder"
msgstr "Kreator Stron"

#: wizard/assets/build/wizard-app.js:1
msgid "Required Plugins"
msgstr "Wymagane wtyczki"

#: wizard/assets/build/wizard-app.js:1
msgid "Done"
msgstr "Zrobione"

#: wizard/assets/build/wizard-app.js:1
msgid "Exit setup wizard"
msgstr "Zakończ kreatora konfiguracji"

#: wizard/assets/build/wizard-app.js:1
msgid "Redirecting.."
msgstr "Przekierowywanie.."

#: wizard/assets/build/wizard-app.js:1
msgid "Skip"
msgstr "Pomiń"

#: wizard/assets/build/wizard-app.js:1
#: wizard/assets/build/wizard-app.js:5
msgid "Finish Store Setup"
msgstr "Zakończ konfigurację sklepu"

#: wizard/assets/build/wizard-app.js:1
msgid "Select Color"
msgstr "Wybierz kolor"

#: wizard/assets/build/wizard-app.js:5
msgid "Recommended"
msgstr "Zalecane"

#: wizard/assets/build/wizard-app.js:5
msgid "Upload a Logo"
msgstr "Prześlij logo"

#: wizard/assets/build/wizard-app.js:5
msgid "Change a Logo"
msgstr "Zmień logo"

#: wizard/assets/build/wizard-app.js:5
msgid "Remove logo"
msgstr "Usuń logo"

#: wizard/assets/build/wizard-app.js:5
msgid "Suggested Dimensions: 180x60 pixels"
msgstr "Sugerowane wymiary: 180x60 pikseli"

#: wizard/assets/build/wizard-app.js:5
msgid "Oops!!! No templates found"
msgstr "Ups!!! Nie znaleziono szablonów"

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Seems like no templates are available for chosen page editor. Don't worry, you can always import the store checkout "
"template from the CartFlows setting menu."
msgstr ""
"Wygląda na to, że nie ma dostępnych szablonów dla wybranego edytora stron. Nie martw się, zawsze możesz zaimportować "
"szablon kasy sklepu z menu ustawień CartFlows."

#: wizard/assets/build/wizard-app.js:5
msgid "Skip to Next"
msgstr "Przejdź do następnego"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 4 of 6"
msgstr "Krok 4 z 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Awesome"
msgstr "Świetnie"

#: wizard/assets/build/wizard-app.js:5
msgid "Now let's setup your new store checkout."
msgstr "Teraz skonfigurujmy kasę w Twoim nowym sklepie."

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Choose one of the store checkout designs below. After setup you can change the text and color or even choose an "
"entirely new store checkout design."
msgstr ""
"Wybierz jeden z poniższych projektów kasy sklepowej. Po skonfigurowaniu możesz zmienić tekst i kolor lub nawet wybrać "
"zupełnie nowy projekt kasy sklepowej."

#: wizard/assets/build/wizard-app.js:5
msgid "Import & Continue"
msgstr "Importuj i kontynuuj"

#: wizard/assets/build/wizard-app.js:5
msgid "Processing.."
msgstr "Przetwarzanie.."

#: wizard/assets/build/wizard-app.js:5
msgid "Importing Failed.."
msgstr "Importowanie nie powiodło się.."

#: wizard/assets/build/wizard-app.js:5
msgid "Selected Template:"
msgstr "Wybrany szablon:"

#: wizard/assets/build/wizard-app.js:5
msgid "Change Primary Color"
msgstr "Zmień kolor podstawowy"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 6 of 6"
msgstr "Krok 6 z 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Congratulations, You Did It!"
msgstr "Gratulacje, udało Ci się!"

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows is set up on your website! Please watch the short video below for your next steps."
msgstr "CartFlows jest skonfigurowany na Twojej stronie internetowej! Obejrzyj krótki film poniżej, aby poznać kolejne kroki."

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows Extended Walkthrough Tutorial"
msgstr "Rozszerzony samouczek CartFlows"

#: wizard/assets/build/wizard-app.js:5
msgid "Finishing the Setup"
msgstr "Kończenie konfiguracji"

#: admin-core/inc/admin-menu.php:1052
msgid "A simple yet powerful way to add content restriction to your website."
msgstr "Prosty, ale skuteczny sposób na dodanie ograniczeń treści na swojej stronie internetowej."

#: classes/class-cartflows-loader.php:368
msgid "Quick Feedback"
msgstr "Szybka opinia"

#: classes/class-cartflows-loader.php:370
msgid "If you have a moment, please share why you are deactivating CartFlows:"
msgstr "Jeśli masz chwilę, proszę podziel się, dlaczego dezaktywujesz CartFlows:"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unlock Premium Features with CartFlows PRO!"
msgstr "Odblokuj funkcje premium dzięki CartFlows PRO!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Get the tools you need to create powerful sales funnels, increase conversions, and grow your business with ease."
msgstr ""
"Zdobądź narzędzia, których potrzebujesz, aby tworzyć skuteczne lejki sprzedażowe, zwiększać konwersje i rozwijać swoją "
"firmę z łatwością."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Select a Settings Tab"
msgstr "Wybierz kartę Ustawienia"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Free vs Pro"
msgstr "Bezpłatna vs Pro"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Increase Your Revenue with Smart Order Bumps"
msgstr "Zwiększ swoje przychody dzięki inteligentnym dodatkom do zamówień"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Boost sales with CartFlows’ Order Bump! Offer personalized add-ons at checkout to increase revenue effortlessly. Quick "
"to set up, no coding needed!"
msgstr ""
"Zwiększ sprzedaż dzięki Order Bump od CartFlows! Oferuj spersonalizowane dodatki przy kasie, aby bez wysiłku zwiększyć "
"przychody. Szybka konfiguracja, bez potrzeby kodowania!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show the Right Offer to the Right People – Automatically!"
msgstr "Pokaż właściwą ofertę właściwym ludziom – automatycznie!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Personalize deals based on location, cart details, and more. Upgrade to CartFlows PRO and unlock this smart feature "
"today!"
msgstr ""
"Personalizuj oferty na podstawie lokalizacji, szczegółów koszyka i nie tylko. Ulepsz do CartFlows PRO i odblokuj tę "
"inteligentną funkcję już dziś!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Boost Sales Instantly with Auto-Applied Coupons!"
msgstr "Zwiększ sprzedaż natychmiast dzięki automatycznie stosowanym kuponom!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"No codes, no hassle—discounts apply instantly at checkout. Upgrade to CartFlows PRO and start converting more customers "
"today!"
msgstr ""
"Bez kodów, bez problemów — rabaty są stosowane natychmiast przy kasie. Ulepsz do CartFlows PRO i zacznij konwertować "
"więcej klientów już dziś!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Give Your Customers More Choices, Boost Your Sales"
msgstr "Daj swoim klientom więcej wyborów, zwiększ swoją sprzedaż"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"Make buying easier with flexible product options. Let customers make the right choices from your checkout. Upgrade to "
"CartFlows PRO and start customizing today!"
msgstr ""
"Ułatw zakupy dzięki elastycznym opcjom produktów. Pozwól klientom dokonywać właściwych wyborów podczas realizacji "
"zamówienia. Ulepsz do CartFlows PRO i zacznij dostosowywać już dziś!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Optin Product"
msgstr "Produkt Optin"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Select free & virtual product only. Once you select a product, it will be displayed here."
msgstr "Wybierz tylko darmowy i wirtualny produkt. Po wybraniu produktu, zostanie on wyświetlony tutaj."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Search for a Free & Virtual Product"
msgstr "Wyszukaj darmowy i wirtualny produkt"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Please select a free & virtual product only."
msgstr "Proszę wybrać tylko darmowy i wirtualny produkt."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:80
msgid "Free"
msgstr "Bezpłatny"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "A/B Testing"
msgstr "Testy A/B"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid ""
"Optimize your sales with A/B testing in CartFlows! Experiment with product pricing, page layouts, messaging, and "
"design. Create variants, analyze results, and discover new ways to boost revenue."
msgstr ""
"Optymalizuj swoją sprzedaż za pomocą testów A/B w CartFlows! Eksperymentuj z cenami produktów, układami stron, "
"komunikatami i designem. Twórz warianty, analizuj wyniki i odkrywaj nowe sposoby na zwiększenie przychodów."

#: admin-core/assets/build/settings-app.js:23
msgid "Finish Setup"
msgstr "Zakończ konfigurację"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup CartFlows"
msgstr "Skonfiguruj CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup Store Checkout"
msgstr "Skonfiguruj kasę sklepu"

#: admin-core/assets/build/settings-app.js:23
msgid "Create"
msgstr "Utwórz"

#: admin-core/assets/build/settings-app.js:23
msgid "Build Your Funnel"
msgstr "Zbuduj swój lejek"

#: admin-core/assets/build/settings-app.js:23
msgid "Start From Scratch"
msgstr "Zacznij od zera"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Library"
msgstr "Idź do biblioteki"

#: admin-core/assets/build/settings-app.js:23
msgid "Offer add-ons with Order Bump."
msgstr "Oferuj dodatki z Order Bump."

#: admin-core/assets/build/settings-app.js:23
msgid "Increase Revenue with Upsells."
msgstr "Zwiększ przychody dzięki sprzedaży dodatkowej."

#: admin-core/assets/build/settings-app.js:23
msgid "Almost There! Let's Go Live."
msgstr "Prawie na miejscu! Przejdźmy na żywo."

#: admin-core/assets/build/settings-app.js:24
#. translators: %d is the number of completed steps.
msgid "%d out of 5 completed"
msgstr "%d z 5 ukończonych"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade to PRO"
msgstr "Ulepsz do PRO"

#: admin-core/assets/build/settings-app.js:24
msgid "Completed"
msgstr "Zakończono"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade your plan anytime and get more detailed analytics data."
msgstr "Zaktualizuj swój plan w dowolnym momencie i uzyskaj bardziej szczegółowe dane analityczne."

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views is a Premium Feature"
msgstr "Całkowita liczba wyświetleń stron to funkcja premium"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue is a Premium Feature"
msgstr "Przychody z oferty to funkcja premium"

#: admin-core/assets/build/settings-app.js:24
msgid "Custom Filter:"
msgstr "Filtr niestandardowy:"

#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Features"
msgstr "Funkcje kasy"

#: admin-core/assets/build/settings-app.js:80
msgid "Modern Checkout Styles"
msgstr "Nowoczesne style kasowe"

#: admin-core/assets/build/settings-app.js:80
msgid "Optimized replacement for the standard WooCommerce checkout page designed for higher conversion"
msgstr "Optymalizowany zamiennik standardowej strony kasy WooCommerce zaprojektowany dla wyższej konwersji"

#: admin-core/assets/build/settings-app.js:80
msgid "Custom Checkout Fields"
msgstr "Niestandardowe pola kasy"

#: admin-core/assets/build/settings-app.js:80
msgid "Have complete control over the field editor to manage the fields as required"
msgstr "Miej pełną kontrolę nad edytorem pól, aby zarządzać polami zgodnie z wymaganiami"

#: admin-core/assets/build/settings-app.js:80
msgid "One-Click Upsells / Downsells"
msgstr "Jednoklikowe upselle / downselle"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic One-Click Upsells"
msgstr "Dynamiczne jednorazowe oferty upsell"

#: admin-core/assets/build/settings-app.js:80
msgid "Use cart contents or customer data to display relevant upsells for maximum conversion"
msgstr "Użyj zawartości koszyka lub danych klienta, aby wyświetlać odpowiednie produkty dodatkowe dla maksymalnej konwersji"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Upsell Templates"
msgstr "Dynamiczne szablony sprzedaży dodatkowej"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional templates to help you sell more even if you’re not a designer"
msgstr "Profesjonalne szablony, które pomogą Ci sprzedawać więcej, nawet jeśli nie jesteś projektantem"

#: admin-core/assets/build/settings-app.js:80
msgid "Order Bump Features"
msgstr "Funkcje Dodatkowego Zamówienia"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Order Bumps"
msgstr "Dynamiczne dodatki do zamówienia"

#: admin-core/assets/build/settings-app.js:80
msgid "Smart order bumps using customer data to display most relevant products or offers"
msgstr ""
"Inteligentne dodatki do zamówień wykorzystujące dane klientów do wyświetlania najbardziej odpowiednich produktów lub "
"ofert"

#: admin-core/assets/build/settings-app.js:80
msgid "Advanced Funnel Features"
msgstr "Zaawansowane funkcje lejka"

#: admin-core/assets/build/settings-app.js:80
msgid "A / B Split Testing"
msgstr "Testowanie A / B"

#: admin-core/assets/build/settings-app.js:80
msgid "Increase conversions and sales with CartFlows A/B Testing by running simple tests"
msgstr "Zwiększ konwersje i sprzedaż dzięki testom A/B CartFlows, przeprowadzając proste testy"

#: admin-core/assets/build/settings-app.js:80
msgid "Analyze transactions and user behavior to refine conversions and make more profit"
msgstr "Analizuj transakcje i zachowania użytkowników, aby udoskonalić konwersje i zwiększyć zyski"

#: admin-core/assets/build/settings-app.js:80
msgid "Cloud-based automation tools that intelligently links your websites, stores, plugins and apps"
msgstr "Narzędzia automatyzacji w chmurze, które inteligentnie łączą Twoje strony internetowe, sklepy, wtyczki i aplikacje"

#: admin-core/assets/build/settings-app.js:80
msgid "SkillJet Academy Access"
msgstr "Dostęp do SkillJet Academy"

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows offers full training to help you make more profit with SkillJet academy"
msgstr "CartFlows oferuje pełne szkolenie, aby pomóc Ci zwiększyć zyski z akademią SkillJet"

#: admin-core/assets/build/settings-app.js:80
msgid "Others Benefits"
msgstr "Inne korzyści"

#: admin-core/assets/build/settings-app.js:80
msgid "Premium Support"
msgstr "Wsparcie Premium"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional Support, Professional Support Team or Dedicated Support Team"
msgstr "Wsparcie Profesjonalne, Zespół Wsparcia Profesjonalnego lub Dedykowany Zespół Wsparcia"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community"
msgstr "Niesamowita społeczność użytkowników"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community is already a great message unless you’re looking for a different meaning"
msgstr "Niesamowita społeczność użytkowników to już świetna wiadomość, chyba że szukasz innego znaczenia"

#: admin-core/assets/build/settings-app.js:80
msgid "Great Documentation & Video Tutorials"
msgstr "Świetna dokumentacja i samouczki wideo"

#: admin-core/assets/build/settings-app.js:80
msgid "Comprehensive Documentation and Video Tutorials or Comprehensive Documentation and Video Guides"
msgstr "Kompleksowa dokumentacja i samouczki wideo lub kompleksowa dokumentacja i przewodniki wideo"

#: admin-core/assets/build/settings-app.js:80
msgid "Free Plugins"
msgstr "Darmowe wtyczki"

#: admin-core/assets/build/settings-app.js:80
msgid "Variation Swatches"
msgstr "Próbki wariantów"

#: admin-core/assets/build/settings-app.js:80
msgid "Give customers choice by including relevant product variations including size, color and more"
msgstr "Daj klientom wybór, uwzględniając odpowiednie warianty produktów, takie jak rozmiar, kolor i inne"

#: admin-core/assets/build/settings-app.js:80
msgid "Stripe Payment Gateway"
msgstr "Bramka płatności Stripe"

#: admin-core/assets/build/settings-app.js:80
msgid "Accepting multiple payment methods gives customers choice and can significantly increase conversion"
msgstr "Akceptowanie wielu metod płatności daje klientom wybór i może znacznie zwiększyć konwersję"

#: admin-core/assets/build/settings-app.js:80
msgid "Features"
msgstr "Funkcje"

#: admin-core/assets/build/settings-app.js:80
msgid "See all CartFlows Pro features"
msgstr "Zobacz wszystkie funkcje CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Sell More with CartFlows Pro"
msgstr "Sprzedawaj więcej z CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Get access to powerful features for painless WordPress designing, without the high costs. With all the time you will "
"save, it’s a product that pays for itself!"
msgstr ""
"Uzyskaj dostęp do potężnych funkcji umożliwiających bezbolesne projektowanie WordPressa, bez wysokich kosztów. Dzięki "
"całemu zaoszczędzonemu czasowi, to produkt, który sam się spłaca!"

#: wizard/assets/build/wizard-app.js:1
msgid "Please complete the previous step before proceeding."
msgstr "Proszę ukończyć poprzedni krok przed kontynuowaniem."

#: cartflows.php
#. Author of the plugin
msgid "Brainstorm Force"
msgstr "Brainstorm Force"

#: cartflows.php
#. Author URI of the plugin
msgid "https://www.brainstormforce.com"
msgstr "https://www.brainstormforce.com"

#: admin-core/ajax/importer.php:1017
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$s article%3$s."
msgstr ""
"Błąd limitu czasu żądania. Proszę sprawdzić, czy zapora sieciowa lub jakakolwiek wtyczka zabezpieczająca nie blokuje "
"wychodzących żądań HTTP/HTTPS do templates.cartflows.com. %1$sAby rozwiązać ten problem, proszę sprawdzić ten %2$s "
"artykuł%3$s."

#: admin-core/inc/admin-menu.php:288
#: admin-core/inc/admin-menu.php:290
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Modern Cart"
msgstr "Nowoczesny Wózek"

#: admin-core/inc/admin-menu.php:1038
msgid "OttoKit"
msgstr "OttoKit"

#: admin-core/inc/admin-menu.php:1039
msgid ""
"OttoKit helps people automate their work by integrating multiple apps and plugins, allowing them to share data and "
"perform tasks automatically."
msgstr ""
"OttoKit pomaga ludziom automatyzować ich pracę poprzez integrację wielu aplikacji i wtyczek, umożliwiając im "
"udostępnianie danych i wykonywanie zadań automatycznie."

#: admin-core/inc/admin-menu.php:1088
msgid "Modern Cart for WooCommerce"
msgstr "Nowoczesny koszyk dla WooCommerce"

#: admin-core/inc/admin-menu.php:1089
msgid ""
"Modern Cart for WooCommerce that helps every shop owner improve their user experience, increase conversions & maximize "
"profits."
msgstr ""
"Nowoczesny koszyk dla WooCommerce, który pomaga każdemu właścicielowi sklepu poprawić doświadczenie użytkownika, "
"zwiększyć konwersje i maksymalizować zyski."

#: admin-core/inc/flow-meta.php:59
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Enable Instant Layout"
msgstr "Włącz natychmiastowy układ"

#: admin-core/inc/flow-meta.php:64
#. translators: %1$s: Break line, %2$s: link html Start, %3$s: Link html end.
msgid ""
"This layout will replace the default page template for the Checkout, Upsell/Downsell and Thank You steps. You can "
"customize the design %1$sin the Checkout, Upsell/Downsell and Thank You step's settings, under the design tab. %2$sRead "
"More.%3$s"
msgstr ""
"Ten układ zastąpi domyślny szablon strony dla kroków: Kasa, Upsell/Downsell i Podziękowanie. Możesz dostosować projekt "
"%1$sw ustawieniach kroków Kasa, Upsell/Downsell i Podziękowanie, w zakładce projekt. %2$sCzytaj więcej.%3$s"

#: admin-core/inc/flow-meta.php:87
msgid "Custom Logo"
msgstr "Logo niestandardowe"

#: admin-core/inc/flow-meta.php:92
msgid "If you've added a custom logo, it will show up here. If not, a default logo from the theme will be used instead."
msgstr "Jeśli dodałeś własne logo, pojawi się ono tutaj. Jeśli nie, zamiast tego zostanie użyte domyślne logo z motywu."

#: admin-core/inc/flow-meta.php:103
msgid "Minimum image size should be 130 x 40 in pixes for ideal display."
msgstr "Minimalny rozmiar obrazu powinien wynosić 130 x 40 pikseli dla idealnego wyświetlania."

#: admin-core/inc/flow-meta.php:143
msgid "Header Color"
msgstr "Kolor nagłówka"

#: admin-core/inc/flow-meta.php:249
msgid ""
"The Test Mode automatically adds sample products to your funnel if you haven't selected any. This helps you preview and "
"test the checkout experience easily."
msgstr ""
"Tryb testowy automatycznie dodaje przykładowe produkty do Twojego lejka, jeśli nie wybrałeś żadnych. To pomaga łatwo "
"przeglądać i testować proces realizacji zakupu."

#: admin-core/inc/flow-meta.php:250
msgid ""
"The Test Mode automatically adds sample products to your store checkout funnel if you haven't selected any. This helps "
"you preview and test the experience easily on all steps except the Checkout page."
msgstr ""
"Tryb testowy automatycznie dodaje przykładowe produkty do ścieżki zakupowej w Twoim sklepie, jeśli nie wybrałeś "
"żadnych. Pomaga to łatwo przeglądać i testować doświadczenie na wszystkich etapach z wyjątkiem strony Kasy."

#: admin-core/inc/flow-meta.php:256
msgid "Disallow Indexing"
msgstr "Nie zezwalaj na indeksowanie"

#: admin-core/inc/flow-meta.php:257
msgid "Changing this will replace the default global setting. To go back to the global setting, just select Default."
msgstr ""
"Zmiana tego ustawienia zastąpi domyślne ustawienie globalne. Aby powrócić do ustawienia globalnego, wystarczy wybrać "
"Domyślne."

#: admin-core/inc/flow-meta.php:280
msgid "Any code you add here will work across all the pages in this funnel."
msgstr "Każdy kod, który dodasz tutaj, będzie działał na wszystkich stronach w tym lejku."

#: admin-core/inc/global-settings.php:50
msgid "Allow full access to all settings to customize everything."
msgstr "Zezwól na pełny dostęp do wszystkich ustawień, aby dostosować wszystko."

#: admin-core/inc/global-settings.php:55
msgid "Allow limited access to create, edit, delete, or import flows and steps."
msgstr "Zezwól na ograniczony dostęp do tworzenia, edytowania, usuwania lub importowania przepływów i kroków."

#: admin-core/inc/global-settings.php:349
#: admin-core/inc/global-settings.php:539
msgid "This event will trigger when someone subscribes or signs up on the opt-in page."
msgstr "To zdarzenie zostanie uruchomione, gdy ktoś zasubskrybuje lub zarejestruje się na stronie opt-in."

#: admin-core/inc/global-settings.php:930
msgid "This option is only available for products that are part of a subscription."
msgstr "Ta opcja jest dostępna tylko dla produktów, które są częścią subskrypcji."

#: admin-core/inc/global-settings.php:1415
msgid "Usage Tracking"
msgstr "Śledzenie użycia"

#: admin-core/inc/global-settings.php:1417
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Allow CartFlows Inc products to track non-sensitive usage tracking data. %1$1s Learn More%2$2s."
msgstr ""
"Zezwól produktom CartFlows Inc na śledzenie niesensytywnych danych dotyczących użytkowania. %1$1s Dowiedz się "
"więcej%2$2s."

#: classes/class-cartflows-admin-notices.php:183
msgid "Hi there! You recently used CartFlows to build a sales funnel &mdash; Thanks a ton!"
msgstr "Cześć! Niedawno użyłeś CartFlows do stworzenia lejka sprzedażowego &mdash; Wielkie dzięki!"

#: classes/class-cartflows-admin-notices.php:184
msgid ""
"It would be awesome if you give us a 5-star review and share your experience on WordPress. Your reviews pump us up and "
"also help other WordPress users make a better decision when choosing CartFlows!"
msgstr ""
"Byłoby wspaniale, gdybyś dał nam 5-gwiazdkową recenzję i podzielił się swoim doświadczeniem na WordPress. Twoje "
"recenzje dodają nam energii i pomagają innym użytkownikom WordPress podjąć lepszą decyzję przy wyborze CartFlows!"

#: classes/class-cartflows-admin-notices.php:186
msgid "Ok, you deserve it"
msgstr "Ok, zasługujesz na to"

#: classes/class-cartflows-admin-notices.php:188
msgid "Nope, maybe later"
msgstr "Nie, może później"

#: classes/class-cartflows-admin-notices.php:189
msgid "I already did"
msgstr "Już to zrobiłem"

#: classes/class-cartflows-flow-frontend.php:90
msgid ""
"Test mode is currently enabled to help you preview your funnel. You can turn it off anytime from the funnel's settings "
"in the admin dashboard."
msgstr ""
"Tryb testowy jest obecnie włączony, aby pomóc Ci w podglądzie lejka. Możesz go wyłączyć w dowolnym momencie w "
"ustawieniach lejka na pulpicie administracyjnym."

#: classes/class-cartflows-flow-frontend.php:91
msgid "Click here to disable it."
msgstr "Kliknij tutaj, aby to wyłączyć."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:119
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:131
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:162
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:145
#: modules/gutenberg/build/blocks.js:7
msgid "Legacy"
msgstr "Dziedzictwo"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:120
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:132
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:163
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:149
#: modules/gutenberg/build/blocks.js:7
msgid "Modern"
msgstr "Nowoczesny"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:132
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:146
#: modules/gutenberg/build/blocks.js:7
msgid "The Thank You Text is only applicable for the old layout."
msgstr "Tekst podziękowania dotyczy tylko starego układu."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:322
msgid "Pick a background color for the left side of your Checkout page."
msgstr "Wybierz kolor tła dla lewej strony swojej strony kasy."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:339
msgid "Pick a background color for the right side of your Checkout page."
msgstr "Wybierz kolor tła dla prawej strony swojej strony realizacji zakupu."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:841
msgid "Change the background color of the payment description box to match your style."
msgstr "Zmień kolor tła pola opisu płatności, aby pasował do Twojego stylu."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1022
msgid "This is the name (slug) of the current step. Changing it will update the URL for this step, so be cautious!"
msgstr "To jest nazwa (slug) bieżącego kroku. Zmiana jej spowoduje aktualizację adresu URL dla tego kroku, więc bądź ostrożny!"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1030
#: modules/landing/classes/class-cartflows-landing-meta-data.php:123
#: modules/optin/classes/class-cartflows-optin-meta-data.php:577
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:405
msgid "Add your own custom code here. If you're adding CSS, make sure to wrap it inside &lt;style&gt; tags."
msgstr "Dodaj tutaj swój własny kod. Jeśli dodajesz CSS, upewnij się, że jest on umieszczony wewnątrz znaczników &lt;style&gt;."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1044
msgid "Turn this ON to show your product images in the order review section."
msgstr "Włącz to, aby wyświetlać zdjęcia produktów w sekcji przeglądu zamówienia."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1052
msgid "Users can easily remove products from the checkout page if they decide not to purchase them."
msgstr "Użytkownicy mogą łatwo usunąć produkty ze strony kasy, jeśli zdecydują się ich nie kupować."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1216
msgid "Turn this on to show a custom message when no shipping options are available at checkout."
msgstr "Włącz to, aby wyświetlić niestandardową wiadomość, gdy przy kasie nie są dostępne opcje wysyłki."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1244
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:449
msgid "Choose this option to adjust where the order summary appears on mobile devices."
msgstr "Wybierz tę opcję, aby dostosować miejsce wyświetlania podsumowania zamówienia na urządzeniach mobilnych."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1297
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1305
msgid "This heading will only appear when you use the Modern Checkout style."
msgstr "Ten nagłówek pojawi się tylko wtedy, gdy użyjesz nowoczesnego stylu kasy."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1313
msgid "This message will appear next to the field name to show an error if something goes wrong."
msgstr "Ta wiadomość pojawi się obok nazwy pola, aby pokazać błąd, jeśli coś pójdzie nie tak."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1346
msgid ""
"Customizes the text on the 'Place Order' button during checkout, allowing you to make it more relevant to your "
"customers."
msgstr ""
"Dostosowuje tekst na przycisku „Złóż zamówienie” podczas realizacji zamówienia, umożliwiając uczynienie go bardziej "
"odpowiednim dla Twoich klientów."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1354
msgid ""
"Enabling this will add a lock icon to the 'Place Order' button on the checkout page, indicating secure payment "
"processing."
msgstr ""
"Włączenie tego spowoduje dodanie ikony kłódki do przycisku „Złóż zamówienie” na stronie realizacji zamówienia, co "
"wskazuje na bezpieczne przetwarzanie płatności."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1363
msgid "This will display the total amount in the cart when you click the 'Place Order' button."
msgstr "To wyświetli łączną kwotę w koszyku, gdy klikniesz przycisk „Złóż zamówienie”."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:408
msgid "This heading will appear only when the Instant Layout option is used."
msgstr "Ten nagłówek pojawi się tylko wtedy, gdy używana jest opcja Natychmiastowy Układ."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:139
msgid "Thank You Skin"
msgstr "Dziękuję Skórze"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:501
msgid "After submitting, users will be sent to this URL instead of the usual thank you page."
msgstr "Po przesłaniu użytkownicy zostaną przekierowani na ten URL zamiast na zwykłą stronę z podziękowaniem."

#: modules/thankyou/templates/instant-thankyou-order-details.php:35
#. Translators: Order ID.
msgid "Order #%s"
msgstr "Zamówienie nr %s"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:139
msgid "Type to search a funnel..."
msgstr "Wpisz, aby wyszukać lejek..."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Disabled"
msgstr "Wyłączony"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "upgrading to PRO"
msgstr "ulepszanie do PRO"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "activating CartFlows Pro"
msgstr "aktywacja CartFlows Pro"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You're using"
msgstr "Używasz"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "CartFlows Free"
msgstr "CartFlows Free"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "To unlock more features, consider"
msgstr "Aby odblokować więcej funkcji, rozważ"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Activate CartFlows Pro"
msgstr "Aktywuj CartFlows Pro"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Activated!"
msgstr "Aktywowane!"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid ""
"You can't edit this step directly because Instant Layout is turned on in the funnel settings. To make design changes, "
"go to the Design tab inside this step's settings."
msgstr ""
"Nie możesz edytować tego kroku bezpośrednio, ponieważ w ustawieniach lejka włączono funkcję Instant Layout. Aby "
"wprowadzić zmiany w projekcie, przejdź do zakładki Projekt w ustawieniach tego kroku."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#. translators: %s: The current step type.
msgid "Use this setting to customize the style of the Instant %s Layout."
msgstr "Użyj tego ustawienia, aby dostosować styl układu Instant %s."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Turn this on to set up rules that decide when visitors should be redirected to a special offer or the next step."
msgstr ""
"Włącz to, aby ustawić zasady decydujące, kiedy odwiedzający powinni zostać przekierowani do specjalnej oferty lub "
"następnego kroku."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Your email address can't be edited when using the Modern Checkout Style."
msgstr "Nie można edytować adresu e-mail podczas korzystania z nowoczesnego stylu kasy."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "The Company field won't be visible if you're using the Instant Layout Style."
msgstr "Pole Firma nie będzie widoczne, jeśli używasz stylu układu Instant."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from all your funnels combined."
msgstr "To pokazuje łączną kwotę pieniędzy zarobioną ze wszystkich Twoich lejków razem wziętych."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of orders placed through your CartFlows checkout pages."
msgstr "To pokazuje łączną liczbę zamówień złożonych za pośrednictwem stron realizacji zamówienia CartFlows."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of times people visited any step in your funnel."
msgstr "To pokazuje całkowitą liczbę razy, kiedy ludzie odwiedzili dowolny krok w Twoim lejku."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from your Upsell and Downsell offers."
msgstr "To pokazuje łączną kwotę pieniędzy zarobioną z Twoich ofert Upsell i Downsell."

#: admin-core/assets/build/settings-app.js:35
msgid "Set up a Store Checkout in just one click:"
msgstr "Skonfiguruj kasę sklepu za pomocą jednego kliknięcia:"

#: admin-core/assets/build/settings-app.js:35
msgid "Thank You Page"
msgstr "Strona podziękowania"

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Use ready-made templates from the CartFlows Library, our custom widget, or shortcodes on each page to set this up "
"easily—no coding needed!"
msgstr ""
"Użyj gotowych szablonów z Biblioteki CartFlows, naszego niestandardowego widżetu lub shortcode'ów na każdej stronie, "
"aby łatwo to skonfigurować — bez potrzeby kodowania!"

#: admin-core/assets/build/settings-app.js:80
msgid "Install OttoKit for Free"
msgstr "Zainstaluj OttoKit za darmo"

#: admin-core/assets/build/settings-app.js:80
msgid "Visit OttoKit Website"
msgstr "<translatedOdwiedź stronę OttoKit"

#: admin-core/assets/build/settings-app.js:80
msgid "Here are a few simple examples of what OttoKit can do on your WooCommerce store:"
msgstr "Oto kilka prostych przykładów tego, co OttoKit może zrobić w Twoim sklepie WooCommerce:"

#: admin-core/assets/build/settings-app.js:80
msgid "Join Thousands of Entrepreneurs Already Using OttoKit."
msgstr "Dołącz do tysięcy przedsiębiorców już korzystających z OttoKit."

#: admin-core/assets/build/settings-app.js:80
msgid "Bonus ($200 Value)"
msgstr "Premia (wartość 200 USD)"

#: admin-core/assets/build/settings-app.js:80
msgid "Access to OttoKit Pro Plan"
msgstr "Dostęp do OttoKit Pro Plan"

#: admin-core/assets/build/settings-app.js:80
msgid "Plus - Annual"
msgstr "Plus - Roczny"

#: admin-core/assets/build/settings-app.js:80
msgid "/ year"
msgstr "/ rok"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Annual"
msgstr "Pro - Roczny"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (One Time Pay)"
msgstr "Pro - Dożywotnia (Jednorazowa opłata)"

#: admin-core/assets/build/settings-app.js:80
msgid "for Lifetime"
msgstr "na całe życie"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (11 x Split Pay)"
msgstr "Pro - Dożywotnio (11 x Płatność ratalna)"

#: admin-core/assets/build/settings-app.js:80
msgid "x 11 Months"
msgstr "x 11 miesięcy"

#: admin-core/assets/build/settings-app.js:80
msgid "Explore the key differences between Plus and Pro to find the perfect fit for your needs."
msgstr "Poznaj kluczowe różnice między Plus a Pro, aby znaleźć idealne dopasowanie do swoich potrzeb."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Free vs Pro Image"
msgstr "Obraz CartFlows Free vs Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Unlock Pro Features"
msgstr "Odblokuj funkcje Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Generate More Sales With CartFlows Pro!"
msgstr "Generuj więcej sprzedaży z CartFlows Pro!"

#: admin-core/assets/build/settings-app.js:80
msgid "And More…"
msgstr "I więcej…"

#: admin-core/assets/build/settings-app.js:80
msgid "Buy Now"
msgstr "Kup teraz"

#: admin-core/assets/build/settings-app.js:80
msgid "View plans"
msgstr "Zobacz plany"

#: admin-core/assets/build/settings-app.js:80
msgid "Get Modern Cart Now"
msgstr "Uzyskaj nowoczesny koszyk teraz"

#: admin-core/assets/build/settings-app.js:80
msgid "Moderncart"
msgstr "Moderncart"

#: admin-core/assets/build/settings-app.js:81
#. translators: %s: line break
msgid "Your Cart Can Do More — Let’s Make It %sa Sales Machine!"
msgstr "Twój koszyk może więcej — zamieńmy go w %smaszynę sprzedażową!"

#: admin-core/assets/build/settings-app.js:81
msgid ""
"Transform your default WooCommerce cart into a high-converting, fast, and user-friendly shopping experience — designed "
"to keep customers engaged and ready to buy."
msgstr ""
"Przekształć swój domyślny koszyk WooCommerce w wysoko konwertujące, szybkie i przyjazne dla użytkownika doświadczenie "
"zakupowe — zaprojektowane, aby utrzymać zaangażowanie klientów i gotowość do zakupu."

#: admin-core/assets/build/settings-app.js:81
msgid "Visit Modern Cart"
msgstr "<translatedOdwiedź Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Why Store Owners ❤️ Modern Cart"
msgstr "Dlaczego właściciele sklepów ❤️ nowoczesny koszyk"

#: admin-core/assets/build/settings-app.js:81
msgid "Trusted by Top Brands to Boost Conversions Instantly"
msgstr "Zaufany przez najlepsze marki, aby natychmiast zwiększyć konwersje"

#: admin-core/assets/build/settings-app.js:81
msgid "Brand logo"
msgstr "Logo marki"

#: admin-core/assets/build/settings-app.js:81
msgid "Stop Losing Sales at the Cart — Fix It in Minutes!"
msgstr "Przestań tracić sprzedaż na etapie koszyka — napraw to w kilka minut!"

#: admin-core/assets/build/settings-app.js:81
msgid "Modern Cart is your instant upgrade for more sales, bigger orders, and smoother checkouts."
msgstr ""
"Modern Cart to Twoja natychmiastowa aktualizacja dla większej sprzedaży, większych zamówień i płynniejszych procesów "
"realizacji zakupów."

#: wizard/assets/build/wizard-app.js:1
msgid "Learn more about usage tracking"
msgstr "Dowiedz się więcej o śledzeniu użytkowania"

#: wizard/assets/build/wizard-app.js:3
msgid "I agree to share anonymous usage data to help improve CartFlows."
msgstr "Zgadzam się na udostępnianie anonimowych danych użytkowania, aby pomóc w ulepszaniu CartFlows."

#: wizard/assets/build/wizard-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag close
msgid ""
"We never collect personal info, Only anonymized data like PHP version, admin language, and feature usage. To learn what "
"we collect and why, see this %1$sdocument%2$s."
msgstr ""
"Nigdy nie zbieramy danych osobowych, tylko zanonimizowane dane, takie jak wersja PHP, język administratora i użycie "
"funkcji. Aby dowiedzieć się, co zbieramy i dlaczego, zobacz ten %1$sdokument%2$s."

#: wizard/assets/build/wizard-app.js:5
#. translators: %1$s: Anchor tag one start, %2$s: Anchor tag one close, %3$s: Anchor tag two start, %4$s: Anchor tag two close.
msgid "By continuing you agree to our %1$sTerms%2$s and %3$sPrivacy Policy%4$s."
msgstr "Kontynuując, zgadzasz się na nasze %1$sWarunki%2$s i %3$sPolitykę Prywatności%4$s."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:266
msgctxt "Width."
msgid "Auto"
msgstr "Samochód"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping %d"
msgstr "Wysyłka %d"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping"
msgstr "Wysyłka"

#: modules/flow/classes/class-cartflows-flow-post-type.php:103
msgctxt "flow general name"
msgid "Flows"
msgstr "Przepływy"

#: modules/flow/classes/class-cartflows-flow-post-type.php:104
msgctxt "flow singular name"
msgid "Flow"
msgstr "Przepływ"

#: modules/flow/classes/class-cartflows-step-post-type.php:170
msgctxt "flow step general name"
msgid "Steps"
msgstr "Kroki"

#: modules/flow/classes/class-cartflows-step-post-type.php:171
msgctxt "flow step singular name"
msgid "Step"
msgstr "Krok"
