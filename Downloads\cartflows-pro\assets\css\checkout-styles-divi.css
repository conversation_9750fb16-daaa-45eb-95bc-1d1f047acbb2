.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .mt20 {
	margin-top: 0;
}

.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-form-login .form-row label:not( .checkbox ).woocommerce-form__label-for-checkbox {
	position: relative;
	margin: 0;
}

.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields .form-row label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-shipping-fields .form-row label:not( .checkbox ) {
	position: absolute;
	z-index: 999;
	margin-top: 19px;
	margin-left: 15px;
	transition: all 0.235s ease;
}

.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields .form-row.wcf-anim-label label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-shipping-fields .form-row.wcf-anim-label label:not( .checkbox ) {
	margin-top: 8px;
	font-size: 12px;
}

.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields .form-row label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-shipping-fields .form-row label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-additional-fields .form-row label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-account-fields .form-row label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-form-login .form-row label:not( .checkbox ) {
	position: absolute;
	z-index: 999;
	margin-top: 19px;
	margin-left: 15px;
	transition: all 0.235s ease;
	overflow: hidden;
	white-space: nowrap;
}

.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-billing-fields .form-row.wcf-anim-label label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-shipping-fields .form-row.wcf-anim-label label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-additional-fields .form-row.wcf-anim-label label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-account-fields .form-row.wcf-anim-label label:not( .checkbox ),
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .woocommerce-form-login .form-row.wcf-anim-label label:not( .checkbox ) {
	margin-top: 8px;
	font-size: 12px;
}

.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="text"],
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="email"],
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="password"],
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .woocommerce .form-row input[type="tel"],
.et_pb_module #wcf-embed-checkout-form.wcf-field-style-one .select2-container--default .select2-selection--single {
	padding: 25px 12px 5px;
}

/**
* *******************************
*    Product Variation CSS Start
* *******************************
*/

.et_pb_module .wcf-product-option-wrap {
	padding: 3px;
}
.et_pb_module .wcf-product-option-wrap #your_products_heading {
	font-family: inherit;
	font-weight: 600;
	font-size: 20px;
	margin: 0 0 25px 0;
	padding: 3px;
}
.et_pb_module .wcf-product-option-wrap .wcf-qty-options .wcf-qty-row {
	position: relative;
}
.et_pb_module .wcf-product-option-wrap .wcf-qty-options .wcf-qty-row .wcf-item-choose-options {
	margin: 5px 0 0 0;
}
.et_pb_module .wcf-product-option-wrap input[type="number"]:focus {
	outline: none;
}
.et_pb_module .wcf-qty-options .wcf-item-selector {
	display: inline-block;
	margin-right: 8px;
}
.et_pb_module .wcf-qty-options .wcf-item-all-text {
	display: inline-block;
	vertical-align: middle;
}
.et_pb_module .wcf-qty-options .wcf-item-image {
	width: 55px;
	/* height: 45px; */
	-js-display: inline-flex;
	display: inline-flex;
	margin-right: 10px;
}
.et_pb_module .wcf-qty-options .wcf-item-wrap {
	font-size: 1em;
	font-weight: 600;
	line-height: 1.5;
}
.et_pb_module .wcf-qty-options .wcf-display-attributes,
.et_pb_module .wcf-qty-options .wcf-display-subscription-details {
	font-size: 0.75em;
	font-weight: 400;
	font-style: italic;
	opacity: 0.65;
}
.et_pb_module .wcf-display-attributes .wcf-att-inner {
	margin-right: 5px;
}
.et_pb_module .wcf-display-attributes .wcf-att-inner:last-child .wcf-att-sep {
	display: none;
}
.et_pb_module .wcf-qty-hidden.wcf-qty {
	visibility: hidden;
	pointer-events: none;
	opacity: 0;
}
.et_pb_module .wcf-qty-options .wcf-item-wrap {
	line-height: 1.8;
}
.et_pb_module .wcf-qty-options ins {
	background: none;
}
.et_pb_module .wcf-qty-options .wcf-price del .woocommerce-Price-amount {
	font-size: inherit;
	opacity: 0.45;
	color: inherit;
	margin-right: 4px;
}
.et_pb_module .wcf-qty-options .wcf-item .wcf-item-wrap span.dashicons.dashicons-no-alt {
	vertical-align: middle;
}
.et_pb_module .wcf-embed-checkout-form-one-column .wcf-product-option-wrap {
	clear: left;
	margin: 15px 0;
	width: 100%;
}
.et_pb_module .wcf-embed-checkout-form-two-column .wcf-product-option-wrap {
	margin: 15px 0;
	width: 55%;
	float: left;
	padding-right: 40px;
}
.et_pb_module .wcf-embed-checkout-form-two-column .wcf-product-option-wrap.wcf-product-option-before-customer,
.et_pb_module .wcf-embed-checkout-form-two-column .wcf-product-option-wrap.wcf-product-option-before-order {
	width: 100%;
	padding: 0;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options {
	border: none;
	border-bottom: 0;
	background-color: #f3f3f3;
	border-radius: 3px;
	border-collapse: collapse;
	font-family: inherit;
	font-weight: inherit;
	font-size: 1em;
	margin: 0 0 0 0 !important;
	padding: 15px;
	text-align: left;
	width: 100%;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-header {
	border-bottom: 1px dashed #ccc;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-header .wcf-field-label {
	font-weight: 600;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-row:not( .wcf-highlight ):last-child {
	border: none;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-row .wcf-item,
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-row .wcf-qty,
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-row .wcf-price {
	padding: 0.3em 0;
	line-height: 1.4em;
	border: none;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-row {
	-js-display: flex;
	display: flex;
	justify-content: space-between;
	position: relative;
	padding: 10px 0;
	border-bottom: 1px solid #ccc;
	align-items: center;
	font-size: 0.95em;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-item,
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty,
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-price {
	display: inline-block;
	vertical-align: middle;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-item {
	-js-display: flex;
	display: flex;
	align-items: center;
	width: 80%;
	flex: 4;
	margin-right: 10px;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-item .wcf-item-subtext {
	font-size: 0.95em;
	line-height: 1.5;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-item .wcf-item-wrap + .wcf-item-subtext {
	margin-top: 5px;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty {
	-js-display: flex;
	display: flex;
	width: 12%;
	text-align: center;
	align-items: center;
	justify-content: center;
	flex: 1;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-qty-selection {
	width: 60px;
	width: 45px;
	padding: 2px 10px 2px 10px;
	margin: 0 auto;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-price {
	-js-display: flex;
	display: flex;
	align-items: center;
	width: 8%;
	text-align: right;
	justify-content: flex-end;
	flex: 1;
	margin-left: 10px;
	font-size: 0.95em;
	font-weight: 600;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-highlight {
	background-color: #fff;
	border: 1px solid #ccc;
	font-weight: 500;
	padding: 18px 28px;
	margin: -1px -28px 0;
	font-size: 0.95em;
	border-radius: 4px;
	box-shadow: 0 5px 10px -5px rgba( 150, 150, 150, 0.5 );
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-table-titles + .wcf-highlight {
	margin-top: 15px;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-highlight + .wcf-highlight {
	margin-top: 25px;
}
.et_pb_module .wcf-yp-skin-classic .wcf-highlight .wcf-highlight-head {
	position: absolute;
	top: -10px;
	right: -10px;
	background: #f16334;
	color: #fff;
	border-radius: 3px;
	padding: 3px 9px;
	font-size: 0.75em;
	font-weight: 400;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-highlight .wcf-item-wrap {
	font-size: 0.95em;
	font-weight: 600;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-highlight .wcf-item-subtext {
	opacity: 0.75;
}
.et_pb_module .wcf-yp-skin-classic .wcf-qty-options .wcf-item-image {
	margin-right: 15px;
}
.et_pb_module .wcf-embed-checkout-form-two-step .wcf-yp-skin-classic .wcf-qty-options .wcf-highlight {
	padding: 18px;
	margin-left: -20px;
	margin-right: -20px;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options {
	-js-display: flex;
	display: flex;
	flex-wrap: wrap;
	margin: 0 -10px;
	font-size: 1em;
	width: calc( 100% + 20px );
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row {
	background-color: #f7f7f7;
	border: 1px solid #e8e8e8;
	padding: 20px 30px;
	margin: 0 10px 22px;
	border-radius: 0.35em;
	width: calc( 50% - 20px );
	font-size: 1em;
	font-weight: 600;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-highlight {
	background-color: #fff;
	box-shadow: 0 5px 10px -5px rgba( 150, 150, 150, 0.5 );
	overflow: hidden;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-highlight .wcf-price {
	font-size: 1.08em;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-choose-options {
	margin: 0 0 3px;
}
.et_pb_module .wcf-yp-skin-cards .wcf-highlight .wcf-highlight-head {
	position: absolute;
	width: 12em;
	top: 1.8em;
	right: -3em;
	background: #f16334;
	color: #fff;
	padding: 0.22em 0;
	text-align: center;
	font-size: 0.8em;
	font-weight: 700;
	transform: rotate( 45deg );
	-webkit-transform: rotate( 45deg );
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item {
	-js-display: flex;
	display: flex;
	align-items: flex-start;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-item-selector,
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-item-image {
	margin-top: 5px;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-item-image {
	margin-right: 15px;
	width: 80px;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options {
	margin: 0 15px 5px 0;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-item-wrap,
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-price {
	font-size: 1.07em;
	font-weight: 600;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-wrap {
	margin-bottom: 5px;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-subtext {
	font-size: inherit;
	font-weight: 400;
	opacity: 0.75;
	margin-bottom: 15px;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-qty,
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-qty input {
	max-width: 50px;
	text-align: center;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-qty,
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-price {
	align-items: center;
	display: inline-block;
	margin-top: 0;
	margin-right: 15px;
	vertical-align: middle;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-hidden.wcf-qty {
	display: none !important;
}
.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-price {
	font-size: 0.95em;
}
.et_pb_module .wcf-embed-checkout-form-two-step .wcf-yp-skin-cards .wcf-qty-row,
.et_pb_module .wcf-embed-checkout-form-two-column .wcf-product-option-after-customer.wcf-yp-skin-cards .wcf-qty-row,
.et_pb_module .wcf-embed-checkout-form-two-column .wcf-product-option-before-order.wcf-yp-skin-cards .wcf-qty-row {
	width: 100%;
}

@media ( max-width: 768px ) {
	.et_pb_module .wcf-product-option-before-customer.wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row,
	.et_pb_module .wcf-product-option-after-customer.wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row,
	.et_pb_module .wcf-product-option-before-order.wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row,
	.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row {
		width: 100%;
		padding: 15px 20px;
	}

	.et_pb_module .wcf-embed-checkout-form-two-column .wcf-product-option-wrap,
	.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-item {
		width: 100%;
		padding: 0;
	}
	.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-price {
		width: auto;
		vertical-align: middle;
	}
	.et_pb_module .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-item-subtext {
		font-size: 0.9em;
	}
}
/**
* *****************************
*    Product Variation CSS End
* *****************************
*/

/**
* *****************************
*    Bump Order CSS Start
* *****************************
*/

/* Style One Start */
.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-wrap {
	display: block;
	float: none;
	margin: 1em auto 1em;
	overflow: hidden;
	width: 100%;
}
.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-1 {
	background: #f1f1f1;
	border-style: none;
	border-width: 2px;
	border-color: #f00;
	border-radius: 3px;
	display: inline-block;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-1 .wcf-bump-order-field-wrap {
	border-style: none;
	border-width: 2px;
	border-color: #f00;
	padding: 20px 25px;
	margin: 0;
	font-size: 1.1em;
	display: block;
	background: #ddd;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-wrap .wcf-bump-order-field-wrap label {
	margin: 0 !important;
	vertical-align: middle;
	font-size: 1em;
	line-height: 1.3em;
	letter-spacing: 0;
	font-family: inherit;
	font-weight: inherit;
	text-transform: none;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-1 .wcf-content-container {
	padding: 25px 0;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-1 .wcf-bump-order-offer {
	padding: 0 25px 10px;
	font-size: 1.2em;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-1 .wcf-bump-order-desc {
	padding: 0 25px;
}

/* Style One End */

/* Style Two Start */

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-2 {
	border: 2px #f00 dashed;
	border-radius: 3px;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-2 .wcf-bump-order-offer {
	padding: 20px 25px;
	font-size: 1.1em;
	font-weight: 600;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-2 .wcf-bump-order-desc {
	padding: 0 25px 20px;
}

.et_pb_module #wcf-embed-checkout-form .wcf-bump-order-style-2 .wcf-bump-order-field-wrap {
	border-top: 2px #f00 dashed;
	padding: 15px 25px;
	margin: 0;
	font-size: 1.1em;
	display: block;
}
/* Style Two End */

/**
* *****************************
*    Bump Order CSS End
* *****************************
*/

/**
* ****************************
*    Two Step CSS Start
* ****************************
*/

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step {
	width: 100%;
	margin: 0 auto;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-note {
	border: 1px dashed;
	border-color: #f16334;
	margin-bottom: 20px;
	padding: 10px 15px;
	padding-top: 10px;
	padding-right: 15px;
	padding-bottom: 10px;
	padding-left: 15px;
	border-radius: 3px;
	color: #fff;
	background-color: #f16334;
	position: relative;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-note::before {
	content: "";
	border: 10px solid;
	border-top-color: #f16334;
	position: absolute;
	width: 20px;
	height: 20px;
	bottom: -20px;
	left: auto;
	top: auto;
	border-left: 10px transparent solid;
	border-right: 10px transparent solid;
	border-bottom: 10px transparent solid;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-nav {
	border: inherit;
	border-left-style: solid;
	border-right-style: solid;
	border-width: 2px;
	border-top-style: solid;
	border-color: #ddd;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
	display: block;
	width: 100%;
	margin: 0 auto;
	padding-bottom: 0;
	/*min-width: 800px;*/
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step ul.wcf-embed-checkout-form-steps {
	background-color: #f4f4f4;
	-js-display: flex;
	display: flex;
	list-style: none;
	margin: 0;
	margin-left: 0;
	margin-bottom: 0;
	padding: 0;
	width: 100%;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce {
	border: inherit;
	border-top: none;
	border-left-style: solid;
	border-right-style: solid;
	border-bottom-style: solid;
	border-width: 2px;
	border-color: #ddd;
	border-bottom-left-radius: 3px;
	border-bottom-right-radius: 3px;
	padding: 10px 20px 20px;
	overflow: hidden;
	background-color: #fff;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div.steps a {
	align-items: center;
	-js-display: flex;
	display: flex;
	padding: 15px;
	width: 100%;
	/* overflow: hidden; */
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div.steps .step-number {
	display: inline-block;
	font-weight: 700;
	font-size: 25px;
	line-height: 1.5;
	/* float: left; */
	margin-right: 10px;
	vertical-align: middle;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps .steps.wcf-current {
	background-color: #fff;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div.step-one {
	/* border-top: 3px solid #F4F4F4; */
	opacity: 1;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div.steps {
	flex: 1;
	width: 100%;
	text-align: left;
	vertical-align: middle;
	position: relative;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps a:visited,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps a:focus,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps a:active,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps a {
	color: #444;
	border: none;
	outline: none;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps .wcf-current .step-name {
	color: #f16334;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce .wcf-embed-checkout-form-nav-btns a.wcf-next-button {
	border: 1px solid;
	border-color: #f16334;
	background-color: #f16334;
	font-family: inherit;
	font-weight: inherit;
	letter-spacing: 0.5px;
	width: 100%;
	padding: 15px 25px;
	font-size: 16px;
	line-height: 1.5;
	border-radius: 3px;
	color: #fff;
	text-transform: none;
	text-align: center;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-nav-btns .wcf-next-button .dashicons-arrow-right-alt {
	margin-right: 5px;
	vertical-align: middle;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-nav-btns .wcf-next-button .wcf-button-text {
	vertical-align: middle;
}

/**
* ****************************
*    Two Step CSS End
* ****************************
*/

/**
* *************************************
* Two Step Divi Compatibility css Start ( Migrated from lite )
* *************************************
*/

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .col2-set,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .wcf-col2-set,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .wcf-product-option-wrap {
	display: block;
	width: 100%;
	float: none;
	padding: 0;
	border-radius: 0;
	margin-top: 10px;
	margin-bottom: 20px;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .wcf-col2-set .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .wcf-col2-set .woocommerce-shipping-fields .woocommerce-shipping-fields__field-wrapper,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .wcf-col2-set .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .col2-set .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .col2-set .woocommerce-shipping-fields .woocommerce-shipping-fields__field-wrapper,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout .col2-set .woocommerce-additional-fields .woocommerce-additional-fields__field-wrapper {
	margin: 0;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-additional-fields > h3,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-billing-fields > h3,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step #order_review_heading,
.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout #order_review_heading {
	display: none !important;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce-checkout #order_review {
	float: none;
	width: 100%;
}

.et_pb_module #wcf-embed-checkout-form.wcf-embed-checkout-form-two-step .woocommerce a:not( .wcf-next-button ) {
	color: #fff;
}

/**
* *************************************
* Two Step Divi Compatibility css End ( Migrated from lite )
* *************************************
*/

/**
* Modern Checkout Styles
*/

.et_pb_module #wcf-embed-checkout-form .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-qty {
	max-width: 100px;
}

.et_pb_module #wcf-embed-checkout-form .wcf-yp-skin-cards .wcf-qty-options .wcf-qty-row .wcf-item-content-options .wcf-price {
	margin-right: 0;
}

@media ( max-width: 768px ) {
	.et_pb_module #wcf-embed-checkout-form .wcf-product-option-wrap.wcf-yp-skin-cards .wcf-qty-options .wcf-qty {
		width: 40%;
		margin-right: 25px;
	}
}
