const cIcons = {};
cIcons.inherit = (
	<svg
		xmlns="https://www.w3.org/2000/svg"
		fillRule="evenodd"
		width="24"
		height="24"
		strokeLinejoin="round"
		strokeMiterlimit="1.5"
		clipRule="evenodd"
		viewBox="0 0 24 24"
	>
		<path
			fill="none"
			stroke="currentColor"
			strokeWidth="1.5"
			d="M12.383 26.976l2.01-.941 1.884.538 1.623 1.666-.692.236.281 1.539 1.552 1.35-1.552 2.153"
			transform="matrix(.68343 0 0 .68343 -5.086 -1.48)"
		></path>
		<path
			fill="none"
			stroke="currentColor"
			strokeLinecap="square"
			strokeWidth="1.5"
			d="M12.016 24.355l.998-.952M33.573 26.085l-1.636-1.343-1.348-1.248M26.35 27.774l.332.37 1.349.756-.711 2.768 1.274 1.203.989-.449 3.933-6.226"
			transform="matrix(.68343 0 0 .68343 -5.086 -1.48)"
		></path>
		<path
			fill="none"
			stroke="currentColor"
			strokeLinecap="square"
			strokeWidth="1.5"
			d="M29.519 9.539c5.298 1.881 9.099 6.977 9.099 12.961 0 7.582-6.102 13.737-13.618 13.737S11.382 30.082 11.382 22.5"
			transform="matrix(.68343 0 0 .68343 -5.086 -1.48)"
		></path>
		<path
			fill="none"
			stroke="currentColor"
			strokeLinecap="square"
			strokeWidth="1.5"
			d="M20.684 3.211v9.433h4.179l-6.429 6.429-6.429-6.429h4.179V8.021"
			transform="matrix(.68343 0 0 .68343 -5.086 -1.48) translate(-6.686 -1.321) scale(1.55693)"
		></path>
		<path
			fill="none"
			stroke="currentColor"
			strokeLinecap="square"
			strokeWidth="1.5"
			d="M32.994 21.014l.2.205 3.364.329 1.532 2.432M32.931 12.002l-3.219.944-1.054 1.996-.408-.003"
			transform="matrix(.68343 0 0 .68343 -5.086 -1.48)"
		></path>
	</svg>
);
export default cIcons;
