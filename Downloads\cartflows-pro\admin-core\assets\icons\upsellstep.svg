<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="128" height="128" fill="#D1D5DB"/>
<g filter="url(#filter0_d_1033_10125)">
<rect x="8" y="8" width="112" height="112" rx="6" fill="#F9FAFB"/>
</g>
<circle cx="17" cy="16" r="3" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M84 16C84 15.4477 84.4477 15 85 15H93C93.5523 15 94 15.4477 94 16C94 16.5523 93.5523 17 93 17H85C84.4477 17 84 16.5523 84 16Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 16C70 15.4477 70.4477 15 71 15H79C79.5523 15 80 15.4477 80 16C80 16.5523 79.5523 17 79 17H71C70.4477 17 70 16.5523 70 16Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M98 16C98 15.4477 98.4477 15 99 15H107C107.552 15 108 15.4477 108 16C108 16.5523 107.552 17 107 17H99C98.4477 17 98 16.5523 98 16Z" fill="#E5E7EB"/>
<path d="M27 51C27 48.7909 28.7909 47 31 47H97C99.2091 47 101 48.7909 101 51V79C101 81.2091 99.2091 83 97 83H31C28.7909 83 27 81.2091 27 79V51Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31 46H97C99.7614 46 102 48.2386 102 51V79C102 81.7614 99.7614 84 97 84H31C28.2386 84 26 81.7614 26 79V51C26 48.2386 28.2386 46 31 46ZM31 47C28.7909 47 27 48.7909 27 51V79C27 81.2091 28.7909 83 31 83H97C99.2091 83 101 81.2091 101 79V51C101 48.7909 99.2091 47 97 47H31Z" fill="#E5E7EB"/>
<path d="M30 53.2C30 52.0799 30 51.5198 30.218 51.092C30.4097 50.7157 30.7157 50.4097 31.092 50.218C31.5198 50 32.0799 50 33.2 50H56.8C57.9201 50 58.4802 50 58.908 50.218C59.2843 50.4097 59.5903 50.7157 59.782 51.092C60 51.5198 60 52.0799 60 53.2V76.8C60 77.9201 60 78.4802 59.782 78.908C59.5903 79.2843 59.2843 79.5903 58.908 79.782C58.4802 80 57.9201 80 56.8 80H33.2C32.0799 80 31.5198 80 31.092 79.782C30.7157 79.5903 30.4097 79.2843 30.218 78.908C30 78.4802 30 77.9201 30 76.8V53.2Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63 63C63 62.4477 63.4477 62 64 62H88C88.5523 62 89 62.4477 89 63C89 63.5523 88.5523 64 88 64H64C63.4477 64 63 63.5523 63 63Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63 59C63 58.4477 63.4477 58 64 58H94C94.5523 58 95 58.4477 95 59C95 59.5523 94.5523 60 94 60H64C63.4477 60 63 59.5523 63 59Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63 54C63 53.4477 63.4477 53 64 53H76C76.5523 53 77 53.4477 77 54C77 54.5523 76.5523 55 76 55H64C63.4477 55 63 54.5523 63 54Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M83 75C83 74.4477 83.3518 74 83.7857 74H93.2143C93.6482 74 94 74.4477 94 75C94 75.5523 93.6482 76 93.2143 76H83.7857C83.3518 76 83 75.5523 83 75Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63 68C63 67.4477 63.4477 67 64 67H66C66.5523 67 67 67.4477 67 68C67 68.5523 66.5523 69 66 69H64C63.4477 69 63 68.5523 63 68Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M69 68C69 67.4477 69.4477 67 70 67H73C73.5523 67 74 67.4477 74 68C74 68.5523 73.5523 69 73 69H70C69.4477 69 69 68.5523 69 68Z" fill="#9CA3AF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M63 75C63 73.8954 64.066 73 65.381 73H77.619C78.934 73 80 73.8954 80 75C80 76.1046 78.934 77 77.619 77H65.381C64.066 77 63 76.1046 63 75Z" fill="#9CA3AF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M48 37C48 36.4477 48.4477 36 49 36H79C79.5523 36 80 36.4477 80 37C80 37.5523 79.5523 38 79 38H49C48.4477 38 48 37.5523 48 37Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M57 32C57 31.4477 57.4477 31 58 31H70C70.5523 31 71 31.4477 71 32C71 32.5523 70.5523 33 70 33H58C57.4477 33 57 32.5523 57 32Z" fill="#D1D5DB"/>
<path d="M44.0261 54.1827C44.2661 53.1518 45.7339 53.1518 45.9739 54.1827L47.4486 60.516C47.5614 61.0001 48.0126 61.328 48.5079 61.2856L54.9869 60.731C56.0415 60.6407 56.4951 62.0367 55.5888 62.5836L50.0212 65.9432C49.5956 66.2 49.4233 66.7304 49.6166 67.1884L52.1462 73.1789C52.5579 74.154 51.3705 75.0168 50.5703 74.3238L45.6546 70.0669C45.2789 69.7415 44.7211 69.7415 44.3454 70.0669L39.4297 74.3238C38.6295 75.0168 37.4421 74.154 37.8538 73.1789L40.3834 67.1884C40.5767 66.7304 40.4044 66.2 39.9788 65.9432L34.4112 62.5836C33.5049 62.0367 33.9584 60.6407 35.0131 60.731L41.4921 61.2856C41.9874 61.328 42.4386 61.0001 42.5514 60.516L44.0261 54.1827Z" fill="#D1D5DB"/>
<defs>
<filter id="filter0_d_1033_10125" x="6" y="8" width="116" height="118" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_1033_10125"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1033_10125"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1033_10125" result="shape"/>
</filter>
</defs>
</svg>
