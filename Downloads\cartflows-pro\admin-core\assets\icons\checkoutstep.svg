<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="128" height="128" fill="#D1D5DB"/>
<g filter="url(#filter0_d_727_2373)">
<rect x="8" y="8" width="112" height="112" rx="6" fill="#F9FAFB"/>
</g>
<path d="M8 17.6C8 14.2397 8 12.5595 8.65396 11.2761C9.2292 10.1471 10.1471 9.2292 11.2761 8.65396C12.5595 8 14.2397 8 17.6 8H110.4C113.76 8 115.44 8 116.724 8.65396C117.853 9.2292 118.771 10.1471 119.346 11.2761C120 12.5595 120 14.2397 120 17.6V40H8V17.6Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 26C20 24.8954 21.066 24 22.381 24H34.619C35.934 24 37 24.8954 37 26C37 27.1046 35.934 28 34.619 28H22.381C21.066 28 20 27.1046 20 26Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 33C20 32.4477 20.4477 32 21 32H53C53.5523 32 54 32.4477 54 33C54 33.5523 53.5523 34 53 34H21C20.4477 34 20 33.5523 20 33Z" fill="#E5E7EB"/>
<circle cx="17" cy="16" r="3" fill="#D1D5DB"/>
<circle cx="96" cy="31" r="3" fill="#E5E7EB"/>
<circle cx="87" cy="31" r="3" fill="#E5E7EB"/>
<circle cx="105" cy="31" r="3" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 16.0002C70 15.448 70.4477 15.0002 71 15.0002H79C79.5523 15.0002 80 15.448 80 16.0002C80 16.5525 79.5523 17.0002 79 17.0002H71C70.4477 17.0002 70 16.5525 70 16.0002Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M84 16.0002C84 15.448 84.4477 15.0002 85 15.0002H93C93.5523 15.0002 94 15.448 94 16.0002C94 16.5525 93.5523 17.0002 93 17.0002H85C84.4477 17.0002 84 16.5525 84 16.0002Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M98 16C98 15.4477 98.4477 15 99 15H107C107.552 15 108 15.4477 108 16C108 16.5523 107.552 17 107 17H99C98.4477 17 98 16.5523 98 16Z" fill="#E5E7EB"/>
<path d="M20 56C20 54.8954 20.8954 54 22 54H63C64.1046 54 65 54.8954 65 56V58C65 59.1046 64.1046 60 63 60H22C20.8954 60 20 59.1046 20 58V56Z" fill="#E5E7EB"/>
<path d="M20 66C20 64.8954 20.8954 64 22 64H63C64.1046 64 65 64.8954 65 66V68C65 69.1046 64.1046 70 63 70H22C20.8954 70 20 69.1046 20 68V66Z" fill="#E5E7EB"/>
<path d="M20 86C20 84.8954 20.8954 84 22 84H63C64.1046 84 65 84.8954 65 86V98C65 99.1046 64.1046 100 63 100H22C20.8954 100 20 99.1046 20 98V86Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 49C20 48.4477 20.4477 48 21 48H37C37.5523 48 38 48.4477 38 49C38 49.5523 37.5523 50 37 50H21C20.4477 50 20 49.5523 20 49Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 79C20 78.4477 20.4477 78 21 78H37C37.5523 78 38 78.4477 38 79C38 79.5523 37.5523 80 37 80H21C20.4477 80 20 79.5523 20 79Z" fill="#D1D5DB"/>
<path d="M20 106C20 104.895 20.8954 104 22 104H63C64.1046 104 65 104.895 65 106V108C65 109.105 64.1046 110 63 110H22C20.8954 110 20 109.105 20 108V106Z" fill="#D1D5DB"/>
<path d="M74 93C74 91.8954 74.8954 91 76 91H93C94.1046 91 95 91.8954 95 93V95C95 96.1046 94.1046 97 93 97H76C74.8954 97 74 96.1046 74 95V93Z" fill="#E5E7EB"/>
<path d="M97 93C97 91.8954 97.8954 91 99 91H106C107.105 91 108 91.8954 108 93V95C108 96.1046 107.105 97 106 97H99C97.8954 97 97 96.1046 97 95V93Z" fill="#D1D5DB"/>
<path d="M74 56C74 54.8954 74.8954 54 76 54H106C107.105 54 108 54.8954 108 56V84C108 85.1046 107.105 86 106 86H76C74.8954 86 74 85.1046 74 84V56Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M76 53H106C107.657 53 109 54.3431 109 56V84C109 85.6569 107.657 87 106 87H76C74.3431 87 73 85.6569 73 84V56C73 54.3431 74.3431 53 76 53ZM76 54C74.8954 54 74 54.8954 74 56V84C74 85.1046 74.8954 86 76 86H106C107.105 86 108 85.1046 108 84V56C108 54.8954 107.105 54 106 54H76Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M77 81C77 80.4477 77.4477 80 78 80H83C83.5523 80 84 80.4477 84 81C84 81.5523 83.5523 82 83 82H78C77.4477 82 77 81.5523 77 81Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M77 76C77 75.4477 77.4477 75 78 75H87C87.5523 75 88 75.4477 88 76C88 76.5523 87.5523 77 87 77H78C77.4477 77 77 76.5523 77 76Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M86 66C86 65.4477 86.4477 65 87 65H94C94.5523 65 95 65.4477 95 66C95 66.5523 94.5523 67 94 67H87C86.4477 67 86 66.5523 86 66Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M86 69C86 68.4477 86.4477 68 87 68H88C88.5523 68 89 68.4477 89 69C89 69.5523 88.5523 70 88 70H87C86.4477 70 86 69.5523 86 69Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M77 59C77 58.4477 77.4477 58 78 58H85C85.5523 58 86 58.4477 86 59C86 59.5523 85.5523 60 85 60H78C77.4477 60 77 59.5523 77 59Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M97 81C97 80.4477 97.4477 80 98 80H104C104.552 80 105 80.4477 105 81C105 81.5523 104.552 82 104 82H98C97.4477 82 97 81.5523 97 81Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M99 76C99 75.4477 99.4477 75 100 75H104C104.552 75 105 75.4477 105 76C105 76.5523 104.552 77 104 77H100C99.4477 77 99 76.5523 99 76Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M99 68C99 67.4477 99.4477 67 100 67H104C104.552 67 105 67.4477 105 68C105 68.5523 104.552 69 104 69H100C99.4477 69 99 68.5523 99 68Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M97 59C97 58.4477 97.4477 58 98 58H104C104.552 58 105 58.4477 105 59C105 59.5523 104.552 60 104 60H98C97.4477 60 97 59.5523 97 59Z" fill="#D1D5DB"/>
<path d="M77 66C77 64.8954 77.8954 64 79 64H82C83.1046 64 84 64.8954 84 66V69C84 70.1046 83.1046 71 82 71H79C77.8954 71 77 70.1046 77 69V66Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M73 49C73 48.4477 73.4477 48 74 48H90C90.5523 48 91 48.4477 91 49C91 49.5523 90.5523 50 90 50H74C73.4477 50 73 49.5523 73 49Z" fill="#D1D5DB"/>
<defs>
<filter id="filter0_d_727_2373" x="6" y="8" width="116" height="118" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_727_2373"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_727_2373"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_727_2373" result="shape"/>
</filter>
</defs>
</svg>
