(i=>{function e(){let o=(()=>{let o,c;var e={hidden:"visibilitychange",webkitHidden:"webkitvisibilitychange",mozHidden:"mozvisibilitychange",msHidden:"msvisibilitychange"};for(o in e)if(o in document){c=e[o];break}return function(e){return e&&document.addEventListener(c,e),!document[o]}})();o(function(){var e=(o=>{var c=document.cookie.split(";");for(let e=0;e<c.length;e++){var t=c[e].split("=");if(o===t[0].trim())return decodeURIComponent(t[1])}return null})(cartflows.active_checkout_cookie);e&&o()&&parseInt(cartflows.current_step)!==parseInt(e)&&(i(".woocommerce-checkout-payment, .woocommerce-checkout-review-order-table").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),i(document.body).trigger("update_checkout"),i(document).ajaxComplete(function(e,o){o.hasOwnProperty("responseJSON")&&(o=o.responseJSON.hasOwnProperty("fragments")?o.responseJSON.fragments:null)&&o.hasOwnProperty("wcf_cart_data")&&i(document.body).trigger("wcf_cart_data_restored",[o.wcf_cart_data])}))})}function o(){function r(e,o,c,t){let a=!1;""===e||"select"===t&&" "===e?o.hasClass("validate-required")&&(c.addClass("field-required"),a=!0):c.removeClass("field-required"),[t=!1,e,o]=[a,o,c],e.find(".wcf-field-required-error").remove(),t&&"yes"===cartflows.field_validation.is_enabled?(t=e.find("label").text(),o.after('<span class="wcf-field-required-error">'+t.replace(/\*/g,"").trim()+" "+cartflows.field_validation.error_msg+"</span>")):e.find(".wcf-field-required-error").remove()}var e=(o=i("form.woocommerce-checkout")).find("input, textarea"),o=o.find("select");e.on("blur",function(){var e,o=i(this),c=o.attr("type"),t=o.closest("p.form-row"),a=o.val();r(a,t,o,c),"number"===c&&(c=o.attr("min"),e=o.attr("max"),a=Number(a),t=t,o=o,c=Number(c),e=Number(e),""===a||a<c||e<a?(o.addClass("field-required"),o.after('<span class="wcf-field-required-error">'+cartflows.field_validation_msgs.number_field+c+" & "+e+"</span>")):(o.removeClass("field-required"),t.find(".wcf-field-required-error").remove()))}),o.on("blur",function(){var e=i(this).closest("p.form-row"),o=e.find(".select2-container--default"),c=e.find("select").val();r(c,e,o,"select")})}function c(){if("yes"===cartflows.allow_persistence&&!1!==(()=>{var e="test";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}})()){var o="form.woocommerce-checkout #customer_details";let e={set(){let e=[];var o=i("form.woocommerce-checkout #customer_details");localStorage.removeItem("cartflows_checkout_form"),o.find("input[type=text], select, input[type=email], input[type=tel]").each(function(){e.push({name:this.name,value:this.value})}),cartflows_checkout_form=JSON.stringify(e),localStorage.setItem("cartflows_checkout_form",cartflows_checkout_form)},get(){if(null!==localStorage.getItem("cartflows_checkout_form")){checkout_data=JSON.parse(localStorage.getItem("cartflows_checkout_form"));for(let e=0;e<checkout_data.length;e++)i("form.woocommerce-checkout [name="+checkout_data[e].name+"]").hasClass("select2-hidden-accessible")?i("form.woocommerce-checkout [name="+checkout_data[e].name+"]").selectWoo("val",[checkout_data[e].value]):i("form.woocommerce-checkout [name="+checkout_data[e].name+"]").val(checkout_data[e].value)}}};e.get(),i(o+" input, "+o+" select").on("change",function(){e.set()})}}function t(){var e=i(".wcf-field-modern-label .woocommerce input, .wcf-field-modern-label .woocommerce select, .wcf-field-modern-label .woocommerce textarea");function o(e){var o=e.closest(".form-row"),c=e.is("select")||e.hasClass("select2-hidden-accessible")?e.find(":selected").text():e.val(),e=e.attr("type");""===c?o.removeClass("wcf-anim-label"):"hidden"===e?o.addClass("wcf-anim-hidden-label"):o.addClass("wcf-anim-label")}e.on("focusout input",function(){var e=i(this);o(e)}),i(e).each(function(){o(i(this))})}function a(){let e=i(".wcf-customer-info #billing_email").val();if(void 0!==e&&!cartflows.is_logged_in){if(""!==e){let o=i("#billing_email"),c=i(".wcf-email-validation-block"),t=i(".wcf-customer-login-section");if(c.remove(),r=e,!/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(r))return o.after('<span class="wcf-email-validation-block error">'+cartflows.email_validation_msgs.error_msg+"</span>"),t.removeClass("wcf-show");clearTimeout(n);let a=++l;n=setTimeout(function(){i.ajax({url:cartflows.ajax_url,type:"POST",data:{action:"wcf_check_email_exists",email_address:e,security:cartflows.check_email_exist_nonce},success(e){a===l&&(c.remove(),e.data&&e.data.success&&t.hasClass("wcf-show")?o.after('<span class="wcf-email-validation-block success">'+cartflows.email_validation_msgs.success_msg+"</span>"):e.data&&e.data.success?(e.data.is_login_allowed&&(o.after('<span class="wcf-email-validation-block success">'+cartflows.email_validation_msgs.success_msg+"</span>"),t.slideDown(400).addClass("wcf-show")),i(".wcf-create-account-section").hide(),i(".woocommerce-billing-fields__customer-login-label").show()):(t.slideUp(400).removeClass("wcf-show"),i(".wcf-create-account-section .create-account label.checkbox").show(),i(".wcf-create-account-section").show(),i(".woocommerce-billing-fields__customer-login-label").hide()))}})},300)}else i(".wcf-create-account-section").hide(),i(".wcf-customer-login-section").hide(),i(".wcf-email-validation-block").hide(),i(".woocommerce-billing-fields__customer-login-label").show();var r}}function r(){i("body").hasClass("cartflows-instant-checkout")&&i(".woocommerce-NoticeGroup").prependTo(".woocommerce-notices-wrapper")}let s={init(){i(document.body).on("click",".wcf-submit-coupon",this.submit_coupon),i(document.body).on("click",".wcf-remove-coupon",this.remove_coupon)},submit_coupon(e){e.preventDefault();let c=i(".wcf-custom-coupon-field"),o=i(this).closest(c),t=o.find(".wcf-coupon-code-input"),a=t.val();if(""===a)return t.addClass("field-required"),!1;t.removeClass("field-required");e=i(".wcf-embed-checkout-form #billing_email").val(),e={coupon_code:a,action:"wcf_woo_apply_coupon",security:cartflows.wcf_validate_coupon_nonce,billing_email:e};i.ajax({type:"POST",url:cartflows.ajax_url,data:e,success(e){var o=i(".wcf-custom-coupon-field");o.find(".woocommerce-error, .woocommerce-message").remove(),e&&!0===e.status?(i(document.body).trigger("update_checkout",{update_shipping_method:!1}),o.prepend(e.msg),c.addClass("wcf-coupon-applied")):e&&e.msg&&(o.prepend(e.msg),c.removeClass("wcf-coupon-applied"))}})},remove_coupon(e){e.preventDefault();e={coupon_code:i(this).attr("data-coupon"),action:"wcf_woo_remove_coupon",security:cartflows.wcf_validate_remove_coupon_nonce};i.ajax({type:"POST",url:cartflows.ajax_url,data:e,success(e){var o=i(".wcf-custom-coupon-field");o.find(".woocommerce-error, .woocommerce-message").hide(),i(".wcf-custom-coupon-field").removeClass("wcf-coupon-applied"),e&&(i(document.body).trigger("update_checkout",{update_shipping_method:!1}),o.prepend(e))}})}},l=0,n;i(function(){c(),e(),o(),i(document.body).on("click","#wcf-embed-checkout-form .wcf-remove-product",function(e){e.preventDefault();let o=i(this).attr("data-id");e={p_key:i(this).attr("data-item-key"),p_id:o,action:"wcf_woo_remove_cart_product",security:cartflows.wcf_validate_remove_cart_product_nonce};i.ajax({type:"POST",url:cartflows.ajax_url,data:e,success(e){e=JSON.parse(e);!1===e.need_shipping&&i("#wcf-embed-checkout-form").find("#ship-to-different-address-checkbox").attr("checked",!1),void 0!==e.is_order_bump&&e.is_order_bump&&e.order_bump_id&&i('input[name="wcf-bump-order-cb-'+e.order_bump_id+'"]').prop("checked",!1),i("#wcf-embed-checkout-form").find(".woocommerce-notices-wrapper").first().html(e.msg),i(document).trigger("cartflows_remove_product",[o]),i("#wcf-embed-checkout-form").trigger("update_checkout")}})}),s.init(),jQuery.each(cartflows_checkout_optimized_fields,function(o,e){e.is_optimized&&(jQuery("#"+o).prepend('<a href="#" id="wcf_optimized_'+o+'">'+e.field_label+"</a>"),jQuery("#wcf_optimized_"+o).on("click",function(e){e.preventDefault(),jQuery("#"+o).removeClass("wcf-hide-field");e=o.replace(/_field/g,"");i("#"+e).trigger("focus"),jQuery(this).remove()}))}),i(document.body).on("click",".coupon-field .wcf-custom-coupon-field .wcf-optimized-coupon-field",function(e){e.preventDefault(),i(".wcf-custom-coupon-field").removeClass("wcf-hide-field"),i(this).remove()}),i(document.body).on("checkout_error updated_checkout",r),t(),i(".wcf-customer-info #billing_email").on("input",function(){a()}),0<i(".wcf-customer-info #billing_email").length&&a(),i(".wcf-customer-login-url").on("click",function(e){e.preventDefault();e=i(".wcf-customer-login-section");e.hasClass("wcf-show")?(e.slideUp(400),e.removeClass("wcf-show")):(e.slideDown(400),e.addClass("wcf-show"))}),i(".wcf-customer-login-section__login-button").on("click",function(){var e=i("#billing_email").val(),o=i("#billing_password").val();i.ajax({url:cartflows.ajax_url,type:"POST",data:{action:"wcf_woocommerce_login",email:e,password:o,security:cartflows.woocommerce_login_nonce},success(e){e.data&&e.data.success?location.reload():i(".wcf-customer-info__notice").addClass("wcf-notice").html(e.data.error)}})})})})(jQuery);