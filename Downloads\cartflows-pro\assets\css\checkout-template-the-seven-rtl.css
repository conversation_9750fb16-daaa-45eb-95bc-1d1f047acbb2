/**
* *************************
* This css is loaded when when the The7 theme is enabled.
* Just to correct layout of the page.
* *************************
*/

/* Basic */

body,
button,
input,
select,
optgroup,
textarea,
body.page,
.wf-container > * {
	font-family: normal 1rem / 2 sans-serif, Helvetica, Arial, Verdana, sans-serif;
	font-size: inherit;
}

.wcf-embed-checkout-form-one-column #customer_details,
.wcf-embed-checkout-form-two-step #customer_details {
	width: 100%;
}
.wcf-embed-checkout-form-two-column #customer_details {
	width: 55%;
}

input[type="text"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="email"],
input[type="number"],
input[type="date"],
input[type="range"],
input[type="password"],
select,
.wpcf7-number {
	height: auto;
}

#ship-to-different-address {
	font: inherit;
}

#ship-to-different-address label,
.create-account label.checkbox span {
	font-size: inherit;
	line-height: inherit;
}

.woocommerce-checkout-review-order .product-name,
.woocommerce-checkout-review-order .product-name a,
.order_details .product-name,
.order_details .product-name a {
	font: inherit;
	font-size: inherit;
	color: inherit;
}
.woocommerce-checkout-review-order .product-quantity,
.woocommerce-checkout-review-order .product-total,
.woocommerce-checkout-review-order .cart-subtotal th,
.woocommerce-checkout-review-order .order-total th,
.woocommerce-checkout-review-order .shipping th,
.woocommerce-checkout-review-order .about_paypal,
.woocommerce-checkout-review-order .product-total .woocommerce-Price-amount,
.woocommerce-checkout-review-order tfoot th,
.order_details .product-quantity,
.order_details .product-total,
.order_details .cart-subtotal th,
.order_details .order-total th,
.order_details .shipping th,
.order_details .about_paypal,
.order_details .product-total .woocommerce-Price-amount,
.order_details tfoot th {
	color: inherit;
}

#shipping_method label,
.payment_methods label {
	color: inherit;
	font-size: inherit;
	line-height: inherit;
}

.woocommerce-terms-and-conditions-text,
.woocommerce-terms-and-conditions-checkbox-text,
.woocommerce-privacy-policy-text {
	font-size: inherit;
	line-height: inherit;
}

div:not( .wc-coupon-wrap ):not( .wc-login-wrap ):not( .woocommerce-MyAccount-content ) > .woocommerce-message .woocommerce-message-text,
div:not( .wc-coupon-wrap ):not( .wc-login-wrap ):not( .woocommerce-MyAccount-content ) > .woocommerce-message .woocommerce-info-text,
div:not( .wc-coupon-wrap ):not( .wc-login-wrap ):not( .woocommerce-MyAccount-content ) > .woocommerce-message .woocommerce-error-text,
.woocommerce-error .woocommerce-message-text,
.woocommerce-error .woocommerce-info-text,
.woocommerce-error .woocommerce-error-text,
div:not( .wc-coupon-wrap ):not( .wc-login-wrap ):not( .woocommerce-MyAccount-content ) > .woocommerce-info .woocommerce-message-text,
div:not( .wc-coupon-wrap ):not( .wc-login-wrap ):not( .woocommerce-MyAccount-content ) > .woocommerce-info .woocommerce-info-text,
div:not( .wc-coupon-wrap ):not( .wc-login-wrap ):not( .woocommerce-MyAccount-content ) > .woocommerce-info .woocommerce-error-text {
	font: inherit;
}

.checkout .woocommerce-NoticeGroup .woocommerce-error .woocommerce-error-text {
	margin: 0;
	padding-right: 0;
}

table.shop_table thead tr th,
table.shop_table thead tr td,
table.shop_table tbody tr th,
table.shop_table tbody tr td {
	border: none;
	font-weight: 400;
	width: 50%;
}

@media screen and ( max-width: 990px ) {
	#customer_details {
		width: 100%;
	}
	.mt20 {
		margin-top: 10px !important;
	}
}

@media only screen and ( max-width: 768px ) {
	#customer_details {
		width: 100%;
	}
	.mt20 {
		margin-top: 10px !important;
	}
}

@media screen and ( max-width: 600px ) {
	#customer_details {
		width: 100%;
	}

	table.shop_table tbody tr {
		padding: 0;
	}
	.shop_table tr {
		border-top: inherit;
		-js-display: flex;
		display: flex;
		flex-flow: inherit;
	}
	.shop_table tr.cart_item td.product-name {
		align-items: baseline;
		flex-flow: inherit;
		text-align: right;
	}
	.shop_table thead {
		display: block;
	}
}
