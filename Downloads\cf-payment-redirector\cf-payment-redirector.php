<?php
/**
 * Plugin Name: CartFlows Payment Redirector
 * Plugin URI: #
 * Description: Redireciona usuários para páginas específicas após o pagamento com base no método de pagamento.
 * Version: 1.0.0
 * Author: CartFlows
 * Author URI: #
 * Text Domain: cf-payment-redirector
 * Domain Path: /languages
 * WC requires at least: 3.0
 * WC tested up to: 9.4
 *
 * @package CF_Payment_Redirector
 */

// Sair se acessado diretamente.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Definir constantes.
 */
define( 'CFPR_FILE', __FILE__ );
define( 'CFPR_BASE', plugin_basename( CFPR_FILE ) );
define( 'CFPR_DIR', plugin_dir_path( CFPR_FILE ) );
define( 'CFPR_URL', plugins_url( '/', CFPR_FILE ) );
define( 'CFPR_VER', '1.0.0' );
define( 'CFPR_SLUG', 'cf-payment-redirector' );

/**
 * Classe Principal do Plugin
 */
class CF_Payment_Redirector {

    /**
     * Variável de instância.
     *
     * @var CF_Payment_Redirector
     */
    private static $instance;

    /**
     * Inicializador.
     */
    public static function get_instance() {
        if ( ! isset( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Construtor.
     */
    public function __construct() {
        $this->define_constants();
        $this->includes();
        $this->init_hooks();

        // Hook para verificação do CartFlows e WooCommerce.
        add_action( 'admin_notices', array( $this, 'check_required_plugins' ) );
    }

    /**
     * Definir constantes.
     */
    private function define_constants() {
        // Já definidas no início do arquivo.
    }

    /**
     * Incluir arquivos necessários.
     */
    private function includes() {
        // Incluir classes principais
        require_once CFPR_DIR . 'includes/class-cfpr-redirector.php';
        require_once CFPR_DIR . 'includes/class-cfpr-integration.php';
        
        // Admin
        if ( is_admin() ) {
            require_once CFPR_DIR . 'includes/admin/class-cfpr-admin.php';
        }
    }

    /**
     * Inicializar hooks.
     */
    private function init_hooks() {
        register_activation_hook( CFPR_FILE, array( $this, 'activation' ) );
        register_deactivation_hook( CFPR_FILE, array( $this, 'deactivation' ) );
        
        add_action( 'plugins_loaded', array( $this, 'load_plugin' ) );
    }

    /**
     * Ativação do plugin.
     */
    public function activation() {
        // Verificar se o CartFlows está ativo
        if ( ! class_exists( 'Cartflows_Loader' ) ) {
            deactivate_plugins( plugin_basename( __FILE__ ) );
            wp_die( __( 'O plugin CartFlows Payment Redirector requer o plugin CartFlows ativo.', 'cf-payment-redirector' ) );
        }

        // Verificar se o WooCommerce está ativo
        if ( ! class_exists( 'WooCommerce' ) ) {
            deactivate_plugins( plugin_basename( __FILE__ ) );
            wp_die( __( 'O plugin CartFlows Payment Redirector requer o plugin WooCommerce ativo.', 'cf-payment-redirector' ) );
        }
    }

    /**
     * Desativação do plugin.
     */
    public function deactivation() {
        // Ações de desativação, se necessário
    }

    /**
     * Carregar plugin após todos os plugins carregados.
     */
    public function load_plugin() {
        // Verificar se o CartFlows e o WooCommerce estão ativos
        if ( class_exists( 'Cartflows_Loader' ) && class_exists( 'WooCommerce' ) ) {
            // Iniciar componentes do plugin
            $this->init_components();
        }
    }

    /**
     * Verificar plugins necessários.
     */
    public function check_required_plugins() {
        if ( ! class_exists( 'Cartflows_Loader' ) ) {
            echo '<div class="notice notice-error"><p>' . __( 'O plugin CartFlows Payment Redirector requer o plugin CartFlows ativo.', 'cf-payment-redirector' ) . '</p></div>';
        }

        if ( ! class_exists( 'WooCommerce' ) ) {
            echo '<div class="notice notice-error"><p>' . __( 'O plugin CartFlows Payment Redirector requer o plugin WooCommerce ativo.', 'cf-payment-redirector' ) . '</p></div>';
        }
    }

    /**
     * Inicializar componentes.
     */
    private function init_components() {
        // Iniciar o redirecionador
        CFPR_Redirector::get_instance();
        
        // Iniciar a integração
        CFPR_Integration::get_instance();
        
        // Iniciar admin
        if ( is_admin() ) {
            CFPR_Admin::get_instance();
        }
    }
}

/**
 * Principal função de inicialização.
 */
function cfpr() {
    return CF_Payment_Redirector::get_instance();
}

// Inicializar o plugin
cfpr(); 