jQuery(function(n){if(jQuery("#wcf-pre-checkout-offer-modal").length<1)return;let o="";jQuery("form.checkout").on("checkout_place_order.wcf_pre_checkout",function(){if("add"===jQuery(".wcf-pre-checkout-offer-action").val())return setTimeout(function(){{const u=jQuery("form.checkout").find('input[name="payment_method"]:checked').val();return o=u,jQuery(".wcf_validation_error").remove(),jQuery("form.checkout").addClass("loader").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),void n.ajax({url:wc_checkout_params.wc_ajax_url.toString().replace("%%endpoint%%","wcf_validate_form"),data:n("form.checkout").serialize()+"&security="+cartflows.wcf_pre_checkout_validate_form_nonce,dataType:"json",type:"POST",success(c){try{jQuery("form.checkout").removeClass("loader").unblock();let e="";if("success"!==c.result)throw"failure"===c.result?"Result failure":"Invalid response";var o;if("yes"===(e="authorize_net_cim_credit_card"===u?"yes":void 0===(o=jQuery("form.checkout").triggerHandler("checkout_place_order_"+u))||void 0!==o&&!0===o?"yes":"no")){var t=jQuery("#billing_first_name").val();const a=jQuery(".wcf-pre-checkout-offer-wrapper .wcf-content-modal-title h1").text();var r=a.match(/{([^}]+)}/);if(null!==r&&0!==t.length){var f=r[1];let e="";e="first_name"===f?a.replace(/{(.*?)}/,'<span class="wcf_first_name"></span>'):a.replace(/{(.*?)}/,""),n(".wcf-pre-checkout-offer-wrapper .wcf-content-modal-title h1").html(e),n(".wcf_first_name").text(t)}n(".wcf-pre-checkout-offer-wrapper").addClass("open"),n("html").addClass("wcf-pre-checkout-offer-open"),n(".wcf-pre-checkout-offer-action").val(""),setTimeout(function(){s()},100)}}catch(e){if(c.messages){jQuery("form.checkout").prepend('<div class="wcf_validation_error">'+c.messages+"</div>"),jQuery("form.checkout").removeClass("processing").unblock(),jQuery("form.checkout").find(".input-text, select, input:checkbox").trigger("validate").blur();let e=n(".wcf_validation_error");e.length||(e=n(".form.checkout")),n.scroll_to_notices(e)}}}})}},100),!1}),n("body").on("click",".wcf-pre-checkout-offer-btn",function(e){e.preventDefault();e=n("._wcf_checkout_id").val();""!==e&&(n(".wcf-pre-checkout-offer-btn").html(cartflows.add_to_cart_text),n.ajax({url:cartflows.ajax_url,data:{action:"wcf_add_to_cart",product_quantity:1,checkout_id:e,security:cartflows.wcf_pre_checkout_offer_nonce},dataType:"json",type:"POST",success(c){jQuery("form.checkout").off("checkout_place_order.wcf_pre_checkout"),"authorize_net_cim_credit_card"===o?(n("body").trigger("update_checkout",{update_shipping_method:!1}),n(document.body).on("updated_checkout.wcf_pre_checkout",function(e){n(".wcf-pre-checkout-offer-action").val(""),n(".wcf-pre-checkout-offer-btn").html(c.message),n(".wcf-pre-checkout-offer-wrapper").removeClass("open"),n("html").removeClass("wcf-pre-checkout-offer-open"),n("form.checkout").submit(),n(document.body).off("updated_checkout.wcf_pre_checkout")})):(n(".wcf-pre-checkout-offer-action").val(""),n(".wcf-pre-checkout-offer-btn").html(c.message),setTimeout(function(){n(".wcf-pre-checkout-offer-wrapper").removeClass("open"),n("html").removeClass("wcf-pre-checkout-offer-open"),n("form.checkout").submit()},600))}}))}),n("body").on("click",".wcf-pre-checkout-skip",function(){jQuery("form.checkout").off("checkout_place_order.wcf_pre_checkout"),n(".wcf-pre-checkout-offer-action").val(""),n(".wcf-pre-checkout-offer-wrapper").removeClass("open"),n("html").removeClass("wcf-pre-checkout-offer-open"),n("form.checkout").submit()}),n(window).on("resize",function(){s()});const s=function(){n(window).height()<n("#wcf-pre-checkout-offer-modal").height()?n("html").removeClass("wcf-pre-checkout-screen-size"):n("html").addClass("wcf-pre-checkout-screen-size")}});