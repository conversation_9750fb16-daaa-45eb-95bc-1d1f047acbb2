.wcf-clear::before,
.wcf-clear::after {
	content: " ";
	display: table;
}

.wcf-clear::after {
	clear: both;
}

/**
* ***********************************
* Custom Width Classes
* ***********************************
*/

.et_pb_module #wcf-optin-form .woocommerce form .wcf-column-33 {
	width: 33.33%;
}
.et_pb_module #wcf-optin-form .woocommerce form .wcf-column-50 {
	width: 50%;
}
.et_pb_module #wcf-optin-form .woocommerce form .wcf-column-100 {
	width: 100%;
	margin-top: 0 !important;
}

/* Select2 */

.et_pb_module #wcf-optin-form .woocommerce .woocommerce-billing-fields .form-row .select2-hidden-accessible {
	height: 0 !important;
	min-height: 0 !important;
	width: 0 !important;
	margin: 0 !important;
}

.et_pb_module #wcf-optin-form .select2-container--default .select2-selection--single .select2-selection__rendered {
	line-height: 26px;
	min-height: 26px;
	overflow: visible;
	padding-left: 0;
}

/**
* ********************
* Fields Skins
* ********************
*/

.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-billing-fields .form-row label:not( .checkbox ),
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-shipping-fields .form-row label,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-additional-fields .form-row label,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-account-fields .form-row label {
	position: absolute;
	z-index: 999;
	margin-top: 19px;
	margin-left: 15px;
	transition: all 0.235s ease;
	overflow: hidden;
	white-space: nowrap;
}

.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-billing-fields .form-row.wcf-anim-label label,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-shipping-fields .form-row.wcf-anim-label label,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-additional-fields .form-row.wcf-anim-label label,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-account-fields .form-row.wcf-anim-label label {
	margin-top: 8px;
	font-size: 12px;
}

/* Compatibility for the Astra Labels as placeholder */
.ast-checkout-labels-as-placeholders .et_pb_module #wcf-optin-form form #customer_details .form-row label {
	opacity: 1;
	position: relative;
	padding: 0;
	margin: 0 0 8px 0;
	line-height: 1em;
}

.ast-checkout-labels-as-placeholders .wcf-optin-form.wcf-field-floating-labels form #customer_details .form-row label {
	position: absolute;
	margin-top: 19px;
	padding: 0 1.02em;
	opacity: 1;
}

.ast-checkout-labels-as-placeholders .wcf-optin-form.wcf-field-floating-labels form #customer_details .form-row.wcf-anim-label label {
	margin-top: 8px;
	font-size: 12px;
}

.ast-checkout-labels-as-placeholders .wcf-optin-form.wcf-field-floating-labels form #customer_details .woocommerce-account-fields .form-row label.woocommerce-form__label-for-checkbox {
	position: relative;
	margin: 0;
	padding: 0;
}

/* Compatibility for the Astra Labels as placeholder */

.wcf-optin-form.wcf-field-floating-labels .woocommerce .form-row input[type="text"],
.wcf-optin-form.wcf-field-floating-labels .woocommerce .form-row input[type="email"],
.wcf-optin-form.wcf-field-floating-labels .woocommerce .form-row input[type="password"],
.wcf-optin-form.wcf-field-floating-labels .woocommerce .form-row input[type="tel"],
.wcf-optin-form.wcf-field-floating-labels .woocommerce .form-row textarea,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .form-row select,
.wcf-optin-form.wcf-field-floating-labels .select2-container--default .select2-selection--single {
	padding: 25px 12px 5px;
}

.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-billing-fields .form-row.wcf-anim-label-fix label,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-shipping-fields .form-row.wcf-anim-label-fix label,
.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-account-fields .form-row.wcf-anim-label-fix label {
	position: relative;
	margin: 0;
}

.wcf-optin-form.wcf-field-floating-labels .woocommerce .woocommerce-account-fields .form-row label.woocommerce-form__label-for-checkbox {
	position: relative;
	margin: 0;
}

/**
* ****************************************
* Mobile css for two step navigation Start
* ****************************************
*/
@media only screen and ( max-width: 768px ) {
	.et_pb_module #wcf-optin-form .woocommerce form .wcf-column-33,
	.et_pb_module #wcf-optin-form .woocommerce form .wcf-column-50,
	.et_pb_module #wcf-optin-form .woocommerce form .wcf-column-100 {
		width: 100%;
	}
}

/**
* ************************
* Optimize Checkout fields
* ************************
*/

.et_pb_module #wcf-optin-form .wcf-hide-field {
	-js-display: flex !important;
	display: flex !important;
	align-items: center;
	font-size: 13px;
}

.et_pb_module #wcf-optin-form .wcf-hide-field label,
.et_pb_module #wcf-optin-form .wcf-hide-field span {
	display: none !important;
}

.et_pb_module #wcf-optin-form .woocommerce .woocommerce-billing-fields .form-row.wcf-hide-field.mt20 {
	margin-top: 0;
}
