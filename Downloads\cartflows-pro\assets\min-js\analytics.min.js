!function(){function w(e,o,t){const n=new Date;n.setTime(n.getTime()+24*t*60*60*1e3);t="expires="+n.toUTCString();document.cookie=e+"="+o+"; "+t+"; path=/"}function _(e){const o=e+"=",t=decodeURIComponent(document.cookie),n=t.split("; ");let r=[];return n.forEach(e=>{0===e.indexOf(o)&&(r=e.substring(o.length))}),r}jQuery(function(){!async function(){if(cartflows&&""!==cartflows.current_step){const c=cartflows.current_flow,i=cartflows.current_step,s=cartflows.flow_cookie+c,a=cartflows.step_cookie+c;let e=_(s);var n=(e=0<e.length?JSON.parse(decodeURIComponent(e)):e).includes(i);n||e.push(i),w(s,encodeURIComponent(JSON.stringify(e)),cartflows.analytics_cookie_expire_time);let o=cartflows?.analytics_base_url;""!==o&&"null"!==o&&"/wp-json/cartflows-pro/v1/flow-analytics/"!==o||(r=window.location.pathname.split("/")[1]||"",r=window.location.origin+"/"+r+"/wp-json",o=r+"cartflows-pro/v1/flow-analytics/");const l=o,f=new URLSearchParams(window.location.search),p=[];0<f.size&&f.forEach(function(e,o){p.push({name:o,value:e})});let t=_(a);var r={step_id:cartflows.current_step,flow_id:cartflows.current_flow,is_returning:n,flow_cookie_data:JSON.stringify(e),step_cookie_data:t,url_params:JSON.stringify(p)};try{fetch(l,{method:"POST",mode:"cors",cache:"no-cache",credentials:"omit",headers:{"Content-Type":"application/json"},redirect:"follow",referrerPolicy:"no-referrer",body:JSON.stringify(r)}).then(e=>e.json()).then(e=>{var o;e.success&&(t=_(a),o=e.prev_control_id,t=0<t.length?JSON.parse(decodeURIComponent(t)):{},o&&t.hasOwnProperty(o)&&(t[o].conversion="yes",w(a,encodeURIComponent(JSON.stringify(t)),cartflows.analytics_cookie_expire_time)),t[cartflows.control_step]=JSON.parse(e.current_step_visit),w(a,encodeURIComponent(JSON.stringify(t)),cartflows.analytics_cookie_expire_time))})}catch(e){}}}()})}();