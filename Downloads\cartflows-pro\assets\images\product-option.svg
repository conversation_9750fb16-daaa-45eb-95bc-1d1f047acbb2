<svg
  role="img"
  aria-labelledby="loading-aria"
  xmlns="http://www.w3.org/2000/svg"
  preserveAspectRatio="none"
>
  <rect
    x="0"
    y="0"
    width="100%"
    height="100%"
    clip-path="url(#clip-path)"
    style='fill: url("#fill");'
  ></rect>
 <defs xmlns="http://www.w3.org/2000/svg">
    <clipPath id="clip-path">
        <rect x="0" y="0" rx="0" ry="0" width="NaN" height="NaN"/> 
        <rect x="91" y="60" rx="2" ry="2" width="70" height="70"/> 
        <rect x="21" y="80" rx="2" ry="2" width="32" height="31"/> 
        <rect x="23" y="196" rx="2" ry="2" width="32" height="31"/> 
        <rect x="91" y="178" rx="2" ry="2" width="70" height="70"/> 
        <rect x="8" y="16" rx="2" ry="2" width="100%" height="21"/> 
        <rect x="190" y="64" rx="2" ry="2" width="90%" height="12"/> 
        <rect x="190" y="90" rx="2" ry="2" width="60%" height="12"/> 
        <rect x="190" y="118" rx="2" ry="2" width="30%" height="12"/> 
        <rect x="190" y="180" rx="2" ry="2" width="90%" height="12"/> 
        <rect x="190" y="207" rx="2" ry="2" width="60%" height="12"/> 
        <rect x="190" y="235" rx="2" ry="2" width="30%" height="12"/>
    </clipPath>
    <linearGradient id="fill">
      <stop offset="0.599964" stop-color="#dddddd" stop-opacity="1">
        <animate attributeName="offset" values="-2; -2; 1" keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite"/>
      </stop>
      <stop offset="1.59996" stop-color="#b0b0b0" stop-opacity="1">
        <animate attributeName="offset" values="-1; -1; 2" keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite"/>
      </stop>
      <stop offset="2.59996" stop-color="#dddddd" stop-opacity="1">
        <animate attributeName="offset" values="0; 0; 3" keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>
  </defs>
</svg>