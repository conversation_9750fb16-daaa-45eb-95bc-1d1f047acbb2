(l=>{var s={timeout:3e5,data:{},remove(e){delete s.data[e]},exist(e){return!!s.data[e]&&(new Date).getTime()-s.data[e]._<s.timeout},get(e){return s.data[e].data},set(e,t,a){s.remove(e),s.data[e]={_:(new Date).getTime(),data:t},a&&"function"==typeof a&&a(t)}};l.ajaxPrefilter(function(o,e,t){if("cartflows"===o.author){let a=e.complete||l.noop,r=e.url;o.beforeSend=function(){var e;return!s.exist(r)||(e=s.get(r),CartFlowsAPI._api_cached_request(e),!1)},o.complete=function(e,t){t={args:o.args,items:e.responseText?JSON.parse(e.responseText):"",items_count:e.getResponseHeader("x-wp-total")||0,callback:o.callback,status:t,XHR:e};s.set(r,t,a)}}}),CartFlowsAPI={_log(e,t){CartFlowsImportVars.debug&&"table"!==t&&(new Date).toLocaleTimeString()},_api_url:CartFlowsImportVars.server_rest_url,_api_cached_request(e){CartFlowsAPI._log(s.data,"table"),CartFlowsAPI._log("Current time "+(new Date).getTime()),CartFlowsAPI._log("Cache expired in "+parseInt(s.timeout)/1e3+" seconds."),void 0!==e.args.trigger&&""!==e.args.trigger&&l(document).trigger(e.args.trigger,[e]),e.callback&&"function"==typeof e.callback&&e.callback(e)},_api_request(o,s){let a={url:CartFlowsAPI._api_url+o.slug,args:o,callback:s,headers:CartFlowsImportVars.headers,author:"cartflows"};l.ajax(a).done(function(e,t,a){var r;"success"===t&&a.getResponseHeader("x-wp-total")?(r={args:o,items:e,items_count:a.getResponseHeader("x-wp-total")||0,callback:s,status:t,XHR:a},void 0!==o.trigger&&""!==o.trigger&&l(document).trigger(o.trigger,[r])):l(document).trigger("cartflows-api-request-error"),s&&"function"==typeof s&&s(r)}).fail(function(e,t){l(document).trigger("cartflows-api-request-fail",[a,e,t])}).always(function(){l(document).trigger("cartflows-api-request-always")})}}})(jQuery);