(i=>{function e(){var e=i('a[href*="wcf-next-step"]');0<e.length&&void 0!==cartflows.next_step&&(e.addClass("wcf-next-step-link"),e.attr("href",(e=>{var t=new URLSearchParams(window.location.search);if(!(t.length<=0)){var o,n={};for(o of t.keys())n[o]=t.get(o);var a=new URLSearchParams(n).toString();""!==a&&(e=-1===e.indexOf("?")?e+"?"+a:e+"&"+a)}return e})(cartflows.next_step)))}i(document).on("click",'a[href*="wcf-next-step"]',function(e){if(e.preventDefault(),void 0===cartflows.is_pb_preview||!0!==cartflows.is_pb_preview)return window.location.href=cartflows.next_step,!1;e.stopPropagation()}),i(document).on("click",".wcf-next-step-link",function(e){if(void 0!==cartflows.is_pb_preview&&"1"===cartflows.is_pb_preview)return e.preventDefault(),e.stopPropagation(),!1}),i(document).on("click",".wcf-pinterest-consent-button",function(){var e,t,o,n,a=i(this).data("action");e=cartflows?.pinterest_consent_cookie,t="accept"===a?"true":"false",o=30,(n=new Date).setTime(n.getTime()+24*o*60*60*1e3),o="expires="+n.toUTCString(),document.cookie=e+"="+t+"; "+o+"; path=/",i("#cartflows-pinterest-consent-wrapper").hide(),"accept"===a&&document.dispatchEvent(new CustomEvent("cartflows_pinterest_consent_changed",{detail:"true"}))}),i(function(){e(),"OceanWP"===cartflows.current_theme&&"default"!==cartflows.page_template&&null!==(a=document.getElementById("oceanwp-style-css"))&&a.remove(),"1"!==cartflows.is_pb_preview&&("enable"===cartflows.fb_setting.facebook_pixel_tracking&&""!==cartflows.fb_setting.facebook_pixel_id&&("enable"===cartflows.fb_setting.facebook_pixel_add_payment_info&&cartflows.is_checkout_page?jQuery("form.woocommerce-checkout").on("submit",function(){fbq("track","AddPaymentInfo",JSON.parse(cartflows.fb_add_payment_info_data))}):cartflows.is_optin&&"enable"===cartflows.fb_setting.facebook_pixel_optin_lead&&jQuery("form.woocommerce-checkout").on("submit",function(){fbq("track","Lead",{plugin:"CartFlows"})})),"enable"===cartflows.ga_setting.enable_google_analytics&&(cartflows.is_checkout_page&&"enable"===cartflows.ga_setting.enable_add_payment_info?jQuery("form.woocommerce-checkout").on("submit",function(){gtag("event","add_payment_info",JSON.parse(cartflows.add_payment_info_data))}):cartflows.is_optin&&"enable"===cartflows.ga_setting.enable_optin_lead&&jQuery("form.woocommerce-checkout").on("submit",function(){gtag("event","Lead",{plugin:"CartFlows"})})),"enable"===cartflows.tik_setting.tiktok_pixel_tracking&&""!==cartflows.tik_setting.tiktok_pixel_id&&("enable"===cartflows.tik_setting.enable_tiktok_add_payment_info&&cartflows.is_checkout_page?jQuery("form.woocommerce-checkout").on("submit",function(){ttq.track("AddPaymentInfo",JSON.parse(cartflows.tiktok_add_payment_info_data))}):cartflows.is_optin&&"enable"===cartflows.tik_setting.enable_tiktok_optin_lead&&jQuery("form.woocommerce-checkout").on("submit",function(){ttq.track("Lead",{plugin:"CartFlows"})})),"enable"===cartflows.gads_setting.google_ads_tracking&&(cartflows.is_checkout_page&&"enable"===cartflows.gads_setting.enable_google_ads_add_payment_info?jQuery("form.woocommerce-checkout").on("submit",function(){gtag("event","add_payment_info",JSON.parse(cartflows.add_payment_info_data))}):cartflows.is_optin&&"enable"===cartflows.gads_setting.enable_google_ads_optin_lead&&jQuery("form.woocommerce-checkout").on("submit",function(){gtag("event","Lead",{plugin:"CartFlows"})})),"enable"===cartflows.snap_settings.snapchat_pixel_tracking&&""!==cartflows.snap_settings.snapchat_pixel_id&&cartflows.is_optin&&"enable"===cartflows.snap_settings.enable_snapchat_optin_lead&&jQuery("form.woocommerce-checkout").on("submit",function(){snaptr("track","SIGN_UP",{sign_up_method:"CartFlows Optin Lead"})}),"enable"===cartflows.pin_settings.pinterest_tag_tracking)&&""!==cartflows.pin_settings.pinterest_tag_id&&"undefined"!=typeof pintrk&&("enable"===cartflows.pin_settings.enable_pinterest_add_payment_info&&cartflows.is_checkout_page?jQuery("form.woocommerce-checkout").on("submit",function(){pintrk("track","AddPaymentInfo",JSON.parse(cartflows.pinterest_add_payment_info_data))}):cartflows.is_optin&&("enable"===cartflows.pin_settings.enable_pinterest_optin_lead&&jQuery("form.woocommerce-checkout").on("submit",function(){pintrk("track","Lead",{lead_type:"CartFlows Optin"})}),"enable"===cartflows.pin_settings.enable_pinterest_signup)&&jQuery("form.woocommerce-checkout").on("submit",function(){pintrk("track","Signup",JSON.parse(cartflows.pinterest_signup_info_data))}));{let t=i(".wcf-collapsed-order-review-section"),o=i(".wcf-cartflows-review-order-wrapper"),e=i(".wcf-order-wrap"),n=!1;var a="onorientationchange"in window?"orientationchange":"resize";i(".wcf-order-review-toggle").on("click",function(e){e.preventDefault(),t.hasClass("wcf-show")?(o.slideUp(400),t.removeClass("wcf-show"),i(".wcf-order-review-toggle-text").text(cartflows.order_review_toggle_texts.toggle_show_text)):(o.slideDown(400),t.addClass("wcf-show"),i(".wcf-order-review-toggle-text").text(cartflows.order_review_toggle_texts.toggle_hide_text))}),i(window).on(a,function(){clearTimeout(n),n=setTimeout(function(){769<=(window.innerWidth||i(window).width())&&(o.css({display:"none"}),o.removeClass("wcf-show"),i(".wcf-order-review-toggle").removeClass("wcf-show"),i(".wcf-order-review-toggle-text").text(cartflows.order_review_toggle_texts.toggle_show_text))},200)}),o.on("change",'select.shipping_method, input[name^="shipping_method"]',function(){e.find('input[name^="shipping_method"][type="radio"]:checked').each(function(){i(this).removeAttr("checked")}),i(document.body).trigger("update_checkout",{update_shipping_method:!0})})}})})(jQuery);