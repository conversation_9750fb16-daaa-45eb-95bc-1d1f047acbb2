msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: gpt-po v1.1.1\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-04T15:33:29+00:00\n"
"PO-Revision-Date: 2025-02-04T15:33:29+00:00\n"
"Language: \n"

#: cartflows.php
#: classes/class-cartflows-admin-notices.php:217
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:62
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:71
#. Plugin Name of the plugin
msgid "CartFlows"
msgstr "CartFlows"

#: cartflows.php
#. Plugin URI of the plugin
msgid "https://cartflows.com/"
msgstr "https://cartflows.com/"

#: cartflows.php
#. Description of the plugin
msgid "Create beautiful checkout pages & sales flows for WooCommerce."
msgstr "Crie páginas de checkout e fluxos de vendas bonitos para WooCommerce."

#: admin-core/ajax/ab-steps.php:89
#. translators: %s step id
msgid "Can't create a variation for this step - %s, Invalid Step ID."
msgstr "Não é possível criar uma variação para esta etapa - %s, ID de etapa inválido."

#: admin-core/ajax/ab-steps.php:105
#. translators: %s flow id
msgid "Step successfully hidden - %s"
msgstr "Etapa ocultada com sucesso - %s"

#: admin-core/ajax/ab-steps.php:140
#. translators: %s step id
msgid "Can't delete a variation for this step - %s, Invalid Step Id or Funnel Id."
msgstr "Não é possível excluir uma variação para esta etapa - %s, Id de Etapa ou Id de Funil inválido."

#: admin-core/ajax/ab-steps.php:188
#. translators: %s flow id
msgid "Step deleted - %s"
msgstr "Etapa excluída - %s"

#: admin-core/ajax/ab-steps.php:223
#. translators: %s step id
msgid "Can't create a variation for this step - %s"
msgstr "Não é possível criar uma variação para esta etapa - %s"

#: admin-core/ajax/ab-steps.php:279
#. translators: %s step id
msgid "A/B test settings updated for this step - %s"
msgstr "Configurações de teste A/B atualizadas para esta etapa - %s"

#: admin-core/ajax/ajax-errors.php:59
#: wizard/ajax/ajax-errors.php:59
msgid "Sorry, you are not allowed to do this operation."
msgstr "Desculpe, você não tem permissão para realizar esta operação."

#: admin-core/ajax/ajax-errors.php:60
#: admin-core/ajax/common-settings.php:217
#: admin-core/ajax/common-settings.php:279
#: admin-core/ajax/common-settings.php:385
#: admin-core/ajax/common-settings.php:418
#: admin-core/inc/meta-ops.php:32
#: modules/checkout/classes/class-cartflows-checkout-ajax.php:110
#: wizard/ajax/ajax-errors.php:60
msgid "Nonce validation failed"
msgstr "A validação do nonce falhou"

#: admin-core/ajax/ajax-errors.php:61
#: wizard/ajax/ajax-errors.php:61
msgid "Sorry, something went wrong."
msgstr "Desculpe, algo deu errado."

#: admin-core/ajax/ajax-errors.php:62
msgid "Required parameter is missing from the posted data."
msgstr "O parâmetro obrigatório está ausente dos dados enviados."

#: admin-core/ajax/common-settings.php:85
msgid "Successfully deleted the dynamic CSS keys!"
msgstr "Chaves CSS dinâmicas excluídas com sucesso!"

#: admin-core/ajax/common-settings.php:105
msgid "No post data found!"
msgstr "Nenhum dado de postagem encontrado!"

#: admin-core/ajax/common-settings.php:152
msgid "Successfully saved data!"
msgstr "Dados salvos com sucesso!"

#: admin-core/ajax/debugger.php:82
#: admin-core/ajax/debugger.php:133
#: admin-core/ajax/debugger.php:157
msgid "You don't have permission to perform this action."
msgstr "Você não tem permissão para realizar esta ação."

#: admin-core/ajax/debugger.php:91
msgid "Sync Success."
msgstr "Sincronização bem-sucedida."

#: admin-core/ajax/debugger.php:105
#: admin-core/inc/log-status.php:79
msgid "You don't have permission to view this page."
msgstr "Você não tem permissão para visualizar esta página."

#: admin-core/ajax/debugger.php:139
#: admin-core/inc/log-status.php:175
msgid "Filename is empty. Please refresh the page and retry."
msgstr "O nome do arquivo está vazio. Por favor, atualize a página e tente novamente."

#: admin-core/ajax/debugger.php:174
#: admin-core/inc/log-status.php:210
msgid "Invalid file."
msgstr "Arquivo inválido."

#: admin-core/ajax/debugger.php:181
msgid "Export logs successfully"
msgstr "Exportar logs com sucesso"

#: admin-core/ajax/flows.php:97
msgid "No Funnel IDs has been supplied to export!"
msgstr "Nenhum ID de funil foi fornecido para exportar!"

#: admin-core/ajax/flows.php:110
#: admin-core/ajax/importer.php:109
#: admin-core/ajax/importer.php:212
msgid "Funnel exported successfully"
msgstr "Funil exportado com sucesso"

#: admin-core/ajax/flows.php:142
msgid "Can't update the flow data"
msgstr "Não é possível atualizar os dados do fluxo"

#: admin-core/ajax/flows.php:159
#: admin-core/ajax/steps.php:415
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "(no title)"
msgstr "(sem título)"

#: admin-core/ajax/flows.php:184
msgid "Successfully saved the flow data!"
msgstr "Dados do fluxo salvos com sucesso!"

#: admin-core/ajax/flows.php:246
msgid "Successfully deleted the Funnels!"
msgstr "Funis excluídos com sucesso!"

#: admin-core/ajax/flows.php:274
#: admin-core/ajax/flows.php:352
#: admin-core/ajax/flows.php:988
msgid "No Funnel IDs has been supplied to delete!"
msgstr "Nenhum ID de funil foi fornecido para exclusão!"

#: admin-core/ajax/flows.php:323
#: admin-core/ajax/flows.php:391
msgid "Successfully trashed the Funnels!"
msgstr "Os Funis foram movidos para a lixeira com sucesso!"

#: admin-core/ajax/flows.php:422
msgid "Invalid Funnel ID has been supplied to update title."
msgstr "Foi fornecido um ID de funil inválido para atualizar o título."

#: admin-core/ajax/flows.php:427
msgid "Can't update the flow title"
msgstr "Não é possível atualizar o título do fluxo"

#: admin-core/ajax/flows.php:443
#. translators: %s flow id
msgid "Funnel title updated - %s"
msgstr "Título do funil atualizado - %s"

#: admin-core/ajax/flows.php:468
msgid "Invalid Funnel ID has been supplied to clone!"
msgstr "Foi fornecido um ID de funil inválido para clonar!"

#: admin-core/ajax/flows.php:502
msgid "Invalid Funnel ID has been supplied to duplicate!"
msgstr "Foi fornecido um ID de funil inválido para duplicar!"

#: admin-core/ajax/flows.php:679
msgid "Successfully cloned the Funnel!"
msgstr "Funil clonado com sucesso!"

#: admin-core/ajax/flows.php:708
msgid "Invalid Funnel ID has been supplied to restore!"
msgstr "Foi fornecido um ID de Funil inválido para restaurar!"

#: admin-core/ajax/flows.php:748
msgid "Successfully restored the Funnel!"
msgstr "Funil restaurado com sucesso!"

#: admin-core/ajax/flows.php:775
msgid "Invalid Funnel ID has been supplied to trash!"
msgstr "Foi fornecido um ID de funil inválido para a lixeira!"

#: admin-core/ajax/flows.php:814
msgid "Successfully trashed the Funnel!"
msgstr "Funil movido para a lixeira com sucesso!"

#: admin-core/ajax/flows.php:841
msgid "Invalid Funnel ID has been supplied to delete!"
msgstr "Foi fornecido um ID de funil inválido para exclusão!"

#: admin-core/ajax/flows.php:882
msgid "Successfully deleted the Funnel!"
msgstr "Funil excluído com sucesso!"

#: admin-core/ajax/flows.php:909
msgid "Invalid Funnel IDs has been supplied to update status!"
msgstr "IDs de Funil inválidos foram fornecidos para atualizar o status!"

#: admin-core/ajax/flows.php:958
#: admin-core/ajax/flows.php:1017
msgid "Successfully updated the Funnel status!"
msgstr "Status do Funil atualizado com sucesso!"

#: admin-core/ajax/flows.php:1057
msgid "Invalid flow ID has been provided."
msgstr "Foi fornecido um ID de fluxo inválido."

#: admin-core/ajax/flows.php:1073
#. translators: %s flow id
msgid "Steps not sorted for flow - %s"
msgstr "Etapas não ordenadas para o fluxo - %s"

#: admin-core/ajax/flows.php:1113
#. translators: %s flow id
msgid "Steps sorted for flow - %s"
msgstr "Etapas ordenadas para o fluxo - %s"

#: admin-core/ajax/flows.php:1146
msgid "No Funnel ID is been supplied"
msgstr "Nenhum ID de funil foi fornecido"

#: admin-core/ajax/flows.php:1159
#. translators: %s flow id
msgid "Notice Dismissed"
msgstr "Aviso Dispensado"

#: admin-core/ajax/importer.php:116
msgid "No Funnels to export"
msgstr "Nenhum funil para exportar"

#: admin-core/ajax/importer.php:205
msgid "Invalid flow ID."
msgstr "ID de fluxo inválido."

#: admin-core/ajax/importer.php:392
msgid "Invalid Funnel Id has been provided."
msgstr "Foi fornecido um ID de funil inválido."

#: admin-core/ajax/importer.php:407
#. translators: %s: step ID
msgid "Invalid step id %1$s."
msgstr "ID de etapa inválido %1$s."

#: admin-core/ajax/importer.php:414
msgid "Successfully created the step!"
msgstr "Etapa criada com sucesso!"

#: admin-core/ajax/importer.php:516
msgid "Theme Activated"
msgstr "Tema ativado"

#: admin-core/ajax/importer.php:575
#: admin-core/ajax/importer.php:590
#: modules/flow/classes/class-cartflows-step-post-type.php:262
#: wizard/ajax/wizard.php:717
msgid "Checkout"
msgstr "Finalizar compra"

#: admin-core/ajax/importer.php:579
#: admin-core/ajax/importer.php:594
#: admin-core/ajax/importer.php:606
#: modules/flow/classes/class-cartflows-step-post-type.php:269
#: wizard/ajax/wizard.php:721
#: wizard/ajax/wizard.php:732
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Thank You"
msgstr "Obrigado"

#: admin-core/ajax/importer.php:586
msgid "Sales Landing"
msgstr "Página de Vendas"

#: admin-core/ajax/importer.php:602
#: modules/flow/classes/class-cartflows-step-post-type.php:248
#: wizard/ajax/wizard.php:728
msgid "Landing"
msgstr "Pouso"

#: admin-core/ajax/importer.php:661
#: wizard/ajax/wizard.php:805
msgid "Successfully created the Funnel!"
msgstr "Funil criado com sucesso!"

#: admin-core/ajax/importer.php:719
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "CartFlows Pro Necessário! %1$sAtualize para CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:721
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$supgrade to CartFlows Pro%2$s."
msgstr "Para importar o fluxo premium %1$sfaça upgrade para o CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:724
#: wizard/ajax/wizard.php:544
#. translators: %1$s: link html start, %2$s: link html end
msgid "Activate the CartFlows Pro to import the flow! %1$sActivate CartFlows Pro%2$s"
msgstr "Ative o CartFlows Pro para importar o fluxo! %1$sAtivar CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:726
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate Cartflows Pro%2$s and validate the license key."
msgstr "Para importar o fluxo premium, %1$sative o Cartflows Pro%2$s e valide a chave de licença."

#: admin-core/ajax/importer.php:729
#. translators: %1$s: link html start, %2$s: link html end
msgid "Invalid License Key! %1$sActivate CartFlows Pro%2$s"
msgstr "Chave de Licença Inválida! %1$sAtivar CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:731
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate CartFlows Pro%2$s."
msgstr "Para importar o fluxo premium, %1$sative o CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:744
#: admin-core/ajax/importer.php:1056
msgid "Funnel data not found."
msgstr "Dados do funil não encontrados."

#: admin-core/ajax/importer.php:791
msgid "Steps not found."
msgstr "Passos não encontrados."

#: admin-core/ajax/importer.php:824
#: wizard/ajax/wizard.php:642
msgid "Successfully imported the Flow!"
msgstr "Fluxo importado com sucesso!"

#: admin-core/ajax/importer.php:873
msgid "Step data ID not found for import."
msgstr "ID de dados de etapa não encontrado para importação."

#: admin-core/ajax/importer.php:885
msgid "Funnel ID not found in the request."
msgstr "ID do funil não encontrado na solicitação."

#: admin-core/inc/admin-helper.php:1127
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$1sTo resolve this issue, please check this %2$2sarticle%3$3s."
msgstr ""
"Erro de tempo limite de solicitação. Por favor, verifique se o firewall ou algum plugin de segurança está bloqueando as "
"solicitações HTTP/HTTPS de saída para templates.cartflows.com ou não. %1$1sPara resolver este problema, por favor, "
"consulte este %2$2sartigo%3$3s."

#: admin-core/ajax/importer.php:915
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sUpgrade to CartFlows Pro.%2$s"
msgstr "%1$sAtualize para o CartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:916
msgid "To import the premium step, please upgrade to CartFlows Pro"
msgstr "Para importar a etapa premium, por favor atualize para o CartFlows Pro"

#: admin-core/ajax/importer.php:919
#: admin-core/ajax/importer.php:1041
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro%2$s"
msgstr "%1$sAtivar CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:920
msgid "To import the premium step activate Cartflows Pro and validate the license key."
msgstr "Para importar a etapa premium, ative o Cartflows Pro e valide a chave de licença."

#: admin-core/ajax/importer.php:923
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro License %2$s"
msgstr "%1$sAtivar Licença do CartFlows Pro %2$s"

#: admin-core/ajax/importer.php:924
msgid "To import the premium step activate the CartFlows Pro."
msgstr "Para importar a etapa premium, ative o CartFlows Pro."

#: admin-core/ajax/importer.php:959
#: admin-core/ajax/importer.php:1080
msgid "Step data not found."
msgstr "Dados de etapa não encontrados."

#: admin-core/ajax/importer.php:967
#: admin-core/ajax/importer.php:1088
msgid "Successfully imported the Step!"
msgstr "Importação do passo concluída com sucesso!"

#: admin-core/ajax/importer.php:1038
#. translators: %1$s: link html start, %2$s: link html end
msgid "Upgrade to %1$sCartFlows Pro.%2$s"
msgstr "Atualize para %1$sCartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:1044
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro license is not active. Activate %1$sCartFlows Pro License %2$s"
msgstr "A licença do CartFlows Pro não está ativa. Ative %1$sLicença do CartFlows Pro %2$s"

#: admin-core/ajax/importer.php:1112
#: admin-core/ajax/importer.php:1198
#. translators: %s: step ID
msgid "Invalid step id %1$s or post id %2$s."
msgstr "ID de etapa %1$s ou ID de postagem %2$s inválido."

#: admin-core/ajax/importer.php:1175
#: admin-core/inc/admin-menu.php:1194
#: admin-core/inc/store-checkout.php:110
msgid "Nonce verification failed."
msgstr "A verificação do nonce falhou."

#: admin-core/ajax/importer.php:1454
#: wizard/ajax/wizard.php:388
msgid "Successful!"
msgstr "Bem-sucedido!"

#: admin-core/ajax/meta-data.php:143
#. Translators: %d stock amount
msgid "Stock: %d"
msgstr "Estoque: %d"

#: admin-core/ajax/meta-data.php:271
msgid "On backorder"
msgstr "Em encomenda"

#: admin-core/ajax/meta-data.php:274
msgid "In stock"
msgstr "Em estoque"

#: admin-core/ajax/meta-data.php:277
msgid "Out of stock"
msgstr "Fora de estoque"

#: admin-core/ajax/setup-page.php:84
msgid "Setup page dismissed successfully."
msgstr "Página de configuração fechada com sucesso."

#: admin-core/ajax/steps.php:91
msgid "Can't update the step title"
msgstr "Não é possível atualizar o título da etapa"

#: admin-core/ajax/steps.php:112
#. translators: %s flow id
msgid "Step title updated - %s"
msgstr "Título da etapa atualizado - %s"

#: admin-core/ajax/steps.php:148
#. translators: %s flow id
msgid "Can't clone this step - %1$s. Flow - %2$s"
msgstr "Não é possível clonar esta etapa - %1$s. Fluxo - %2$s"

#: admin-core/ajax/steps.php:267
#. translators: %s flow id
msgid "Step - %1$s cloned. Flow - %2$s"
msgstr "Etapa - %1$s clonada. Fluxo - %2$s"

#: admin-core/ajax/steps.php:315
#. translators: %s flow id
msgid "Step not deleted for flow - %s"
msgstr "Etapa não excluída para o fluxo - %s"

#: admin-core/ajax/steps.php:358
#. translators: %s flow id
msgid "Step deleted for flow - %s"
msgstr "Etapa excluída para o fluxo - %s"

#: admin-core/ajax/steps.php:367
#. translators: %s flow id
msgid "This step can not be deleted."
msgstr "Esta etapa não pode ser excluída."

#: admin-core/ajax/steps.php:400
#. translators: %s flow id
msgid "Invalid Step Id has been provided."
msgstr "Foi fornecido um Id de Etapa inválido."

#: admin-core/ajax/steps.php:451
#. translators: %s flow id
msgid "Data saved successfully for step id %s"
msgstr "Dados salvos com sucesso para o id do passo %s"

#: admin-core/api/common-settings.php:129
#: admin-core/api/flow-data.php:139
#: admin-core/api/flows.php:287
#: admin-core/api/home-page.php:172
#: admin-core/api/product/product-data.php:121
#: admin-core/api/step-data.php:144
msgid "Sorry, you cannot list resources."
msgstr "Desculpe, você não pode listar recursos."

#: admin-core/api/flow-data.php:70
msgid "Flow ID."
msgstr "ID de fluxo."

#: admin-core/api/flows.php:194
#: modules/flow/classes/class-cartflows-flow-post-type.php:229
#: admin-core/assets/build/settings-app.js:80
msgid "View"
msgstr "Ver"

#: admin-core/api/flows.php:202
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Edit"
msgstr "Editar"

#: admin-core/api/flows.php:210
#: admin-core/inc/admin-helper.php:763
#: admin-core/inc/admin-helper.php:845
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate"
msgstr "Duplicar"

#: admin-core/api/flows.php:217
#: admin-core/assets/build/settings-app.js:32
msgid "Export"
msgstr "Exportar"

#: admin-core/api/flows.php:224
#: admin-core/inc/admin-helper.php:780
#: admin-core/inc/admin-helper.php:854
#: admin-core/inc/admin-helper.php:922
#: admin-core/inc/admin-helper.php:942
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Delete"
msgstr "Excluir"

#: admin-core/api/product/product-data.php:68
#: admin-core/api/step-data.php:69
msgid "Step ID."
msgstr "ID do passo."

#: admin-core/inc/admin-helper.php:580
#: admin-core/inc/flow-meta.php:262
#: classes/class-cartflows-helper.php:1412
#: classes/class-cartflows-helper.php:1429
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:167
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:194
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:96
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1211
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:103
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:442
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:228
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:143
#: modules/optin/classes/class-cartflows-optin-meta-data.php:305
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:50
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Default"
msgstr "Padrão"

#: admin-core/inc/admin-helper.php:597
msgid "System Fonts"
msgstr "Fontes do Sistema"

#: admin-core/inc/admin-helper.php:615
msgid "Google Fonts"
msgstr "Google Fonts"

#: admin-core/inc/admin-helper.php:772
msgid "A/B Test"
msgstr "Teste A/B"

#: admin-core/inc/admin-helper.php:797
msgid "Automation"
msgstr "Automação"

#: admin-core/inc/admin-helper.php:800
msgid "(Connect)"
msgstr "(Conectar)"

#: admin-core/inc/admin-helper.php:862
msgid "Archive"
msgstr "Arquivo"

#: admin-core/inc/admin-helper.php:869
msgid "Declare as Winner"
msgstr "Declarar como Vencedor"

#: admin-core/inc/admin-helper.php:913
msgid "Deleted variation can't be restored."
msgstr "A variação excluída não pode ser restaurada."

#: admin-core/inc/admin-helper.php:914
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:188
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:200
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:212
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:224
msgid "Hide"
msgstr "Esconder"

#: admin-core/inc/admin-helper.php:934
#: admin-core/assets/build/settings-app.js:32
msgid "Restore"
msgstr "Restaurar"

#: admin-core/inc/admin-helper.php:1115
msgid "Ooops! Something went wrong. Please open a support ticket from the website."
msgstr "Ops! Algo deu errado. Por favor, abra um ticket de suporte no site."

#: admin-core/inc/admin-helper.php:1116
msgid "No error found."
msgstr "Nenhum erro encontrado."

#: admin-core/inc/admin-helper.php:1141
#. translators: %1$s: HTML, %2$s: HTML, %3$s: HTML
msgid ""
"Sorry for the inconvenience, but your website seems to be having trouble connecting to our server. %1$s Please open a "
"technical %2$ssupport ticket%3$s and share the server's outgoing IP address."
msgstr ""
"Desculpe pelo inconveniente, mas o seu site parece estar tendo problemas para se conectar ao nosso servidor. %1$s Por "
"favor, abra um %2$sticket de suporte técnico%3$s e compartilhe o endereço IP de saída do servidor."

#: admin-core/inc/admin-helper.php:1143
msgid "Server's outgoing IP address: "
msgstr "Endereço IP de saída do servidor:"

#: admin-core/inc/admin-menu.php:123
#: admin-core/inc/admin-menu.php:174
#: classes/class-cartflows-flow-frontend.php:70
#: admin-core/assets/build/settings-app.js:32
msgid "Edit Funnel"
msgstr "Editar Funil"

#: admin-core/inc/admin-menu.php:210
msgid "Go to Funnel Editing"
msgstr "Ir para Edição de Funil"

#: admin-core/inc/admin-menu.php:258
#: admin-core/inc/admin-menu.php:259
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:25
msgid "Funnels"
msgstr "Funis"

#: admin-core/inc/admin-menu.php:269
#: admin-core/inc/admin-menu.php:270
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: wizard/assets/build/wizard-app.js:1
msgid "Store Checkout"
msgstr "Finalização da Compra"

#: admin-core/inc/admin-menu.php:278
#: admin-core/inc/admin-menu.php:280
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Automations"
msgstr "Automatizações"

#: admin-core/inc/admin-menu.php:280
#: admin-core/inc/admin-menu.php:290
msgid "New"
msgstr "Novo"

#: admin-core/inc/admin-menu.php:298
#: admin-core/inc/admin-menu.php:299
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Add-ons"
msgstr "Complementos"

#: admin-core/inc/admin-menu.php:308
#: admin-core/inc/admin-menu.php:309
msgid "Setup"
msgstr "Configuração"

#: admin-core/inc/admin-menu.php:328
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Dashboard"
msgstr "Painel de Controle"

#: admin-core/inc/admin-menu.php:541
msgid "Thin 100"
msgstr "Fino 100"

#: admin-core/inc/admin-menu.php:542
msgid "Extra-Light 200"
msgstr "Extra-Leve 200"

#: admin-core/inc/admin-menu.php:543
msgid "Light 300"
msgstr "Luz 300"

#: admin-core/inc/admin-menu.php:544
msgid "Normal 400"
msgstr "Normal 400"

#: admin-core/inc/admin-menu.php:545
msgid "Medium 500"
msgstr "Médio 500"

#: admin-core/inc/admin-menu.php:546
msgid "Semi-Bold 600"
msgstr "Semi-Bold 600"

#: admin-core/inc/admin-menu.php:547
msgid "Bold 700"
msgstr "Negrito 700"

#: admin-core/inc/admin-menu.php:548
msgid "Extra-Bold 800"
msgstr "Extra-Negrito 800"

#: admin-core/inc/admin-menu.php:549
msgid "Ultra-Bold 900"
msgstr "Ultra-Bold 900"

#: admin-core/inc/admin-menu.php:634
#. Translators: %1$s is the required page builder title, %2$s is the opening anchor tag to plugins.php, %3$s is the closing anchor tag, %4$s is the plugin title.
msgid "The default page builder is set to %1$s. Please %2$sinstall & activate%3$s the %4$s to start editing the steps."
msgstr ""
"O construtor de páginas padrão está definido para %1$s. Por favor, %2$sinstale e ative%3$s o %4$s para começar a editar "
"os passos."

#: admin-core/inc/admin-menu.php:1002
msgid "Stripe Payments For WooCommerce"
msgstr "Pagamentos Stripe para WooCommerce"

#: admin-core/inc/admin-menu.php:1003
msgid "Accept credit card payments in your store with Stripe for WooCommerce."
msgstr "Aceite pagamentos com cartão de crédito na sua loja com o Stripe para WooCommerce."

#: admin-core/inc/admin-menu.php:1014
msgid "PayPal Payments For WooCommerce"
msgstr "Pagamentos PayPal para WooCommerce"

#: admin-core/inc/admin-menu.php:1015
msgid "Accept payments in your store with PayPal for WooCommerce."
msgstr "Aceite pagamentos na sua loja com PayPal para WooCommerce."

#: admin-core/inc/admin-menu.php:1026
msgid "WooCommerce"
msgstr "WooCommerce"

#: admin-core/inc/admin-menu.php:1027
msgid "WooCommerce is a customizable, open-source ecommerce platform built on WordPress."
msgstr "WooCommerce é uma plataforma de comércio eletrônico personalizável e de código aberto construída no WordPress."

#: admin-core/inc/admin-menu.php:1051
msgid "SureMembers"
msgstr "SureMembers"

#: admin-core/inc/admin-menu.php:1065
msgid "Transform your WordPress form-building experience with stunning designs, ai integration, and no-code flexibility."
msgstr ""
"Transforme sua experiência de criação de formulários no WordPress com designs deslumbrantes, integração com IA e "
"flexibilidade sem código."

#: admin-core/inc/admin-menu.php:1064
msgid "SureForms"
msgstr "SureForms"

#: admin-core/inc/admin-menu.php:1076
#: wizard/assets/build/wizard-app.js:1
msgid "Spectra"
msgstr "Espectros"

#: admin-core/inc/admin-menu.php:1077
msgid "Power-up the Gutenberg editor with advanced and powerful blocks."
msgstr "Potencie o editor Gutenberg com blocos avançados e poderosos."

#: admin-core/inc/admin-menu.php:1108
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Cart Abandonment"
msgstr "Abandono de Carrinho"

#: admin-core/inc/admin-menu.php:1109
msgid "Recover abandonded carts with ease in less than 10 minutes."
msgstr "Recupere carrinhos abandonados com facilidade em menos de 10 minutos."

#: admin-core/inc/admin-menu.php:1120
msgid "Variation Swatches for WooCommerce"
msgstr "Variações de Amostras para WooCommerce"

#: admin-core/inc/admin-menu.php:1121
msgid "Convert dropdown boxes into highly engaging variation swatches."
msgstr "Converta caixas suspensas em amostras de variação altamente envolventes."

#: admin-core/inc/admin-menu.php:1137
msgid "Astra"
msgstr "Astra"

#: admin-core/inc/admin-menu.php:1138
msgid ""
"Astra is fast, fully customizable & beautiful WordPress theme suitable for blog, personal portfolio, business website "
"and WooCommerce storefront."
msgstr ""
"Astra é um tema WordPress rápido, totalmente personalizável e bonito, adequado para blog, portfólio pessoal, site de "
"negócios e loja WooCommerce."

#: admin-core/inc/admin-menu.php:1148
msgid "Spectra One"
msgstr "Spectra One"

#: admin-core/inc/admin-menu.php:1149
msgid ""
"Spectra One is a beautiful and modern WordPress theme built with the Full Site Editing (FSE) feature. It's a versatile "
"theme that can be used for blogs, portfolios, businesses, and more."
msgstr ""
"O Spectra One é um tema WordPress bonito e moderno, construído com o recurso de Edição Completa do Site (FSE). É um "
"tema versátil que pode ser usado para blogs, portfólios, negócios e mais."

#: admin-core/inc/flow-meta.php:54
msgid "Instant Layout "
msgstr "Layout Instantâneo"

#: admin-core/inc/flow-meta.php:73
msgid "Logo"
msgstr "Logotipo"

#: admin-core/inc/flow-meta.php:108
msgid "Width (In px)"
msgstr "Largura (Em px)"

#: admin-core/inc/flow-meta.php:125
msgid "Height (In px)"
msgstr "Altura (Em px)"

#: admin-core/inc/flow-meta.php:159
msgid "Global Styling"
msgstr "Estilo Global"

#: admin-core/inc/flow-meta.php:164
msgid "Enable Global Styling"
msgstr "Ativar Estilo Global"

#: admin-core/inc/flow-meta.php:172
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:211
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:119
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:119
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:291
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:297
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:168
#: modules/optin/classes/class-cartflows-optin-meta-data.php:277
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:125
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Primary Color"
msgstr "Cor Primária"

#: admin-core/inc/flow-meta.php:187
msgid "Secondary Color"
msgstr "Cor Secundária"

#: admin-core/inc/flow-meta.php:202
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:219
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:247
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:441
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:674
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:861
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:397
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:222
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:250
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:325
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:390
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:456
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:522
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1052
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:172
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:251
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:303
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:329
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:592
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:690
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:763
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:361
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:350
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:410
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:334
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:383
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:413
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:478
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:541
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:572
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:633
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:663
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:722
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:752
#: modules/optin/classes/class-cartflows-optin-meta-data.php:500
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Color"
msgstr "Cor do Texto"

#: admin-core/inc/flow-meta.php:217
msgid "Heading/Accent Color"
msgstr "Cor do Cabeçalho/Realce"

#: admin-core/inc/flow-meta.php:233
msgid "General "
msgstr "Geral"

#: admin-core/inc/flow-meta.php:239
#: admin-core/inc/global-settings.php:149
msgid "Funnel Slug"
msgstr "Slug do Funil"

#: admin-core/inc/flow-meta.php:245
msgid "Enable Test Mode"
msgstr "Ativar Modo de Teste"

#: admin-core/inc/flow-meta.php:266
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:139
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:148
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:157
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:166
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "Yes"
msgstr "Sim"

#: admin-core/inc/flow-meta.php:270
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:140
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:149
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:158
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:167
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "No"
msgstr "Não"

#: admin-core/inc/flow-meta.php:277
msgid "Funnel Custom Script"
msgstr "Script Personalizado de Funil"

#: admin-core/inc/global-settings.php:45
msgid "No Access"
msgstr "Sem acesso"

#: admin-core/inc/global-settings.php:49
msgid "Full Access"
msgstr "Acesso Completo"

#: admin-core/inc/global-settings.php:54
msgid "Limited Access"
msgstr "Acesso Limitado"

#: admin-core/inc/global-settings.php:71
msgid "Show Ready Templates for"
msgstr "Mostrar Modelos Prontos para"

#: admin-core/inc/global-settings.php:73
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"Please choose your preferred page builder from the list so you will only see templates that are made using that page "
"builder. %1$sLearn More >>%2$s"
msgstr ""
"Por favor, escolha o seu construtor de páginas preferido da lista para que você veja apenas os modelos feitos com esse "
"construtor de páginas. %1$sSaiba Mais >>%2$s"

#: admin-core/inc/global-settings.php:77
msgid "Block Editor"
msgstr "Editor de Blocos"

#: admin-core/inc/global-settings.php:82
msgid "Elementor"
msgstr "Elementor"

#: admin-core/inc/global-settings.php:87
msgid "Bricks"
msgstr "Tijolos"

#: admin-core/inc/global-settings.php:92
msgid "Beaver"
msgstr "Castor"

#: admin-core/inc/global-settings.php:97
msgid "Other"
msgstr "Outro"

#: admin-core/inc/global-settings.php:110
msgid "Override Store Checkout"
msgstr "Substituir Finalização de Compra da Loja"

#: admin-core/inc/global-settings.php:112
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the Store Checkout settings please %1$sClick here%2$s."
msgstr "Para mais informações sobre as configurações de Checkout da Loja, por favor %1$sClique aqui%2$s."

#: admin-core/inc/global-settings.php:120
msgid "Disallow search engine from indexing funnels."
msgstr "Impedir que o motor de busca indexe funis."

#: admin-core/inc/global-settings.php:122
msgid "Prevent search engines from including funnels in their search results."
msgstr "Impedir que os motores de busca incluam funis nos seus resultados de pesquisa."

#: admin-core/inc/global-settings.php:139
msgid "Default Permalinks"
msgstr "Links permanentes padrão"

#: admin-core/inc/global-settings.php:140
msgid "Default WordPress Permalink"
msgstr "Link permanente padrão do WordPress"

#: admin-core/inc/global-settings.php:144
msgid "Funnel and Step Slug"
msgstr "Funil e Identificador de Etapa"

#: admin-core/inc/global-settings.php:154
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1019
#: modules/landing/classes/class-cartflows-landing-meta-data.php:113
#: modules/optin/classes/class-cartflows-optin-meta-data.php:568
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:396
msgid "Step Slug"
msgstr "Etapa Slug"

#: admin-core/inc/global-settings.php:164
msgid "Post Type Permalink Base"
msgstr "Base do Permalink do Tipo de Postagem"

#: admin-core/inc/global-settings.php:168
msgid "Step Base"
msgstr "Base de Passo"

#: admin-core/inc/global-settings.php:174
msgid "Funnel Base"
msgstr "Base do Funil"

#: admin-core/inc/global-settings.php:185
#: admin-core/inc/global-settings.php:376
#: admin-core/inc/global-settings.php:582
#: admin-core/inc/global-settings.php:956
msgid "Enable For CartFlows Pages"
msgstr "Ativar para páginas do CartFlows"

#: admin-core/inc/global-settings.php:203
#: admin-core/inc/global-settings.php:394
#: admin-core/inc/global-settings.php:600
#: admin-core/inc/global-settings.php:788
#: admin-core/inc/global-settings.php:974
#: admin-core/inc/global-settings.php:1180
msgid "Enable for the whole site"
msgstr "Ativar para o site inteiro"

#: admin-core/inc/global-settings.php:205
msgid "If checked, page view and view content event will also be triggered for other pages/posts of site."
msgstr ""
"Se marcado, a visualização de página e o evento de visualização de conteúdo também serão acionados para outras "
"páginas/publicações do site."

#: admin-core/inc/global-settings.php:231
msgid "Enter Facebook pixel ID"
msgstr "Insira o ID do pixel do Facebook"

#: admin-core/inc/global-settings.php:258
msgid "Facebook Pixel Events"
msgstr "Eventos do Pixel do Facebook"

#: admin-core/inc/global-settings.php:272
#: admin-core/inc/global-settings.php:701
#: admin-core/inc/global-settings.php:888
#: admin-core/inc/global-settings.php:1093
msgid "View Content"
msgstr "Ver conteúdo"

#: admin-core/inc/global-settings.php:288
msgid "Initiate Checkout"
msgstr "Iniciar Checkout"

#: admin-core/inc/global-settings.php:304
#: admin-core/inc/global-settings.php:495
#: admin-core/inc/global-settings.php:716
#: admin-core/inc/global-settings.php:1108
#: admin-core/inc/global-settings.php:1309
msgid "Add Payment Info"
msgstr "Adicionar Informações de Pagamento"

#: admin-core/inc/global-settings.php:321
msgid "Purchase Complete"
msgstr "Compra Concluída"

#: admin-core/inc/global-settings.php:337
#: admin-core/inc/global-settings.php:527
#: admin-core/inc/global-settings.php:748
#: admin-core/inc/global-settings.php:934
#: admin-core/inc/global-settings.php:1140
#: admin-core/inc/global-settings.php:1357
msgid "Optin Lead"
msgstr "Lead de Opt-in"

#: admin-core/inc/global-settings.php:358
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Facebook Pixel not working correctly? %1$1s Click here %2$2s to know more."
msgstr "O Pixel do Facebook não está funcionando corretamente? %1$1s Clique aqui %2$2s para saber mais."

#: admin-core/inc/global-settings.php:396
msgid "If checked, page view event will also be triggered for other pages/posts of site."
msgstr "Se marcado, o evento de visualização de página também será acionado para outras páginas/publicações do site."

#: admin-core/inc/global-settings.php:422
msgid "Enter Google Analytics ID"
msgstr "Insira o ID do Google Analytics"

#: admin-core/inc/global-settings.php:426
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s google analytics account %2$2s to find your ID. e.g. G-XXXXX or UA-XXXXX-X"
msgstr "Faça login na sua conta %1$1s do Google Analytics %2$2s para encontrar seu ID. por exemplo, G-XXXXX ou UA-XXXXX-X"

#: admin-core/inc/global-settings.php:451
msgid "Google Analytics Events"
msgstr "Eventos do Google Analytics"

#: admin-core/inc/global-settings.php:464
#: admin-core/inc/global-settings.php:670
#: admin-core/inc/global-settings.php:858
#: admin-core/inc/global-settings.php:1062
#: admin-core/inc/global-settings.php:1279
msgid "Begin Checkout"
msgstr "Iniciar Checkout"

#: admin-core/inc/global-settings.php:480
#: admin-core/inc/global-settings.php:686
#: admin-core/inc/global-settings.php:873
#: admin-core/inc/global-settings.php:1078
#: admin-core/inc/global-settings.php:1294
msgid "Add To Cart"
msgstr "Adicionar ao Carrinho"

#: admin-core/inc/global-settings.php:511
#: admin-core/inc/global-settings.php:732
#: admin-core/inc/global-settings.php:903
#: admin-core/inc/global-settings.php:1124
#: admin-core/inc/global-settings.php:1325
msgid "Purchase"
msgstr "Compra"

#: admin-core/inc/global-settings.php:548
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Google Analytics not working correctly? %1$1s Click here %2$2s to know more."
msgstr "O Google Analytics não está funcionando corretamente? %1$1s Clique aqui %2$2s para saber mais."

#: admin-core/inc/global-settings.php:566
msgid "Enter Google Map API key"
msgstr "Insira a chave da API do Google Maps"

#: admin-core/inc/global-settings.php:573
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Check this %1$1s article %2$2s to setup and find an API key."
msgstr "Confira este %1$1s artigo %2$2s para configurar e encontrar uma chave de API."

#: admin-core/inc/global-settings.php:602
#: admin-core/inc/global-settings.php:790
#: admin-core/inc/global-settings.php:976
msgid "If checked, PageView event will also be triggered for other pages/posts of site."
msgstr "Se marcado, o evento PageView também será acionado para outras páginas/publicações do site."

#: admin-core/inc/global-settings.php:628
msgid "Enter TikTok ID"
msgstr "Insira o ID do TikTok"

#: admin-core/inc/global-settings.php:632
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s TikTok business account %2$2s to find your ID."
msgstr "Faça login na sua conta comercial do %1$1s TikTok %2$2s para encontrar seu ID."

#: admin-core/inc/global-settings.php:657
msgid "TikTok Events"
msgstr "Eventos do TikTok"

#: admin-core/inc/global-settings.php:760
#: admin-core/inc/global-settings.php:946
#: admin-core/inc/global-settings.php:1152
#: admin-core/inc/global-settings.php:1369
msgid "Optin Lead event will be triggered for optin page."
msgstr "O evento de Lead de Optin será acionado para a página de optin."

#: admin-core/inc/global-settings.php:770
#: admin-core/inc/global-settings.php:1162
msgid "Enable for CartFlows pages"
msgstr "Ativar para páginas do CartFlows"

#: admin-core/inc/global-settings.php:816
msgid "Enter Snapchat pixel ID"
msgstr "Insira o ID do pixel do Snapchat"

#: admin-core/inc/global-settings.php:820
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Snapchat business account %2$2s to find your ID."
msgstr "Faça login na sua %1$1s conta comercial do Snapchat %2$2s para encontrar seu ID."

#: admin-core/inc/global-settings.php:845
msgid "Snapchat Events"
msgstr "Eventos do Snapchat"

#: admin-core/inc/global-settings.php:918
#: wizard/assets/build/wizard-app.js:1
msgid "Subscribe"
msgstr "Inscrever-se"

#: admin-core/inc/global-settings.php:1002
msgid "Enter Google Ads Conversion ID"
msgstr "Insira o ID de Conversão do Google Ads"

#: admin-core/inc/global-settings.php:1006
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion ID."
msgstr "Faça login na sua conta do %1$1s Google Ads %2$2s para encontrar seu ID de conversão."

#: admin-core/inc/global-settings.php:1019
msgid "Enter Google Ads Conversion Label"
msgstr "Insira o Rótulo de Conversão do Google Ads"

#: admin-core/inc/global-settings.php:1023
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion label."
msgstr "Faça login na sua conta %1$1s do Google Ads %2$2s para encontrar seu rótulo de conversão."

#: admin-core/inc/global-settings.php:1049
msgid "Google Ads Events"
msgstr "Eventos do Google Ads"

#: admin-core/inc/global-settings.php:1182
msgid "If checked, PageVisit event will also be triggered for other pages/posts of site."
msgstr "Se marcado, o evento PageVisit também será acionado para outras páginas/publicações do site."

#: admin-core/inc/global-settings.php:1208
msgid "Enter Pinterest Tag ID"
msgstr "Insira o ID da Tag do Pinterest"

#: admin-core/inc/global-settings.php:1212
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Pinterest business account %2$2s to find your ID."
msgstr "Faça login na sua conta comercial do %1$1s Pinterest %2$2s para encontrar seu ID."

#: admin-core/inc/global-settings.php:1237
msgid "Enable Pinterest tag tracking consent notice"
msgstr "Ativar aviso de consentimento de rastreamento de tag do Pinterest"

#: admin-core/inc/global-settings.php:1250
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"This setting enables a consent notice for Pinterest Tag tracking on your website. For more information check "
"%1$1sPinterest documentation%2$2s."
msgstr ""
"Esta configuração ativa um aviso de consentimento para o rastreamento do Pinterest Tag no seu site. Para mais "
"informações, consulte a %1$1sdocumentação do Pinterest%2$2s."

#: admin-core/inc/global-settings.php:1266
msgid "Pinterest Events"
msgstr "Eventos do Pinterest"

#: admin-core/inc/global-settings.php:1340
msgid "Signup"
msgstr "Inscrever-se"

#: admin-core/inc/global-settings.php:1352
msgid "Signup event will be triggered for optin page."
msgstr "O evento de inscrição será acionado para a página de opt-in."

#: admin-core/inc/global-settings.php:1382
msgid "Store Revenue Report Emails"
msgstr "E-mails de Relatório de Receita da Loja"

#: admin-core/inc/global-settings.php:1387
msgid "Enable Store Report Email."
msgstr "Ativar e-mail de relatório da loja."

#: admin-core/inc/global-settings.php:1390
#. translators: %1$1s: link html start, %2$12: link html end
msgid "If enabled, you will receive the weekly report emails of your store for the revenue stats generated by CartFlows."
msgstr ""
"Se ativado, você receberá os e-mails de relatório semanal da sua loja com as estatísticas de receita geradas pelo "
"CartFlows."

#: admin-core/inc/global-settings.php:1397
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:577
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:579
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:174
msgid "Email Address"
msgstr "Endereço de Email"

#: admin-core/inc/global-settings.php:1398
msgid "Email address to receive the weekly sales report emails. For multiple emails, add each email address per line."
msgstr ""
"Endereço de e-mail para receber os e-mails do relatório de vendas semanal. Para múltiplos e-mails, adicione cada "
"endereço de e-mail por linha."

#: admin-core/inc/global-settings.php:1425
msgid "Delete plugin data on plugin deletion"
msgstr "Excluir dados do plugin ao excluir o plugin"

#: admin-core/inc/global-settings.php:1430
msgid "Are you sure? Do you want to delete plugin data while deleting the plugin? Type \"DELETE\" to confirm!"
msgstr "Tem certeza? Deseja excluir os dados do plugin ao deletar o plugin? Digite \"DELETE\" para confirmar!"

#: admin-core/inc/global-settings.php:1433
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"This option will delete all the CartFlows options data on plugin deletion. If you enable this and deletes the plugin, "
"you can't restore your saved data. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Esta opção irá excluir todos os dados de opções do CartFlows na exclusão do plugin. Se você ativar isso e excluir o "
"plugin, não poderá restaurar seus dados salvos. Para saber mais, %1$1s Clique aqui %2$2s."

#: admin-core/inc/log-status.php:108
msgid "Log deleted successfully!"
msgstr "Log excluído com sucesso!"

#: admin-core/inc/log-status.php:171
#: admin-core/inc/log-status.php:195
msgid "Nonce verification failed. Please refresh the page and retry."
msgstr "A verificação do nonce falhou. Por favor, atualize a página e tente novamente."

#: admin-core/inc/store-checkout.php:63
msgid "Checkout (Store)"
msgstr "Finalizar compra (Loja)"

#: admin-core/inc/store-checkout.php:67
msgid "Thank You (Store)"
msgstr "Obrigado (Loja)"

#: admin-core/views/404-error.php:36
msgid "404 ERROR"
msgstr "ERRO 404"

#: admin-core/views/404-error.php:37
msgid "Page Not Found."
msgstr "Página não encontrada."

#: admin-core/views/404-error.php:38
msgid "Sorry, we couldn’t find the page you’re looking for."
msgstr "Desculpe, não conseguimos encontrar a página que você está procurando."

#: admin-core/views/404-error.php:39
msgid "Go back home"
msgstr "Volte para casa"

#: admin-core/views/header.php:22
msgid "Generate More Leads & More Sales"
msgstr "Gerar Mais Leads e Mais Vendas"

#: classes/class-cartflows-admin-notices.php:88
#. translators: %1$s Software Title, %2$s Plugin, %3$s Anchor opening tag, %4$s Anchor closing tag, %5$s Software Title.
msgid ""
"%1$sCartFlows:%2$s We just introduced an awesome new feature, weekly store revenue reports via email. Now you can see "
"how many revenue we are generating for your store each week, without having to log into your website. You can set the "
"email address for these email from %3$shere.%4$s"
msgstr ""
"%1$sCartFlows:%2$s Acabamos de introduzir um novo recurso incrível, relatórios semanais de receita da loja por e-mail. "
"Agora você pode ver quanto de receita estamos gerando para sua loja a cada semana, sem precisar acessar seu site. Você "
"pode definir o endereço de e-mail para esses e-mails %3$saqui.%4$s"

#: classes/class-cartflows-admin-notices.php:218
msgid "How likely are you to recommend #pluginname to your friends or colleagues?"
msgstr "Qual a probabilidade de você recomendar o #pluginname para seus amigos ou colegas?"

#: classes/class-cartflows-admin-notices.php:221
msgid ""
"Could you please do us a favor and give us a 5-star rating on WordPress? It would help others choose CartFlows with "
"confidence. Thank you!"
msgstr ""
"Poderia, por favor, nos fazer um favor e nos dar uma avaliação de 5 estrelas no WordPress? Isso ajudaria outros a "
"escolher o CartFlows com confiança. Obrigado!"

#: classes/class-cartflows-admin-notices.php:225
msgid "Thank you for your feedback"
msgstr "Obrigado pelo seu feedback"

#: classes/class-cartflows-admin-notices.php:226
msgid "We value your input. How can we improve your experience?"
msgstr "Valorizamos a sua opinião. Como podemos melhorar a sua experiência?"

#: classes/class-cartflows-admin-notices.php:249
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Heads up! The Gutenberg plugin is not recommended on production sites as it may contain non-final features that cause "
"compatibility issues with CartFlows and other plugins. %1$s Please deactivate the Gutenberg plugin %2$s to ensure the "
"proper functioning of your website."
msgstr ""
"Atenção! O plugin Gutenberg não é recomendado em sites de produção, pois pode conter funcionalidades não finais que "
"causam problemas de compatibilidade com o CartFlows e outros plugins. %1$s Por favor, desative o plugin Gutenberg %2$s "
"para garantir o funcionamento adequado do seu site."

#: classes/class-cartflows-admin.php:122
#: wizard/views/wizard-base.php:19
msgid "CartFlows Setup"
msgstr "Configuração do CartFlows"

#: classes/class-cartflows-admin.php:167
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "Step"
msgstr "Passo"

#: classes/class-cartflows-admin.php:167
msgid "of"
msgstr "de"

#: classes/class-cartflows-admin.php:173
msgid "You're almost there! Once you complete CartFlows setup you can start receiving orders from flows."
msgstr "Você está quase lá! Assim que concluir a configuração do CartFlows, você poderá começar a receber pedidos dos fluxos."

#: classes/class-cartflows-admin.php:175
#: admin-core/assets/build/settings-app.js:34
msgid "Complete Setup"
msgstr "Concluir Configuração"

#: classes/class-cartflows-admin.php:233
msgid "Docs"
msgstr "Documentos"

#: classes/class-cartflows-admin.php:246
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:162
#: modules/landing/classes/class-cartflows-landing-meta-data.php:57
#: modules/optin/classes/class-cartflows-optin-meta-data.php:188
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:58
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Settings"
msgstr "Configurações"

#: classes/class-cartflows-admin.php:362
msgid "You do not have permission to access this page."
msgstr "Você não tem permissão para acessar esta página."

#: classes/class-cartflows-admin.php:363
#: classes/class-cartflows-admin.php:394
#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:11
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback to Previous Version"
msgstr "Reverter para a Versão Anterior"

#: classes/class-cartflows-admin.php:376
msgid "Error occurred, The version selected is invalid. Try selecting different version."
msgstr "Ocorreu um erro, a versão selecionada é inválida. Tente selecionar uma versão diferente."

#: classes/class-cartflows-default-meta.php:163
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1880
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1889
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1223
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:501
#: modules/checkout/templates/checkout/shipping-methods.php:69
msgid ""
"There are no shipping options available. Please ensure that your address has been entered correctly, or contact us if "
"you need any help."
msgstr ""
"Não há opções de envio disponíveis. Por favor, certifique-se de que o seu endereço foi inserido corretamente, ou entre "
"em contato conosco se precisar de ajuda."

#: classes/class-cartflows-default-meta.php:176
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1345
msgid "Place Order"
msgstr "Fazer Pedido"

#: classes/class-cartflows-default-meta.php:367
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1320
msgid "is required"
msgstr "é necessário"

#: classes/class-cartflows-default-meta.php:629
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:126
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:143
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:174
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:484
#: modules/thankyou/templates/instant-thankyou.php:76
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank you. Your order has been received."
msgstr "Obrigado. Seu pedido foi recebido."

#: classes/class-cartflows-default-meta.php:820
#: modules/optin/classes/class-cartflows-optin-meta-data.php:593
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit"
msgstr "Enviar"

#: classes/class-cartflows-flow-frontend.php:284
msgid "Edit Design"
msgstr "Editar Design"

#: classes/class-cartflows-functions.php:595
#. translators: %1$s page builder name "string"
msgid ""
"We have introduced %1$1s widgets for CartFlows shortcodes. Now, you can add/change/update design settings directly from "
"the page builder as well."
msgstr ""
"Introduzimos widgets %1$1s para shortcodes do CartFlows. Agora, você também pode adicionar/alterar/atualizar as "
"configurações de design diretamente do construtor de páginas."

#: classes/class-cartflows-functions.php:596
msgid "Learn More »"
msgstr "Saiba Mais »"

#: classes/class-cartflows-helper.php:568
msgid "First name"
msgstr "Primeiro nome"

#: classes/class-cartflows-helper.php:577
msgid "Last name"
msgstr "Sobrenome"

#: classes/class-cartflows-helper.php:586
#: wizard/assets/build/wizard-app.js:3
msgid "Email address"
msgstr "Endereço de e-mail"

#: classes/class-cartflows-helper.php:1370
msgid "Enable Field"
msgstr "Ativar Campo"

#: classes/class-cartflows-helper.php:1376
msgid "Field Width"
msgstr "Largura do Campo"

#: classes/class-cartflows-helper.php:1382
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "33%"
msgstr "33%"

#: classes/class-cartflows-helper.php:1386
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "50%"
msgstr "50%"

#: classes/class-cartflows-helper.php:1390
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "100%"
msgstr "100%"

#: classes/class-cartflows-helper.php:1397
msgid "Field Label"
msgstr "Rótulo do Campo"

#: classes/class-cartflows-helper.php:1402
#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
msgid "Field ID"
msgstr "ID do campo"

#: classes/class-cartflows-helper.php:1406
msgid "Copy this field id to use in Order Custom Field rule of dynamic offers."
msgstr "Copie este ID de campo para usar na regra de Campo Personalizado de Pedido de ofertas dinâmicas."

#: classes/class-cartflows-helper.php:1418
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Checked"
msgstr "Verificado"

#: classes/class-cartflows-helper.php:1422
msgid "Un-Checked"
msgstr "Desmarcado"

#: classes/class-cartflows-helper.php:1439
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:411
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Options"
msgstr "Opções"

#: classes/class-cartflows-helper.php:1463
msgid "Min Date"
msgstr "Data Mín"

#: classes/class-cartflows-helper.php:1470
msgid "Max Date"
msgstr "Data Máxima"

#: classes/class-cartflows-helper.php:1482
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Placeholder"
msgstr "Espaço reservado"

#: classes/class-cartflows-helper.php:1491
msgid "Min Number"
msgstr "Número Mín"

#: classes/class-cartflows-helper.php:1497
msgid "Max Number"
msgstr "Número Máximo"

#: classes/class-cartflows-helper.php:1506
msgid "Show In Email"
msgstr "Mostrar no Email"

#: classes/class-cartflows-helper.php:1513
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Required"
msgstr "Obrigatório"

#: classes/class-cartflows-helper.php:1521
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Collapsible"
msgstr "Dobrável"

#: classes/class-cartflows-helper.php:1571
msgid "CartFlows Primary Color"
msgstr "Cor Primária do CartFlows"

#: classes/class-cartflows-helper.php:1572
msgid "CartFlows Secondary Color"
msgstr "Cor Secundária do CartFlows"

#: classes/class-cartflows-helper.php:1573
msgid "CartFlows Text Color"
msgstr "Cor do Texto do CartFlows"

#: classes/class-cartflows-helper.php:1574
msgid "CartFlows Heading/Accent Color"
msgstr "Cor do Título/Acento do CartFlows"

#: classes/class-cartflows-loader.php:292
#. translators: %s: html tags
msgid ""
"The new version of  %1$s%3$s%2$s is released. Please download the latest zip to install the new updates. Click here to "
"%4$sdownload%5$s."
msgstr ""
"A nova versão de %1$s%3$s%2$s foi lançada. Por favor, baixe o zip mais recente para instalar as novas atualizações. "
"Clique aqui para %4$sbaixar%5$s."

#: classes/class-cartflows-loader.php:309
#. translators: %s: html tags
msgid "You are using an older version of %1$s%3$s%2$s. Please update %1$s%3$s%2$s plugin to version %1$s%4$s%2$s or higher."
msgstr ""
"Você está usando uma versão mais antiga de %1$s%3$s%2$s. Por favor, atualize o plugin %1$s%3$s%2$s para a versão "
"%1$s%4$s%2$s ou superior."

#: classes/class-cartflows-loader.php:612
#. translators: %s: html tags
msgid "This %1$sCartFlows%2$s page requires %1$sWooCommerce%2$s plugin installed & activated."
msgstr "Esta página %1$sCartFlows%2$s requer o plugin %1$sWooCommerce%2$s instalado e ativado."

#: classes/class-cartflows-loader.php:622
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
msgid "Activate WooCommerce"
msgstr "Ativar WooCommerce"

#: classes/class-cartflows-loader.php:629
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Install WooCommerce"
msgstr "Instalar WooCommerce"

#: classes/class-cartflows-rollback.php:167
msgid "CartFlows <p>Rollback to Previous Version</p>"
msgstr "CartFlows <p>Reverter para a Versão Anterior</p>"

#: classes/class-cartflows-tracking.php:1396
msgid "We use Pinterest tags to improve your experience. Do you consent to our use of Pinterest tags?"
msgstr "Usamos tags do Pinterest para melhorar sua experiência. Você consente com o uso de tags do Pinterest?"

#: classes/class-cartflows-tracking.php:1397
msgid "Accept"
msgstr "Aceitar"

#: classes/class-cartflows-tracking.php:1398
msgid "Decline"
msgstr "Recusar"

#: classes/class-cartflows-tracking.php:1403
msgid "Pinterest Consent"
msgstr "Consentimento do Pinterest"

#: classes/importer/batch-process/class-cartflows-batch-process.php:482
msgid ""
"ERROR! Cron schedules are disabled by setting constant DISABLE_WP_CRON to true.<br/>To start the import process please "
"enable the cron by setting the constant to false. E.g. define( 'DISABLE_WP_CRON', false );"
msgstr ""
"ERRO! Os agendamentos do Cron estão desativados ao definir a constante DISABLE_WP_CRON como true.<br/>Para iniciar o "
"processo de importação, por favor, ative o cron definindo a constante como false. Ex.: define( 'DISABLE_WP_CRON', false "
");"

#: classes/importer/batch-process/class-cartflows-batch-process.php:486
msgid ""
"ERROR! Cron schedules are disabled by setting constant ALTERNATE_WP_CRON to true.<br/>To start the import process "
"please enable the cron by setting the constant to false. E.g. define( 'ALTERNATE_WP_CRON', false );"
msgstr ""
"ERRO! As programações do Cron estão desativadas ao definir a constante ALTERNATE_WP_CRON como verdadeira.<br/>Para "
"iniciar o processo de importação, ative o cron definindo a constante como falsa. Por exemplo, defina( "
"'ALTERNATE_WP_CRON', false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:522
#. translators: 1: The HTTP response code.
msgid "Unexpected HTTP response code: %s"
msgstr "Código de resposta HTTP inesperado: %s"

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:46
msgid "(✕) Empty content."
msgstr "(✕) Conteúdo vazio."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:51
msgid "(✕) Invalid content."
msgstr "(✕) Conteúdo inválido."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:62
msgid "Invalid content. Expected an array."
msgstr "Conteúdo inválido. Esperava um array."

#: classes/importer/batch-process/helpers/class-wp-background-process-cartflows-sync-library.php:69
msgid "All processes are complete"
msgstr "Todos os processos estão concluídos"

#: classes/importer/batch-process/helpers/class-wp-background-process.php:440
#. Translators: %d: interval
msgid "Every %d Minutes"
msgstr "A cada %d minutos"

#: classes/importer/class-cartflows-api.php:428
msgid "Request successfully processed!"
msgstr "Pedido processado com sucesso!"

#: classes/logger/class-cartflows-log-handler-file.php:355
#: classes/logger/class-cartflows-log-handler-file.php:375
msgid "This method should not be called before plugins_loaded."
msgstr "Este método não deve ser chamado antes de plugins_loaded."

#: classes/logger/class-cartflows-wc-logger.php:58
#. translators: 1: class name 2: Cartflows_Log_Handler_Interface
msgid "The provided handler %1$s does not implement %2$s."
msgstr "O manipulador fornecido %1$s não implementa %2$s."

#: classes/logger/class-cartflows-wc-logger.php:136
#. translators: 1: Cartflows_WC_Logger::log 2: level
msgid "%1$s was called with an invalid level \"%2$s\"."
msgstr "%1$s foi chamado com um nível inválido \"%2$s\"."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:85
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:354
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:546
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:220
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:249
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:202
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:348
#: modules/gutenberg/dist/blocks.build.js:1
msgid "None"
msgstr "Nenhum"

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:112
#. translators: 1: anchor start, 2: anchor close
msgid ""
"Non-enrolled students will redirect to the selected CartFlows template. If you have not created any Flow already, add "
"new Flow from %1$shere%2$s."
msgstr ""
"Os alunos não matriculados serão redirecionados para o modelo CartFlows selecionado. Se você ainda não criou nenhum "
"Fluxo, adicione um novo Fluxo a partir de %1$saqui%2$s."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:118
msgid "Select CartFlows Template for this Course"
msgstr "Selecione o Modelo CartFlows para este Curso"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:44
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:150
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:373
#: modules/gutenberg/build/blocks-placeholder.js:9
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Checkout Form"
msgstr "Formulário de Checkout"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:34
msgid "Checkout Form."
msgstr "Formulário de Checkout."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:35
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:36
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:34
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:36
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:34
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:35
msgid "Cartflows Modules"
msgstr "Módulos Cartflows"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:137
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:146
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:59
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:68
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:251
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:198
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:207
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Checkout"
msgstr "Checkout Moderno"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:138
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:60
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:69
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:255
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:199
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:208
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern One Column"
msgstr "Moderno Uma Coluna"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:139
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:149
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:61
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:70
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:263
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:200
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:209
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "One Column"
msgstr "Uma Coluna"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:140
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:150
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:62
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:71
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:267
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:210
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Column"
msgstr "Duas Colunas"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:141
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:64
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:203
msgid "MultiStep Checkout ( PRO )"
msgstr "Checkout MultiEtapas ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:142
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:63
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:202
msgid "Two Step ( PRO )"
msgstr "Dois Passos ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:148
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:73
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:212
#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout"
msgstr "Checkout em Várias Etapas"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:151
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:271
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:211
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step"
msgstr "Dois Passos"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:168
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1212
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:446
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:229
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Labels"
msgstr "Etiquetas Modernas"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:132
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:109
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:68
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:82
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1012
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:157
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:294
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:160
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:152
#: modules/landing/classes/class-cartflows-landing-meta-data.php:106
#: modules/optin/classes/class-cartflows-optin-meta-data.php:561
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:389
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "General"
msgstr "Geral"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:190
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:253
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Layout"
msgstr "Selecionar Layout"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:192
#. translators: %s: link
msgid "The PRO layout options are available in the CartFlows Pro. %1$s  Upgrade Now! %2$s"
msgstr "As opções de layout PRO estão disponíveis no CartFlows Pro. %1$s  Atualize agora! %2$s"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:204
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:277
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:186
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:112
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:141
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:390
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:164
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:414
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:207
#: modules/optin/classes/class-cartflows-optin-meta-data.php:298
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Style"
msgstr "Estilo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:207
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:115
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1226
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:70
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:289
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Global"
msgstr "Global"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:233
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:262
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:341
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:529
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:458
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:473
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:127
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:189
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:334
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:236
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:264
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:310
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:346
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:399
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:689
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:228
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:347
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Typography"
msgstr "Tipografia"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:243
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:218
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1234
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:78
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:362
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:301
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:523
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:616
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:705
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:168
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading"
msgstr "Título"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:273
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:137
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1230
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:421
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:406
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:199
#: modules/optin/classes/class-cartflows-optin-meta-data.php:292
msgid "Input Fields"
msgstr "Campos de Entrada"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:287
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:460
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:913
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1114
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:443
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:878
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:226
#: modules/optin/classes/class-cartflows-optin-meta-data.php:375
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label Color"
msgstr "Cor da Etiqueta"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:301
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:161
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:477
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:188
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:572
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:454
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:237
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Background Color"
msgstr "Cor de Fundo do Campo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:320
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:503
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:465
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:248
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Text / Placeholder Color"
msgstr "Cor do Texto de Entrada / Espaço Reservado"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:350
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:542
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:198
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:344
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:533
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:476
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:259
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Style"
msgstr "Estilo de Borda"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:352
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:544
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:200
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:346
msgid "The type of border to use. Double borders must have a width of at least 3px to render properly."
msgstr "O tipo de borda a ser usado. Bordas duplas devem ter uma largura de pelo menos 3px para serem renderizadas corretamente."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:355
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:547
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:203
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:349
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:539
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:482
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:265
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Solid"
msgstr "Sólido"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:356
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:548
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:204
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:350
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:542
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:485
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:268
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dashed"
msgstr "Tracejado"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:357
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:549
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:205
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:351
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:541
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:484
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:267
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dotted"
msgstr "Pontilhado"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:358
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:550
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:206
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:352
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:540
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:483
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:266
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Double"
msgstr "Duplo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:377
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:582
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:230
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:377
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:575
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:500
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:278
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width"
msgstr "Largura da Borda"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:398
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:605
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:840
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:245
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:393
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:610
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1024
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:517
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:940
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:290
#: modules/optin/classes/class-cartflows-optin-meta-data.php:396
#: modules/optin/classes/class-cartflows-optin-meta-data.php:528
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Color"
msgstr "Cor da Borda"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:416
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:646
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:752
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:258
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:421
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius"
msgstr "Raio da Borda"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:437
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:274
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:556
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:287
#: modules/gutenberg/build/blocks.js:11
msgid "Buttons"
msgstr "Botões"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:465
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:411
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:292
#: modules/optin/classes/class-cartflows-optin-meta-data.php:507
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Hover Color"
msgstr "Cor do Texto ao Passar o Mouse"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:485
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:824
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:874
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:425
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:306
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:293
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:339
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:404
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:470
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:536
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:743
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1002
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1070
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:264
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:329
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:240
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:263
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:360
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:411
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:459
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:505
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:607
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:724
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:927
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:989
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:374
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:363
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:433
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:437
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:585
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:676
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:765
#: modules/optin/classes/class-cartflows-optin-meta-data.php:389
#: modules/optin/classes/class-cartflows-optin-meta-data.php:514
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Color"
msgstr "Cor de Fundo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:509
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:433
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:320
#: modules/optin/classes/class-cartflows-optin-meta-data.php:521
#: modules/gutenberg/build/blocks.js:11
msgid "Background Hover Color"
msgstr "Cor de fundo ao passar o mouse"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:626
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:331
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:407
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:316
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:703
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:456
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:421
#: modules/optin/classes/class-cartflows-optin-meta-data.php:535
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Hover Color"
msgstr "Cor de contorno ao passar o mouse"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:670
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1250
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:756
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Payment Section"
msgstr "Seção de Pagamento"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:688
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1131
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:776
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Description Color"
msgstr "Cor da Descrição"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:702
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:802
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Information Background Color"
msgstr "Cor de Fundo da Informação"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:710
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1143
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:829
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:789
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:256
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Background Color"
msgstr "Cor de Fundo da Seção"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:724
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1161
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:814
msgid "Section Padding"
msgstr "Preenchimento da Seção"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:738
#: modules/gutenberg/build/blocks.js:11
msgid "Margin"
msgstr "Margem"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:768
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1246
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:861
msgid "Field Validation & Error Messages"
msgstr "Validação de Campo e Mensagens de Erro"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:772
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:602
msgid "Field Label Color"
msgstr "Cor do Rótulo do Campo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:788
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:936
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:587
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:890
msgid "Field Border Color"
msgstr "Cor da Borda do Campo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:808
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:980
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:913
msgid "Error Message Color"
msgstr "Cor da Mensagem de Erro"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:857
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1242
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:962
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Review"
msgstr "Revisão do Pedido"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:32
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:70
#: modules/gutenberg/classes/class-cartflows-block-config.php:54
#: modules/gutenberg/build/blocks-placeholder.js:10
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button"
msgstr "Botão Próximo Passo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:33
msgid "A simple next step button."
msgstr "Um simples botão de próximo passo."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:139
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:192
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text"
msgstr "Texto"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:140
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:61
msgid "Next Step"
msgstr "Próximo Passo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:149
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon"
msgstr "Ícone"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:160
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:195
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Position"
msgstr "Posição do Ícone"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:163
msgid "Before Text"
msgstr "Antes do Texto"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:164
msgid "After Text"
msgstr "Após o texto"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:172
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:240
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Spacing"
msgstr "Espaçamento de Ícones"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:190
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Type"
msgstr "Digite"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:195
msgid "Flat"
msgstr "Plano"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:196
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Gradient"
msgstr "Gradiente"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:197
msgid "Transparent"
msgstr "Transparente"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:198
msgid "3D"
msgstr "3D"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:208
msgid "Border Size"
msgstr "Tamanho da Borda"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:217
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:233
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:246
msgid "Hover Styles"
msgstr "Estilos de Hover"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:221
msgid "Fade Background"
msgstr "Desvanecer Fundo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:222
msgid "Fill Background From Top"
msgstr "Preencher o Fundo de Cima"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:223
msgid "Fill Background From Bottom"
msgstr "Preencher o Fundo de Baixo para Cima"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:224
msgid "Fill Background From Left"
msgstr "Preencher o fundo da esquerda"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:225
msgid "Fill Background From Right"
msgstr "Preencher o fundo da direita"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:226
msgid "Fill Background Vertical"
msgstr "Preencher Fundo Vertical"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:227
msgid "Fill Background Diagonal"
msgstr "Preencher Fundo Diagonal"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:228
msgid "Fill Background Horizontal"
msgstr "Preencher Fundo Horizontal"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:236
msgid "Move Down"
msgstr "Mover para baixo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:237
msgid "Move Up"
msgstr "Mover para cima"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:238
msgid "Move Left"
msgstr "Mover para a esquerda"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:239
msgid "Move Right"
msgstr "Mover para a direita"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:240
msgid "Animate Top"
msgstr "Animar Topo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:241
msgid "Animate Bottom"
msgstr "Animar Inferior"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:250
msgid "Appear Icon From Right"
msgstr "Fazer o ícone aparecer da direita"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:251
msgid "Appear Icon From Left"
msgstr "Apresentar ícone da esquerda"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:252
msgid "Appear Icon From Top"
msgstr "Aparecer ícone de cima"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:253
msgid "Appear Icon From Bottom"
msgstr "Aparecer ícone de baixo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:259
msgid "Structure"
msgstr "Estrutura"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:263
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Width"
msgstr "Largura"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:267
#: modules/gutenberg/build/blocks.js:11
msgid "Full Width"
msgstr "Largura Total"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:268
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:509
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:680
#: modules/optin/classes/class-cartflows-optin-meta-data.php:355
#: modules/optin/classes/class-cartflows-optin-meta-data.php:459
msgid "Custom"
msgstr "Personalizado"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:284
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:302
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:309
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Alignment"
msgstr "Alinhamento"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:287
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:297
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:310
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:507
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:490
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center"
msgstr "Centro"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:288
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:298
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:306
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:503
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:313
#: modules/optin/classes/class-cartflows-optin-meta-data.php:486
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Left"
msgstr "Esquerda"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:289
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:299
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:314
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:511
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:321
#: modules/optin/classes/class-cartflows-optin-meta-data.php:494
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Right"
msgstr "Certo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:294
msgid "Mobile Alignment"
msgstr "Alinhamento Móvel"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:304
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:340
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Padding"
msgstr "Preenchimento"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:318
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:447
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:792
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:215
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:277
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:626
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:395
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:373
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border"
msgstr "Fronteira"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:342
msgid "Custom Width"
msgstr "Largura Personalizada"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:351
msgid "Custom Height"
msgstr "Altura Personalizada"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:360
msgid "Padding Top/Bottom"
msgstr "Preenchimento Superior/Inferior"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:369
msgid "Padding Left/Right"
msgstr "Preenchimento à Esquerda/Direita"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:378
msgid "Round Corners"
msgstr "Arredondar Cantos"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:393
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Colors"
msgstr "Cores"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:444
msgid "Apply Hover Color To"
msgstr "Aplicar Cor de Hover Para"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:448
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:322
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
msgid "Background"
msgstr "Plano de fundo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:461
msgid "Button Settings"
msgstr "Configurações do Botão"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:465
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:260
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Size"
msgstr "Tamanho do Ícone"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:46
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:578
#: modules/optin/classes/class-cartflows-optin-meta-data.php:181
#: modules/optin/classes/class-cartflows-optin-meta-data.php:260
#: modules/gutenberg/build/blocks-placeholder.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Optin Form"
msgstr "Formulário de Inscrição"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:34
msgid "Optin Form."
msgstr "Formulário de Inscrição."

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:97
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:104
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:144
#: modules/optin/classes/class-cartflows-optin-meta-data.php:309
msgid "Floating Labels"
msgstr "Rótulos Flutuantes"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:32
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:46
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:65
#: modules/gutenberg/classes/class-cartflows-block-config.php:157
msgid "Order Details Form"
msgstr "Formulário de Detalhes do Pedido"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:33
msgid "Order Details Form."
msgstr "Formulário de Detalhes do Pedido."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:125
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:141
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:172
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank You Text"
msgstr "Texto de Agradecimento"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:136
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:321
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:86
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:150
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:458
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Overview"
msgstr "Visão Geral do Pedido"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:145
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:428
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:94
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:157
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:605
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:99
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Details"
msgstr "Detalhes do Pedido"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:154
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:164
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:209
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Billing Address"
msgstr "Endereço de Cobrança"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:163
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:171
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:221
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Shipping Address"
msgstr "Endereço de Entrega"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:178
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:74
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:245
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing"
msgstr "Espaçamento"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:182
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:253
msgid "Heading Bottom Spacing"
msgstr "Espaçamento Inferior do Cabeçalho"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:197
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:269
msgid "Spacing Between Sections"
msgstr "Espaçamento Entre Seções"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:375
msgid "Sections Heading"
msgstr "Títulos das Seções"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:274
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:404
msgid "Sections Content"
msgstr "Conteúdo das Seções"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:353
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:418
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:484
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:550
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:398
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:446
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:492
msgid "Text Typography"
msgstr "Tipografia de Texto"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:363
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:90
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:515
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Downloads"
msgstr "Downloads"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:367
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:432
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:498
#: modules/gutenberg/build/blocks.js:11
msgid "Heading Color"
msgstr "Cor do Cabeçalho"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:381
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:447
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:513
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:381
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:432
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:479
msgid "Heading Typography"
msgstr "Tipografia de Cabeçalho"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:494
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:98
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:697
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Customer Details"
msgstr "Detalhes do Cliente"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:116
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:117
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:128
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:159
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Layout"
msgstr "Layout"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:134
#. translators: %s is the URL for upgrading
msgid "This feature is available in the CartFlows higher plan. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">%2$s</a>."
msgstr ""
"Este recurso está disponível no plano superior do CartFlows. <a href=\"%1$s\" target=\"_blank\" "
"rel=\"noopener\">%2$s</a>."

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:136
msgid "Upgrade Now!"
msgstr "Atualize agora!"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:154
msgid " Global Text Typography"
msgstr "Tipografia de Texto Global"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:252
msgid " Global Primary Color"
msgstr "Cor Primária Global"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:538
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:481
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:264
msgid "Inherit"
msgstr "Herdar"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:645
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:528
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:643
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:403
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:301
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:381
msgid "Rounded Corners"
msgstr "Cantos Arredondados"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:841
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:290
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Box Shadow"
msgstr "Sombra da Caixa"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:906
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:869
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Validation"
msgstr "Validação de Campo"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:904
msgid "Error Messages"
msgstr "Mensagens de Erro"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1174
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:827
msgid "Section Margin"
msgstr "Margem da Seção"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1238
msgid "Buttons (Normal)"
msgstr "Botões (Normal)"

#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:76
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button"
msgstr "Botão"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:276
msgid "Sections Heading Typography"
msgstr "Tipografia de Cabeçalhos de Seções"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:288
msgid "Sections Text Typography"
msgstr "Seções Texto Tipografia"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:304
msgid "Sections Background Color"
msgstr "Cor de Fundo das Seções"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:145
msgid "Sorry there was a problem removing this coupon."
msgstr "Desculpe, houve um problema ao remover este cupom."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:148
msgid "Coupon has been removed."
msgstr "O cupom foi removido."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:166
msgid "Sorry there was a problem removing "
msgstr "Desculpe, houve um problema ao remover"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:169
msgid " has been removed."
msgstr "foi removido."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email Exist."
msgstr "Email existente."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email not exist"
msgstr "Email não existe"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:126
#. Translators: %1$s & %2$s is replaced with Field Name
msgid "%1$s Add %2$s"
msgstr "%1$s Adicionar %2$s"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:137
#. Translators: %s is replaced with Field Icon
msgid "%s Have a coupon?"
msgstr "%s Tem um cupom?"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:146
#. Translators: %s is replaced with Field Icon
msgid "%s Add Order Notes"
msgstr "%s Adicionar Notas do Pedido"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:474
#: modules/optin/classes/class-cartflows-optin-markup.php:228
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:232
msgid "WooCommerce functions do not exist. If you are in an IFrame, please reload it."
msgstr "As funções do WooCommerce não existem. Se você estiver em um IFrame, por favor, recarregue-o."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:475
#: modules/optin/classes/class-cartflows-optin-markup.php:229
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:233
msgid "Click Here to Reload"
msgstr "Clique aqui para recarregar"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:503
msgid "Checkout ID not found"
msgstr "ID de checkout não encontrado"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:506
#. translators: %1$1s, %2$2s Link to article
msgid ""
"It seems that this is not the CartFlows Checkout page where you have added this shortcode. Please refer to this "
"%1$1sarticle%2$2s to know more."
msgstr ""
"Parece que esta não é a página de Checkout do CartFlows onde você adicionou este shortcode. Por favor, consulte este "
"%1$1sartigo%2$2s para saber mais."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:576
#: modules/checkout/templates/embed/checkout-template-simple.php:48
#: modules/checkout/templates/wcf-template.php:40
#: modules/optin/templates/optin-template-simple.php:29
msgid "Your cart is currently empty."
msgstr "Seu carrinho está vazio no momento."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:616
#. translators: %1$1s, %2$2s Link to meta
msgid "No product is selected. Please select products from the %1$1scheckout meta settings%2$2s to continue."
msgstr ""
"Nenhum produto foi selecionado. Por favor, selecione produtos nas %1$1sconfigurações de meta de checkout%2$2s para "
"continuar."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:724
msgid "Variations Not set"
msgstr "Variações Não definidas"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:735
msgid "This product can't be purchased"
msgstr "Este produto não pode ser comprado"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1467
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1511
#: modules/checkout/templates/checkout/collapsed-order-summary.php:46
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:82
msgid "Coupon Code"
msgstr "Código do Cupom"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1468
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1520
#: modules/checkout/templates/checkout/collapsed-order-summary.php:51
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:87
msgid "Apply"
msgstr "Aplicar"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1544
msgid "Entered email address is not a valid email."
msgstr "O endereço de e-mail inserido não é um e-mail válido."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1545
msgid "This email is already registered. Please enter the password to continue."
msgstr "Este email já está registrado. Por favor, insira a senha para continuar."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1548
msgid "Value must be between "
msgstr "O valor deve estar entre"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1752
msgid "Show Order Summary"
msgstr "Mostrar Resumo do Pedido"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1753
msgid "Hide Order Summary"
msgstr "Ocultar resumo do pedido"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1876
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:498
#: modules/checkout/templates/checkout/shipping-methods.php:66
msgid "Enter your address to view shipping options."
msgstr "Insira seu endereço para ver as opções de envio."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1885
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:405
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:407
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:796
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:110
#: modules/thankyou/templates/instant-thankyou-order-details.php:107
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping"
msgstr "Envio"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:138
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:943
#: modules/optin/classes/class-cartflows-optin-meta-data.php:174
msgid "Products"
msgstr "Produtos"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:144
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Order Bumps"
msgstr "Ofertas Adicionais"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:156
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Offers"
msgstr "Ofertas Dinâmicas"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:172
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:982
msgid "Checkout Offer"
msgstr "Oferta de Checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:205
msgid "Two Step (Available in higher plan) "
msgstr "Dois Passos (Disponível em plano superior)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:206
msgid "Multistep Checkout (Available in higher plan) "
msgstr "Checkout em várias etapas (Disponível em plano superior)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:213
#: modules/landing/classes/class-cartflows-landing-meta-data.php:91
#: modules/optin/classes/class-cartflows-optin-meta-data.php:253
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:92
msgid "Shortcode"
msgstr "Código curto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:230
msgid "CartFlows Checkout"
msgstr "Checkout do CartFlows"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:232
msgid "Add this shortcode to your checkout page"
msgstr "Adicione este código curto à sua página de checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:239
msgid "Checkout Design"
msgstr "Design de Checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:245
msgid "Checkout Skin"
msgstr "Finalizar Compra Skin"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:259
#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout"
msgstr "Checkout em várias etapas"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:296
#: modules/optin/classes/class-cartflows-optin-meta-data.php:284
#: modules/optin/classes/class-cartflows-optin-meta-data.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:420
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:181
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:205
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:306
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Family"
msgstr "Família de Fontes"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:304
msgid "Instant Checkout"
msgstr "Checkout Instantâneo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:319
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:328
msgid "Left Column Background Color"
msgstr "Cor de Fundo da Coluna Esquerda"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:336
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:344
msgid "Right Column Background Color"
msgstr "Cor de Fundo da Coluna Direita"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:355
msgid "Checkout Texts & Buttons"
msgstr "Textos e Botões de Checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:362
msgid "Enable Advance Options"
msgstr "Ativar Opções Avançadas"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:370
msgid "Heading Font"
msgstr "Fonte do Cabeçalho"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:384
#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text Color"
msgstr "Cor do Texto do Cabeçalho"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:400
msgid "Heading Font Family"
msgstr "Família de Fontes do Cabeçalho"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:436
msgid "Input Field Style"
msgstr "Estilo do Campo de Entrada"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:463
msgid "Input Field Font Family"
msgstr "Família de Fontes do Campo de Entrada"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:483
msgid "Field Size"
msgstr "Tamanho do Campo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:489
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:660
#: modules/optin/classes/class-cartflows-optin-meta-data.php:335
#: modules/optin/classes/class-cartflows-optin-meta-data.php:439
msgid "Extra Small"
msgstr "Extra Pequeno"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:493
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:664
#: modules/optin/classes/class-cartflows-optin-meta-data.php:339
#: modules/optin/classes/class-cartflows-optin-meta-data.php:443
msgid "Small"
msgstr "Pequeno"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:497
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:668
#: modules/optin/classes/class-cartflows-optin-meta-data.php:343
#: modules/optin/classes/class-cartflows-optin-meta-data.php:447
msgid "Medium"
msgstr "Médio"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:501
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:672
#: modules/optin/classes/class-cartflows-optin-meta-data.php:347
#: modules/optin/classes/class-cartflows-optin-meta-data.php:451
msgid "Large"
msgstr "Grande"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:505
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:676
#: modules/optin/classes/class-cartflows-optin-meta-data.php:351
#: modules/optin/classes/class-cartflows-optin-meta-data.php:455
msgid "Extra Large"
msgstr "Extra Grande"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:525
msgid "Field Top-Bottom Spacing"
msgstr "Espaçamento de Campo Superior-Inferior"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:541
msgid "Field Left-Right Spacing"
msgstr "Espaçamento de Campo Esquerda-Direita"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:557
msgid "Field Text / Placeholder Color"
msgstr "Cor do Texto do Campo / Placeholder"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:618
msgid "Button Fields"
msgstr "Campos de Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:634
msgid "Button Font Family"
msgstr "Família de Fontes do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:654
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:330
msgid "Button Size"
msgstr "Tamanho do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:696
msgid "Button Top-Bottom Spacing"
msgstr "Espaçamento Superior-Inferior do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:717
msgid "Button Left-Right Spacing"
msgstr "Espaçamento Esquerda-Direita do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:738
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:294
msgid "Button Text Color"
msgstr "Cor do Texto do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:753
msgid "Button Text Hover Color"
msgstr "Cor do Texto do Botão ao Passar o Mouse"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:768
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:300
msgid "Button Background Color"
msgstr "Cor de Fundo do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:783
msgid "Button Background Hover Color"
msgstr "Cor de fundo do botão ao passar o mouse"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:798
msgid "Button Border Color"
msgstr "Cor da Borda do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:813
msgid "Button Border Hover Color"
msgstr "Cor da Borda do Botão ao Passar o Mouse"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:861
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:865
msgid "Enable Product Options"
msgstr "Ativar opções de produto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:870
msgid "Enable Conditions"
msgstr "Ativar Condições"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:874
msgid "Restrict user to purchase all products"
msgstr "Restringir o usuário de comprar todos os produtos"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:878
msgid "Let user select one product from all options"
msgstr "Permitir que o usuário selecione um produto entre todas as opções"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:882
msgid "Let user select multiple products from all options"
msgstr "Permitir que o usuário selecione vários produtos de todas as opções"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:888
msgid "Enable Variations"
msgstr "Ativar Variações"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:897
msgid "Show variations inline"
msgstr "Mostrar variações em linha"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:901
msgid "Show variations in popup"
msgstr "Mostrar variações no popup"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:907
msgid "Enable Quantity"
msgstr "Ativar Quantidade"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:920
msgid "Select Coupon"
msgstr "Selecionar Cupom"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:921
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a coupon"
msgstr "Procure um cupom"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:925
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows coupon please %1$1s Click here.%2$2s"
msgstr "Para mais informações sobre o cupom CartFlows, por favor %1$1s clique aqui.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:951
msgid "Select Product"
msgstr "Selecionar Produto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:952
msgid "Search for a product..."
msgstr "Procure um produto..."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:962
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the checkout product settings please %1$1s Click here.%2$2s"
msgstr "Para mais informações sobre as configurações do produto de checkout, por favor %1$1s clique aqui.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:968
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Auto Apply Coupon"
msgstr "Aplicar Cupom Automaticamente"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:975
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Product Options"
msgstr "Opções de Produto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1026
#: modules/flow/classes/class-cartflows-step-meta-base.php:80
#: modules/landing/classes/class-cartflows-landing-meta-data.php:119
#: modules/optin/classes/class-cartflows-optin-meta-data.php:574
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:402
msgid "Custom Script"
msgstr "Script Personalizado"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1035
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:475
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Advanced"
msgstr "Avançado"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1041
msgid "Display product images"
msgstr "Exibir imagens do produto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1049
msgid "Enable cart editing on checkout"
msgstr "Ativar edição do carrinho no checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1064
#: modules/landing/classes/class-cartflows-landing-meta-data.php:134
#: modules/optin/classes/class-cartflows-optin-meta-data.php:634
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:523
msgid "Step Note"
msgstr "Nota de Etapa"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1148
msgid "Form Settings"
msgstr "Configurações do Formulário"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1153
msgid "Enable Coupon Field"
msgstr "Ativar Campo de Cupom"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1159
msgid "Collapsible Coupon Field"
msgstr "Campo de Cupom Retrátil"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1175
msgid "Enable Additional Field"
msgstr "Ativar Campo Adicional"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1181
msgid "Collapsible Additional Field"
msgstr "Campo Adicional Colapsável"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1197
msgid "Enable Ship To Different Address"
msgstr "Ativar Enviar para Endereço Diferente"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1204
msgid "Enable Google Address Autocomplete"
msgstr "Ativar o preenchimento automático de endereço do Google"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1207
#. translators: %1$s: link html start, %2$s: link html end
msgid "Before enabling this option, make sure that you have added API key in Google Address Autocomplete Settings."
msgstr ""
"Antes de ativar esta opção, certifique-se de que você adicionou a chave API nas Configurações de Autocompletar Endereço "
"do Google."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1212
msgid "Enable Custom Shipping Message"
msgstr "Ativar Mensagem de Envio Personalizada"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1220
msgid "Shipping Message"
msgstr "Mensagem de Envio"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1224
msgid "This message will be displayed when no shipping method is available."
msgstr "Esta mensagem será exibida quando nenhum método de envio estiver disponível."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1241
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:446
msgid "Order Summary Position"
msgstr "Posição do Resumo do Pedido"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1248
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:219
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:453
msgid "Top"
msgstr "Topo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1252
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:457
msgid "Bottom"
msgstr "Inferior"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1266
msgid "Form Headings"
msgstr "Títulos de Formulário"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1273
msgid "Billing Details"
msgstr "Detalhes de Cobrança"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1275
msgid "Billing details"
msgstr "Detalhes de faturamento"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1280
msgid "Shipping Details"
msgstr "Detalhes de Envio"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1282
msgid "Ship to a different address?"
msgstr "Enviar para um endereço diferente?"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1287
#: modules/thankyou/templates/instant-thankyou-your-product.php:23
msgid "Your Order"
msgstr "Seu Pedido"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1289
msgid "Your order"
msgstr "Seu pedido"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1294
msgid "Customer Information"
msgstr "Informações do Cliente"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1296
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:159
msgid "Customer information"
msgstr "Informações do cliente"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1302
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1304
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:823
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:276
#: modules/thankyou/templates/instant-thankyou-order-details.php:127
msgid "Payment"
msgstr "Pagamento"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1310
msgid "Enable Field validation error message"
msgstr "Ativar mensagem de erro de validação de campo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1318
msgid "Validation error message"
msgstr "Mensagem de erro de validação"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1336
msgid "Place Order Button"
msgstr "Botão de Fazer Pedido"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1342
#: modules/optin/classes/class-cartflows-optin-meta-data.php:590
#: modules/gutenberg/build/blocks.js:11
msgid "Button Text"
msgstr "Texto do Botão"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1351
msgid "Enable Lock Icon"
msgstr "Ativar ícone de bloqueio"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1360
msgid "Enable Price Display"
msgstr "Ativar exibição de preço"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:260
msgid "Home"
msgstr "Início"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:496
#: modules/checkout/templates/checkout/shipping-methods.php:64
msgid "Shipping costs are calculated during checkout."
msgstr "Os custos de envio são calculados durante o checkout."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:515
#: modules/checkout/templates/checkout/shipping-methods.php:72
#. Translators: $s shipping destination.
msgid "No shipping options were found for %s."
msgstr "Não foram encontradas opções de envio para %s."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:519
#: modules/checkout/templates/checkout/shipping-methods.php:73
#. Translators: $s shipping destination.
msgid "Enter a different address"
msgstr "Insira um endereço diferente"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:562
#: modules/thankyou/templates/instant-thankyou-order-details.php:67
msgid "Contact"
msgstr "Contato"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:564
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "%1$1s Log in%2$2s"
msgstr "%1$1s Entrar%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:594
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:192
msgid "Password"
msgstr "Senha"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:596
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:194
#. translators: %s: asterisk mark
msgid "Password %s"
msgstr "Senha %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:602
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:200
msgid "Login"
msgstr "Login"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:603
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:201
msgid "Lost your password?"
msgstr "Perdeu sua senha?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:608
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:206
msgid "Login is optional, you can continue with your order below."
msgstr "O login é opcional, você pode continuar com seu pedido abaixo."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:620
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:218
msgid "Create an account?"
msgstr "Criar uma conta?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:635
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:233
msgid "Account username"
msgstr "Nome de usuário da conta"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:637
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:235
#. translators: %s: asterisk mark
msgid "Account username %s"
msgstr "Nome de usuário da conta %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:649
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:247
msgid "Create account password"
msgstr "Criar senha da conta"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:651
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:249
#. translators: %s: asterisk mark
msgid "Create account password %s"
msgstr "Criar senha da conta %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:660
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:258
#. translators: %1$s: username, %2$s emailid
msgid " Welcome Back %1$s ( %2$s )"
msgstr "Bem-vindo de volta %1$s ( %2$s )"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:915
msgid "Looks like you haven't added any items to cart yet — start shopping to fill it up!"
msgstr "Parece que você ainda não adicionou nenhum item ao carrinho — comece a comprar para preenchê-lo!"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:927
msgid "Your Cart is Currently Empty."
msgstr "Seu carrinho está atualmente vazio."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:929
msgid "Start Shopping"
msgstr "Começar a Comprar"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:161
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "Already have an account? %1$1s Log in%2$2s"
msgstr "Já tem uma conta? %1$1s Faça login%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:177
#. translators: %s: asterisk mark
msgid "Email Address %s"
msgstr "Endereço de Email %s"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:69
msgid "Coupon code applied successfully."
msgstr "Código do cupom aplicado com sucesso."

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:76
msgid "Have a coupon?"
msgstr "Tem um cupom?"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:95
#: modules/checkout/templates/checkout/order-review-table.php:17
#: modules/checkout/templates/checkout/order-review-table.php:43
#: modules/thankyou/templates/instant-thankyou-your-product.php:118
msgid "Subtotal"
msgstr "Subtotal"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:147
#: modules/checkout/templates/checkout/order-review-table.php:79
#: modules/thankyou/templates/instant-thankyou-your-product.php:148
msgid "Total"
msgstr "Total"

#: modules/checkout/templates/checkout/order-review-table.php:16
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Product"
msgstr "Produto"

#: modules/checkout/templates/checkout/shipping-methods.php:53
#. Translators: $s shipping destination.
msgid "Shipping to %s."
msgstr "Enviando para %s."

#: modules/checkout/templates/checkout/shipping-methods.php:54
#. Translators: $s shipping destination.
msgid "Change address"
msgstr "Alterar endereço"

#: modules/checkout/templates/checkout/shipping-methods.php:56
msgid "Shipping options will be updated during checkout."
msgstr "As opções de envio serão atualizadas durante o checkout."

#: modules/checkout/templates/wcf-template.php:51
msgid "Copyright &copy;"
msgstr "Direitos autorais &copy;"

#: modules/checkout/templates/wcf-template.php:56
msgid "All Rights Reserved"
msgstr "Todos os direitos reservados"

#: modules/elementor/class-cartflows-el-widgets-loader.php:177
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:588
msgid "Cartflows"
msgstr "Cartflows"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:267
#. translators: %s admin link
msgid ""
"This feature is available in the CartFlows higher plan. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Upgrade "
"Now!</a>."
msgstr ""
"Este recurso está disponível no plano superior do CartFlows. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Atualize "
"agora!</a>."

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:370
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:175
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color"
msgstr "Cor"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:585
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:354
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:343
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Normal"
msgstr "Normal"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:683
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:417
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:403
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover"
msgstr "Passe o mouse"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:839
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Rounded Corners"
msgstr "Seção Canto Arredondado"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:164
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title"
msgstr "Título"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:166
msgid "BUY NOW"
msgstr "COMPRE AGORA"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:176
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sub Title"
msgstr "Subtítulo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title"
msgstr "Antes do Título"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:200
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title"
msgstr "Após o Título"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:201
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title & Sub Title"
msgstr "Antes do Título & Subtítulo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:202
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title & Sub Title"
msgstr "Após Título & Subtítulo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:213
msgid "Icon Vertical Alignment"
msgstr "Alinhamento Vertical do Ícone"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:223
msgid "Middle"
msgstr "Meio"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:318
msgid "Justify"
msgstr "Justificar"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:424
msgid "Hover Text Color"
msgstr "Cor do Texto ao Passar o Mouse"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:437
msgid "Hover Background Color"
msgstr "Cor de Fundo ao Passar o Mouse"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:467
msgid "Hover Animation"
msgstr "Animação de Passagem"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:554
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:645
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:734
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Content"
msgstr "Conteúdo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:499
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Alignment"
msgstr "Alinhamento de Texto"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:556
msgid "Title and Sub Title Spacing"
msgstr "Espaçamento de Título e Subtítulo"

#: modules/elementor/widgets/class-cartflows-el-optin-form.php:324
#: modules/optin/classes/class-cartflows-optin-meta-data.php:406
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button"
msgstr "Botão de Enviar"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:187
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:199
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:211
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:223
msgid "Show"
msgstr "Mostrar"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:367
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sections"
msgstr "Seções"

#: modules/email-report/class-cartflows-admin-report-emails.php:104
msgid "There"
msgstr "Lá"

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "You have successfully unsubscribed from our weekly emails list."
msgstr "Você cancelou com sucesso a inscrição na nossa lista de e-mails semanais."

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "Unsubscribed"
msgstr "Cancelado"

#: modules/email-report/class-cartflows-admin-report-emails.php:174
msgid "Here’s how your store performed last week!"
msgstr "Aqui está o desempenho da sua loja na semana passada!"

#: modules/email-report/templates/email-body.php:17
msgid "CartFlows Weekly Report"
msgstr "Relatório Semanal do CartFlows"

#: modules/email-report/templates/email-cf-pro-block.php:26
msgid "CartFlows Pro can help you to increase conversion and maximize profits."
msgstr "O CartFlows Pro pode ajudá-lo a aumentar a conversão e maximizar os lucros."

#: modules/email-report/templates/email-cf-pro-block.php:43
msgid ""
"Want to earn 30% more store revenue on autopilot? CartFlows order bumps and upsells help you do just that. Try "
"CartFlows Pro risk-free for 30 days!"
msgstr ""
"Quer ganhar 30% mais de receita da loja automaticamente? Os bumps de pedido e upsells do CartFlows ajudam você a fazer "
"exatamente isso. Experimente o CartFlows Pro sem riscos por 30 dias!"

#: modules/email-report/templates/email-cf-pro-block.php:62
msgid "GET CARTFLOWS NOW"
msgstr "OBTENHA CARTFLOWS AGORA"

#: modules/email-report/templates/email-content-section.php:27
#. translators: %s user name
msgid "Hey %s!"
msgstr "Ei %s!"

#: modules/email-report/templates/email-content-section.php:42
#. translators: %1$s: store name, %2$s: total revenue.  %3$s: total revenue
msgid ""
"%1$s has earned a total %2$s in revenue last week by using CartFlows to power your store! And in the last month, it "
"earned %3$s"
msgstr ""
"%1$s ganhou um total de %2$s em receita na semana passada usando o CartFlows para impulsionar sua loja! E no último "
"mês, ganhou %3$s"

#: modules/email-report/templates/email-content-section.php:79
msgid "(Last 7 days)"
msgstr "(Últimos 7 dias)"

#: modules/email-report/templates/email-content-section.php:93
msgid "(Last 30 days)"
msgstr "(Últimos 30 dias)"

#: modules/email-report/templates/email-footer.php:63
#. translators: %1$s - link to a site;
msgid "This email was auto-generated and sent from %1$s."
msgstr "Este e-mail foi gerado automaticamente e enviado de %1$s."

#: modules/email-report/templates/email-footer.php:70
msgid "Unsubscribe"
msgstr "Cancelar subscrição"

#: modules/email-report/templates/email-header.php:27
msgid "Your weekly summary from CartFlows."
msgstr "Seu resumo semanal do CartFlows."

#: modules/email-report/templates/email-other-product-block.php:26
msgid "Would you like to try our other products that help WooCommerce stores sell more?"
msgstr "Gostaria de experimentar nossos outros produtos que ajudam as lojas WooCommerce a vender mais?"

#: modules/email-report/templates/email-other-product-block.php:41
msgid "TRY OUR OTHER PRODUCTS"
msgstr "EXPERIMENTE NOSSOS OUTROS PRODUTOS"

#: modules/email-report/templates/email-stat-content.php:26
msgid "Full Overview"
msgstr "Visão Geral Completa"

#: modules/email-report/templates/email-stat-content.php:82
#: modules/email-report/templates/email-stat-content.php:220
msgid "Order Placed"
msgstr "Pedido Realizado"

#: modules/email-report/templates/email-stat-content.php:112
#: modules/email-report/templates/email-stat-content.php:247
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total Visits"
msgstr "Total de Visitas"

#: modules/email-report/templates/email-stat-content.php:142
#: modules/email-report/templates/email-stat-content.php:274
msgid "Order Bumps Revenue"
msgstr "Receita de Incrementos de Pedido"

#: modules/email-report/templates/email-stat-content.php:172
#: modules/email-report/templates/email-stat-content.php:304
msgid "Offers Revenue"
msgstr "Receita de Ofertas"

#: modules/email-report/templates/email-stat-content.php:250
#: modules/email-report/templates/email-stat-content.php:282
#: modules/email-report/templates/email-stat-content.php:307
msgid "CartFlows Pro"
msgstr "CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Flow: "
msgstr "Fluxo:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Name: "
msgstr "Nome:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:105
msgid "Search Flows"
msgstr "Pesquisar Fluxos"

#: modules/flow/classes/class-cartflows-flow-post-type.php:106
msgid "All Flows"
msgstr "Todos os Fluxos"

#: modules/flow/classes/class-cartflows-flow-post-type.php:107
msgid "Edit Flow"
msgstr "Editar Fluxo"

#: modules/flow/classes/class-cartflows-flow-post-type.php:108
msgid "View Flow"
msgstr "Ver Fluxo"

#: modules/flow/classes/class-cartflows-flow-post-type.php:109
#: modules/flow/classes/class-cartflows-flow-post-type.php:111
#: modules/flow/classes/class-cartflows-step-post-type.php:176
#: modules/flow/classes/class-cartflows-step-post-type.php:178
#: admin-core/assets/build/settings-app.js:25
msgid "Add New"
msgstr "Adicionar Novo"

#: modules/flow/classes/class-cartflows-flow-post-type.php:110
msgid "Update Flow"
msgstr "Atualizar Fluxo"

#: modules/flow/classes/class-cartflows-flow-post-type.php:112
msgid "New Flow Name"
msgstr "Novo Nome do Fluxo"

#: modules/flow/classes/class-cartflows-flow-post-type.php:194
#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Upgrade to CartFlows Pro"
msgstr "Atualize para o CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:213
msgid "Slug"
msgstr "Lesma"

#: modules/flow/classes/class-cartflows-flow-post-type.php:332
#: modules/flow/classes/class-cartflows-flow-post-type.php:338
#: modules/flow/classes/class-cartflows-step-post-type.php:410
#: modules/flow/classes/class-cartflows-step-post-type.php:416
#. translators: %s: singular custom post type name
msgid "%s updated."
msgstr "%s atualizado."

#: modules/flow/classes/class-cartflows-flow-post-type.php:334
#: modules/flow/classes/class-cartflows-step-post-type.php:412
#. translators: %s: singular custom post type name
msgid "Custom %s updated."
msgstr "%s personalizado atualizado."

#: modules/flow/classes/class-cartflows-flow-post-type.php:336
#: modules/flow/classes/class-cartflows-step-post-type.php:414
#. translators: %s: singular custom post type name
msgid "Custom %s deleted."
msgstr "%s personalizado excluído."

#: modules/flow/classes/class-cartflows-flow-post-type.php:340
#: modules/flow/classes/class-cartflows-step-post-type.php:418
#. translators: %1$s: singular custom post type name ,%2$s: date and time of the revision
msgid "%1$s restored to revision from %2$s"
msgstr "%1$s restaurado para a revisão de %2$s"

#: modules/flow/classes/class-cartflows-flow-post-type.php:342
#: modules/flow/classes/class-cartflows-step-post-type.php:420
#. translators: %s: singular custom post type name
msgid "%s published."
msgstr "%s publicado."

#: modules/flow/classes/class-cartflows-flow-post-type.php:344
#: modules/flow/classes/class-cartflows-step-post-type.php:422
#. translators: %s: singular custom post type name
msgid "%s saved."
msgstr "%s salvo."

#: modules/flow/classes/class-cartflows-flow-post-type.php:346
#: modules/flow/classes/class-cartflows-step-post-type.php:424
#. translators: %s: singular custom post type name
msgid "%s submitted."
msgstr "%s enviado."

#: modules/flow/classes/class-cartflows-flow-post-type.php:348
#: modules/flow/classes/class-cartflows-step-post-type.php:426
#. translators: %s: singular custom post type name
msgid "%s scheduled for."
msgstr "%s agendado para."

#: modules/flow/classes/class-cartflows-flow-post-type.php:350
#: modules/flow/classes/class-cartflows-step-post-type.php:428
#. translators: %s: singular custom post type name
msgid "%s draft updated."
msgstr "Rascunho de %s atualizado."

#: modules/flow/classes/class-cartflows-step-meta-base.php:58
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:118
msgid "Design"
msgstr "Design"

#: modules/flow/classes/class-cartflows-step-meta-base.php:82
msgid "Custom script lets you add your own custom script on front end of this flow page."
msgstr "O script personalizado permite que você adicione seu próprio script personalizado na interface desta página de fluxo."

#: modules/flow/classes/class-cartflows-step-post-type.php:172
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Search Steps"
msgstr "Etapas de Pesquisa"

#: modules/flow/classes/class-cartflows-step-post-type.php:173
msgid "All Steps"
msgstr "Todos os passos"

#: modules/flow/classes/class-cartflows-step-post-type.php:174
msgid "Edit Step"
msgstr "Editar Etapa"

#: modules/flow/classes/class-cartflows-step-post-type.php:175
msgid "View Step"
msgstr "Ver Etapa"

#: modules/flow/classes/class-cartflows-step-post-type.php:177
msgid "Update Step"
msgstr "Etapa de Atualização"

#: modules/flow/classes/class-cartflows-step-post-type.php:179
msgid "New Step Name"
msgstr "Novo Nome do Passo"

#: modules/flow/classes/class-cartflows-step-post-type.php:220
msgid "Step Type"
msgstr "Tipo de Etapa"

#: modules/flow/classes/class-cartflows-step-post-type.php:230
msgid "Step Flow"
msgstr "Fluxo de Etapas"

#: modules/flow/classes/class-cartflows-step-post-type.php:255
msgid "Optin"
msgstr "Optin"

#: modules/flow/classes/class-cartflows-step-post-type.php:276
msgid "Upsell"
msgstr "Venda adicional"

#: modules/flow/classes/class-cartflows-step-post-type.php:283
msgid "Downsell"
msgstr "Venda adicional"

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:146
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:201
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:311
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:86
msgid "Permission denied."
msgstr "Permissão negada."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:225
msgid "No product is selected. Please select products from the checkout meta settings to continue."
msgstr "Nenhum produto foi selecionado. Por favor, selecione produtos nas configurações de meta de checkout para continuar."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:328
#: modules/optin/classes/class-cartflows-optin-markup.php:321
msgid "No product is selected. Please select a Simple, Virtual and Free product from the meta settings."
msgstr "Nenhum produto está selecionado. Por favor, selecione um produto Simples, Virtual e Gratuito nas configurações de meta."

#: modules/landing/classes/class-cartflows-landing-meta-data.php:98
msgid "Next Step Link"
msgstr "Link para o Próximo Passo"

#: modules/optin/classes/class-cartflows-optin-markup.php:261
msgid "Please place shortcode on Optin step-type only."
msgstr "Por favor, coloque o shortcode apenas no tipo de etapa Optin."

#: modules/optin/classes/class-cartflows-optin-markup.php:338
msgid "Please update the selected product's price to zero (0)."
msgstr "Por favor, atualize o preço do produto selecionado para zero (0)."

#: modules/optin/classes/class-cartflows-optin-markup.php:347
#: modules/optin/classes/class-cartflows-optin-markup.php:351
msgid "Please select a Simple, Virtual and Free product."
msgstr "Por favor, selecione um produto Simples, Virtual e Gratuito."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:76
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enable Custom Field Editor"
msgstr "Ativar Editor de Campo Personalizado"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a Product"
msgstr "Procurar um Produto"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:229
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows Optin step please %1$sClick here.%2$s"
msgstr "Para mais informações sobre o passo de Optin do CartFlows, por favor %1$sclique aqui.%2$s"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:262
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:101
msgid "Add this shortcode to your optin page"
msgstr "Adicione este shortcode à sua página de inscrição"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:270
msgid "Global Settings"
msgstr "Configurações Globais"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:328
#: modules/optin/classes/class-cartflows-optin-meta-data.php:432
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size"
msgstr "Tamanho"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:361
#: modules/optin/classes/class-cartflows-optin-meta-data.php:465
msgid "Top Bottom Spacing"
msgstr "Espaçamento Superior Inferior"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:368
#: modules/optin/classes/class-cartflows-optin-meta-data.php:472
msgid "Left Right Spacing"
msgstr "Espaçamento à Esquerda e à Direita"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:382
msgid "Text / Placeholder Color"
msgstr "Cor do Texto / Placeholder"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:412
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Size"
msgstr "Tamanho da Fonte"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:479
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Position"
msgstr "Posição"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:584
msgid "Optin Settings"
msgstr "Configurações de Opt-in"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:598
msgid "Pass Fields as URL Parameters"
msgstr "Passar Campos como Parâmetros de URL"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:601
msgid "You can pass specific fields from the form to next step as URL query parameters."
msgstr "Você pode passar campos específicos do formulário para a próxima etapa como parâmetros de consulta de URL."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:606
msgid "Enter form field"
msgstr "Insira o campo do formulário"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:609
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:492
msgid "Enter comma seprated field name. E.g. first_name, last_name"
msgstr "Insira o nome do campo separado por vírgulas. Ex.: primeiro_nome, último_nome"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:610
msgid "Fields to pass, separated by commas"
msgstr "Campos a passar, separados por vírgulas"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:612
#. translators: %s: link
msgid "You can pass field value as a URL parameter to the next step. %1$sLearn More >>%2$s"
msgstr "Você pode passar o valor do campo como um parâmetro de URL para a próxima etapa. %1$sSaiba Mais >>%2$s"

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:180
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:183
msgid "We can't seem to find an order for you."
msgstr "Parece que não conseguimos encontrar um pedido para você."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:272
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:662
msgid "No completed or processing order found to show the order details form demo."
msgstr "Nenhum pedido concluído ou em processamento encontrado para mostrar o formulário de detalhes do pedido demo."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:280
msgid "Order not found. You cannot access this page directly."
msgstr "Pedido não encontrado. Você não pode acessar esta página diretamente."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:673
msgid "Order Details Not Found."
msgstr "Detalhes do Pedido Não Encontrados."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:675
msgid "Return to Shopping"
msgstr "Voltar às Compras"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:212
msgid "Font Size (In px)"
msgstr "Tamanho da Fonte (Em px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:221
msgid "Advanced Options"
msgstr "Opções Avançadas"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:227
msgid "Enable Advanced Options"
msgstr "Ativar Opções Avançadas"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:234
msgid "Container Width (In px)"
msgstr "Largura do Contêiner (Em px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:331
msgid "Background color of left side column for Instant Thank You Layout."
msgstr "Cor de fundo da coluna do lado esquerdo para o Layout de Agradecimento Instantâneo."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:347
msgid "Background color of right side column for Instant Thank You Layout."
msgstr "Cor de fundo da coluna do lado direito para o Layout de Agradecimento Instantâneo."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:417
msgid "Enable Order Overview"
msgstr "Ativar Visão Geral do Pedido"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:424
msgid "Enable Order Details"
msgstr "Ativar Detalhes do Pedido"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:431
msgid "Enable Billing Details"
msgstr "Ativar Detalhes de Cobrança"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:438
msgid "Enable Shipping Details"
msgstr "Ativar Detalhes de Envio"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:481
msgid "Thank You Page Text"
msgstr "Texto da Página de Agradecimento"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:489
msgid "Redirect After Purchase"
msgstr "Redirecionar após a compra"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:497
msgid "Redirect Link"
msgstr "Redirecionar Link"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:500
msgid "https://"
msgstr "https://"

#: modules/thankyou/templates/instant-thankyou-order-details.php:41
#. Translators: First name.
msgid "Thank you, %s!"
msgstr "Obrigado, %s!"

#: modules/thankyou/templates/instant-thankyou-order-details.php:58
msgid "Order Updates"
msgstr "Atualizações de Pedido"

#: modules/thankyou/templates/instant-thankyou-order-details.php:59
msgid "You will receive order and shipping updates via email."
msgstr "Você receberá atualizações de pedidos e envios por e-mail."

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
msgid "Address"
msgstr "Endereço"

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Billing"
msgstr "Faturamento"

#: modules/thankyou/templates/instant-thankyou-order-details.php:155
msgid "Continue Shopping"
msgstr "Continuar comprando"

#: modules/thankyou/templates/instant-thankyou.php:37
msgid ""
"Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please "
"attempt your purchase again."
msgstr ""
"Infelizmente, seu pedido não pode ser processado, pois o banco/mercador de origem recusou sua transação. Por favor, "
"tente sua compra novamente."

#: modules/thankyou/templates/instant-thankyou.php:41
msgid "Pay"
msgstr "Pagar"

#: modules/thankyou/templates/instant-thankyou.php:43
msgid "My account"
msgstr "Minha conta"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:137
msgid "Select the Flow"
msgstr "Selecione o Fluxo"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:147
msgid "Add to Cart text"
msgstr "Texto Adicionar ao Carrinho"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:149
msgid "Add to cart"
msgstr "Adicionar ao carrinho"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:154
#. translators: %1$s,%2$s HTML content
msgid ""
"If you want to start the flow from the product page, select the appropriate flow & button text field if required. Refer "
"%1$sthis article%2$s for more information."
msgstr ""
"Se você quiser iniciar o fluxo a partir da página do produto, selecione o campo de texto do botão e fluxo apropriado, "
"se necessário. Consulte %1$seste artigo%2$s para mais informações."

#: wizard/ajax/wizard.php:207
msgid "Please enter your email ID."
msgstr "Por favor, insira seu ID de e-mail."

#: wizard/ajax/wizard.php:262
msgid "Oops! Something went wrong. Please refresh the page and try again."
msgstr "Ops! Algo deu errado. Por favor, atualize a página e tente novamente."

#: wizard/ajax/wizard.php:363
msgid "Please select any of the page builder to display the ready templates."
msgstr "Por favor, selecione qualquer um dos construtores de página para exibir os modelos prontos."

#: wizard/ajax/wizard.php:502
msgid "No flow ID found. Please select atleast one flow to import."
msgstr "Nenhum ID de fluxo encontrado. Por favor, selecione pelo menos um fluxo para importar."

#: admin-core/ajax/importer.php:893
#: wizard/ajax/wizard.php:516
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$sarticle%3$s."
msgstr ""
"Erro de tempo limite da solicitação. Por favor, verifique se o firewall ou algum plugin de segurança está bloqueando as "
"solicitações HTTP/HTTPS de saída para templates.cartflows.com ou não. %1$sPara resolver este problema, por favor, "
"consulte este %2$sartigo%3$s."

#: wizard/ajax/wizard.php:539
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import this template, CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "Para importar este modelo, é necessário o CartFlows Pro! %1$sAtualize para o CartFlows Pro%2$s"

#: wizard/ajax/wizard.php:540
#: wizard/ajax/wizard.php:542
msgid "CartFlows Pro Required"
msgstr "CartFlows Pro Necessário"

#: wizard/ajax/wizard.php:546
msgid "Invalid License Key"
msgstr "Chave de Licença Inválida"

#: wizard/ajax/wizard.php:548
#. translators: %1$s: link html start, %2$s: link html end
msgid "No valid license key found! %1$sActivate license%2$s"
msgstr "Nenhuma chave de licença válida encontrada! %1$sAtivar licença%2$s"

#: wizard/inc/wizard-core.php:174
msgid "Thanks for installing and using CartFlows!"
msgstr "Obrigado por instalar e usar o CartFlows!"

#: wizard/inc/wizard-core.php:175
msgid "It is easy to use the CartFlows. Please use the setup wizard to quick start setup."
msgstr "É fácil usar o CartFlows. Por favor, use o assistente de configuração para iniciar rapidamente a configuração."

#: wizard/inc/wizard-core.php:177
msgid "Start Wizard"
msgstr "Iniciar Assistente"

#: wizard/inc/wizard-core.php:178
msgid "Skip Setup"
msgstr "Pular Configuração"

#: wizard/inc/wizard-core.php:394
msgid "Oops!! Unexpected error occoured"
msgstr "Ops!! Ocorreu um erro inesperado"

#: wizard/inc/wizard-core.php:395
msgid "Import template API call failed. Please reload the page and try again!"
msgstr "A chamada da API do modelo de importação falhou. Por favor, recarregue a página e tente novamente!"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Settings Saved"
msgstr "Configurações salvas"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:53
msgid "Saving…"
msgstr "Salvando…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:42
msgid "Save Settings"
msgstr "Salvar configurações"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Regular Price of the product"
msgstr "Preço regular do produto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Price after discount."
msgstr "Preço após desconto."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Product Name"
msgstr "Nome do Produto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{product_name}} and {{quantity}} to dynamically fetch respective product details."
msgstr "Use {{product_name}} e {{quantity}} para buscar dinamicamente os detalhes respectivos do produto."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Subtext"
msgstr "Subtexto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{quantity}}, {{discount_value}}, {{discount_percent}} to dynamically fetch respective product details."
msgstr "Use {{quantity}}, {{discount_value}}, {{discount_percent}} para buscar dinamicamente os respectivos detalhes do produto."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Enable Highlight"
msgstr "Ativar Destaque"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Highlight Text"
msgstr "Destacar Texto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Create New Product"
msgstr "Criar Novo Produto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add"
msgstr "Adicionar"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Once you have selected products, they will be displayed here."
msgstr "Depois de selecionar os produtos, eles serão exibidos aqui."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Items"
msgstr "Itens"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Quantity"
msgstr "Quantidade"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Discount"
msgstr "Desconto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Adding…"
msgstr "Adicionando…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Please search and select at-lease one product to add."
msgstr "Por favor, procure e selecione pelo menos um produto para adicionar."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
#: wizard/assets/build/wizard-app.js:1
msgid "Reset"
msgstr "Redefinir"

#: admin-core/assets/build/editor-app.js:1
msgid "Image Preview"
msgstr "Pré-visualização da imagem"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "Upload a file"
msgstr "Carregar um arquivo"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "or drag and drop"
msgstr "ou arraste e solte"

#: admin-core/assets/build/editor-app.js:1
msgid "PNG, JPG, GIF up to 10MB"
msgstr "PNG, JPG, GIF até 10MB"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Select Dates"
msgstr "Selecionar Datas"

#: admin-core/assets/build/editor-app.js:4
#: admin-core/assets/build/settings-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Pro %2$s to use %3$s feature."
msgstr "Por favor, atualize para o %1$s CartFlows Pro %2$s para usar o recurso %3$s."

#: admin-core/assets/build/editor-app.js:7
#: admin-core/assets/build/settings-app.js:7
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Higher Plan %2$s to use %3$s feature."
msgstr "Por favor, faça o upgrade para o %1$s Plano Superior do CartFlows %2$s para usar o recurso %3$s."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
#. translators: %s is replaced with feature name
msgid "Please upgrade to the CartFlows Higher Plan to use the %s feature."
msgstr "Por favor, faça upgrade para o Plano Superior do CartFlows para usar o recurso %s."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Role"
msgstr "Função"

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Access"
msgstr "Acesso"

#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/settings-app.js:11
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the user role management please %1$sClick here.%2$s"
msgstr "Para mais informações sobre a gestão de funções de usuário, por favor %1$sClique aqui.%2$s"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
#. translators: %1$s is the selected product of CartFlows, %2$s is the selected version of CartFlows.
msgid "Are you sure you want to rollback to %1$s v%2$s?"
msgstr "Tem certeza de que deseja reverter para %1$s v%2$s?"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback"
msgstr "Reverter"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:14
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Cancel"
msgstr "Cancelar"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid ""
"Experiencing an issue with the current version of CartFlows? Roll back to a previous version to help troubleshoot the "
"problem."
msgstr ""
"Está a ter um problema com a versão atual do CartFlows? Reverter para uma versão anterior para ajudar a resolver o "
"problema."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Now"
msgstr "Regenerar agora"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:19
msgid "Reset Permalinks Settings"
msgstr "Redefinir Configurações de Links Permanentes"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate License"
msgstr "Ativar Licença"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Deactivate License"
msgstr "Desativar Licença"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Please enter a valid license key!"
msgstr "Por favor, insira uma chave de licença válida!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
#: wizard/assets/build/wizard-app.js:4
msgid "Processing"
msgstr "Processando"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unknown error occurred while activating the license."
msgstr "Ocorreu um erro desconhecido ao ativar a licença."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Facebook Pixel"
msgstr "Pixel do Facebook"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Analytics Pixel"
msgstr "Pixel do Google Analytics"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Ads Pixel"
msgstr "Pixel do Google Ads"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Tiktok Pixel"
msgstr "Tiktok Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Pinterest Tag"
msgstr "Tag do Pinterest"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Snapchat Pixel"
msgstr "Snapchat Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Auto Address"
msgstr "Endereço Automático do Google"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Inline CSS"
msgstr "Regenerar CSS Inline"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid " Regenerating…."
msgstr "Regenerando…."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerated"
msgstr "Regenerado"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"If you are using the CartFlows Shortcode and using the Design Settings, then this option will regenerate the steps "
"inline CSS. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Se você estiver usando o Shortcode do CartFlows e as Configurações de Design, esta opção irá regenerar o CSS inline dos "
"passos. Para saber mais, %1$1s Clique aqui %2$2s."

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Updating"
msgstr "Atualizando"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Permalinks reset successfully"
msgstr "Links permanentes redefinidos com sucesso"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the CartFlows Permalink settings please %1$sClick here.%2$s"
msgstr "Para mais informações sobre as configurações de Permalink do CartFlows, por favor %1$sClique aqui.%2$s"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Oops! You don't have access to this page."
msgstr "Ops! Você não tem acesso a esta página."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You don't have permission to access this page. Please reach out to your admin for help."
msgstr "Você não tem permissão para acessar esta página. Por favor, entre em contato com o seu administrador para obter ajuda."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Back to Dashboard"
msgstr "Voltar para o Painel"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Integrations"
msgstr "Integrações"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "User Role Manager"
msgstr "Gerenciador de Funções do Usuário"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Permalink"
msgstr "Link permanente"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Version Control"
msgstr "Controle de Versão"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Order"
msgstr "Pedido"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "License"
msgstr "Licença"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Sync Knowledge Base"
msgstr "Sincronizar Base de Conhecimento"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Knowledge Base"
msgstr "Base de Conhecimento"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Search knowledge base"
msgstr "Pesquisar na base de conhecimento"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "No Docs Founds"
msgstr "Nenhum documento encontrado"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Please try syncing the docs library"
msgstr "Por favor, tente sincronizar a biblioteca de documentos"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Syncing…"
msgstr "Sincronizando…"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Synced. Reloading.."
msgstr "Sincronizado. Recarregando.."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Need Help?"
msgstr "Precisa de ajuda?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "We aim to answer all priority support requests within 2-3 hours."
msgstr "Nosso objetivo é responder a todas as solicitações de suporte prioritário dentro de 2-3 horas."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Get Support"
msgstr "Obter Suporte"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "All Documentation"
msgstr "Toda a documentação"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse documentation, reference material, and tutorials for CartFlows."
msgstr "Navegue pela documentação, material de referência e tutoriais para CartFlows."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "View documentation"
msgstr "Ver documentação"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Videos"
msgstr "Vídeos"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse tutorial videos on our YouTube channel."
msgstr "Navegue por vídeos tutoriais no nosso canal do YouTube."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Youtube Channel"
msgstr "Canal do Youtube"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Support"
msgstr "Suporte"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "What's New?"
msgstr "O que há de novo?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Unlicensed"
msgstr "Sem licença"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Licensed"
msgstr "Licenciado"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Open Global Settings"
msgstr "Abrir Configurações Globais"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Tutorial Videos"
msgstr "Vídeos Tutoriais"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:35
msgid "More Options"
msgstr "Mais opções"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:35
msgid "Control"
msgstr "Controle"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
#. translators: %d is replaced with the count
msgid "Variation-%d"
msgstr "Variação-%d"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "No Product Assigned"
msgstr "Nenhum Produto Atribuído"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Store Checkout - Remove selected checkout product"
msgstr "Finalização da Compra - Remover produto selecionado do carrinho"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "PRO"
msgstr "PRO"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Accepted"
msgstr "Oferta Aceita"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Rejected"
msgstr "Oferta Rejeitada"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Invalid Position"
msgstr "Posição Inválida"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Views"
msgstr "Visualizações"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Conversions"
msgstr "Conversões"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Revenue"
msgstr "Receita"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "Upgrade to Pro"
msgstr "Atualizar para Pro"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:39
#: admin-core/assets/build/settings-app.js:42
#. translators: %s: step slug
msgid "%s Step"
msgstr "Etapa %s"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid "Step Editing is Disabled"
msgstr "A edição de etapas está desativada"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Open Settings"
msgstr "Abrir Configurações"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Duplicate Step"
msgstr "Etapa Duplicada"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to duplicate this step? Are you sure?"
msgstr "Você quer duplicar esta etapa? Você tem certeza?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Delete Step"
msgstr "Excluir Etapa"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to delete this step? Are you sure?"
msgstr "Você quer excluir esta etapa? Tem certeza?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Declare Winner"
msgstr "Declarar Vencedor"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to declare this step as winner? Are you sure?"
msgstr "Você quer declarar esta etapa como vencedora? Tem certeza?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Archive Step"
msgstr "Etapa de Arquivo"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to archive this step? Are you sure?"
msgstr "Você quer arquivar esta etapa? Você tem certeza?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Saving.."
msgstr "Salvando.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:53
msgid "Saved"
msgstr "Salvo"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading.."
msgstr "Recarregando.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Testing"
msgstr "Teste de Divisão"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stop Split Test"
msgstr "Parar Teste A/B"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Start Split Test"
msgstr "Iniciar Teste A/B"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test"
msgstr "Teste A/B"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stopping…"
msgstr "Parando…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Starting…"
msgstr "Iniciando…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading…"
msgstr "Recarregando…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Settings"
msgstr "Configurações de Teste A/B"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Restore Archived Variation"
msgstr "Restaurar Variação Arquivada"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to restore this archived variation? Are you sure?"
msgstr "Você quer restaurar esta variação arquivada? Tem certeza?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:44
msgid "Trash Archived Variation"
msgstr "Variação Arquivada do Lixo"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will trash this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Esta ação irá descartar permanentemente esta variação arquivada e seus dados analíticos. %1$s Você deseja excluir esta "
"variação arquivada?"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
msgid "Hide Archived Variation"
msgstr "Ocultar Variação Arquivada"

#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:44
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will hide this archived variation from the list of steps, but its analytics will be visible. %1$s Do you "
"want to hide this archived variation?"
msgstr ""
"Esta ação ocultará esta variação arquivada da lista de etapas, mas suas análises estarão visíveis. %1$s Você deseja "
"ocultar esta variação arquivada?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will delete this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Esta ação excluirá permanentemente esta variação arquivada e seus dados analíticos. %1$s Deseja excluir esta variação "
"arquivada?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Deleted On: "
msgstr "Excluído em:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived On: "
msgstr "Arquivado em:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived Steps"
msgstr "Passos Arquivados"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Step Name"
msgstr "Editar Nome do Passo"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "Save"
msgstr "Salvar"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Update Template"
msgstr "Atualizar Modelo"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Changing the template will permanently delete the current design in this step. Would you still like to proceed?"
msgstr "Alterar o modelo excluirá permanentemente o design atual nesta etapa. Você ainda gostaria de continuar?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Change Template"
msgstr "Alterar Modelo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If you are using shortcodes, enable this design settings to apply styles."
msgstr "Se você estiver usando shortcodes, ative estas configurações de design para aplicar estilos."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Design Settings"
msgstr "Ativar Configurações de Design"

#: admin-core/assets/build/editor-app.js:15
#: admin-core/assets/build/settings-app.js:15
#. translators: %s is replaced with plugin name
msgid "Activate %s"
msgstr "Ativar %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
#. translators: %s is replaced with plugin name
msgid "Activating %s"
msgstr "Ativando %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Activated!"
msgstr "Ativado com sucesso!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Failed! Activation!"
msgstr "Falhou! Ativação!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Upgrade to Cartflows Pro"
msgstr "Atualize para o Cartflows Pro"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Activate the License"
msgstr "Ativar a Licença"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Step Type: "
msgstr "Tipo de Etapa:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "License is required!"
msgstr "É necessária uma licença!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Activate the license to modify this offer step's settings"
msgstr "Ative a licença para modificar as configurações desta etapa da oferta"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product Selected"
msgstr "Nenhum produto selecionado"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you select the product, they will be displayed here."
msgstr "Assim que você selecionar o produto, eles serão exibidos aqui."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping Rate"
msgstr "Taxa de Envio"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you have add product, it will be displayed here."
msgstr "Assim que você adicionar o produto, ele será exibido aqui."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Coupon will apply on checkout page"
msgstr "O cupom será aplicado na página de finalização da compra"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "AND"
msgstr "E"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the conditional order bump feature."
msgstr "Por favor, atualize o CartFlows Pro para a versão mais recente para usar o recurso de oferta adicional condicional."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable conditional order bump "
msgstr "Ativar aumento de pedido condicional"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "By enabling this option, you can create the conditions to display the order bump."
msgstr "Ao ativar esta opção, você pode criar as condições para exibir o aumento de pedido."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show this order bump if following conditions are true"
msgstr "Mostre este aumento de pedido se as seguintes condições forem verdadeiras"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Condition"
msgstr "Adicionar Condição"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:33
#: admin-core/assets/build/settings-app.js:48
msgid "OR"
msgstr "OU"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Conditions Group"
msgstr "Adicionar Grupo de Condições"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Bump Product Image"
msgstr "Imagem do Produto de Venda Adicional"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Styles"
msgstr "Estilos"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Conditions"
msgstr "Condições"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Save Changes"
msgstr "Salvar alterações"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "You have made changes. Do you want to save the changes?"
msgstr "Você fez alterações. Deseja salvar as alterações?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product"
msgstr "Sem produto"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Duplicate Order Bump"
msgstr "Duplicar Aumento de Pedido"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Order Bump"
msgstr "Editar Bump de Pedido"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Trash Order Bump"
msgstr "Ordem de Lixo Adicional"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Do you really want to trash this order bump permanently?"
msgstr "Você realmente deseja descartar este adicional de pedido permanentemente?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Delete Order Bump"
msgstr "Excluir Order Bump"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
msgid "Status"
msgstr "Status"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Actions"
msgstr "Ações"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Create an order bump."
msgstr "Crie um aumento de pedido."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enter order bump name"
msgstr "Insira o nome do aumento de pedido"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please enter the order bump title"
msgstr "Por favor, insira o título do complemento de pedido"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Preview"
msgstr "Pré-visualização"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "View in Full Screen"
msgstr "Ver em Tela Cheia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Exit Full Screen"
msgstr "Sair da Tela Cheia"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Submitted"
msgstr "Pedido Enviado"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Special Offer"
msgstr "Oferta Especial"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Receipt"
msgstr "Recibo de Pedido"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Checkout Offer"
msgstr "Ativar Oferta de Checkout"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Dynamic Conditions"
msgstr "Condições Dinâmicas"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the dynamic offers feature."
msgstr "Por favor, atualize o CartFlows Pro para a versão mais recente para usar o recurso de ofertas dinâmicas."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Dynamic Offers"
msgstr "Ativar Ofertas Dinâmicas"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Redirect to: "
msgstr "Redirecionar para:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for step…"
msgstr "Procurar por etapa…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If the following conditions are true"
msgstr "Se as seguintes condições forem verdadeiras"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Dynamic Offer"
msgstr "Adicionar Oferta Dinâmica"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for default step…"
msgstr "Procurar pelo passo padrão…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If all of the above conditions failed."
msgstr "Se todas as condições acima falharem."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label"
msgstr "Etiqueta"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "ID"
msgstr "ID"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable"
msgstr "Habilitar"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add New Field"
msgstr "Adicionar Novo Campo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Date & Time"
msgstr "Data e Hora"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Custom Field"
msgstr "Adicionar Campo Personalizado"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "TextArea"
msgstr "Área de Texto"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Number"
msgstr "Número"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Checkbox"
msgstr "Caixa de seleção"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Radio"
msgstr "Rádio"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Select"
msgstr "Selecionar"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Hidden"
msgstr "Oculto"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add To"
msgstr "Adicionar a"

#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
#. translators: %$s is replaced with the HTML tag
msgid "Label %1$s*%2$s"
msgstr "Rótulo %1$s*%2$s"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#. translators: %$s is replaced with the HTML tag
msgid ""
"Field value will store in this meta key. Add field id without prefix like \"billing_\" or \"shipping_\". %s Use \"_\" "
"instead of spaces."
msgstr ""
"O valor do campo será armazenado nesta chave meta. Adicione o id do campo sem o prefixo como \"billing_\" ou "
"\"shipping_\". %s Use \"_\" em vez de espaços."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min Value"
msgstr "Valor Mínimo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max Value"
msgstr "Valor Máximo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enter your options separated by (|)."
msgstr "Insira suas opções separadas por (|)."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "UnChecked"
msgstr "Desmarcado"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Input Type"
msgstr "Tipo de Entrada do Campo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:50
msgid "Date"
msgstr "Data"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Time"
msgstr "Tempo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min "
msgstr "Min"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max "
msgstr "Max"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Show in Email"
msgstr "Mostrar no Email"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Billing Fields"
msgstr "Campos de Faturamento"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Shipping Fields"
msgstr "Campos de Envio"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Delete Field"
msgstr "Excluir Campo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Are you really want to delete field?"
msgstr "Tem certeza de que deseja excluir o campo?"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Editor"
msgstr "Editor de Campo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Label is required field"
msgstr "O rótulo é um campo obrigatório"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "WooCommerce is Required!"
msgstr "WooCommerce é necessário!"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
#. translators: %s: step type
msgid "To modify the %s step options, please install and activate the WooCommerce plugin."
msgstr "Para modificar as opções do passo %s, por favor, instale e ative o plugin WooCommerce."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activating…"
msgstr "Ativando…"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activated"
msgstr "Ativado"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Funnel Settings"
msgstr "Configurações do Funil"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Save Setting"
msgstr "Salvar Configuração"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Disable Store Checkout"
msgstr "Desativar finalização de compra na loja"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Enable Store Checkout"
msgstr "Ativar finalização de compra na loja"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Edit Title"
msgstr "Editar Título"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Publish or Draft the Funnel"
msgstr "Publicar ou Rascunhar o Funil"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Publish"
msgstr "Publicar"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to publish this funnel?"
msgstr "Você realmente deseja publicar este funil?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Draft Funnel"
msgstr "Funil de Rascunho"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to draft this funnel?"
msgstr "Você realmente quer elaborar este funil?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Unique Visits"
msgstr "Visitas Únicas"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Conversion Rate"
msgstr "Taxa de Conversão"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total number of orders."
msgstr "Número total de pedidos."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all orders."
msgstr "Total geral de todos os pedidos."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Average total of every order."
msgstr "Média total de cada pedido."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all order bumps."
msgstr "Total geral de todos os acréscimos de pedidos."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Export Flow"
msgstr "Fluxo de Exportação"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "View Funnel"
msgstr "Ver Funil"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Open Funnel Settings"
msgstr "Abrir Configurações do Funil"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Delete Store Checkout"
msgstr "Excluir Checkout da Loja"

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
#. translators: %s new line break
msgid "Do you really want to delete store checkout?%1$1sNOTE: This action cannot be reversed."
msgstr "Você realmente deseja excluir o checkout da loja?%1$1sNOTA: Esta ação não pode ser desfeita."

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "archived_date"
msgstr "data_arquivada"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
#. translators: %1$s: html tag, %2$s: html tag
msgid "%1$sNote:%2$s The orders which are placed by the admins are not considered while calculating the analytics."
msgstr "%1$sNota:%2$s As encomendas feitas pelos administradores não são consideradas ao calcular as análises."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Reset Analytics"
msgstr "Redefinir Análises"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Are you really want to reset funnel analytics?"
msgstr "Você realmente quer redefinir a análise do funil?"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Resetting"
msgstr "Redefinindo"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Automation for"
msgstr "Automação para"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Create a distraction free, high-converting checkout experience without needing a page builder."
msgstr "Crie uma experiência de checkout sem distrações e de alta conversão sem precisar de um construtor de páginas."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Funnel Steps"
msgstr "Etapas do Funil"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add New Step"
msgstr "Adicionar Novo Passo"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "No Steps Added."
msgstr "Nenhum passo adicionado."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Seems like there are no steps created or added in this flow"
msgstr "Parece que não há etapas criadas ou adicionadas neste fluxo"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add new step"
msgstr "Adicionar novo passo"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Select Step Type"
msgstr "Selecionar Tipo de Etapa"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Activating WooCommerce.."
msgstr "Ativando o WooCommerce.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installing WooCommerce.."
msgstr "Instalando o WooCommerce.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Installed!"
msgstr "Instalado com sucesso!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installation Failed!"
msgstr "Instalação falhou!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Step"
msgstr "Etapa de Importação"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
msgid "Add multiple steps to your flows today with an upgraded CartFlows plan."
msgstr "Adicione várias etapas aos seus fluxos hoje com um plano CartFlows atualizado."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
msgid "Get CartFlows Higher Plan"
msgstr "Obtenha o Plano Superior do CartFlows"

#: admin-core/assets/build/editor-app.js:44
#: admin-core/assets/build/editor-app.js:45
#: admin-core/assets/build/editor-app.js:54
#: admin-core/assets/build/editor-app.js:55
#: admin-core/assets/build/editor-app.js:59
#: admin-core/assets/build/editor-app.js:63
#: admin-core/assets/build/editor-app.js:64
#: admin-core/assets/build/settings-app.js:56
#: admin-core/assets/build/settings-app.js:57
#: admin-core/assets/build/settings-app.js:66
#: admin-core/assets/build/settings-app.js:67
#: admin-core/assets/build/settings-app.js:70
#: admin-core/assets/build/settings-app.js:74
#: admin-core/assets/build/settings-app.js:75
#. translators: %s is replaced with plugin name
msgid "Add multiple steps to your flows by activating %s."
msgstr "Adicione várias etapas aos seus fluxos ativando %s."

#: admin-core/assets/build/editor-app.js:46
#: admin-core/assets/build/editor-app.js:47
#: admin-core/assets/build/editor-app.js:56
#: admin-core/assets/build/editor-app.js:60
#: admin-core/assets/build/editor-app.js:65
#: admin-core/assets/build/settings-app.js:58
#: admin-core/assets/build/settings-app.js:59
#: admin-core/assets/build/settings-app.js:68
#: admin-core/assets/build/settings-app.js:71
#: admin-core/assets/build/settings-app.js:76
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating %2$s."
msgstr "Adicione o passo %1$s aos seus fluxos ativando %2$s."

#: admin-core/assets/build/editor-app.js:48
#: admin-core/assets/build/editor-app.js:49
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:60
#: admin-core/assets/build/settings-app.js:61
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click %s to your flows when you upgrade to our CartFlows Higher plan today."
msgstr ""
"Adicione %s de aumento de renda ilimitado com um clique aos seus fluxos quando você fizer upgrade para o nosso plano "
"CartFlows Higher hoje."

#: admin-core/assets/build/editor-app.js:50
#: admin-core/assets/build/settings-app.js:62
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flow by activating CartFlows license."
msgstr "Adicione o passo %1$s ao seu fluxo ativando a licença do CartFlows."

#: admin-core/assets/build/editor-app.js:51
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:63
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s."
msgstr "Acesse todos os nossos modelos profissionais ativando %s."

#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Access all of our pro templates when you upgrade your plan to CartFlows Pro today."
msgstr "Acesse todos os nossos modelos profissionais quando você atualizar seu plano para o CartFlows Pro hoje."

#: admin-core/inc/admin-menu.php:318
#: admin-core/inc/admin-menu.php:319
#: classes/class-cartflows-admin.php:250
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Get CartFlows Pro"
msgstr "Obtenha o CartFlows Pro"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating license."
msgstr "Adicione o passo %1$s aos seus fluxos ativando a licença."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:78
msgid "You need WooCommerce plugin installed and activated to import this step."
msgstr "Você precisa do plugin WooCommerce instalado e ativado para importar esta etapa."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Imported! Redirecting…"
msgstr "Importado! Redirecionando…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid " Please sync the library and try importing the template again."
msgstr "Por favor, sincronize a biblioteca e tente importar o modelo novamente."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Failed! Try again."
msgstr "Falha na importação! Tente novamente."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create Step"
msgstr "Criar Etapa"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Creating Step.."
msgstr "Criando etapa.."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Step Created! Redirecting…"
msgstr "Etapa Criada! Redirecionando…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
msgid "Failed to Create Step!"
msgstr "Falha ao criar etapa!"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate license for adding more steps and other features."
msgstr "Ative a licença para adicionar mais etapas e outros recursos."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Close the window"
msgstr "Feche a janela"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Name Your Step"
msgstr "Nomeie Seu Passo"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Step Name"
msgstr "Nome da Etapa"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Enter Step Name"
msgstr "Insira o Nome da Etapa"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Learn How"
msgstr "Aprenda Como"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create from Scratch"
msgstr "Criar do Zero"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Step thumbnail image"
msgstr "Imagem em miniatura do passo"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Import"
msgstr "Importar"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
#: wizard/assets/build/wizard-app.js:1
msgid "Back"
msgstr "Voltar"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/settings-app.js:34
msgid "Sync Library"
msgstr "Sincronizar Biblioteca"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
#. translators: %d is replaced with the condition number
msgid "Importing page %d"
msgstr "Importando página %d"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Sync Complete"
msgstr "Sincronização Completa"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Syncing Library…"
msgstr "Sincronizando Biblioteca…"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Library Synced"
msgstr "Biblioteca Sincronizada"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Steps Library"
msgstr "Biblioteca de Passos"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:69
msgid "Get CartFlows Higher plan"
msgstr "Obtenha o plano superior do CartFlows"

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Activate license for adding more flows and other features."
msgstr "Ative a licença para adicionar mais fluxos e outros recursos."

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Importing Step.."
msgstr "Importando etapa.."

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Close"
msgstr "Fechar"

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Error"
msgstr "Erro"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click upsells to your flows by activating %s"
msgstr "Adicione upsells de um clique para aumentar a renda ilimitada aos seus fluxos ativando %s"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
msgid "Add unlimited income boosting one-click upsells to your flows when you upgrade to our CartFlows Plus or Pro plan today."
msgstr ""
"Adicione upsells de um clique para aumentar a renda ilimitada aos seus fluxos quando você fizer o upgrade para o nosso "
"plano CartFlows Plus ou Pro hoje."

#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:79
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s"
msgstr "Acesse todos os nossos modelos profissionais ativando %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:80
#. translators: %s is replaced with the step title
msgid "Templates for %s"
msgstr "Modelos para %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait…"
msgstr "Por favor, aguarde…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating funnel…"
msgstr "Por favor, aguarde. Duplicando funil…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Drafting funnel…"
msgstr "Por favor, aguarde. Elaborando funil…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting funnel…"
msgstr "Por favor, aguarde. Excluindo funil…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Restoring funnel…"
msgstr "Por favor, aguarde. Restaurando o funil…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Exporting…"
msgstr "Por favor, aguarde. Exportando…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating step…"
msgstr "Por favor, aguarde. Duplicando etapa…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting step…"
msgstr "Por favor, aguarde. Excluindo etapa…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Creating variation…"
msgstr "Por favor, aguarde. Criando variação…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Archiving variation…"
msgstr "Por favor, aguarde. Arquivando variação…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Declaring winner…"
msgstr "Por favor, aguarde. Declarando o vencedor…"

#: admin-core/assets/build/settings-app.js:23
msgid "Getting Started"
msgstr "Começando"

#: admin-core/assets/build/settings-app.js:23
msgid "Introduction to CartFlows"
msgstr "Introdução ao CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Modernizing WordPress eCommerce!"
msgstr "Modernizando o eCommerce do WordPress!"

#: admin-core/assets/build/settings-app.js:23
msgid "Create Your First Flow"
msgstr "Crie Seu Primeiro Fluxo"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Setup Wizard"
msgstr "Ir para o Assistente de Configuração"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:34
msgid "Import Funnel"
msgstr "Importar Funil"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Click for more info"
msgstr "Clique para mais informações"

#: admin-core/assets/build/settings-app.js:23
msgid "You need WooCommerce plugin installed and activated to import this funnel."
msgstr "Você precisa do plugin WooCommerce instalado e ativado para importar este funil."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates by activating CartFlows Pro."
msgstr "Acesse todos os nossos modelos profissionais ativando o CartFlows Pro."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates when you activate CartFlows Pro license."
msgstr "Acesse todos os nossos modelos profissionais quando você ativar a licença do CartFlows Pro."

#: admin-core/assets/build/settings-app.js:23
msgid "Importing Complete Funnel.."
msgstr "Importação do Funil Completa.."

#: admin-core/assets/build/settings-app.js:23
msgid "Design Your Funnel"
msgstr "Desenhe o Seu Funil"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Created! Redirecting…"
msgstr "Criado! Redirecionando…"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Failed to Create Flow!"
msgstr "Falha ao Criar Fluxo!"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To CartFlows Pro"
msgstr "Atualize para o CartFlows Pro"

#: admin-core/assets/build/settings-app.js:23
msgid "Name Your Funnel"
msgstr "Nomeie Seu Funil"

#: admin-core/assets/build/settings-app.js:23
msgid "Funnel Name"
msgstr "Nome do Funil"

#: admin-core/assets/build/settings-app.js:23
msgid "Enter Funnel Name"
msgstr "Insira o Nome do Funil"

#: admin-core/assets/build/settings-app.js:24
msgid "Welcome to CartFlows "
msgstr "Bem-vindo ao CartFlows"

#: admin-core/assets/build/settings-app.js:24
msgid "Sales funnel builder turns your WordPress website into an optimized selling machine."
msgstr "O construtor de funil de vendas transforma seu site WordPress em uma máquina de vendas otimizada."

#: admin-core/assets/build/settings-app.js:24
msgid "Create Your First Funnel"
msgstr "Crie Seu Primeiro Funil"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"A sales funnel is the sequence of steps a buyer takes to make a purchase. CartFlows helps optimize funnels to turn "
"visitors into customers."
msgstr ""
"Um funil de vendas é a sequência de etapas que um comprador segue para fazer uma compra. O CartFlows ajuda a otimizar "
"funis para transformar visitantes em clientes."

#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
msgid "Create New Funnel"
msgstr "Criar Novo Funil"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views"
msgstr "Total de visualizações de página"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Revenue"
msgstr "Receita Total"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Orders"
msgstr "Total de Pedidos"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue"
msgstr "Receita de Oferta"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Views"
msgstr "Total de visualizações"

#: admin-core/assets/build/settings-app.js:24
msgid "Overview"
msgstr "Visão geral"

#: admin-core/assets/build/settings-app.js:24
msgid "WooCommerce plugin is required."
msgstr "É necessário o plugin WooCommerce."

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the overview"
msgstr "Você precisa do plugin WooCommerce instalado e ativado para visualizar a visão geral"

#: admin-core/assets/build/settings-app.js:24
msgid "Recent Orders"
msgstr "Pedidos Recentes"

#: admin-core/assets/build/settings-app.js:24
msgid "View All"
msgstr "Ver tudo"

#: admin-core/assets/build/settings-app.js:24
msgid "Customer"
msgstr "Cliente"

#: admin-core/assets/build/settings-app.js:24
msgid "Payment Method"
msgstr "Método de Pagamento"

#: admin-core/assets/build/settings-app.js:24
msgid "Value"
msgstr "Valor"

#: admin-core/assets/build/settings-app.js:24
msgid "at"
msgstr "em"

#: admin-core/assets/build/settings-app.js:24
msgid "Find recent order here"
msgstr "Encontre o pedido recente aqui"

#: admin-core/assets/build/settings-app.js:24
msgid "Once you have received orders, come back here to find it again easily"
msgstr "Assim que receber as encomendas, volte aqui para encontrá-las facilmente"

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the recent orders"
msgstr "Você precisa do plugin WooCommerce instalado e ativado para visualizar os pedidos recentes"

#: admin-core/assets/build/settings-app.js:24
msgid "Quick Actions"
msgstr "Ações Rápidas"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Funnel"
msgstr "Criar um Funil"

#: admin-core/assets/build/settings-app.js:80
msgid "Analytics"
msgstr "Análise"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Product"
msgstr "Criar um Produto"

#: admin-core/assets/build/settings-app.js:24
msgid "Create new Product"
msgstr "Criar novo Produto"

#: admin-core/assets/build/settings-app.js:24
msgid "All Funnels"
msgstr "Todos os Funis"

#: admin-core/assets/build/settings-app.js:24
msgid "View all funnels"
msgstr "Ver todos os funis"

#: admin-core/assets/build/settings-app.js:24
msgid "Previous"
msgstr "Anterior"

#: admin-core/assets/build/settings-app.js:24
#: wizard/assets/build/wizard-app.js:1
msgid "Next"
msgstr "Próximo"

#: admin-core/assets/build/settings-app.js:24
msgid "First"
msgstr "Primeiro"

#: admin-core/assets/build/settings-app.js:24
msgid "Last"
msgstr "Último"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"You can specify a file to import by either dragging it into the drag and drop area.(Maximum file size of 5MB; .json "
"file extensions only.)"
msgstr ""
"Você pode especificar um arquivo para importar arrastando-o para a área de arrastar e soltar. (Tamanho máximo do "
"arquivo de 5MB; apenas extensões de arquivo .json.)"

#: admin-core/assets/build/settings-app.js:24
msgid "Change a file"
msgstr "Alterar um arquivo"

#: admin-core/assets/build/settings-app.js:24
msgid "JSON file up to 5MB"
msgstr "Arquivo JSON de até 5MB"

#: admin-core/assets/build/settings-app.js:25
#. translators: %s is replaced with the file name.
msgid "File Selected: %s"
msgstr "Arquivo selecionado: %s"

#: admin-core/assets/build/settings-app.js:25
msgid "Please select the valid json file."
msgstr "Por favor, selecione o arquivo json válido."

#: admin-core/assets/build/settings-app.js:25
#: wizard/assets/build/wizard-app.js:5
msgid "Importing.."
msgstr "Importando.."

#: admin-core/assets/build/settings-app.js:25
msgid "Export All"
msgstr "Exportar tudo"

#: admin-core/assets/build/settings-app.js:25
msgid "Exporting…"
msgstr "Exportando…"

#: admin-core/assets/build/settings-app.js:25
msgid "Search Funnels"
msgstr "Funis de Pesquisa"

#: admin-core/assets/build/settings-app.js:25
msgid "Filter Funnels by Date"
msgstr "Filtrar Funis por Data"

#: admin-core/assets/build/settings-app.js:25
msgid "Publish "
msgstr "Publicar"

#: admin-core/assets/build/settings-app.js:25
msgid "Draft "
msgstr "Rascunho"

#: admin-core/assets/build/settings-app.js:25
msgid "Trash "
msgstr "Lixo"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:33
msgid "Mode"
msgstr "Modo"

#: admin-core/assets/build/settings-app.js:25
msgid "All"
msgstr "Todos"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
msgid "Live"
msgstr "Ao vivo"

#: admin-core/assets/build/settings-app.js:25
msgid "SandBox"
msgstr "SandBox"

#: admin-core/assets/build/settings-app.js:25
msgid "Reset Filters"
msgstr "Redefinir Filtros"

#: admin-core/assets/build/settings-app.js:28
#. translators: %s: action name.
msgid "%s This Flow"
msgstr "%s Este Fluxo"

#: admin-core/assets/build/settings-app.js:31
#. translators: %s: action status name.
msgid "Do you want to %s this flow? Are you sure?"
msgstr "Você quer %s este fluxo? Tem certeza?"

#: admin-core/assets/build/settings-app.js:31
msgid "items selected"
msgstr "itens selecionados"

#: admin-core/assets/build/settings-app.js:31
msgid "Applying changes…"
msgstr "Aplicando alterações…"

#: admin-core/assets/build/settings-app.js:31
msgid " Published "
msgstr "Publicado"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate Funnel"
msgstr "Duplicar Funil"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to duplicate this funnel?"
msgstr "Você realmente deseja duplicar este funil?"

#: admin-core/assets/build/settings-app.js:31
msgid "Trash Funnel"
msgstr "Funil de Lixo"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this funnel?"
msgstr "Você realmente quer descartar este funil?"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this Funnel?"
msgstr "Você realmente deseja descartar este Funil?"

#: admin-core/assets/build/settings-app.js:31
msgid "Restore Funnel"
msgstr "Restaurar Funil"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to restore this funnel?"
msgstr "Você realmente deseja restaurar este funil?"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Draft"
msgstr "Rascunho"

#: admin-core/assets/build/settings-app.js:32
#. translators: %s date
msgid "Last Modified: %s"
msgstr "Última modificação: %s"

#: admin-core/assets/build/settings-app.js:32
msgid "Updated "
msgstr "Atualizado"

#: admin-core/assets/build/settings-app.js:32
msgid "Sandbox"
msgstr "Caixa de areia"

#: admin-core/assets/build/settings-app.js:32
msgid "WooCommerce Required to display the revenue."
msgstr "WooCommerce necessário para exibir a receita."

#: admin-core/assets/build/settings-app.js:32
msgid "Restore Flow"
msgstr "Restaurar Fluxo"

#: admin-core/assets/build/settings-app.js:32
msgid "Delete Flow"
msgstr "Excluir Fluxo"

#: admin-core/assets/build/settings-app.js:32
msgid "Upgrade to Pro for this feature."
msgstr "Atualize para o Pro para ter este recurso."

#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate (Pro)"
msgstr "Duplicar (Pro)"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash Flow"
msgstr "Fluxo de Lixo"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash"
msgstr "Lixo"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Name"
msgstr "Nome"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Sales"
msgstr "Vendas"

#: admin-core/assets/build/settings-app.js:32
msgid "Move to Trash"
msgstr "Mover para o Lixo"

#: admin-core/assets/build/settings-app.js:33
#. translators: %d Search term
msgid "No matching results found for the search term \"%s\"."
msgstr "Nenhum resultado correspondente encontrado para o termo de pesquisa \"%s\"."

#: admin-core/assets/build/settings-app.js:33
msgid "No flows found for the selected filter."
msgstr "Nenhum fluxo encontrado para o filtro selecionado."

#: admin-core/assets/build/settings-app.js:33
msgid "Please try using different keywords, date range, or filters to refine your results."
msgstr "Por favor, tente usar palavras-chave diferentes, intervalo de datas ou filtros para refinar seus resultados."

#: admin-core/assets/build/settings-app.js:33
msgid "Create New"
msgstr "Criar Novo"

#: admin-core/assets/build/settings-app.js:34
#. translators: %d flow count
msgid " %d items"
msgstr "%d itens"

#: admin-core/assets/build/settings-app.js:34
msgid "Create your first funnel"
msgstr "Crie seu primeiro funil"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Build a sales funnel with everything you need to generate leads and grow sales."
msgstr "Construa um funil de vendas com tudo o que você precisa para gerar leads e aumentar as vendas."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "One Click Upsells"
msgstr "Upsells com Um Clique"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "A/B Split Testing"
msgstr "Teste A/B"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Conversion Templates"
msgstr "Modelos de Conversão"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Editor"
msgstr "Editor de Checkout"

#: admin-core/assets/build/settings-app.js:34
msgid "Insights"
msgstr "Insights"

#: admin-core/assets/build/settings-app.js:34
msgid "Create Funnel"
msgstr "Criar Funil"

#: admin-core/assets/build/settings-app.js:34
msgid "Plugin Required"
msgstr "Plugin Necessário"

#: admin-core/assets/build/settings-app.js:34
msgid "You need WooCommerce plugin installed and activated to access this page."
msgstr "Você precisa do plugin WooCommerce instalado e ativado para acessar esta página."

#: admin-core/assets/build/settings-app.js:34
msgid "Installing"
msgstr "Instalando"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
msgid "Activating"
msgstr "Ativando"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Failed"
msgstr "Falhou"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Redirecting"
msgstr "Redirecionando"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Create Store Checkout"
msgstr "Criar Checkout da Loja"

#: admin-core/assets/build/settings-app.js:34
msgid "Name Your Store Checkout"
msgstr "Nomeie o Checkout da Sua Loja"

#: admin-core/assets/build/settings-app.js:34
msgid "You can't create more than 3 flows in free version. Upgrade to CartFlows Pro for adding more flows and other features."
msgstr ""
"Você não pode criar mais de 3 fluxos na versão gratuita. Faça o upgrade para o CartFlows Pro para adicionar mais fluxos "
"e outros recursos."

#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To Cartflows Pro"
msgstr "Atualize para o Cartflows Pro"

#: admin-core/assets/build/settings-app.js:34
msgid "Store Checkout Name"
msgstr "Nome do Checkout da Loja"

#: admin-core/assets/build/settings-app.js:34
msgid "Enter Store Checkout Name"
msgstr "Insira o Nome do Caixa da Loja"

#: admin-core/assets/build/settings-app.js:34
msgid "Create a global store checkout"
msgstr "Criar um checkout de loja global"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"A well-designed checkout page can help streamline the checkout process, reduce cart abandonment rates and increase "
"conversions."
msgstr ""
"Uma página de checkout bem projetada pode ajudar a simplificar o processo de checkout, reduzir as taxas de abandono de "
"carrinho e aumentar as conversões."

#: admin-core/assets/build/settings-app.js:34
msgid "Improved user experience"
msgstr "Experiência do usuário aprimorada"

#: admin-core/assets/build/settings-app.js:34
msgid "Brand consistency"
msgstr "Consistência da marca"

#: admin-core/assets/build/settings-app.js:34
msgid "Increased trust and credibility"
msgstr "Maior confiança e credibilidade"

#: admin-core/assets/build/settings-app.js:34
msgid "Flexibility and customization"
msgstr "Flexibilidade e personalização"

#: admin-core/assets/build/settings-app.js:34
msgid "Competitive advantage"
msgstr "Vantagem competitiva"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"By setting up the store checkout, your default checkout page will be replaced by the CartFlows modern checkout which "
"will lead to more conversion and leads."
msgstr ""
"Ao configurar o checkout da loja, sua página de checkout padrão será substituída pelo checkout moderno do CartFlows, o "
"que levará a mais conversões e leads."

#: admin-core/assets/build/settings-app.js:34
msgid "Get Started"
msgstr "Começar"

#: admin-core/assets/build/settings-app.js:34
msgid "Connect a Payment Gateway"
msgstr "Conectar um Gateway de Pagamento"

#: admin-core/assets/build/settings-app.js:34
msgid "Stripe for WooCommerce delivers a simple, secure way to accept credit card payments in your WooCommerce store."
msgstr ""
"Stripe para WooCommerce oferece uma maneira simples e segura de aceitar pagamentos com cartão de crédito na sua loja "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:34
msgid "Connect with Stripe"
msgstr "Conectar com o Stripe"

#: admin-core/assets/build/settings-app.js:34
msgid "Setting up…"
msgstr "Configurando…"

#: admin-core/assets/build/settings-app.js:34
msgid "Recover Abandoned Carts"
msgstr "Recuperar Carrinhos Abandonados"

#: admin-core/assets/build/settings-app.js:34
msgid "Use our cart abandonment plugin and automatically recover your lost revenue absolutely free."
msgstr "Use nosso plugin de abandono de carrinho e recupere automaticamente sua receita perdida absolutamente grátis."

#: admin-core/assets/build/settings-app.js:34
msgid "Finishing…"
msgstr "Finalizando…"

#: admin-core/assets/build/settings-app.js:34
msgid "Setup Email Reports"
msgstr "Configurar Relatórios de Email"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send you an email report with "
"key metrics and insights."
msgstr ""
"Deixe o CartFlows eliminar as suposições dos seus resultados de checkout. Toda semana, sua loja enviará um relatório "
"por e-mail com métricas e insights principais."

#: admin-core/assets/build/settings-app.js:34
msgid "Add Email Address"
msgstr "Adicionar Endereço de Email"

#: admin-core/assets/build/settings-app.js:34
msgid "Dismiss Setup"
msgstr "Dispensar Configuração"

#: admin-core/assets/build/settings-app.js:34
msgid "Active"
msgstr "Ativo"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Activate"
msgstr "Ativar"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Install"
msgstr "Instalar"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Installing…"
msgstr "Instalando…"

#: admin-core/assets/build/settings-app.js:34
msgid "Installed"
msgstr "Instalado"

#: admin-core/assets/build/settings-app.js:34
msgid "Let's Go"
msgstr "Vamos"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Plugins"
msgstr "Plugins Recomendados"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Themes"
msgstr "Temas Recomendados"

#: admin-core/assets/build/settings-app.js:34
msgid "View All Steps"
msgstr "Ver todas as etapas"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel thumbnail image"
msgstr "Imagem em miniatura do funil"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel Preview"
msgstr "Pré-visualização do Funil"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Live Preview"
msgstr "Pré-visualização ao vivo"

#: admin-core/assets/build/settings-app.js:34
msgid "Funnel Templates"
msgstr "Modelos de Funil"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Start from scratch"
msgstr "Começar do zero"

#: admin-core/assets/build/settings-app.js:34
msgid "It seems that the page builder you selected is inactive."
msgstr "Parece que o construtor de páginas que você selecionou está inativo."

#: admin-core/assets/build/settings-app.js:34
msgid " to see CartFlows templates. If you prefer another page builder tool, you can "
msgstr "para ver os modelos do CartFlows. Se preferir outra ferramenta de criação de páginas, você pode"

#: admin-core/assets/build/settings-app.js:34
msgid "select it here"
msgstr "selecione-o aqui"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using any other page builder? No worries. CartFlows works well with every other page builder. Right now we do "
"not have ready templates for every page builder but we are planning to add it very soon."
msgstr ""
"Você está usando algum outro construtor de páginas? Não se preocupe. O CartFlows funciona bem com qualquer outro "
"construtor de páginas. No momento, não temos modelos prontos para todos os construtores de páginas, mas estamos "
"planejando adicioná-los em breve."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Learn How "
msgstr "Aprenda como"

#: admin-core/assets/build/settings-app.js:35
msgid "No Results Found."
msgstr "Nenhum resultado encontrado."

#: admin-core/assets/build/settings-app.js:35
msgid "Don't see a funnel that you would like to import?"
msgstr "Não vê um funil que gostaria de importar?"

#: admin-core/assets/build/settings-app.js:35
msgid "Please suggest us "
msgstr "Por favor, sugira-nos"

#: admin-core/assets/build/settings-app.js:35
msgid "Choose a Funnel Templates"
msgstr "Escolha um Modelo de Funil"

#: admin-core/assets/build/settings-app.js:35
msgid "Search Templates"
msgstr "Pesquisar Modelos"

#: admin-core/assets/build/settings-app.js:35
msgid "Start from Scratch"
msgstr "Começar do zero"

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the Bricks Builder."
msgstr "Parece que você está usando o Bricks Builder."

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the page builder other than Elementor, Beaver Builder, Block Builder."
msgstr "Parece que você está usando um construtor de páginas diferente do Elementor, Beaver Builder, Block Builder."

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using Bricks Builder? No worries. CartFlows works well with Bricks Builder. Right now we do not have ready "
"templates for Bricks Builder but we are planning to add it very soon."
msgstr ""
"Você está usando o Bricks Builder? Não se preocupe. O CartFlows funciona bem com o Bricks Builder. No momento, não "
"temos modelos prontos para o Bricks Builder, mas estamos planejando adicioná-los em breve."

#: admin-core/assets/build/settings-app.js:35
msgid "Checkout Page"
msgstr "Página de Finalização de Compra"

#: admin-core/assets/build/settings-app.js:35
msgid "Oops!!! No template Found."
msgstr "Ops!!! Nenhum modelo encontrado."

#: admin-core/assets/build/settings-app.js:35
msgid "Seems like no template is available for chosen editor."
msgstr "Parece que não há modelo disponível para o editor escolhido."

#: admin-core/assets/build/settings-app.js:35
msgid "Store Checkout Templates"
msgstr "Modelos de Checkout da Loja"

#: admin-core/assets/build/settings-app.js:80
msgid "No CartFlows Logs Found."
msgstr "Nenhum registro do CartFlows encontrado."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Logs"
msgstr "Registros do CartFlows"

#: admin-core/assets/build/settings-app.js:80
msgid "Copied"
msgstr "Copiado"

#: admin-core/assets/build/settings-app.js:80
msgid "Copy"
msgstr "Copiar"

#: admin-core/assets/build/settings-app.js:80
msgid "Downloading"
msgstr "Baixando"

#: admin-core/assets/build/settings-app.js:80
msgid "Download"
msgstr "Baixar"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleting"
msgstr "Excluindo"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleted"
msgstr "Excluído"

#: admin-core/assets/build/settings-app.js:80
msgid "Email Marketing Automation"
msgstr "Automação de Email Marketing"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate email marketing campaigns based on customer actions, such as abandoned carts or completed purchases in "
"WooCommerce."
msgstr ""
"Automatize campanhas de marketing por e-mail com base nas ações dos clientes, como carrinhos abandonados ou compras "
"concluídas no WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Customer Birthday Campaigns"
msgstr "Campanhas de Aniversário de Clientes"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automatically send personalized birthday offers or discounts to customers based on their birthdate stored in "
"WooCommerce."
msgstr ""
"Envie automaticamente ofertas ou descontos personalizados de aniversário para os clientes com base na data de "
"nascimento armazenada no WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Order Notification"
msgstr "Notificação de Pedido"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Receive instant notifications via SMS, Slack, WhatsApp, or messaging apps when new orders are placed in your "
"WooCommerce store."
msgstr ""
"Receba notificações instantâneas via SMS, Slack, WhatsApp ou aplicativos de mensagens quando novos pedidos forem feitos "
"na sua loja WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Payment and Accounting Integration"
msgstr "Integração de Pagamentos e Contabilidade"

#: admin-core/assets/build/settings-app.js:80
msgid "Sync WooCommerce sales data with your accounting software for streamlined financial management."
msgstr "Sincronize os dados de vendas do WooCommerce com seu software de contabilidade para uma gestão financeira simplificada."

#: admin-core/assets/build/settings-app.js:80
msgid "Coupon Code Marketing"
msgstr "Marketing de Código de Cupom"

#: admin-core/assets/build/settings-app.js:80
msgid "Automate the creation and distribution of coupon codes based on specific conditions or customer actions in WooCommerce."
msgstr ""
"Automatize a criação e distribuição de códigos de cupom com base em condições específicas ou ações dos clientes no "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Upsell and Cross-sell Campaigns"
msgstr "Campanhas de Upsell e Cross-sell"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate targeted upsell and cross-sell offers based on customers' purchase history or product interactions in "
"WooCommerce."
msgstr ""
"Automatize ofertas direcionadas de upsell e cross-sell com base no histórico de compras dos clientes ou interações com "
"produtos no WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Connect Your Website"
msgstr "Conecte seu site"

#: admin-core/assets/build/settings-app.js:80
msgid "Reloading"
msgstr "Recarregando"

#: admin-core/assets/build/settings-app.js:80
msgid "Connecting"
msgstr "Conectando"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate WooCommerce and CartFlows with Anything"
msgstr "Integre WooCommerce e CartFlows com qualquer coisa"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate all your apps, plugins, and services to automate repetitive tasks."
msgstr "Integre todos os seus aplicativos, plugins e serviços para automatizar tarefas repetitivas."

#: admin-core/assets/build/settings-app.js:80
msgid "These are just some examples. The possibilities are truly endless!"
msgstr "Estes são apenas alguns exemplos. As possibilidades são realmente infinitas!"

#: admin-core/assets/build/settings-app.js:80
msgid "Trusted by World's Top Brands to Connect Their Apps"
msgstr "Confiado pelas Principais Marcas do Mundo para Conectar Seus Aplicativos"

#: admin-core/assets/build/settings-app.js:80
msgid "Connect your apps and automate your business."
msgstr "Conecte seus aplicativos e automatize seu negócio."

#: modules/gutenberg/build/blocks-placeholder.js:12
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail Form"
msgstr "Formulário de Detalhes do Pedido"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Overview."
msgstr "Ocultar/Mostrar Visão Geral do Pedido."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail"
msgstr "Detalhe do Pedido"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Detail."
msgstr "Ocultar/Mostrar Detalhes do Pedido."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Billing Address."
msgstr "Ocultar/Mostrar Endereço de Cobrança."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Shipping Address."
msgstr "Ocultar/Mostrar Endereço de Envio."

#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text"
msgstr "Texto do Cabeçalho"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading Bottom Spacing(px)"
msgstr "Espaçamento Inferior do Cabeçalho(px)"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Opacity"
msgstr "Opacidade"

#: modules/gutenberg/build/blocks.js:7
msgid "Section Spacing"
msgstr "Espaçamento de Seção"

#: modules/gutenberg/build/blocks.js:7
msgid "Download Details"
msgstr "Detalhes do Download"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Loading"
msgstr "Carregando"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Order Detail Form Block"
msgstr "Bloco de Formulário de Detalhes do Pedido do CartFlows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cartflows"
msgstr "cartflows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "order detail form"
msgstr "formulário de detalhes do pedido"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cf"
msgstr "cf"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Color"
msgstr "Cor do Ícone"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Hover Color"
msgstr "Cor de Hover do Ícone"

#: modules/gutenberg/build/blocks.js:11
msgid "Gap Between Icon And Text"
msgstr "Lacuna Entre Ícone E Texto"

#: modules/gutenberg/build/blocks.js:11
msgid "Subtitle"
msgstr "Legenda"

#: modules/gutenberg/build/blocks.js:11
msgid "Enable Subtitle"
msgstr "Ativar legenda"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Bottom Spacing"
msgstr "Espaçamento Inferior do Título"

#: modules/gutenberg/build/blocks.js:11
msgid "SubTitle"
msgstr "Subtítulo"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Add text…"
msgstr "Adicionar texto…"

#: modules/gutenberg/build/blocks.js:11
msgid "CartFlows Next Step Button Block."
msgstr "Bloco de Botão de Próximo Passo do CartFlows."

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "next step button"
msgstr "botão de próximo passo"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step ( Pro )"
msgstr "Dois Passos ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout ( Pro )"
msgstr "Checkout em várias etapas ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This feature is available in the CartFlows higher plan. Upgrade Now!."
msgstr "Nota: Este recurso está disponível no plano superior do CartFlows. Faça o upgrade agora!."

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Skin"
msgstr "Pele do Campo de Entrada"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Field"
msgstr "Campo de Entrada"

#: modules/gutenberg/build/blocks.js:11
msgid "Field Text Color"
msgstr "Cor do Texto do Campo"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Validation"
msgstr "Validação do Campo de Entrada"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This styling can be only seen at frontend"
msgstr "Nota: Este estilo só pode ser visto no frontend"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Horizontal"
msgstr "Horizontal"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Vertical"
msgstr "Vertical"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Blur"
msgstr "Desfoque"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spread"
msgstr "Espalhar"

#: modules/gutenberg/build/blocks.js:11
msgid "Buttons Text"
msgstr "Texto dos Botões"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Color"
msgstr "Cor do Título"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Background Color"
msgstr "Cor de Fundo do Título"

#: modules/gutenberg/build/blocks.js:11
msgid "Desc Background Color"
msgstr "Cor de Fundo da Desc"

#: modules/gutenberg/build/blocks.js:11
msgid "Success/Error Message"
msgstr "Mensagem de Sucesso/Erro"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Message Color"
msgstr "Cor da Mensagem"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label"
msgstr "Rótulo Flutuante"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Skin"
msgstr "Pele de entrada"

#: modules/gutenberg/build/blocks.js:11
msgid "Skin"
msgstr "Pele"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide Advanced"
msgstr "Ocultar Avançado"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Line Height"
msgstr "Altura da Linha"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color Settings"
msgstr "Configurações de Cor"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Overlay Color"
msgstr "Cor de Sobreposição"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Inset"
msgstr "Inserir"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Outset"
msgstr "Início"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing Between Sections(px)"
msgstr "Espaçamento Entre Seções(px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Type"
msgstr "Tipo de Fundo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image"
msgstr "Imagem"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Image"
msgstr "Imagem de Fundo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Background Image"
msgstr "Selecionar imagem de fundo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Replace image"
msgstr "Substituir imagem"

#: admin-core/assets/build/editor-app.js:1
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Remove Image"
msgstr "Remover imagem"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image Position"
msgstr "Posição da Imagem"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Left"
msgstr "Canto superior esquerdo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Center"
msgstr "Centro Superior"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Right"
msgstr "Canto superior direito"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Left"
msgstr "Centro Esquerda"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Center"
msgstr "Centro Centro"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Right"
msgstr "Centro-direita"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Left"
msgstr "Inferior esquerdo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Center"
msgstr "Centro Inferior"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Right"
msgstr "Canto Inferior Direito"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Attachment"
msgstr "Anexo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Fixed"
msgstr "Corrigido"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Scroll"
msgstr "Rolar"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat"
msgstr "Repetir"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "No Repeat"
msgstr "Sem Repetição"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-x"
msgstr "Repetir-x"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-y"
msgstr "Repetir-y"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Auto"
msgstr "Carro"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Cover"
msgstr "Capa"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Contain"
msgstr "Conter"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Weight"
msgstr "Peso da Fonte"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Subset"
msgstr "Subconjunto de Fonte"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "px"
msgstr "px"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "em"
msgstr "em"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size Type"
msgstr "Tipo de Tamanho"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button Block"
msgstr "Bloco do Botão Próximo Passo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Icon"
msgstr "Selecionar ícone"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button Hover Color"
msgstr "Cor do Hover do Botão"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Groove"
msgstr "Ritmo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Ridge"
msgstr "Cume"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Bottom Spacing (px)"
msgstr "Espaçamento Inferior do Título (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover Color"
msgstr "Cor de Passagem"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Transform"
msgstr "Transformação de Texto"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Capitalize"
msgstr "Capitalizar"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Uppercase"
msgstr "Maiúsculas"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Lowercase"
msgstr "minúsculas"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Letter Spacing (px)"
msgstr "Espaçamento entre letras (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Checkout Block"
msgstr "Bloco de Checkout do CartFlows"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "checkout form"
msgstr "formulário de checkout"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout ( Pro )"
msgstr "Checkout MultiEtapas ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width (px)"
msgstr "Largura da Borda (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Note: This feature is available in the CartFlows Pro. Upgrade Now!."
msgstr "Nota: Este recurso está disponível no CartFlows Pro. Atualize agora!."

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input/Text Placeholder Color"
msgstr "Cor do Espaço Reservado de Entrada/Texto"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius (px)"
msgstr "Raio da Borda (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Padding (px)"
msgstr "Preenchimento da Seção (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Margin (px)"
msgstr "Margem da Seção (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Success / Error Message"
msgstr "Mensagem de Sucesso / Erro"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Form success / Error validation"
msgstr "Sucesso do formulário / Validação de erro"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Error Message"
msgstr "Mensagem de Erro"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Optin Form Block"
msgstr "Bloco de Formulário de Inscrição do CartFlows"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "optin form"
msgstr "formulário de inscrição"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label ( Pro )"
msgstr "Rótulo Flutuante ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button Text"
msgstr "Texto do Botão Enviar"

#: wizard/assets/build/wizard-app.js:1
msgid "Let's Start"
msgstr "Vamos começar"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 1 of 6"
msgstr "Etapa 1 de 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome to CartFlows"
msgstr "Bem-vindo ao CartFlows"

#: wizard/assets/build/wizard-app.js:1
msgid ""
"You're only minutes away from having a more profitable WooCommerce store! This short setup wizard will help you get "
"started with CartFlows."
msgstr ""
"Você está a apenas alguns minutos de ter uma loja WooCommerce mais lucrativa! Este assistente de configuração rápida "
"ajudará você a começar com o CartFlows."

#: wizard/assets/build/wizard-app.js:1
msgid "Save & Continue"
msgstr "Salvar e Continuar"

#: wizard/assets/build/wizard-app.js:1
msgid "Saving"
msgstr "Salvando"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 2 of 6"
msgstr "Etapa 2 de 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Hi there! Tell us which page builder you use."
msgstr "Olá! Diga-nos qual construtor de páginas você usa."

#: wizard/assets/build/wizard-app.js:1
msgid "CartFlows works with all page builders, so don't worry if your page builder is not in the list. "
msgstr ""
"O CartFlows funciona com todos os construtores de páginas, então não se preocupe se o seu construtor de páginas não "
"estiver na lista."

#: wizard/assets/build/wizard-app.js:1
msgid "Install & Activate"
msgstr "Instalar e Ativar"

#: wizard/assets/build/wizard-app.js:1
msgid "Installing Required Plugins"
msgstr "Instalando Plugins Necessários"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 3 of 6"
msgstr "Etapa 3 de 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Great job!"
msgstr "Ótimo trabalho!"

#: wizard/assets/build/wizard-app.js:1
msgid "Now let's install some required plugins."
msgstr "Agora vamos instalar alguns plugins necessários."

#: wizard/assets/build/wizard-app.js:1
msgid ""
"Since CartFlows uses WooCommerce, we'll set it up for you along with Cart Abandonment and Stripe Payments so you can "
"recover abandoned orders and easily accept payments."
msgstr ""
"Como o CartFlows usa o WooCommerce, vamos configurá-lo para você junto com o Abandono de Carrinho e Pagamentos via "
"Stripe, para que você possa recuperar pedidos abandonados e aceitar pagamentos facilmente."

#: wizard/assets/build/wizard-app.js:1
msgid "The following plugins will be installed and activated for you:"
msgstr "Os seguintes plugins serão instalados e ativados para você:"

#: wizard/assets/build/wizard-app.js:1
msgid "Continuing…"
msgstr "Continuando…"

#: wizard/assets/build/wizard-app.js:1
msgid "Continue"
msgstr "Continuar"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 5 of 6"
msgstr "Etapa 5 de 6"

#: wizard/assets/build/wizard-app.js:2
#. translators: %s: html tag
msgid "One last step. %s Let's setup email reports on how your store is doing."
msgstr "Um último passo. %s Vamos configurar relatórios por e-mail sobre como sua loja está indo."

#: wizard/assets/build/wizard-app.js:3
#. translators: %1$s: html tag, %2$s: html tag
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send %1$s you an email report "
"with key metrics and insights. You also will receive emails from us to %2$s help your store sell more."
msgstr ""
"Deixe o CartFlows eliminar as suposições dos seus resultados de checkout. Toda semana, sua loja enviará %1$s a você um "
"relatório por e-mail com métricas e insights importantes. Você também receberá e-mails nossos para %2$s ajudar sua loja "
"a vender mais."

#: wizard/assets/build/wizard-app.js:3
msgid "First Name"
msgstr "Primeiro Nome"

#: wizard/assets/build/wizard-app.js:3
msgid "Please enter your name"
msgstr "Por favor, insira seu nome"

#: wizard/assets/build/wizard-app.js:3
msgid "Enter Your Email"
msgstr "Digite seu e-mail"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Name"
msgstr "Por favor, insira o nome"

#: wizard/assets/build/wizard-app.js:4
msgid "Entered email address is not a valid email"
msgstr "O endereço de e-mail inserido não é um e-mail válido"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Email ID"
msgstr "Por favor, insira o ID de e-mail"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome"
msgstr "Bem-vindo"

#: wizard/assets/build/wizard-app.js:1
msgid "Page Builder"
msgstr "Criador de Páginas"

#: wizard/assets/build/wizard-app.js:1
msgid "Required Plugins"
msgstr "Plugins Necessários"

#: wizard/assets/build/wizard-app.js:1
msgid "Done"
msgstr "Concluído"

#: wizard/assets/build/wizard-app.js:1
msgid "Exit setup wizard"
msgstr "Sair do assistente de configuração"

#: wizard/assets/build/wizard-app.js:1
msgid "Redirecting.."
msgstr "Redirecionando.."

#: wizard/assets/build/wizard-app.js:1
msgid "Skip"
msgstr "Pular"

#: wizard/assets/build/wizard-app.js:1
#: wizard/assets/build/wizard-app.js:5
msgid "Finish Store Setup"
msgstr "Concluir Configuração da Loja"

#: wizard/assets/build/wizard-app.js:1
msgid "Select Color"
msgstr "Selecionar Cor"

#: wizard/assets/build/wizard-app.js:5
msgid "Recommended"
msgstr "Recomendado"

#: wizard/assets/build/wizard-app.js:5
msgid "Upload a Logo"
msgstr "Carregar um logotipo"

#: wizard/assets/build/wizard-app.js:5
msgid "Change a Logo"
msgstr "Alterar um logotipo"

#: wizard/assets/build/wizard-app.js:5
msgid "Remove logo"
msgstr "Remover logotipo"

#: wizard/assets/build/wizard-app.js:5
msgid "Suggested Dimensions: 180x60 pixels"
msgstr "Dimensões sugeridas: 180x60 pixels"

#: wizard/assets/build/wizard-app.js:5
msgid "Oops!!! No templates found"
msgstr "Ops!!! Nenhum modelo encontrado"

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Seems like no templates are available for chosen page editor. Don't worry, you can always import the store checkout "
"template from the CartFlows setting menu."
msgstr ""
"Parece que não há modelos disponíveis para o editor de página escolhido. Não se preocupe, você sempre pode importar o "
"modelo de checkout da loja no menu de configurações do CartFlows."

#: wizard/assets/build/wizard-app.js:5
msgid "Skip to Next"
msgstr "Pular para o próximo"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 4 of 6"
msgstr "Etapa 4 de 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Awesome"
msgstr "Incrível"

#: wizard/assets/build/wizard-app.js:5
msgid "Now let's setup your new store checkout."
msgstr "Agora vamos configurar o checkout da sua nova loja."

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Choose one of the store checkout designs below. After setup you can change the text and color or even choose an "
"entirely new store checkout design."
msgstr ""
"Escolha um dos designs de checkout da loja abaixo. Após a configuração, você pode alterar o texto e a cor ou até mesmo "
"escolher um design de checkout de loja completamente novo."

#: wizard/assets/build/wizard-app.js:5
msgid "Import & Continue"
msgstr "Importar e Continuar"

#: wizard/assets/build/wizard-app.js:5
msgid "Processing.."
msgstr "Processando.."

#: wizard/assets/build/wizard-app.js:5
msgid "Importing Failed.."
msgstr "Falha na importação.."

#: wizard/assets/build/wizard-app.js:5
msgid "Selected Template:"
msgstr "Modelo Selecionado:"

#: wizard/assets/build/wizard-app.js:5
msgid "Change Primary Color"
msgstr "Alterar Cor Primária"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 6 of 6"
msgstr "Etapa 6 de 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Congratulations, You Did It!"
msgstr "Parabéns, Você Conseguiu!"

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows is set up on your website! Please watch the short video below for your next steps."
msgstr "O CartFlows está configurado no seu site! Por favor, assista ao breve vídeo abaixo para os próximos passos."

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows Extended Walkthrough Tutorial"
msgstr "Tutorial Estendido de Passo a Passo do CartFlows"

#: wizard/assets/build/wizard-app.js:5
msgid "Finishing the Setup"
msgstr "Finalizando a Configuração"

#: admin-core/inc/admin-menu.php:1052
msgid "A simple yet powerful way to add content restriction to your website."
msgstr "Uma maneira simples, mas poderosa, de adicionar restrição de conteúdo ao seu site."

#: classes/class-cartflows-loader.php:368
msgid "Quick Feedback"
msgstr "Feedback Rápido"

#: classes/class-cartflows-loader.php:370
msgid "If you have a moment, please share why you are deactivating CartFlows:"
msgstr "Se você tiver um momento, por favor, compartilhe por que está desativando o CartFlows:"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unlock Premium Features with CartFlows PRO!"
msgstr "Desbloqueie Recursos Premium com o CartFlows PRO!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Get the tools you need to create powerful sales funnels, increase conversions, and grow your business with ease."
msgstr ""
"Obtenha as ferramentas necessárias para criar funis de vendas poderosos, aumentar as conversões e expandir seu negócio "
"com facilidade."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Select a Settings Tab"
msgstr "Selecione uma Aba de Configurações"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Free vs Pro"
msgstr "Grátis vs Pro"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Increase Your Revenue with Smart Order Bumps"
msgstr "Aumente sua receita com ofertas adicionais inteligentes"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Boost sales with CartFlows’ Order Bump! Offer personalized add-ons at checkout to increase revenue effortlessly. Quick "
"to set up, no coding needed!"
msgstr ""
"Aumente as vendas com o Order Bump do CartFlows! Ofereça complementos personalizados no checkout para aumentar a "
"receita sem esforço. Rápido de configurar, sem necessidade de codificação!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show the Right Offer to the Right People – Automatically!"
msgstr "Mostre a oferta certa para as pessoas certas – automaticamente!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Personalize deals based on location, cart details, and more. Upgrade to CartFlows PRO and unlock this smart feature "
"today!"
msgstr ""
"Personalize ofertas com base na localização, detalhes do carrinho e mais. Faça o upgrade para o CartFlows PRO e "
"desbloqueie este recurso inteligente hoje!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Boost Sales Instantly with Auto-Applied Coupons!"
msgstr "Aumente as Vendas Instantaneamente com Cupons Aplicados Automaticamente!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"No codes, no hassle—discounts apply instantly at checkout. Upgrade to CartFlows PRO and start converting more customers "
"today!"
msgstr ""
"Sem códigos, sem complicações—os descontos são aplicados instantaneamente no checkout. Faça o upgrade para o CartFlows "
"PRO e comece a converter mais clientes hoje!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Give Your Customers More Choices, Boost Your Sales"
msgstr "Dê Mais Opções aos Seus Clientes, Aumente Suas Vendas"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"Make buying easier with flexible product options. Let customers make the right choices from your checkout. Upgrade to "
"CartFlows PRO and start customizing today!"
msgstr ""
"Torne a compra mais fácil com opções de produtos flexíveis. Deixe os clientes fazerem as escolhas certas no seu "
"checkout. Faça o upgrade para o CartFlows PRO e comece a personalizar hoje!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Optin Product"
msgstr "Produto de Opt-in"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Select free & virtual product only. Once you select a product, it will be displayed here."
msgstr "Selecione apenas produtos gratuitos e virtuais. Assim que você selecionar um produto, ele será exibido aqui."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Search for a Free & Virtual Product"
msgstr "Procure por um Produto Gratuito e Virtual"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Please select a free & virtual product only."
msgstr "Por favor, selecione apenas um produto gratuito e virtual."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:80
msgid "Free"
msgstr "Grátis"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "A/B Testing"
msgstr "Teste A/B"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid ""
"Optimize your sales with A/B testing in CartFlows! Experiment with product pricing, page layouts, messaging, and "
"design. Create variants, analyze results, and discover new ways to boost revenue."
msgstr ""
"Otimize suas vendas com testes A/B no CartFlows! Experimente com preços de produtos, layouts de página, mensagens e "
"design. Crie variantes, analise os resultados e descubra novas maneiras de aumentar a receita."

#: admin-core/assets/build/settings-app.js:23
msgid "Finish Setup"
msgstr "Concluir Configuração"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup CartFlows"
msgstr "Configurar CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup Store Checkout"
msgstr "Configurar Checkout da Loja"

#: admin-core/assets/build/settings-app.js:23
msgid "Create"
msgstr "Criar"

#: admin-core/assets/build/settings-app.js:23
msgid "Build Your Funnel"
msgstr "Construa o Seu Funil"

#: admin-core/assets/build/settings-app.js:23
msgid "Start From Scratch"
msgstr "Começar do Zero"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Library"
msgstr "Ir Para a Biblioteca"

#: admin-core/assets/build/settings-app.js:23
msgid "Offer add-ons with Order Bump."
msgstr "Ofereça complementos com Order Bump."

#: admin-core/assets/build/settings-app.js:23
msgid "Increase Revenue with Upsells."
msgstr "Aumente a Receita com Vendas Adicionais."

#: admin-core/assets/build/settings-app.js:23
msgid "Almost There! Let's Go Live."
msgstr "Quase lá! Vamos ao vivo."

#: admin-core/assets/build/settings-app.js:24
#. translators: %d is the number of completed steps.
msgid "%d out of 5 completed"
msgstr "%d de 5 concluídos"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade to PRO"
msgstr "Atualize para PRO"

#: admin-core/assets/build/settings-app.js:24
msgid "Completed"
msgstr "Concluído"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade your plan anytime and get more detailed analytics data."
msgstr "Atualize seu plano a qualquer momento e obtenha dados analíticos mais detalhados."

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views is a Premium Feature"
msgstr "Total de visualizações de página é um recurso premium"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue is a Premium Feature"
msgstr "Receita de Oferta é um Recurso Premium"

#: admin-core/assets/build/settings-app.js:24
msgid "Custom Filter:"
msgstr "Filtro Personalizado:"

#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Features"
msgstr "Recursos de Checkout"

#: admin-core/assets/build/settings-app.js:80
msgid "Modern Checkout Styles"
msgstr "Estilos Modernos de Checkout"

#: admin-core/assets/build/settings-app.js:80
msgid "Optimized replacement for the standard WooCommerce checkout page designed for higher conversion"
msgstr "Substituição otimizada para a página padrão de finalização de compra do WooCommerce, projetada para maior conversão"

#: admin-core/assets/build/settings-app.js:80
msgid "Custom Checkout Fields"
msgstr "Campos Personalizados de Checkout"

#: admin-core/assets/build/settings-app.js:80
msgid "Have complete control over the field editor to manage the fields as required"
msgstr "Tenha controle total sobre o editor de campos para gerenciar os campos conforme necessário"

#: admin-core/assets/build/settings-app.js:80
msgid "One-Click Upsells / Downsells"
msgstr "Upsells / Downsells com um clique"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic One-Click Upsells"
msgstr "Upsells Dinâmicos com Um Clique"

#: admin-core/assets/build/settings-app.js:80
msgid "Use cart contents or customer data to display relevant upsells for maximum conversion"
msgstr "Use o conteúdo do carrinho ou os dados do cliente para exibir upsells relevantes para a máxima conversão"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Upsell Templates"
msgstr "Modelos Dinâmicos de Venda Cruzada"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional templates to help you sell more even if you’re not a designer"
msgstr "Modelos profissionais para ajudá-lo a vender mais, mesmo que você não seja um designer"

#: admin-core/assets/build/settings-app.js:80
msgid "Order Bump Features"
msgstr "Recursos de Incremento de Pedido"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Order Bumps"
msgstr "Incrementos de Pedido Dinâmicos"

#: admin-core/assets/build/settings-app.js:80
msgid "Smart order bumps using customer data to display most relevant products or offers"
msgstr "Ofertas adicionais inteligentes usando dados do cliente para exibir os produtos ou ofertas mais relevantes"

#: admin-core/assets/build/settings-app.js:80
msgid "Advanced Funnel Features"
msgstr "Recursos Avançados de Funil"

#: admin-core/assets/build/settings-app.js:80
msgid "A / B Split Testing"
msgstr "Teste A / B"

#: admin-core/assets/build/settings-app.js:80
msgid "Increase conversions and sales with CartFlows A/B Testing by running simple tests"
msgstr "Aumente as conversões e vendas com o teste A/B do CartFlows realizando testes simples"

#: admin-core/assets/build/settings-app.js:80
msgid "Analyze transactions and user behavior to refine conversions and make more profit"
msgstr "Analisar transações e o comportamento dos usuários para refinar conversões e obter mais lucro"

#: admin-core/assets/build/settings-app.js:80
msgid "Cloud-based automation tools that intelligently links your websites, stores, plugins and apps"
msgstr "Ferramentas de automação baseadas em nuvem que conectam inteligentemente seus sites, lojas, plugins e aplicativos"

#: admin-core/assets/build/settings-app.js:80
msgid "SkillJet Academy Access"
msgstr "Acesso à SkillJet Academy"

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows offers full training to help you make more profit with SkillJet academy"
msgstr "CartFlows oferece treinamento completo para ajudá-lo a obter mais lucro com a academia SkillJet"

#: admin-core/assets/build/settings-app.js:80
msgid "Others Benefits"
msgstr "Outros Benefícios"

#: admin-core/assets/build/settings-app.js:80
msgid "Premium Support"
msgstr "Suporte Premium"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional Support, Professional Support Team or Dedicated Support Team"
msgstr "Suporte Profissional, Equipe de Suporte Profissional ou Equipe de Suporte Dedicada"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community"
msgstr "Comunidade de Usuários Incrível"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community is already a great message unless you’re looking for a different meaning"
msgstr "Amazing User Community já é uma ótima mensagem, a menos que você esteja procurando um significado diferente"

#: admin-core/assets/build/settings-app.js:80
msgid "Great Documentation & Video Tutorials"
msgstr "Ótima documentação e tutoriais em vídeo"

#: admin-core/assets/build/settings-app.js:80
msgid "Comprehensive Documentation and Video Tutorials or Comprehensive Documentation and Video Guides"
msgstr "Documentação abrangente e tutoriais em vídeo ou Documentação abrangente e guias em vídeo"

#: admin-core/assets/build/settings-app.js:80
msgid "Free Plugins"
msgstr "Plugins Gratuitos"

#: admin-core/assets/build/settings-app.js:80
msgid "Variation Swatches"
msgstr "Amostras de Variação"

#: admin-core/assets/build/settings-app.js:80
msgid "Give customers choice by including relevant product variations including size, color and more"
msgstr "Dê aos clientes a opção, incluindo variações relevantes de produtos, como tamanho, cor e mais"

#: admin-core/assets/build/settings-app.js:80
msgid "Stripe Payment Gateway"
msgstr "Gateway de Pagamento Stripe"

#: admin-core/assets/build/settings-app.js:80
msgid "Accepting multiple payment methods gives customers choice and can significantly increase conversion"
msgstr "Aceitar múltiplos métodos de pagamento oferece escolha aos clientes e pode aumentar significativamente a conversão"

#: admin-core/assets/build/settings-app.js:80
msgid "Features"
msgstr "Recursos"

#: admin-core/assets/build/settings-app.js:80
msgid "See all CartFlows Pro features"
msgstr "Veja todos os recursos do CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Sell More with CartFlows Pro"
msgstr "Venda mais com o CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Get access to powerful features for painless WordPress designing, without the high costs. With all the time you will "
"save, it’s a product that pays for itself!"
msgstr ""
"Obtenha acesso a recursos poderosos para um design de WordPress sem complicações, sem os altos custos. Com todo o tempo "
"que você economizará, é um produto que se paga sozinho!"

#: wizard/assets/build/wizard-app.js:1
msgid "Please complete the previous step before proceeding."
msgstr "Por favor, complete a etapa anterior antes de prosseguir."

#: cartflows.php
#. Author of the plugin
msgid "Brainstorm Force"
msgstr "Brainstorm Force"

#: cartflows.php
#. Author URI of the plugin
msgid "https://www.brainstormforce.com"
msgstr "https://www.brainstormforce.com"

#: admin-core/ajax/importer.php:1017
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$s article%3$s."
msgstr ""
"Erro de tempo limite de solicitação. Por favor, verifique se o firewall ou algum plugin de segurança está bloqueando as "
"solicitações HTTP/HTTPS de saída para templates.cartflows.com ou não. %1$sPara resolver este problema, por favor, "
"consulte este %2$s artigo%3$s."

#: admin-core/inc/admin-menu.php:288
#: admin-core/inc/admin-menu.php:290
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Modern Cart"
msgstr "Carrinho Moderno"

#: admin-core/inc/admin-menu.php:1038
msgid "OttoKit"
msgstr "OttoKit"

#: admin-core/inc/admin-menu.php:1039
msgid ""
"OttoKit helps people automate their work by integrating multiple apps and plugins, allowing them to share data and "
"perform tasks automatically."
msgstr ""
"OttoKit ajuda as pessoas a automatizar seu trabalho integrando vários aplicativos e plugins, permitindo que "
"compartilhem dados e realizem tarefas automaticamente."

#: admin-core/inc/admin-menu.php:1088
msgid "Modern Cart for WooCommerce"
msgstr "Carrinho Moderno para WooCommerce"

#: admin-core/inc/admin-menu.php:1089
msgid ""
"Modern Cart for WooCommerce that helps every shop owner improve their user experience, increase conversions & maximize "
"profits."
msgstr ""
"Carrinho Moderno para WooCommerce que ajuda todo proprietário de loja a melhorar a experiência do usuário, aumentar as "
"conversões e maximizar os lucros."

#: admin-core/inc/flow-meta.php:59
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Enable Instant Layout"
msgstr "Ativar Layout Instantâneo"

#: admin-core/inc/flow-meta.php:64
#. translators: %1$s: Break line, %2$s: link html Start, %3$s: Link html end.
msgid ""
"This layout will replace the default page template for the Checkout, Upsell/Downsell and Thank You steps. You can "
"customize the design %1$sin the Checkout, Upsell/Downsell and Thank You step's settings, under the design tab. %2$sRead "
"More.%3$s"
msgstr ""
"Este layout substituirá o modelo de página padrão para as etapas de Checkout, Upsell/Downsell e Obrigado. Você pode "
"personalizar o design %1$snas configurações das etapas de Checkout, Upsell/Downsell e Obrigado, na aba de design. "
"%2$sLeia mais.%3$s"

#: admin-core/inc/flow-meta.php:87
msgid "Custom Logo"
msgstr "Logotipo Personalizado"

#: admin-core/inc/flow-meta.php:92
msgid "If you've added a custom logo, it will show up here. If not, a default logo from the theme will be used instead."
msgstr "Se você adicionou um logotipo personalizado, ele aparecerá aqui. Caso contrário, um logotipo padrão do tema será usado. "

#: admin-core/inc/flow-meta.php:103
msgid "Minimum image size should be 130 x 40 in pixes for ideal display."
msgstr "O tamanho mínimo da imagem deve ser 130 x 40 pixels para uma exibição ideal."

#: admin-core/inc/flow-meta.php:143
msgid "Header Color"
msgstr "Cor do Cabeçalho"

#: admin-core/inc/flow-meta.php:249
msgid ""
"The Test Mode automatically adds sample products to your funnel if you haven't selected any. This helps you preview and "
"test the checkout experience easily."
msgstr ""
"O Modo de Teste adiciona automaticamente produtos de amostra ao seu funil se você não tiver selecionado nenhum. Isso "
"ajuda a visualizar e testar a experiência de checkout facilmente."

#: admin-core/inc/flow-meta.php:250
msgid ""
"The Test Mode automatically adds sample products to your store checkout funnel if you haven't selected any. This helps "
"you preview and test the experience easily on all steps except the Checkout page."
msgstr ""
"O Modo de Teste adiciona automaticamente produtos de amostra ao funil de checkout da sua loja se você não tiver "
"selecionado nenhum. Isso ajuda você a visualizar e testar a experiência facilmente em todas as etapas, exceto na página "
"de Checkout."

#: admin-core/inc/flow-meta.php:256
msgid "Disallow Indexing"
msgstr "Proibir Indexação"

#: admin-core/inc/flow-meta.php:257
msgid "Changing this will replace the default global setting. To go back to the global setting, just select Default."
msgstr "Alterar isso substituirá a configuração global padrão. Para voltar à configuração global, basta selecionar Padrão."

#: admin-core/inc/flow-meta.php:280
msgid "Any code you add here will work across all the pages in this funnel."
msgstr "Qualquer código que você adicionar aqui funcionará em todas as páginas deste funil."

#: admin-core/inc/global-settings.php:50
msgid "Allow full access to all settings to customize everything."
msgstr "Permitir acesso total a todas as configurações para personalizar tudo."

#: admin-core/inc/global-settings.php:55
msgid "Allow limited access to create, edit, delete, or import flows and steps."
msgstr "Permitir acesso limitado para criar, editar, excluir ou importar fluxos e etapas."

#: admin-core/inc/global-settings.php:349
#: admin-core/inc/global-settings.php:539
msgid "This event will trigger when someone subscribes or signs up on the opt-in page."
msgstr "Este evento será acionado quando alguém se inscrever ou registrar na página de opt-in."

#: admin-core/inc/global-settings.php:930
msgid "This option is only available for products that are part of a subscription."
msgstr "Esta opção está disponível apenas para produtos que fazem parte de uma assinatura."

#: admin-core/inc/global-settings.php:1415
msgid "Usage Tracking"
msgstr "Rastreamento de Uso"

#: admin-core/inc/global-settings.php:1417
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Allow CartFlows Inc products to track non-sensitive usage tracking data. %1$1s Learn More%2$2s."
msgstr "Permitir que os produtos da CartFlows Inc rastreiem dados de uso não sensíveis. %1$1s Saiba Mais%2$2s."

#: classes/class-cartflows-admin-notices.php:183
msgid "Hi there! You recently used CartFlows to build a sales funnel &mdash; Thanks a ton!"
msgstr "Olá! Você usou recentemente o CartFlows para criar um funil de vendas &mdash; Muito obrigado!"

#: classes/class-cartflows-admin-notices.php:184
msgid ""
"It would be awesome if you give us a 5-star review and share your experience on WordPress. Your reviews pump us up and "
"also help other WordPress users make a better decision when choosing CartFlows!"
msgstr ""
"Seria incrível se você nos desse uma avaliação de 5 estrelas e compartilhasse sua experiência no WordPress. Suas "
"avaliações nos motivam e também ajudam outros usuários do WordPress a tomar uma decisão melhor ao escolher o CartFlows!"

#: classes/class-cartflows-admin-notices.php:186
msgid "Ok, you deserve it"
msgstr "Ok, você merece isso"

#: classes/class-cartflows-admin-notices.php:188
msgid "Nope, maybe later"
msgstr "Não, talvez mais tarde"

#: classes/class-cartflows-admin-notices.php:189
msgid "I already did"
msgstr "Eu já fiz"

#: classes/class-cartflows-flow-frontend.php:90
msgid ""
"Test mode is currently enabled to help you preview your funnel. You can turn it off anytime from the funnel's settings "
"in the admin dashboard."
msgstr ""
"O modo de teste está atualmente ativado para ajudá-lo a visualizar seu funil. Você pode desativá-lo a qualquer momento "
"nas configurações do funil no painel de administração."

#: classes/class-cartflows-flow-frontend.php:91
msgid "Click here to disable it."
msgstr "Clique aqui para desativá-lo."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:119
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:131
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:162
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:145
#: modules/gutenberg/build/blocks.js:7
msgid "Legacy"
msgstr "Legado"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:120
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:132
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:163
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:149
#: modules/gutenberg/build/blocks.js:7
msgid "Modern"
msgstr "Moderno"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:132
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:146
#: modules/gutenberg/build/blocks.js:7
msgid "The Thank You Text is only applicable for the old layout."
msgstr "O texto de agradecimento é aplicável apenas ao layout antigo."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:322
msgid "Pick a background color for the left side of your Checkout page."
msgstr "Escolha uma cor de fundo para o lado esquerdo da sua página de Checkout."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:339
msgid "Pick a background color for the right side of your Checkout page."
msgstr "Escolha uma cor de fundo para o lado direito da sua página de finalização de compra."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:841
msgid "Change the background color of the payment description box to match your style."
msgstr "Altere a cor de fundo da caixa de descrição de pagamento para combinar com seu estilo."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1022
msgid "This is the name (slug) of the current step. Changing it will update the URL for this step, so be cautious!"
msgstr "Este é o nome (slug) da etapa atual. Alterá-lo atualizará o URL para esta etapa, portanto, seja cauteloso!"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1030
#: modules/landing/classes/class-cartflows-landing-meta-data.php:123
#: modules/optin/classes/class-cartflows-optin-meta-data.php:577
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:405
msgid "Add your own custom code here. If you're adding CSS, make sure to wrap it inside &lt;style&gt; tags."
msgstr ""
"Adicione seu próprio código personalizado aqui. Se você estiver adicionando CSS, certifique-se de envolvê-lo dentro de "
"tags &lt;style&gt;."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1044
msgid "Turn this ON to show your product images in the order review section."
msgstr "Ative isto para mostrar as imagens do seu produto na seção de revisão do pedido."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1052
msgid "Users can easily remove products from the checkout page if they decide not to purchase them."
msgstr "Os usuários podem facilmente remover produtos da página de checkout se decidirem não comprá-los."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1216
msgid "Turn this on to show a custom message when no shipping options are available at checkout."
msgstr "Ative isto para mostrar uma mensagem personalizada quando não houver opções de envio disponíveis no checkout."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1244
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:449
msgid "Choose this option to adjust where the order summary appears on mobile devices."
msgstr "Escolha esta opção para ajustar onde o resumo do pedido aparece em dispositivos móveis."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1297
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1305
msgid "This heading will only appear when you use the Modern Checkout style."
msgstr "Este cabeçalho só aparecerá quando você usar o estilo de Checkout Moderno."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1313
msgid "This message will appear next to the field name to show an error if something goes wrong."
msgstr "Esta mensagem aparecerá ao lado do nome do campo para mostrar um erro se algo der errado."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1346
msgid ""
"Customizes the text on the 'Place Order' button during checkout, allowing you to make it more relevant to your "
"customers."
msgstr ""
"Personaliza o texto no botão 'Fazer Pedido' durante o checkout, permitindo que você o torne mais relevante para seus "
"clientes."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1354
msgid ""
"Enabling this will add a lock icon to the 'Place Order' button on the checkout page, indicating secure payment "
"processing."
msgstr ""
"Ativar isso adicionará um ícone de cadeado ao botão 'Fazer Pedido' na página de checkout, indicando processamento de "
"pagamento seguro."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1363
msgid "This will display the total amount in the cart when you click the 'Place Order' button."
msgstr "Isso exibirá o valor total no carrinho quando você clicar no botão 'Fazer Pedido'."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:408
msgid "This heading will appear only when the Instant Layout option is used."
msgstr "Este título aparecerá apenas quando a opção de Layout Instantâneo for usada."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:139
msgid "Thank You Skin"
msgstr "Obrigado Pele"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:501
msgid "After submitting, users will be sent to this URL instead of the usual thank you page."
msgstr "Após o envio, os usuários serão redirecionados para este URL em vez da página de agradecimento habitual."

#: modules/thankyou/templates/instant-thankyou-order-details.php:35
#. Translators: Order ID.
msgid "Order #%s"
msgstr "Pedido nº %s"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:139
msgid "Type to search a funnel..."
msgstr "Digite para pesquisar um funil..."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Disabled"
msgstr "Desativado"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "upgrading to PRO"
msgstr "atualizando para PRO"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "activating CartFlows Pro"
msgstr "ativando o CartFlows Pro"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You're using"
msgstr "Você está usando"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "CartFlows Free"
msgstr "CartFlows Grátis"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "To unlock more features, consider"
msgstr "Para desbloquear mais recursos, considere"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Activate CartFlows Pro"
msgstr "Ativar CartFlows Pro"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Activated!"
msgstr "Ativado!"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid ""
"You can't edit this step directly because Instant Layout is turned on in the funnel settings. To make design changes, "
"go to the Design tab inside this step's settings."
msgstr ""
"Você não pode editar esta etapa diretamente porque o Layout Instantâneo está ativado nas configurações do funil. Para "
"fazer alterações de design, vá para a guia Design dentro das configurações desta etapa."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#. translators: %s: The current step type.
msgid "Use this setting to customize the style of the Instant %s Layout."
msgstr "Use esta configuração para personalizar o estilo do Layout Instantâneo %s."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Turn this on to set up rules that decide when visitors should be redirected to a special offer or the next step."
msgstr ""
"Ative isto para configurar regras que decidem quando os visitantes devem ser redirecionados para uma oferta especial ou "
"para o próximo passo."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Your email address can't be edited when using the Modern Checkout Style."
msgstr "Seu endereço de e-mail não pode ser editado ao usar o Estilo de Checkout Moderno."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "The Company field won't be visible if you're using the Instant Layout Style."
msgstr "O campo Empresa não estará visível se você estiver usando o Estilo de Layout Instantâneo."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from all your funnels combined."
msgstr "Isso mostra o valor total de dinheiro ganho de todos os seus funis combinados."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of orders placed through your CartFlows checkout pages."
msgstr "Isso mostra o número total de pedidos feitos através das suas páginas de checkout do CartFlows."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of times people visited any step in your funnel."
msgstr "Isso mostra o número total de vezes que as pessoas visitaram qualquer etapa no seu funil."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from your Upsell and Downsell offers."
msgstr "Isso mostra o valor total de dinheiro ganho com suas ofertas de Upsell e Downsell."

#: admin-core/assets/build/settings-app.js:35
msgid "Set up a Store Checkout in just one click:"
msgstr "Configure um Checkout de Loja com apenas um clique:"

#: admin-core/assets/build/settings-app.js:35
msgid "Thank You Page"
msgstr "Página de Agradecimento"

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Use ready-made templates from the CartFlows Library, our custom widget, or shortcodes on each page to set this up "
"easily—no coding needed!"
msgstr ""
"Use modelos prontos da Biblioteca CartFlows, nosso widget personalizado ou shortcodes em cada página para configurar "
"isso facilmente—sem necessidade de codificação!"

#: admin-core/assets/build/settings-app.js:80
msgid "Install OttoKit for Free"
msgstr "Instale o OttoKit gratuitamente"

#: admin-core/assets/build/settings-app.js:80
msgid "Visit OttoKit Website"
msgstr "Visite o site OttoKit"

#: admin-core/assets/build/settings-app.js:80
msgid "Here are a few simple examples of what OttoKit can do on your WooCommerce store:"
msgstr "Aqui estão alguns exemplos simples do que o OttoKit pode fazer na sua loja WooCommerce:"

#: admin-core/assets/build/settings-app.js:80
msgid "Join Thousands of Entrepreneurs Already Using OttoKit."
msgstr "Junte-se a milhares de empreendedores que já utilizam o OttoKit."

#: admin-core/assets/build/settings-app.js:80
msgid "Bonus ($200 Value)"
msgstr "Bônus (Valor de $200)"

#: admin-core/assets/build/settings-app.js:80
msgid "Access to OttoKit Pro Plan"
msgstr "Acesso ao Plano Pro do OttoKit"

#: admin-core/assets/build/settings-app.js:80
msgid "Plus - Annual"
msgstr "Plus - Anual"

#: admin-core/assets/build/settings-app.js:80
msgid "/ year"
msgstr "/ ano"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Annual"
msgstr "Pro - Anual"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (One Time Pay)"
msgstr "Pro - Vitalício (Pagamento Único)"

#: admin-core/assets/build/settings-app.js:80
msgid "for Lifetime"
msgstr "para toda a vida"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (11 x Split Pay)"
msgstr "Pro - Vitalício (11 x Pagamento Parcelado)"

#: admin-core/assets/build/settings-app.js:80
msgid "x 11 Months"
msgstr "x 11 Meses"

#: admin-core/assets/build/settings-app.js:80
msgid "Explore the key differences between Plus and Pro to find the perfect fit for your needs."
msgstr "Explore as principais diferenças entre Plus e Pro para encontrar a opção perfeita para suas necessidades."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Free vs Pro Image"
msgstr "Imagem do CartFlows Free vs Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Unlock Pro Features"
msgstr "Desbloquear Recursos Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Generate More Sales With CartFlows Pro!"
msgstr "Gere Mais Vendas com o CartFlows Pro!"

#: admin-core/assets/build/settings-app.js:80
msgid "And More…"
msgstr "E mais…"

#: admin-core/assets/build/settings-app.js:80
msgid "Buy Now"
msgstr "Compre Agora"

#: admin-core/assets/build/settings-app.js:80
msgid "View plans"
msgstr "Ver planos"

#: admin-core/assets/build/settings-app.js:80
msgid "Get Modern Cart Now"
msgstr "Obtenha o Carrinho Moderno Agora"

#: admin-core/assets/build/settings-app.js:80
msgid "Moderncart"
msgstr "Moderncart"

#: admin-core/assets/build/settings-app.js:81
#. translators: %s: line break
msgid "Your Cart Can Do More — Let’s Make It %sa Sales Machine!"
msgstr "Seu Carrinho Pode Fazer Mais — Vamos Transformá-lo %sem uma Máquina de Vendas!"

#: admin-core/assets/build/settings-app.js:81
msgid ""
"Transform your default WooCommerce cart into a high-converting, fast, and user-friendly shopping experience — designed "
"to keep customers engaged and ready to buy."
msgstr ""
"Transforme seu carrinho padrão do WooCommerce em uma experiência de compra de alta conversão, rápida e amigável — "
"projetada para manter os clientes engajados e prontos para comprar."

#: admin-core/assets/build/settings-app.js:81
msgid "Visit Modern Cart"
msgstr "Visite o Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Why Store Owners ❤️ Modern Cart"
msgstr "Por que os Proprietários de Lojas ❤️ o Carrinho Moderno"

#: admin-core/assets/build/settings-app.js:81
msgid "Trusted by Top Brands to Boost Conversions Instantly"
msgstr "Confiado pelas Principais Marcas para Aumentar as Conversões Instantaneamente"

#: admin-core/assets/build/settings-app.js:81
msgid "Brand logo"
msgstr "Logotipo da marca"

#: admin-core/assets/build/settings-app.js:81
msgid "Stop Losing Sales at the Cart — Fix It in Minutes!"
msgstr "Pare de perder vendas no carrinho — resolva isso em minutos!"

#: admin-core/assets/build/settings-app.js:81
msgid "Modern Cart is your instant upgrade for more sales, bigger orders, and smoother checkouts."
msgstr "Modern Cart é sua atualização instantânea para mais vendas, pedidos maiores e checkouts mais suaves."

#: wizard/assets/build/wizard-app.js:1
msgid "Learn more about usage tracking"
msgstr "Saiba mais sobre o rastreamento de uso"

#: wizard/assets/build/wizard-app.js:3
msgid "I agree to share anonymous usage data to help improve CartFlows."
msgstr "Concordo em compartilhar dados de uso anônimos para ajudar a melhorar o CartFlows."

#: wizard/assets/build/wizard-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag close
msgid ""
"We never collect personal info, Only anonymized data like PHP version, admin language, and feature usage. To learn what "
"we collect and why, see this %1$sdocument%2$s."
msgstr ""
"Nunca coletamos informações pessoais, apenas dados anonimizados como a versão do PHP, idioma do administrador e uso de "
"recursos. Para saber o que coletamos e por quê, veja este %1$sdocumento%2$s."

#: wizard/assets/build/wizard-app.js:5
#. translators: %1$s: Anchor tag one start, %2$s: Anchor tag one close, %3$s: Anchor tag two start, %4$s: Anchor tag two close.
msgid "By continuing you agree to our %1$sTerms%2$s and %3$sPrivacy Policy%4$s."
msgstr "Ao continuar, você concorda com nossos %1$sTermos%2$s e %3$sPolítica de Privacidade%4$s."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:266
msgctxt "Width."
msgid "Auto"
msgstr "Carro"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping %d"
msgstr "Envio %d"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping"
msgstr "Envio"

#: modules/flow/classes/class-cartflows-flow-post-type.php:103
msgctxt "flow general name"
msgid "Flows"
msgstr "Fluxos"

#: modules/flow/classes/class-cartflows-flow-post-type.php:104
msgctxt "flow singular name"
msgid "Flow"
msgstr "Fluxo"

#: modules/flow/classes/class-cartflows-step-post-type.php:170
msgctxt "flow step general name"
msgid "Steps"
msgstr "Passos"

#: modules/flow/classes/class-cartflows-step-post-type.php:171
msgctxt "flow step singular name"
msgid "Step"
msgstr "Passo"
