.wcf-plugin-update-notification {
	margin-bottom: 10px;
	max-width: 1000px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
.wcf-plugin-update-notification__separator {
	margin: 15px -12px;
}
.wcf-plugin-update-notification__icon {
	font-size: 17px;
	margin-right: 9px;
	margin-left: 2px;
}
.wcf-plugin-update-notification__title {
	font-weight: 600;
	margin-bottom: 10px;
}
.wcf-plugin-update-notification + p {
	display: none;
}

.notice-success .wcf-plugin-update-notification__separator {
	border: 1px solid #46b450;
}
.notice-success .wcf-plugin-update-notification__icon {
	color: #79ba49;
}

.notice-warning .wcf-plugin-update-notification__separator {
	border: 1px solid #ffb900;
}
.notice-warning .wcf-plugin-update-notification__icon {
	color: #f56e28;
}

.toplevel_page_cartflows .wp-submenu li:has( > a[href*="path=free-vs-pro"]) a{
    background-color: #00a32a !important;
    color: #fff !important;
	font-weight: 600;
}
