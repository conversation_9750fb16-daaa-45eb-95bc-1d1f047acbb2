$border-color: #d6d9dd;

.components-panel[data-uagb-tab] {
	z-index: 99;

	&:not([data-uagb-tab="advance"]) {

		.components-panel__body.block-editor-block-inspector__advanced,
		.components-panel__body.editor-block-inspector__advanced.block-editor-block-inspector__advanced,
		.block-editor-block-inspector > div > .components-panel__body {
			display: none;
		}
	}

	.block-editor-block-card {
		padding-bottom: 25px;
		position: relative;
		z-index: 1;
		overflow: hidden;

		&::before {
			content: "";
			position: absolute;
			top: 0;
			left: -16px;
			right: -16px;
			bottom: 0;
			background: #fff;
			z-index: -1;
		}
	}

	.components-panel__body.edit-post-settings-sidebar__panel-block {

		.block-editor-block-card {
			overflow: unset;

			&::before {
				top: -16px;
			}
		}
	}
}

.components-panel[data-uagb-tab="advance"] .block-editor-block-inspector__advanced {
	border-color: transparent;
}

.uagb-inspector-tabs {
	display: flex;
	//margin-left: -16px;
	//margin-right: -16px;
	position: relative;
	background: #fff;
	z-index: 9;

	&::after {
		content: "";
		position: absolute;
		left: 0;
		bottom: 0;
		top: 50%;
		right: 0;
		z-index: 0;
		background: #fff;
		pointer-events: none;
	}

	button {
		flex-grow: 1;
		flex-basis: 0;
		display: inline-flex;
		flex-direction: column;
		height: 65px;
		align-items: center;
		justify-content: center;
		border: none;
		position: relative;
		border-radius: 10px;
		background: #fff;
		cursor: pointer;

		&::after,
		&::before {
			z-index: 1;
			content: "";
			position: absolute;
			top: 0;
			left: -0.5px;
			right: -0.5px;
			bottom: 50%;
			border: 1px solid transparent;
			border-bottom: none;
			pointer-events: none;
		}

		&::after {
			top: 50%;
			bottom: 0;
			border-color: $border-color;
			border-bottom: 1px solid $border-color;
			border-top: none;
		}

		&:not(.uagb-active)::after {
			background: #fff;
		}

		&.uagb-active {
			background: #fff;
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;

			&::before {
				border-color: $border-color;
				border-top-color: #0085ba;
				border-top-width: 4px;
			}

			&::after {
				border-color: transparent;
			}

			+ button {
				border-bottom-right-radius: 0;
				border-bottom-left-radius: 10px;

				&::after {
					border-bottom-right-radius: 0;
					border-right-color: transparent;
				}

				+ button {
					border-bottom-left-radius: 0;

					&::after {
						border-bottom-left-radius: 0;
						border-left-color: transparent;
					}
				}
			}
		}

		&:first-child {
			border-bottom-left-radius: 0;

			&::after {
				border-bottom-left-radius: 0;
				border-left-color: transparent;
			}

			&.uagb-active::after {
				border-left-color: $border-color;
			}

			&::before,
			&::after {
				left: -1px;
			}
		}

		&:last-child {
			border-bottom-right-radius: 0;

			&::after {
				border-bottom-right-radius: 0;
				border-right-color: transparent;
			}

			&.uagb-active::after {
				border-right-color: $border-color;
			}

			&::before,
			&::after {
				right: 0;
			}
		}

		&:focus {
			outline: none;
		}

		h5 {
			margin: 6px 0 0;
			font-size: 13px;
			font-weight: 400;
			line-height: 1.2;
		}

		h5,
		svg {
			z-index: 2;
			fill: #585858;
		}

		&.uagb-active h5 {
			color: #0085ba;
		}

		&:focus path,
		&.uagb-active path {
			fill: #0085ba;
		}
	}

	&.advance.uagb-inspector-tabs-count-3 {

		button:first-child {
			border-bottom-right-radius: 0;

			&::after {
				border-bottom-right-radius: 0;
				border-right-color: transparent;
			}

			+ button {
				border-bottom-left-radius: 0;

				&::after {
					border-bottom-left-radius: 0;
					border-left-color: transparent;
				}
			}
		}
	}
}

.components-panel .components-panel__body .uagb-inspector-tabs-container {
	position: static !important;
	margin-left: -16px;
	margin-right: -16px;
	border-top: 0 !important;
	top: 0 !important;
}

.uagb-inspector-tabs-container {
	position: sticky;
	top: -3px;
	z-index: 9;
	border-top: calc(0.1em + 1px) solid #f2f4f5;
}

.uagb-inspector-tab .components-panel__body:first-child,
.edit-post-settings-sidebar__panel-block .uagb-inspector-tab .components-panel__body:first-child {
	margin-top: 0;
	border-top: none !important;
}
