# CartFlows Payment Redirector

Este plugin adiciona redirecionamento condicional após o pagamento no CartFlows, permitindo direcionar os clientes para diferentes páginas de acordo com o método de pagamento utilizado.

## Descrição

O CartFlows Payment Redirector resolve o problema de redirecionamento condicional após o checkout no CartFlows. Ao contrário da funcionalidade "Dynamic Offers" nativa do CartFlows, que pode apresentar problemas com certos métodos de pagamento, este plugin oferece uma solução robusta e fácil de configurar.

### Principais recursos:

- Redirecionamento automático baseado no método de pagamento
- Suporte a todos os métodos de pagamento do WooCommerce
- Interface de administração fácil de usar
- Preservação dos parâmetros de URL necessários para o funcionamento do CartFlows
- Integração perfeita com o CartFlows e WooCommerce

## Requisitos

- WordPress 5.0 ou superior
- WooCommerce 3.0 ou superior
- CartFlows 1.0 ou superior

## Instalação

1. Faça o upload dos arquivos do plugin para o diretório `/wp-content/plugins/cf-payment-redirector/`
2. Ative o plugin através do menu 'Plugins' no WordPress
3. Acesse 'CartFlows > Redirecionamento' para configurar as regras de redirecionamento

## Como usar

1. Crie seus funis de vendas normalmente no CartFlows, incluindo as páginas de agradecimento específicas para cada método de pagamento
2. Acesse o menu 'CartFlows > Redirecionamento'
3. Para cada funil, configure o redirecionamento desejado com base no método de pagamento
4. Selecione a página de destino para cada método de pagamento
5. Salve as configurações

## Suporte

Se você encontrar algum problema ou tiver alguma dúvida, entre em contato através do e-mail de suporte.

## Changelog

### 1.0.0
- Versão inicial do plugin

## Licença

GPL v2 ou posterior 