/**
 * This is a dummy file that contains blocks settings placeholder.
 * WordPress plugin directory detects registered blocks from this file and shows them on plugin page
 * It doesn't do anything in the actual code.
 *
 * It is auto-generated.
 */

registerBlockType( 'wcfb/checkout-form', { title: __( 'Checkout Form', 'cartflows' ) } );
registerBlockType( 'wcfb/next-step-button', { title: __( 'Next Step Button', 'cartflows' ) } );
registerBlockType( 'wcfb/optin-form', { title: __( 'Optin Form', 'cartflows' ) } );
registerBlockType( 'wcfb/order-detail-form', { title: __( 'Order Detail Form', 'cartflows' ) } );