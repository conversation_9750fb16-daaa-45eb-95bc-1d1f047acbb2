/**
 * Admin JS para CartFlows Payment Redirector
 */
(function($) {
    'use strict';

    // Quando o documento estiver pronto
    $(document).ready(function() {
        // Inicializar abas
        $('#cfpr-flow-tabs').tabs();
        
        // Quando mudar de fluxo, atualizar a lista de passos
        $('.cfpr-step-select').on('focus', function() {
            var select = $(this);
            var flowId = select.data('flow-id');
            
            // Verificar se já carregamos os dados antes
            if (select.hasClass('loaded')) {
                return;
            }
            
            // Marcamos como carregado para não buscar novamente
            select.addClass('loaded');
            
            // Obter passos deste fluxo via AJAX
            $.ajax({
                url: cfpr_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfpr_get_flow_steps',
                    flow_id: flowId,
                    nonce: cfpr_admin.nonce
                },
                beforeSend: function() {
                    select.find('option:first').text('Carregando...');
                },
                success: function(response) {
                    // Se a resposta for bem-sucedida e houver passos
                    if (response.success && response.data.steps) {
                        // Salvar valor atual
                        var currentValue = select.val();
                        
                        // Limpar as opções antigas e adicionar a opção padrão
                        select.empty().append('<option value="">-- Nenhum --</option>');
                        
                        // Adicionar os novos passos
                        $.each(response.data.steps, function(stepId, stepTitle) {
                            select.append('<option value="' + stepId + '">' + stepTitle + '</option>');
                        });
                        
                        // Restaurar valor selecionado
                        if (currentValue) {
                            select.val(currentValue);
                        }
                    }
                },
                error: function() {
                    select.find('option:first').text('Erro ao carregar passos');
                }
            });
        });
    });

})(jQuery); 