/**
 * Admin JS para CartFlows Payment Redirector
 * Integrado com padrões do CartFlows Pro
 */
(function($) {
    'use strict';

    /**
     * Objeto principal do admin
     */
    const CFPRAdmin = {

        /**
         * Inicializar
         */
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.addLoadingStates();
        },

        /**
         * Vincular eventos
         */
        bindEvents: function() {
            // Quando mudar de fluxo, atualizar a lista de passos
            $(document).on('focus', '.cfpr-step-select', this.loadFlowSteps);

            // Adicionar feedback visual para formulário
            $(document).on('submit', '.cfpr-admin-settings form', this.handleFormSubmit);
        },

        /**
         * Inicializar abas com animações suaves
         */
        initTabs: function() {
            $('#cfpr-flow-tabs').tabs({
                activate: function(event, ui) {
                    // Adicionar animação suave ao trocar de aba
                    ui.newPanel.hide().fadeIn(300);
                },
                beforeActivate: function(event, ui) {
                    // Adicionar classe de loading
                    ui.newPanel.addClass('cfpr-loading');

                    // Remover classe após um tempo
                    setTimeout(function() {
                        ui.newPanel.removeClass('cfpr-loading');
                    }, 200);
                }
            });
        },

        /**
         * Adicionar estados de loading
         */
        addLoadingStates: function() {
            // Adicionar spinner CSS para elementos de loading
            if (!$('#cfpr-loading-styles').length) {
                $('head').append(`
                    <style id="cfpr-loading-styles">
                        .cfpr-loading {
                            position: relative;
                            opacity: 0.7;
                        }
                        .cfpr-loading::before {
                            content: '';
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            width: 20px;
                            height: 20px;
                            margin: -10px 0 0 -10px;
                            border: 2px solid #f06335;
                            border-top: 2px solid transparent;
                            border-radius: 50%;
                            animation: cfpr-spin 1s linear infinite;
                            z-index: 1000;
                        }
                        @keyframes cfpr-spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                `);
            }
        },

        /**
         * Carregar passos do fluxo
         */
        loadFlowSteps: function() {
            var select = $(this);
            var flowId = select.data('flow-id');

            // Verificar se já carregamos os dados antes
            if (select.hasClass('loaded')) {
                return;
            }

            // Marcamos como carregado para não buscar novamente
            select.addClass('loaded');

            // Adicionar estado de loading
            select.addClass('cfpr-loading');

            // Obter passos deste fluxo via AJAX
            $.ajax({
                url: cfpr_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfpr_get_flow_steps',
                    flow_id: flowId,
                    nonce: cfpr_admin.nonce
                },
                beforeSend: function() {
                    select.find('option:first').text('Carregando...');
                },
                success: function(response) {
                    // Se a resposta for bem-sucedida e houver passos
                    if (response.success && response.data.steps) {
                        // Salvar valor atual
                        var currentValue = select.val();

                        // Limpar as opções antigas e adicionar a opção padrão
                        select.empty().append('<option value="">-- Nenhum --</option>');

                        // Adicionar os novos passos
                        $.each(response.data.steps, function(stepId, stepTitle) {
                            select.append('<option value="' + stepId + '">' + stepTitle + '</option>');
                        });

                        // Restaurar valor selecionado
                        if (currentValue) {
                            select.val(currentValue);
                        }

                        // Mostrar notificação de sucesso
                        CFPRAdmin.showNotice('Passos carregados com sucesso!', 'success');
                    } else {
                        CFPRAdmin.showNotice('Erro ao carregar passos do fluxo.', 'error');
                    }
                },
                error: function() {
                    select.find('option:first').text('Erro ao carregar passos');
                    CFPRAdmin.showNotice('Erro de conexão ao carregar passos.', 'error');
                },
                complete: function() {
                    // Remover estado de loading
                    select.removeClass('cfpr-loading');
                }
            });
        },

        /**
         * Manipular envio do formulário
         */
        handleFormSubmit: function(e) {
            var form = $(this);
            var submitButton = form.find('.button-primary');

            // Adicionar estado de loading ao botão
            submitButton.addClass('cfpr-loading').prop('disabled', true);

            // Simular delay para feedback visual
            setTimeout(function() {
                submitButton.removeClass('cfpr-loading').prop('disabled', false);
            }, 1000);
        },

        /**
         * Mostrar notificação
         */
        showNotice: function(message, type) {
            type = type || 'info';

            var notice = $(`
                <div class="notice notice-${type} is-dismissible cfpr-notice" style="display: none;">
                    <p>${message}</p>
                </div>
            `);

            // Adicionar após o cabeçalho
            $('.wcf-menu-page-header').after(notice);

            // Mostrar com animação
            notice.slideDown(300);

            // Auto-remover após 5 segundos
            setTimeout(function() {
                notice.slideUp(300, function() {
                    notice.remove();
                });
            }, 5000);
        }
    };

    /**
     * Inicializar quando o documento estiver pronto
     */
    $(document).ready(function() {
        CFPRAdmin.init();
    });

})(jQuery);