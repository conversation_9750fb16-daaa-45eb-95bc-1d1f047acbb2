msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: gpt-po v1.1.1\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-04T15:33:29+00:00\n"
"PO-Revision-Date: 2025-02-04T15:33:29+00:00\n"
"Language: \n"

#: cartflows.php
#: classes/class-cartflows-admin-notices.php:217
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:62
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:71
#. Plugin Name of the plugin
msgid "CartFlows"
msgstr "CartFlows"

#: cartflows.php
#. Plugin URI of the plugin
msgid "https://cartflows.com/"
msgstr "https://cartflows.com/"

#: cartflows.php
#. Description of the plugin
msgid "Create beautiful checkout pages & sales flows for WooCommerce."
msgstr "Maak prachtige afrekenpagina's en verkoopstromen voor WooCommerce."

#: admin-core/ajax/ab-steps.php:89
#. translators: %s step id
msgid "Can't create a variation for this step - %s, Invalid Step ID."
msgstr "Kan geen variatie maken voor deze stap - %s, Ongeldige Stap-ID."

#: admin-core/ajax/ab-steps.php:105
#. translators: %s flow id
msgid "Step successfully hidden - %s"
msgstr "Stap succesvol verborgen - %s"

#: admin-core/ajax/ab-steps.php:140
#. translators: %s step id
msgid "Can't delete a variation for this step - %s, Invalid Step Id or Funnel Id."
msgstr "Kan een variatie voor deze stap niet verwijderen - %s, Ongeldige Stap-ID of Trechter-ID."

#: admin-core/ajax/ab-steps.php:188
#. translators: %s flow id
msgid "Step deleted - %s"
msgstr "Stap verwijderd - %s"

#: admin-core/ajax/ab-steps.php:223
#. translators: %s step id
msgid "Can't create a variation for this step - %s"
msgstr "Kan geen variatie maken voor deze stap - %s"

#: admin-core/ajax/ab-steps.php:279
#. translators: %s step id
msgid "A/B test settings updated for this step - %s"
msgstr "A/B-testinstellingen bijgewerkt voor deze stap - %s"

#: admin-core/ajax/ajax-errors.php:59
#: wizard/ajax/ajax-errors.php:59
msgid "Sorry, you are not allowed to do this operation."
msgstr "Sorry, je mag deze bewerking niet uitvoeren."

#: admin-core/ajax/ajax-errors.php:60
#: admin-core/ajax/common-settings.php:217
#: admin-core/ajax/common-settings.php:279
#: admin-core/ajax/common-settings.php:385
#: admin-core/ajax/common-settings.php:418
#: admin-core/inc/meta-ops.php:32
#: modules/checkout/classes/class-cartflows-checkout-ajax.php:110
#: wizard/ajax/ajax-errors.php:60
msgid "Nonce validation failed"
msgstr "Nonce-validatie mislukt"

#: admin-core/ajax/ajax-errors.php:61
#: wizard/ajax/ajax-errors.php:61
msgid "Sorry, something went wrong."
msgstr "Sorry, er is iets misgegaan."

#: admin-core/ajax/ajax-errors.php:62
msgid "Required parameter is missing from the posted data."
msgstr "Vereist parameter ontbreekt in de geposte gegevens."

#: admin-core/ajax/common-settings.php:85
msgid "Successfully deleted the dynamic CSS keys!"
msgstr "De dynamische CSS-sleutels zijn succesvol verwijderd!"

#: admin-core/ajax/common-settings.php:105
msgid "No post data found!"
msgstr "Geen postgegevens gevonden!"

#: admin-core/ajax/common-settings.php:152
msgid "Successfully saved data!"
msgstr "Gegevens succesvol opgeslagen!"

#: admin-core/ajax/debugger.php:82
#: admin-core/ajax/debugger.php:133
#: admin-core/ajax/debugger.php:157
msgid "You don't have permission to perform this action."
msgstr "Je hebt geen toestemming om deze actie uit te voeren."

#: admin-core/ajax/debugger.php:91
msgid "Sync Success."
msgstr "Synchronisatie geslaagd."

#: admin-core/ajax/debugger.php:105
#: admin-core/inc/log-status.php:79
msgid "You don't have permission to view this page."
msgstr "Je hebt geen toestemming om deze pagina te bekijken."

#: admin-core/ajax/debugger.php:139
#: admin-core/inc/log-status.php:175
msgid "Filename is empty. Please refresh the page and retry."
msgstr "Bestandsnaam is leeg. Vernieuw de pagina en probeer het opnieuw."

#: admin-core/ajax/debugger.php:174
#: admin-core/inc/log-status.php:210
msgid "Invalid file."
msgstr "Ongeldig bestand."

#: admin-core/ajax/debugger.php:181
msgid "Export logs successfully"
msgstr "Logboeken succesvol geëxporteerd"

#: admin-core/ajax/flows.php:97
msgid "No Funnel IDs has been supplied to export!"
msgstr "Er zijn geen trechter-ID's opgegeven om te exporteren!"

#: admin-core/ajax/flows.php:110
#: admin-core/ajax/importer.php:109
#: admin-core/ajax/importer.php:212
msgid "Funnel exported successfully"
msgstr "Trechter succesvol geëxporteerd"

#: admin-core/ajax/flows.php:142
msgid "Can't update the flow data"
msgstr "Kan de stroomgegevens niet bijwerken"

#: admin-core/ajax/flows.php:159
#: admin-core/ajax/steps.php:415
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "(no title)"
msgstr "(geen titel)"

#: admin-core/ajax/flows.php:184
msgid "Successfully saved the flow data!"
msgstr "De stroomgegevens zijn succesvol opgeslagen!"

#: admin-core/ajax/flows.php:246
msgid "Successfully deleted the Funnels!"
msgstr "Trechters succesvol verwijderd!"

#: admin-core/ajax/flows.php:274
#: admin-core/ajax/flows.php:352
#: admin-core/ajax/flows.php:988
msgid "No Funnel IDs has been supplied to delete!"
msgstr "Er zijn geen trechter-ID's opgegeven om te verwijderen!"

#: admin-core/ajax/flows.php:323
#: admin-core/ajax/flows.php:391
msgid "Successfully trashed the Funnels!"
msgstr "De trechters zijn succesvol verwijderd!"

#: admin-core/ajax/flows.php:422
msgid "Invalid Funnel ID has been supplied to update title."
msgstr "Ongeldige trechter-ID is opgegeven om de titel bij te werken."

#: admin-core/ajax/flows.php:427
msgid "Can't update the flow title"
msgstr "Kan de stroomtitel niet bijwerken"

#: admin-core/ajax/flows.php:443
#. translators: %s flow id
msgid "Funnel title updated - %s"
msgstr "Trechtertitel bijgewerkt - %s"

#: admin-core/ajax/flows.php:468
msgid "Invalid Funnel ID has been supplied to clone!"
msgstr "Ongeldige trechter-ID is opgegeven om te klonen!"

#: admin-core/ajax/flows.php:502
msgid "Invalid Funnel ID has been supplied to duplicate!"
msgstr "Ongeldige trechter-ID is opgegeven om te dupliceren!"

#: admin-core/ajax/flows.php:679
msgid "Successfully cloned the Funnel!"
msgstr "Trechter succesvol gekloond!"

#: admin-core/ajax/flows.php:708
msgid "Invalid Funnel ID has been supplied to restore!"
msgstr "Ongeldige trechter-ID is opgegeven om te herstellen!"

#: admin-core/ajax/flows.php:748
msgid "Successfully restored the Funnel!"
msgstr "Trechter succesvol hersteld!"

#: admin-core/ajax/flows.php:775
msgid "Invalid Funnel ID has been supplied to trash!"
msgstr "Ongeldige trechter-ID is opgegeven om te verwijderen!"

#: admin-core/ajax/flows.php:814
msgid "Successfully trashed the Funnel!"
msgstr "De trechter is succesvol verwijderd!"

#: admin-core/ajax/flows.php:841
msgid "Invalid Funnel ID has been supplied to delete!"
msgstr "Ongeldige trechter-ID is opgegeven om te verwijderen!"

#: admin-core/ajax/flows.php:882
msgid "Successfully deleted the Funnel!"
msgstr "Trechter succesvol verwijderd!"

#: admin-core/ajax/flows.php:909
msgid "Invalid Funnel IDs has been supplied to update status!"
msgstr "Ongeldige trechter-ID's zijn verstrekt om de status bij te werken!"

#: admin-core/ajax/flows.php:958
#: admin-core/ajax/flows.php:1017
msgid "Successfully updated the Funnel status!"
msgstr "De status van de Funnel is succesvol bijgewerkt!"

#: admin-core/ajax/flows.php:1057
msgid "Invalid flow ID has been provided."
msgstr "Ongeldige flow-ID is opgegeven."

#: admin-core/ajax/flows.php:1073
#. translators: %s flow id
msgid "Steps not sorted for flow - %s"
msgstr "Stappen niet gesorteerd voor flow - %s"

#: admin-core/ajax/flows.php:1113
#. translators: %s flow id
msgid "Steps sorted for flow - %s"
msgstr "Stappen gesorteerd voor flow - %s"

#: admin-core/ajax/flows.php:1146
msgid "No Funnel ID is been supplied"
msgstr "Er is geen trechter-ID opgegeven"

#: admin-core/ajax/flows.php:1159
#. translators: %s flow id
msgid "Notice Dismissed"
msgstr "Kennisgeving verworpen"

#: admin-core/ajax/importer.php:116
msgid "No Funnels to export"
msgstr "Geen trechters om te exporteren"

#: admin-core/ajax/importer.php:205
msgid "Invalid flow ID."
msgstr "Ongeldige flow-ID."

#: admin-core/ajax/importer.php:392
msgid "Invalid Funnel Id has been provided."
msgstr "Ongeldige trechter-ID is opgegeven."

#: admin-core/ajax/importer.php:407
#. translators: %s: step ID
msgid "Invalid step id %1$s."
msgstr "Ongeldige stap-id %1$s."

#: admin-core/ajax/importer.php:414
msgid "Successfully created the step!"
msgstr "De stap is succesvol aangemaakt!"

#: admin-core/ajax/importer.php:516
msgid "Theme Activated"
msgstr "Thema geactiveerd"

#: admin-core/ajax/importer.php:575
#: admin-core/ajax/importer.php:590
#: modules/flow/classes/class-cartflows-step-post-type.php:262
#: wizard/ajax/wizard.php:717
msgid "Checkout"
msgstr "Afrekenen"

#: admin-core/ajax/importer.php:579
#: admin-core/ajax/importer.php:594
#: admin-core/ajax/importer.php:606
#: modules/flow/classes/class-cartflows-step-post-type.php:269
#: wizard/ajax/wizard.php:721
#: wizard/ajax/wizard.php:732
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Thank You"
msgstr "Dank je"

#: admin-core/ajax/importer.php:586
msgid "Sales Landing"
msgstr "Verkooplandingspagina"

#: admin-core/ajax/importer.php:602
#: modules/flow/classes/class-cartflows-step-post-type.php:248
#: wizard/ajax/wizard.php:728
msgid "Landing"
msgstr "Landing"

#: admin-core/ajax/importer.php:661
#: wizard/ajax/wizard.php:805
msgid "Successfully created the Funnel!"
msgstr "Trechter succesvol aangemaakt!"

#: admin-core/ajax/importer.php:719
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "CartFlows Pro vereist! %1$sUpgrade naar CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:721
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$supgrade to CartFlows Pro%2$s."
msgstr "Om de premium flow te importeren %1$supgrade naar CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:724
#: wizard/ajax/wizard.php:544
#. translators: %1$s: link html start, %2$s: link html end
msgid "Activate the CartFlows Pro to import the flow! %1$sActivate CartFlows Pro%2$s"
msgstr "Activeer de CartFlows Pro om de flow te importeren! %1$sActiveer CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:726
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate Cartflows Pro%2$s and validate the license key."
msgstr "Om de premium flow te importeren, %1$sactiveer Cartflows Pro%2$s en valideer de licentiesleutel."

#: admin-core/ajax/importer.php:729
#. translators: %1$s: link html start, %2$s: link html end
msgid "Invalid License Key! %1$sActivate CartFlows Pro%2$s"
msgstr "Ongeldige licentiesleutel! %1$sActiveer CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:731
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate CartFlows Pro%2$s."
msgstr "Om de premium flow te importeren, %1$sactiveer CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:744
#: admin-core/ajax/importer.php:1056
msgid "Funnel data not found."
msgstr "Trechtergegevens niet gevonden."

#: admin-core/ajax/importer.php:791
msgid "Steps not found."
msgstr "Stappen niet gevonden."

#: admin-core/ajax/importer.php:824
#: wizard/ajax/wizard.php:642
msgid "Successfully imported the Flow!"
msgstr "De Flow is succesvol geïmporteerd!"

#: admin-core/ajax/importer.php:873
msgid "Step data ID not found for import."
msgstr "Stapgegevens-ID niet gevonden voor import."

#: admin-core/ajax/importer.php:885
msgid "Funnel ID not found in the request."
msgstr "Trechter-ID niet gevonden in het verzoek."

#: admin-core/inc/admin-helper.php:1127
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$1sTo resolve this issue, please check this %2$2sarticle%3$3s."
msgstr ""
"Time-outfout bij verzoek. Controleer of de firewall of een beveiligingsplugin de uitgaande HTTP/HTTPS-verzoeken naar "
"templates.cartflows.com blokkeert of niet. %1$1sOm dit probleem op te lossen, bekijk dit %2$2sartikel%3$3s."

#: admin-core/ajax/importer.php:915
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sUpgrade to CartFlows Pro.%2$s"
msgstr "%1$sUpgrade naar CartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:916
msgid "To import the premium step, please upgrade to CartFlows Pro"
msgstr "Om de premiumstap te importeren, upgrade naar CartFlows Pro"

#: admin-core/ajax/importer.php:919
#: admin-core/ajax/importer.php:1041
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro%2$s"
msgstr "%1$sActiveer CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:920
msgid "To import the premium step activate Cartflows Pro and validate the license key."
msgstr "Om de premiumstap te importeren, activeer Cartflows Pro en valideer de licentiesleutel."

#: admin-core/ajax/importer.php:923
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro License %2$s"
msgstr "%1$sActiveer CartFlows Pro-licentie %2$s"

#: admin-core/ajax/importer.php:924
msgid "To import the premium step activate the CartFlows Pro."
msgstr "Om de premiumstap te importeren, activeer CartFlows Pro."

#: admin-core/ajax/importer.php:959
#: admin-core/ajax/importer.php:1080
msgid "Step data not found."
msgstr "Stapgegevens niet gevonden."

#: admin-core/ajax/importer.php:967
#: admin-core/ajax/importer.php:1088
msgid "Successfully imported the Step!"
msgstr "De stap is succesvol geïmporteerd!"

#: admin-core/ajax/importer.php:1038
#. translators: %1$s: link html start, %2$s: link html end
msgid "Upgrade to %1$sCartFlows Pro.%2$s"
msgstr "Upgrade naar %1$sCartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:1044
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro license is not active. Activate %1$sCartFlows Pro License %2$s"
msgstr "CartFlows Pro-licentie is niet actief. Activeer %1$sCartFlows Pro-licentie %2$s"

#: admin-core/ajax/importer.php:1112
#: admin-core/ajax/importer.php:1198
#. translators: %s: step ID
msgid "Invalid step id %1$s or post id %2$s."
msgstr "Ongeldige stap-id %1$s of post-id %2$s."

#: admin-core/ajax/importer.php:1175
#: admin-core/inc/admin-menu.php:1194
#: admin-core/inc/store-checkout.php:110
msgid "Nonce verification failed."
msgstr "Verificatie van nonce mislukt."

#: admin-core/ajax/importer.php:1454
#: wizard/ajax/wizard.php:388
msgid "Successful!"
msgstr "Succesvol!"

#: admin-core/ajax/meta-data.php:143
#. Translators: %d stock amount
msgid "Stock: %d"
msgstr "Voorraad: %d"

#: admin-core/ajax/meta-data.php:271
msgid "On backorder"
msgstr "In nabestelling"

#: admin-core/ajax/meta-data.php:274
msgid "In stock"
msgstr "Op voorraad"

#: admin-core/ajax/meta-data.php:277
msgid "Out of stock"
msgstr "Niet op voorraad"

#: admin-core/ajax/setup-page.php:84
msgid "Setup page dismissed successfully."
msgstr "Instellingspagina succesvol gesloten."

#: admin-core/ajax/steps.php:91
msgid "Can't update the step title"
msgstr "Kan de staptitel niet bijwerken"

#: admin-core/ajax/steps.php:112
#. translators: %s flow id
msgid "Step title updated - %s"
msgstr "Stap titel bijgewerkt - %s"

#: admin-core/ajax/steps.php:148
#. translators: %s flow id
msgid "Can't clone this step - %1$s. Flow - %2$s"
msgstr "Kan deze stap niet klonen - %1$s. Flow - %2$s"

#: admin-core/ajax/steps.php:267
#. translators: %s flow id
msgid "Step - %1$s cloned. Flow - %2$s"
msgstr "Stap - %1$s gekloond. Stroom - %2$s"

#: admin-core/ajax/steps.php:315
#. translators: %s flow id
msgid "Step not deleted for flow - %s"
msgstr "Stap niet verwijderd voor flow - %s"

#: admin-core/ajax/steps.php:358
#. translators: %s flow id
msgid "Step deleted for flow - %s"
msgstr "Stap verwijderd voor flow - %s"

#: admin-core/ajax/steps.php:367
#. translators: %s flow id
msgid "This step can not be deleted."
msgstr "Deze stap kan niet worden verwijderd."

#: admin-core/ajax/steps.php:400
#. translators: %s flow id
msgid "Invalid Step Id has been provided."
msgstr "Ongeldige stap-ID is opgegeven."

#: admin-core/ajax/steps.php:451
#. translators: %s flow id
msgid "Data saved successfully for step id %s"
msgstr "Gegevens succesvol opgeslagen voor stap-id %s"

#: admin-core/api/common-settings.php:129
#: admin-core/api/flow-data.php:139
#: admin-core/api/flows.php:287
#: admin-core/api/home-page.php:172
#: admin-core/api/product/product-data.php:121
#: admin-core/api/step-data.php:144
msgid "Sorry, you cannot list resources."
msgstr "Sorry, je kunt geen middelen opsommen."

#: admin-core/api/flow-data.php:70
msgid "Flow ID."
msgstr "Flow-ID."

#: admin-core/api/flows.php:194
#: modules/flow/classes/class-cartflows-flow-post-type.php:229
#: admin-core/assets/build/settings-app.js:80
msgid "View"
msgstr "Bekijken"

#: admin-core/api/flows.php:202
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Edit"
msgstr "Bewerken"

#: admin-core/api/flows.php:210
#: admin-core/inc/admin-helper.php:763
#: admin-core/inc/admin-helper.php:845
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate"
msgstr "Dupliceren"

#: admin-core/api/flows.php:217
#: admin-core/assets/build/settings-app.js:32
msgid "Export"
msgstr "Exporteren"

#: admin-core/api/flows.php:224
#: admin-core/inc/admin-helper.php:780
#: admin-core/inc/admin-helper.php:854
#: admin-core/inc/admin-helper.php:922
#: admin-core/inc/admin-helper.php:942
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Delete"
msgstr "Verwijderen"

#: admin-core/api/product/product-data.php:68
#: admin-core/api/step-data.php:69
msgid "Step ID."
msgstr "Stap-ID."

#: admin-core/inc/admin-helper.php:580
#: admin-core/inc/flow-meta.php:262
#: classes/class-cartflows-helper.php:1412
#: classes/class-cartflows-helper.php:1429
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:167
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:194
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:96
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1211
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:103
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:442
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:228
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:143
#: modules/optin/classes/class-cartflows-optin-meta-data.php:305
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:50
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Default"
msgstr "Standaard"

#: admin-core/inc/admin-helper.php:597
msgid "System Fonts"
msgstr "Systeemlettertypen"

#: admin-core/inc/admin-helper.php:615
msgid "Google Fonts"
msgstr "Google Fonts"

#: admin-core/inc/admin-helper.php:772
msgid "A/B Test"
msgstr "A/B-test"

#: admin-core/inc/admin-helper.php:797
msgid "Automation"
msgstr "Automatisering"

#: admin-core/inc/admin-helper.php:800
msgid "(Connect)"
msgstr "(Verbinden)"

#: admin-core/inc/admin-helper.php:862
msgid "Archive"
msgstr "Archief"

#: admin-core/inc/admin-helper.php:869
msgid "Declare as Winner"
msgstr "Als winnaar verklaren"

#: admin-core/inc/admin-helper.php:913
msgid "Deleted variation can't be restored."
msgstr "Verwijderde variatie kan niet worden hersteld."

#: admin-core/inc/admin-helper.php:914
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:188
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:200
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:212
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:224
msgid "Hide"
msgstr "Verbergen"

#: admin-core/inc/admin-helper.php:934
#: admin-core/assets/build/settings-app.js:32
msgid "Restore"
msgstr "Herstellen"

#: admin-core/inc/admin-helper.php:1115
msgid "Ooops! Something went wrong. Please open a support ticket from the website."
msgstr "Oeps! Er is iets misgegaan. Open alstublieft een supportticket via de website."

#: admin-core/inc/admin-helper.php:1116
msgid "No error found."
msgstr "Geen fout gevonden."

#: admin-core/inc/admin-helper.php:1141
#. translators: %1$s: HTML, %2$s: HTML, %3$s: HTML
msgid ""
"Sorry for the inconvenience, but your website seems to be having trouble connecting to our server. %1$s Please open a "
"technical %2$ssupport ticket%3$s and share the server's outgoing IP address."
msgstr ""
"Sorry voor het ongemak, maar uw website lijkt problemen te hebben met het verbinden met onze server. %1$s Gelieve een "
"technisch %2$ssteunticket%3$s te openen en het uitgaande IP-adres van de server te delen."

#: admin-core/inc/admin-helper.php:1143
msgid "Server's outgoing IP address: "
msgstr "Uitgaande IP-adres van de server:"

#: admin-core/inc/admin-menu.php:123
#: admin-core/inc/admin-menu.php:174
#: classes/class-cartflows-flow-frontend.php:70
#: admin-core/assets/build/settings-app.js:32
msgid "Edit Funnel"
msgstr "Trechter bewerken"

#: admin-core/inc/admin-menu.php:210
msgid "Go to Funnel Editing"
msgstr "Ga naar Trechter Bewerken"

#: admin-core/inc/admin-menu.php:258
#: admin-core/inc/admin-menu.php:259
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:25
msgid "Funnels"
msgstr "Trechters"

#: admin-core/inc/admin-menu.php:269
#: admin-core/inc/admin-menu.php:270
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: wizard/assets/build/wizard-app.js:1
msgid "Store Checkout"
msgstr "Winkelafrekenen"

#: admin-core/inc/admin-menu.php:278
#: admin-core/inc/admin-menu.php:280
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Automations"
msgstr "Automatiseringen"

#: admin-core/inc/admin-menu.php:280
#: admin-core/inc/admin-menu.php:290
msgid "New"
msgstr "Nieuw"

#: admin-core/inc/admin-menu.php:298
#: admin-core/inc/admin-menu.php:299
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Add-ons"
msgstr "Invoegtoepassingen"

#: admin-core/inc/admin-menu.php:308
#: admin-core/inc/admin-menu.php:309
msgid "Setup"
msgstr "Installatie"

#: admin-core/inc/admin-menu.php:328
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Dashboard"
msgstr "Dashboard"

#: admin-core/inc/admin-menu.php:541
msgid "Thin 100"
msgstr "Dun 100"

#: admin-core/inc/admin-menu.php:542
msgid "Extra-Light 200"
msgstr "Extra-Light 200"

#: admin-core/inc/admin-menu.php:543
msgid "Light 300"
msgstr "Licht 300"

#: admin-core/inc/admin-menu.php:544
msgid "Normal 400"
msgstr "Normaal 400"

#: admin-core/inc/admin-menu.php:545
msgid "Medium 500"
msgstr "Middel 500"

#: admin-core/inc/admin-menu.php:546
msgid "Semi-Bold 600"
msgstr "Semi-Bold 600"

#: admin-core/inc/admin-menu.php:547
msgid "Bold 700"
msgstr "Vet 700"

#: admin-core/inc/admin-menu.php:548
msgid "Extra-Bold 800"
msgstr "Extra-Vet 800"

#: admin-core/inc/admin-menu.php:549
msgid "Ultra-Bold 900"
msgstr "Ultra-Vet 900"

#: admin-core/inc/admin-menu.php:634
#. Translators: %1$s is the required page builder title, %2$s is the opening anchor tag to plugins.php, %3$s is the closing anchor tag, %4$s is the plugin title.
msgid "The default page builder is set to %1$s. Please %2$sinstall & activate%3$s the %4$s to start editing the steps."
msgstr ""
"De standaard paginabouwer is ingesteld op %1$s. Gelieve %2$sinstalleren en activeren%3$s van de %4$s om te beginnen met "
"het bewerken van de stappen."

#: admin-core/inc/admin-menu.php:1002
msgid "Stripe Payments For WooCommerce"
msgstr "Stripe-betalingen voor WooCommerce"

#: admin-core/inc/admin-menu.php:1003
msgid "Accept credit card payments in your store with Stripe for WooCommerce."
msgstr "Accepteer creditcardbetalingen in uw winkel met Stripe voor WooCommerce."

#: admin-core/inc/admin-menu.php:1014
msgid "PayPal Payments For WooCommerce"
msgstr "PayPal-betalingen voor WooCommerce"

#: admin-core/inc/admin-menu.php:1015
msgid "Accept payments in your store with PayPal for WooCommerce."
msgstr "Accepteer betalingen in uw winkel met PayPal voor WooCommerce."

#: admin-core/inc/admin-menu.php:1026
msgid "WooCommerce"
msgstr "WooCommerce"

#: admin-core/inc/admin-menu.php:1027
msgid "WooCommerce is a customizable, open-source ecommerce platform built on WordPress."
msgstr "WooCommerce is een aanpasbaar, open-source e-commerceplatform gebouwd op WordPress."

#: admin-core/inc/admin-menu.php:1051
msgid "SureMembers"
msgstr "SureMembers"

#: admin-core/inc/admin-menu.php:1065
msgid "Transform your WordPress form-building experience with stunning designs, ai integration, and no-code flexibility."
msgstr "Transformeer je WordPress-formulierbouwervaring met verbluffende ontwerpen, AI-integratie en no-code flexibiliteit."

#: admin-core/inc/admin-menu.php:1064
msgid "SureForms"
msgstr "SureForms"

#: admin-core/inc/admin-menu.php:1076
#: wizard/assets/build/wizard-app.js:1
msgid "Spectra"
msgstr "Spectra"

#: admin-core/inc/admin-menu.php:1077
msgid "Power-up the Gutenberg editor with advanced and powerful blocks."
msgstr "Geef de Gutenberg-editor een boost met geavanceerde en krachtige blokken."

#: admin-core/inc/admin-menu.php:1108
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Cart Abandonment"
msgstr "Winkelwagen Verlating"

#: admin-core/inc/admin-menu.php:1109
msgid "Recover abandonded carts with ease in less than 10 minutes."
msgstr "Herstel verlaten winkelwagentjes moeiteloos in minder dan 10 minuten."

#: admin-core/inc/admin-menu.php:1120
msgid "Variation Swatches for WooCommerce"
msgstr "Variatie Stalen voor WooCommerce"

#: admin-core/inc/admin-menu.php:1121
msgid "Convert dropdown boxes into highly engaging variation swatches."
msgstr "Zet keuzelijsten om in zeer boeiende variatie-stalen."

#: admin-core/inc/admin-menu.php:1137
msgid "Astra"
msgstr "Astra"

#: admin-core/inc/admin-menu.php:1138
msgid ""
"Astra is fast, fully customizable & beautiful WordPress theme suitable for blog, personal portfolio, business website "
"and WooCommerce storefront."
msgstr ""
"Astra is een snel, volledig aanpasbaar en prachtig WordPress-thema dat geschikt is voor blogs, persoonlijke "
"portfolio's, zakelijke websites en WooCommerce-winkels."

#: admin-core/inc/admin-menu.php:1148
msgid "Spectra One"
msgstr "Spectra One"

#: admin-core/inc/admin-menu.php:1149
msgid ""
"Spectra One is a beautiful and modern WordPress theme built with the Full Site Editing (FSE) feature. It's a versatile "
"theme that can be used for blogs, portfolios, businesses, and more."
msgstr ""
"Spectra One is een prachtig en modern WordPress-thema dat is gebouwd met de Full Site Editing (FSE) functie. Het is een "
"veelzijdig thema dat kan worden gebruikt voor blogs, portfolio's, bedrijven en meer."

#: admin-core/inc/flow-meta.php:54
msgid "Instant Layout "
msgstr "Directe indeling"

#: admin-core/inc/flow-meta.php:73
msgid "Logo"
msgstr "Logo"

#: admin-core/inc/flow-meta.php:108
msgid "Width (In px)"
msgstr "Breedte (in px)"

#: admin-core/inc/flow-meta.php:125
msgid "Height (In px)"
msgstr "Hoogte (in px)"

#: admin-core/inc/flow-meta.php:159
msgid "Global Styling"
msgstr "Globale styling"

#: admin-core/inc/flow-meta.php:164
msgid "Enable Global Styling"
msgstr "Globale styling inschakelen"

#: admin-core/inc/flow-meta.php:172
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:211
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:119
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:119
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:291
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:297
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:168
#: modules/optin/classes/class-cartflows-optin-meta-data.php:277
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:125
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Primary Color"
msgstr "Primaire kleur"

#: admin-core/inc/flow-meta.php:187
msgid "Secondary Color"
msgstr "Secundaire kleur"

#: admin-core/inc/flow-meta.php:202
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:219
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:247
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:441
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:674
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:861
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:397
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:222
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:250
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:325
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:390
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:456
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:522
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1052
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:172
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:251
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:303
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:329
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:592
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:690
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:763
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:361
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:350
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:410
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:334
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:383
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:413
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:478
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:541
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:572
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:633
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:663
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:722
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:752
#: modules/optin/classes/class-cartflows-optin-meta-data.php:500
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Color"
msgstr "Tekstkleur"

#: admin-core/inc/flow-meta.php:217
msgid "Heading/Accent Color"
msgstr "Koptekst/Accentkleur"

#: admin-core/inc/flow-meta.php:233
msgid "General "
msgstr "Algemeen"

#: admin-core/inc/flow-meta.php:239
#: admin-core/inc/global-settings.php:149
msgid "Funnel Slug"
msgstr "Trechter Slug"

#: admin-core/inc/flow-meta.php:245
msgid "Enable Test Mode"
msgstr "Testmodus inschakelen"

#: admin-core/inc/flow-meta.php:266
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:139
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:148
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:157
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:166
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "Yes"
msgstr "Ja"

#: admin-core/inc/flow-meta.php:270
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:140
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:149
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:158
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:167
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "No"
msgstr "Nee"

#: admin-core/inc/flow-meta.php:277
msgid "Funnel Custom Script"
msgstr "Trechter Aangepast Script"

#: admin-core/inc/global-settings.php:45
msgid "No Access"
msgstr "Geen toegang"

#: admin-core/inc/global-settings.php:49
msgid "Full Access"
msgstr "Volledige toegang"

#: admin-core/inc/global-settings.php:54
msgid "Limited Access"
msgstr "Beperkte toegang"

#: admin-core/inc/global-settings.php:71
msgid "Show Ready Templates for"
msgstr "Toon gereed sjablonen voor"

#: admin-core/inc/global-settings.php:73
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"Please choose your preferred page builder from the list so you will only see templates that are made using that page "
"builder. %1$sLearn More >>%2$s"
msgstr ""
"Kies alstublieft uw voorkeurspaginabouwer uit de lijst, zodat u alleen sjablonen ziet die met die paginabouwer zijn "
"gemaakt. %1$sMeer informatie >>%2$s"

#: admin-core/inc/global-settings.php:77
msgid "Block Editor"
msgstr "Blokeditor"

#: admin-core/inc/global-settings.php:82
msgid "Elementor"
msgstr "Elementor"

#: admin-core/inc/global-settings.php:87
msgid "Bricks"
msgstr "Bakstenen"

#: admin-core/inc/global-settings.php:92
msgid "Beaver"
msgstr "Bever"

#: admin-core/inc/global-settings.php:97
msgid "Other"
msgstr "Anders"

#: admin-core/inc/global-settings.php:110
msgid "Override Store Checkout"
msgstr "Winkelafrekenen overschrijven"

#: admin-core/inc/global-settings.php:112
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the Store Checkout settings please %1$sClick here%2$s."
msgstr "Voor meer informatie over de Store Checkout-instellingen, %1$sklik hier%2$s."

#: admin-core/inc/global-settings.php:120
msgid "Disallow search engine from indexing funnels."
msgstr "Zoekmachines verbieden om trechters te indexeren."

#: admin-core/inc/global-settings.php:122
msgid "Prevent search engines from including funnels in their search results."
msgstr "Voorkom dat zoekmachines trechters opnemen in hun zoekresultaten."

#: admin-core/inc/global-settings.php:139
msgid "Default Permalinks"
msgstr "Standaard Permalinks"

#: admin-core/inc/global-settings.php:140
msgid "Default WordPress Permalink"
msgstr "Standaard WordPress Permalink"

#: admin-core/inc/global-settings.php:144
msgid "Funnel and Step Slug"
msgstr "Trechter en Stap Slug"

#: admin-core/inc/global-settings.php:154
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1019
#: modules/landing/classes/class-cartflows-landing-meta-data.php:113
#: modules/optin/classes/class-cartflows-optin-meta-data.php:568
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:396
msgid "Step Slug"
msgstr "Stap Slug"

#: admin-core/inc/global-settings.php:164
msgid "Post Type Permalink Base"
msgstr "Permalinkbasis voor berichttype"

#: admin-core/inc/global-settings.php:168
msgid "Step Base"
msgstr "Stapbasis"

#: admin-core/inc/global-settings.php:174
msgid "Funnel Base"
msgstr "Trechterbasis"

#: admin-core/inc/global-settings.php:185
#: admin-core/inc/global-settings.php:376
#: admin-core/inc/global-settings.php:582
#: admin-core/inc/global-settings.php:956
msgid "Enable For CartFlows Pages"
msgstr "Inschakelen voor CartFlows-pagina's"

#: admin-core/inc/global-settings.php:203
#: admin-core/inc/global-settings.php:394
#: admin-core/inc/global-settings.php:600
#: admin-core/inc/global-settings.php:788
#: admin-core/inc/global-settings.php:974
#: admin-core/inc/global-settings.php:1180
msgid "Enable for the whole site"
msgstr "Inschakelen voor de hele site"

#: admin-core/inc/global-settings.php:205
msgid "If checked, page view and view content event will also be triggered for other pages/posts of site."
msgstr ""
"Als dit is aangevinkt, worden ook de paginaweergave- en inhoudweergavegebeurtenissen geactiveerd voor andere "
"pagina's/berichten van de site."

#: admin-core/inc/global-settings.php:231
msgid "Enter Facebook pixel ID"
msgstr "Voer Facebook-pixel-ID in"

#: admin-core/inc/global-settings.php:258
msgid "Facebook Pixel Events"
msgstr "Facebook Pixel-gebeurtenissen"

#: admin-core/inc/global-settings.php:272
#: admin-core/inc/global-settings.php:701
#: admin-core/inc/global-settings.php:888
#: admin-core/inc/global-settings.php:1093
msgid "View Content"
msgstr "Inhoud bekijken"

#: admin-core/inc/global-settings.php:288
msgid "Initiate Checkout"
msgstr "Start afrekenen"

#: admin-core/inc/global-settings.php:304
#: admin-core/inc/global-settings.php:495
#: admin-core/inc/global-settings.php:716
#: admin-core/inc/global-settings.php:1108
#: admin-core/inc/global-settings.php:1309
msgid "Add Payment Info"
msgstr "Betalingsinformatie toevoegen"

#: admin-core/inc/global-settings.php:321
msgid "Purchase Complete"
msgstr "Aankoop voltooid"

#: admin-core/inc/global-settings.php:337
#: admin-core/inc/global-settings.php:527
#: admin-core/inc/global-settings.php:748
#: admin-core/inc/global-settings.php:934
#: admin-core/inc/global-settings.php:1140
#: admin-core/inc/global-settings.php:1357
msgid "Optin Lead"
msgstr "Optin Lead"

#: admin-core/inc/global-settings.php:358
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Facebook Pixel not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Werkt de Facebook-pixel niet correct? %1$1s Klik hier %2$2s om meer te weten."

#: admin-core/inc/global-settings.php:396
msgid "If checked, page view event will also be triggered for other pages/posts of site."
msgstr "Als dit is aangevinkt, wordt het paginabekijkgebeurtenis ook geactiveerd voor andere pagina's/berichten van de site."

#: admin-core/inc/global-settings.php:422
msgid "Enter Google Analytics ID"
msgstr "Voer Google Analytics-ID in"

#: admin-core/inc/global-settings.php:426
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s google analytics account %2$2s to find your ID. e.g. G-XXXXX or UA-XXXXX-X"
msgstr "Log in op je %1$1s Google Analytics-account %2$2s om je ID te vinden, bijv. G-XXXXX of UA-XXXXX-X"

#: admin-core/inc/global-settings.php:451
msgid "Google Analytics Events"
msgstr "Google Analytics-evenementen"

#: admin-core/inc/global-settings.php:464
#: admin-core/inc/global-settings.php:670
#: admin-core/inc/global-settings.php:858
#: admin-core/inc/global-settings.php:1062
#: admin-core/inc/global-settings.php:1279
msgid "Begin Checkout"
msgstr "Begin met afrekenen"

#: admin-core/inc/global-settings.php:480
#: admin-core/inc/global-settings.php:686
#: admin-core/inc/global-settings.php:873
#: admin-core/inc/global-settings.php:1078
#: admin-core/inc/global-settings.php:1294
msgid "Add To Cart"
msgstr "Toevoegen aan winkelwagen"

#: admin-core/inc/global-settings.php:511
#: admin-core/inc/global-settings.php:732
#: admin-core/inc/global-settings.php:903
#: admin-core/inc/global-settings.php:1124
#: admin-core/inc/global-settings.php:1325
msgid "Purchase"
msgstr "Aankoop"

#: admin-core/inc/global-settings.php:548
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Google Analytics not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Werkt Google Analytics niet correct? %1$1s Klik hier %2$2s om meer te weten."

#: admin-core/inc/global-settings.php:566
msgid "Enter Google Map API key"
msgstr "Voer de Google Map API-sleutel in"

#: admin-core/inc/global-settings.php:573
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Check this %1$1s article %2$2s to setup and find an API key."
msgstr "Bekijk dit %1$1s artikel %2$2s om een API-sleutel in te stellen en te vinden."

#: admin-core/inc/global-settings.php:602
#: admin-core/inc/global-settings.php:790
#: admin-core/inc/global-settings.php:976
msgid "If checked, PageView event will also be triggered for other pages/posts of site."
msgstr "Als dit is aangevinkt, wordt het PageView-evenement ook geactiveerd voor andere pagina's/berichten van de site."

#: admin-core/inc/global-settings.php:628
msgid "Enter TikTok ID"
msgstr "Voer TikTok-ID in"

#: admin-core/inc/global-settings.php:632
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s TikTok business account %2$2s to find your ID."
msgstr "Meld je aan bij je %1$1s TikTok-bedrijfsaccount %2$2s om je ID te vinden."

#: admin-core/inc/global-settings.php:657
msgid "TikTok Events"
msgstr "TikTok-evenementen"

#: admin-core/inc/global-settings.php:760
#: admin-core/inc/global-settings.php:946
#: admin-core/inc/global-settings.php:1152
#: admin-core/inc/global-settings.php:1369
msgid "Optin Lead event will be triggered for optin page."
msgstr "Optin Lead-evenement wordt geactiveerd voor optin-pagina."

#: admin-core/inc/global-settings.php:770
#: admin-core/inc/global-settings.php:1162
msgid "Enable for CartFlows pages"
msgstr "Inschakelen voor CartFlows-pagina's"

#: admin-core/inc/global-settings.php:816
msgid "Enter Snapchat pixel ID"
msgstr "Voer Snapchat-pixel-ID in"

#: admin-core/inc/global-settings.php:820
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Snapchat business account %2$2s to find your ID."
msgstr "Log in op je %1$1s Snapchat zakelijke account %2$2s om je ID te vinden."

#: admin-core/inc/global-settings.php:845
msgid "Snapchat Events"
msgstr "Snapchat-evenementen"

#: admin-core/inc/global-settings.php:918
#: wizard/assets/build/wizard-app.js:1
msgid "Subscribe"
msgstr "Abonneren"

#: admin-core/inc/global-settings.php:1002
msgid "Enter Google Ads Conversion ID"
msgstr "Voer Google Ads-conversie-ID in"

#: admin-core/inc/global-settings.php:1006
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion ID."
msgstr "Log in op je %1$1s Google Ads-account %2$2s om je conversie-ID te vinden."

#: admin-core/inc/global-settings.php:1019
msgid "Enter Google Ads Conversion Label"
msgstr "Voer Google Ads-conversielabel in"

#: admin-core/inc/global-settings.php:1023
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion label."
msgstr "Log in op je %1$1s Google Ads-account %2$2s om je conversielabel te vinden."

#: admin-core/inc/global-settings.php:1049
msgid "Google Ads Events"
msgstr "Google Ads-evenementen"

#: admin-core/inc/global-settings.php:1182
msgid "If checked, PageVisit event will also be triggered for other pages/posts of site."
msgstr "Als dit is aangevinkt, wordt het PageVisit-evenement ook geactiveerd voor andere pagina's/berichten van de site."

#: admin-core/inc/global-settings.php:1208
msgid "Enter Pinterest Tag ID"
msgstr "Voer Pinterest-tag-ID in"

#: admin-core/inc/global-settings.php:1212
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Pinterest business account %2$2s to find your ID."
msgstr "Log in op je %1$1s Pinterest zakelijke account %2$2s om je ID te vinden."

#: admin-core/inc/global-settings.php:1237
msgid "Enable Pinterest tag tracking consent notice"
msgstr "Schakel Pinterest-tag tracking toestemmingsmelding in"

#: admin-core/inc/global-settings.php:1250
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"This setting enables a consent notice for Pinterest Tag tracking on your website. For more information check "
"%1$1sPinterest documentation%2$2s."
msgstr ""
"Deze instelling activeert een toestemmingsmelding voor Pinterest Tag-tracking op uw website. Voor meer informatie, "
"bekijk %1$1sPinterest-documentatie%2$2s."

#: admin-core/inc/global-settings.php:1266
msgid "Pinterest Events"
msgstr "Pinterest-evenementen"

#: admin-core/inc/global-settings.php:1340
msgid "Signup"
msgstr "Aanmelden"

#: admin-core/inc/global-settings.php:1352
msgid "Signup event will be triggered for optin page."
msgstr "Het aanmeldingsgebeurtenis wordt geactiveerd voor de opt-in pagina."

#: admin-core/inc/global-settings.php:1382
msgid "Store Revenue Report Emails"
msgstr "E-mails met winkelomzetrapporten"

#: admin-core/inc/global-settings.php:1387
msgid "Enable Store Report Email."
msgstr "E-mailrapport voor winkel inschakelen."

#: admin-core/inc/global-settings.php:1390
#. translators: %1$1s: link html start, %2$12: link html end
msgid "If enabled, you will receive the weekly report emails of your store for the revenue stats generated by CartFlows."
msgstr ""
"Als dit is ingeschakeld, ontvang je de wekelijkse rapport-e-mails van je winkel voor de omzetstatistieken die door "
"CartFlows zijn gegenereerd."

#: admin-core/inc/global-settings.php:1397
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:577
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:579
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:174
msgid "Email Address"
msgstr "E-mailadres"

#: admin-core/inc/global-settings.php:1398
msgid "Email address to receive the weekly sales report emails. For multiple emails, add each email address per line."
msgstr "E-mailadres om de wekelijkse verkooprapporten te ontvangen. Voor meerdere e-mails, voeg elk e-mailadres per regel toe."

#: admin-core/inc/global-settings.php:1425
msgid "Delete plugin data on plugin deletion"
msgstr "Plugingegevens verwijderen bij het verwijderen van de plugin"

#: admin-core/inc/global-settings.php:1430
msgid "Are you sure? Do you want to delete plugin data while deleting the plugin? Type \"DELETE\" to confirm!"
msgstr ""
"Weet je het zeker? Wil je de plugingegevens verwijderen terwijl je de plugin verwijdert? Typ \"DELETE\" om te "
"bevestigen!"

#: admin-core/inc/global-settings.php:1433
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"This option will delete all the CartFlows options data on plugin deletion. If you enable this and deletes the plugin, "
"you can't restore your saved data. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Deze optie verwijdert alle CartFlows-optiegegevens bij het verwijderen van de plugin. Als je dit inschakelt en de "
"plugin verwijdert, kun je je opgeslagen gegevens niet herstellen. Om meer te leren, %1$1s Klik hier %2$2s."

#: admin-core/inc/log-status.php:108
msgid "Log deleted successfully!"
msgstr "Log succesvol verwijderd!"

#: admin-core/inc/log-status.php:171
#: admin-core/inc/log-status.php:195
msgid "Nonce verification failed. Please refresh the page and retry."
msgstr "Verificatie van nonce mislukt. Vernieuw de pagina en probeer het opnieuw."

#: admin-core/inc/store-checkout.php:63
msgid "Checkout (Store)"
msgstr "Afrekenen (Winkel)"

#: admin-core/inc/store-checkout.php:67
msgid "Thank You (Store)"
msgstr "Bedankt (Winkel)"

#: admin-core/views/404-error.php:36
msgid "404 ERROR"
msgstr "404 FOUT"

#: admin-core/views/404-error.php:37
msgid "Page Not Found."
msgstr "Pagina niet gevonden."

#: admin-core/views/404-error.php:38
msgid "Sorry, we couldn’t find the page you’re looking for."
msgstr "Sorry, we konden de pagina die je zoekt niet vinden."

#: admin-core/views/404-error.php:39
msgid "Go back home"
msgstr "Ga terug naar huis"

#: admin-core/views/header.php:22
msgid "Generate More Leads & More Sales"
msgstr "Genereer meer leads en meer verkoop"

#: classes/class-cartflows-admin-notices.php:88
#. translators: %1$s Software Title, %2$s Plugin, %3$s Anchor opening tag, %4$s Anchor closing tag, %5$s Software Title.
msgid ""
"%1$sCartFlows:%2$s We just introduced an awesome new feature, weekly store revenue reports via email. Now you can see "
"how many revenue we are generating for your store each week, without having to log into your website. You can set the "
"email address for these email from %3$shere.%4$s"
msgstr ""
"%1$sCartFlows:%2$s We hebben zojuist een geweldige nieuwe functie geïntroduceerd: wekelijkse winkelomzetrapporten via "
"e-mail. Nu kunt u zien hoeveel omzet we elke week voor uw winkel genereren, zonder dat u hoeft in te loggen op uw "
"website. U kunt het e-mailadres voor deze e-mails instellen vanaf %3$shier.%4$s"

#: classes/class-cartflows-admin-notices.php:218
msgid "How likely are you to recommend #pluginname to your friends or colleagues?"
msgstr "Hoe waarschijnlijk is het dat je #pluginname aan je vrienden of collega's zou aanbevelen?"

#: classes/class-cartflows-admin-notices.php:221
msgid ""
"Could you please do us a favor and give us a 5-star rating on WordPress? It would help others choose CartFlows with "
"confidence. Thank you!"
msgstr ""
"Zou je ons alsjeblieft een plezier willen doen en ons een 5-sterrenbeoordeling op WordPress willen geven? Het zou "
"anderen helpen om met vertrouwen voor CartFlows te kiezen. Dank je!"

#: classes/class-cartflows-admin-notices.php:225
msgid "Thank you for your feedback"
msgstr "Dank u voor uw feedback"

#: classes/class-cartflows-admin-notices.php:226
msgid "We value your input. How can we improve your experience?"
msgstr "We waarderen uw inbreng. Hoe kunnen we uw ervaring verbeteren?"

#: classes/class-cartflows-admin-notices.php:249
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Heads up! The Gutenberg plugin is not recommended on production sites as it may contain non-final features that cause "
"compatibility issues with CartFlows and other plugins. %1$s Please deactivate the Gutenberg plugin %2$s to ensure the "
"proper functioning of your website."
msgstr ""
"Let op! De Gutenberg-plugin wordt niet aanbevolen op productiesites, omdat deze mogelijk niet-definitieve functies "
"bevat die compatibiliteitsproblemen kunnen veroorzaken met CartFlows en andere plugins. %1$s Deactiveer de "
"Gutenberg-plugin %2$s om de goede werking van uw website te garanderen."

#: classes/class-cartflows-admin.php:122
#: wizard/views/wizard-base.php:19
msgid "CartFlows Setup"
msgstr "CartFlows Setup"

#: classes/class-cartflows-admin.php:167
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "Step"
msgstr "Stap"

#: classes/class-cartflows-admin.php:167
msgid "of"
msgstr "van"

#: classes/class-cartflows-admin.php:173
msgid "You're almost there! Once you complete CartFlows setup you can start receiving orders from flows."
msgstr "Je bent er bijna! Zodra je de CartFlows-setup hebt voltooid, kun je bestellingen ontvangen via flows."

#: classes/class-cartflows-admin.php:175
#: admin-core/assets/build/settings-app.js:34
msgid "Complete Setup"
msgstr "Voltooi de installatie"

#: classes/class-cartflows-admin.php:233
msgid "Docs"
msgstr "Documenten"

#: classes/class-cartflows-admin.php:246
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:162
#: modules/landing/classes/class-cartflows-landing-meta-data.php:57
#: modules/optin/classes/class-cartflows-optin-meta-data.php:188
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:58
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Settings"
msgstr "Instellingen"

#: classes/class-cartflows-admin.php:362
msgid "You do not have permission to access this page."
msgstr "Je hebt geen toestemming om deze pagina te bekijken."

#: classes/class-cartflows-admin.php:363
#: classes/class-cartflows-admin.php:394
#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:11
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback to Previous Version"
msgstr "Terugzetten naar vorige versie"

#: classes/class-cartflows-admin.php:376
msgid "Error occurred, The version selected is invalid. Try selecting different version."
msgstr "Er is een fout opgetreden, de geselecteerde versie is ongeldig. Probeer een andere versie te selecteren."

#: classes/class-cartflows-default-meta.php:163
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1880
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1889
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1223
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:501
#: modules/checkout/templates/checkout/shipping-methods.php:69
msgid ""
"There are no shipping options available. Please ensure that your address has been entered correctly, or contact us if "
"you need any help."
msgstr ""
"Er zijn geen verzendopties beschikbaar. Zorg ervoor dat uw adres correct is ingevoerd, of neem contact met ons op als u "
"hulp nodig heeft."

#: classes/class-cartflows-default-meta.php:176
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1345
msgid "Place Order"
msgstr "Bestelling plaatsen"

#: classes/class-cartflows-default-meta.php:367
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1320
msgid "is required"
msgstr "is vereist"

#: classes/class-cartflows-default-meta.php:629
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:126
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:143
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:174
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:484
#: modules/thankyou/templates/instant-thankyou.php:76
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank you. Your order has been received."
msgstr "Dank u. Uw bestelling is ontvangen."

#: classes/class-cartflows-default-meta.php:820
#: modules/optin/classes/class-cartflows-optin-meta-data.php:593
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit"
msgstr "Indienen"

#: classes/class-cartflows-flow-frontend.php:284
msgid "Edit Design"
msgstr "Ontwerp bewerken"

#: classes/class-cartflows-functions.php:595
#. translators: %1$s page builder name "string"
msgid ""
"We have introduced %1$1s widgets for CartFlows shortcodes. Now, you can add/change/update design settings directly from "
"the page builder as well."
msgstr ""
"We hebben %1$1s widgets geïntroduceerd voor CartFlows shortcodes. Nu kun je ook ontwerpinstellingen direct vanuit de "
"paginabouwer toevoegen/wijzigen/bijwerken."

#: classes/class-cartflows-functions.php:596
msgid "Learn More »"
msgstr "Meer informatie »"

#: classes/class-cartflows-helper.php:568
msgid "First name"
msgstr "Voornaam"

#: classes/class-cartflows-helper.php:577
msgid "Last name"
msgstr "Achternaam"

#: classes/class-cartflows-helper.php:586
#: wizard/assets/build/wizard-app.js:3
msgid "Email address"
msgstr "E-mailadres"

#: classes/class-cartflows-helper.php:1370
msgid "Enable Field"
msgstr "Veld inschakelen"

#: classes/class-cartflows-helper.php:1376
msgid "Field Width"
msgstr "Veldbreedte"

#: classes/class-cartflows-helper.php:1382
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "33%"
msgstr "33%"

#: classes/class-cartflows-helper.php:1386
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "50%"
msgstr "50%"

#: classes/class-cartflows-helper.php:1390
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "100%"
msgstr "100%"

#: classes/class-cartflows-helper.php:1397
msgid "Field Label"
msgstr "Veldlabel"

#: classes/class-cartflows-helper.php:1402
#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
msgid "Field ID"
msgstr "Veld-ID"

#: classes/class-cartflows-helper.php:1406
msgid "Copy this field id to use in Order Custom Field rule of dynamic offers."
msgstr "Kopieer deze veld-id om te gebruiken in de Order Custom Field-regel van dynamische aanbiedingen."

#: classes/class-cartflows-helper.php:1418
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Checked"
msgstr "Gecontroleerd"

#: classes/class-cartflows-helper.php:1422
msgid "Un-Checked"
msgstr "Niet aangevinkt"

#: classes/class-cartflows-helper.php:1439
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:411
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Options"
msgstr "Opties"

#: classes/class-cartflows-helper.php:1463
msgid "Min Date"
msgstr "Minimale datum"

#: classes/class-cartflows-helper.php:1470
msgid "Max Date"
msgstr "Maximale datum"

#: classes/class-cartflows-helper.php:1482
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Placeholder"
msgstr "Placeholder"

#: classes/class-cartflows-helper.php:1491
msgid "Min Number"
msgstr "Minimaal aantal"

#: classes/class-cartflows-helper.php:1497
msgid "Max Number"
msgstr "Maximaal aantal"

#: classes/class-cartflows-helper.php:1506
msgid "Show In Email"
msgstr "In e-mail weergeven"

#: classes/class-cartflows-helper.php:1513
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Required"
msgstr "Vereist"

#: classes/class-cartflows-helper.php:1521
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Collapsible"
msgstr "Inklapbaar"

#: classes/class-cartflows-helper.php:1571
msgid "CartFlows Primary Color"
msgstr "CartFlows Primaire Kleur"

#: classes/class-cartflows-helper.php:1572
msgid "CartFlows Secondary Color"
msgstr "CartFlows Secundaire Kleur"

#: classes/class-cartflows-helper.php:1573
msgid "CartFlows Text Color"
msgstr "CartFlows Tekstkleur"

#: classes/class-cartflows-helper.php:1574
msgid "CartFlows Heading/Accent Color"
msgstr "CartFlows Koptekst/Accentkleur"

#: classes/class-cartflows-loader.php:292
#. translators: %s: html tags
msgid ""
"The new version of  %1$s%3$s%2$s is released. Please download the latest zip to install the new updates. Click here to "
"%4$sdownload%5$s."
msgstr ""
"De nieuwe versie van %1$s%3$s%2$s is uitgebracht. Download de nieuwste zip om de nieuwe updates te installeren. Klik "
"hier om te %4$sdownloaden%5$s."

#: classes/class-cartflows-loader.php:309
#. translators: %s: html tags
msgid "You are using an older version of %1$s%3$s%2$s. Please update %1$s%3$s%2$s plugin to version %1$s%4$s%2$s or higher."
msgstr "U gebruikt een oudere versie van %1$s%3$s%2$s. Werk de %1$s%3$s%2$s plugin bij naar versie %1$s%4$s%2$s of hoger."

#: classes/class-cartflows-loader.php:612
#. translators: %s: html tags
msgid "This %1$sCartFlows%2$s page requires %1$sWooCommerce%2$s plugin installed & activated."
msgstr "Deze %1$sCartFlows%2$s pagina vereist dat de %1$sWooCommerce%2$s plugin is geïnstalleerd en geactiveerd."

#: classes/class-cartflows-loader.php:622
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
msgid "Activate WooCommerce"
msgstr "Activeer WooCommerce"

#: classes/class-cartflows-loader.php:629
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Install WooCommerce"
msgstr "Installeer WooCommerce"

#: classes/class-cartflows-rollback.php:167
msgid "CartFlows <p>Rollback to Previous Version</p>"
msgstr "CartFlows <p>Terugzetten naar Vorige Versie</p>"

#: classes/class-cartflows-tracking.php:1396
msgid "We use Pinterest tags to improve your experience. Do you consent to our use of Pinterest tags?"
msgstr "We gebruiken Pinterest-tags om uw ervaring te verbeteren. Gaat u akkoord met ons gebruik van Pinterest-tags?"

#: classes/class-cartflows-tracking.php:1397
msgid "Accept"
msgstr "Accepteren"

#: classes/class-cartflows-tracking.php:1398
msgid "Decline"
msgstr "Afwijzen"

#: classes/class-cartflows-tracking.php:1403
msgid "Pinterest Consent"
msgstr "Pinterest-toestemming"

#: classes/importer/batch-process/class-cartflows-batch-process.php:482
msgid ""
"ERROR! Cron schedules are disabled by setting constant DISABLE_WP_CRON to true.<br/>To start the import process please "
"enable the cron by setting the constant to false. E.g. define( 'DISABLE_WP_CRON', false );"
msgstr ""
"FOUT! Cron-schema's zijn uitgeschakeld door de constante DISABLE_WP_CRON op true te zetten.<br/>Om het importproces te "
"starten, schakel de cron in door de constante op false te zetten. Bijvoorbeeld: define( 'DISABLE_WP_CRON', false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:486
msgid ""
"ERROR! Cron schedules are disabled by setting constant ALTERNATE_WP_CRON to true.<br/>To start the import process "
"please enable the cron by setting the constant to false. E.g. define( 'ALTERNATE_WP_CRON', false );"
msgstr ""
"FOUT! Cron-schema's zijn uitgeschakeld door de constante ALTERNATE_WP_CRON op true te zetten.<br/>Om het importproces "
"te starten, schakel de cron in door de constante op false te zetten. Bijvoorbeeld: define( 'ALTERNATE_WP_CRON', false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:522
#. translators: 1: The HTTP response code.
msgid "Unexpected HTTP response code: %s"
msgstr "Onverwachte HTTP-responscode: %s"

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:46
msgid "(✕) Empty content."
msgstr "(✕) Lege inhoud."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:51
msgid "(✕) Invalid content."
msgstr "(✕) Ongeldige inhoud."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:62
msgid "Invalid content. Expected an array."
msgstr "Ongeldige inhoud. Verwacht een array."

#: classes/importer/batch-process/helpers/class-wp-background-process-cartflows-sync-library.php:69
msgid "All processes are complete"
msgstr "Alle processen zijn voltooid"

#: classes/importer/batch-process/helpers/class-wp-background-process.php:440
#. Translators: %d: interval
msgid "Every %d Minutes"
msgstr "Elke %d minuten"

#: classes/importer/class-cartflows-api.php:428
msgid "Request successfully processed!"
msgstr "Verzoek succesvol verwerkt!"

#: classes/logger/class-cartflows-log-handler-file.php:355
#: classes/logger/class-cartflows-log-handler-file.php:375
msgid "This method should not be called before plugins_loaded."
msgstr "Deze methode mag niet worden aangeroepen voordat plugins_loaded is uitgevoerd."

#: classes/logger/class-cartflows-wc-logger.php:58
#. translators: 1: class name 2: Cartflows_Log_Handler_Interface
msgid "The provided handler %1$s does not implement %2$s."
msgstr "De opgegeven handler %1$s implementeert %2$s niet."

#: classes/logger/class-cartflows-wc-logger.php:136
#. translators: 1: Cartflows_WC_Logger::log 2: level
msgid "%1$s was called with an invalid level \"%2$s\"."
msgstr "%1$s werd aangeroepen met een ongeldig niveau \"%2$s\"."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:85
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:354
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:546
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:220
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:249
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:202
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:348
#: modules/gutenberg/dist/blocks.build.js:1
msgid "None"
msgstr "Geen"

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:112
#. translators: 1: anchor start, 2: anchor close
msgid ""
"Non-enrolled students will redirect to the selected CartFlows template. If you have not created any Flow already, add "
"new Flow from %1$shere%2$s."
msgstr ""
"Niet-ingeschreven studenten worden doorgestuurd naar de geselecteerde CartFlows-sjabloon. Als je nog geen Flow hebt "
"gemaakt, voeg dan een nieuwe Flow toe vanaf %1$shier%2$s."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:118
msgid "Select CartFlows Template for this Course"
msgstr "Selecteer CartFlows-sjabloon voor deze cursus"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:44
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:150
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:373
#: modules/gutenberg/build/blocks-placeholder.js:9
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Checkout Form"
msgstr "Afrekenformulier"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:34
msgid "Checkout Form."
msgstr "Afrekenformulier."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:35
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:36
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:34
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:36
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:34
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:35
msgid "Cartflows Modules"
msgstr "Cartflows-modules"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:137
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:146
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:59
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:68
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:251
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:198
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:207
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Checkout"
msgstr "Moderne Kassa"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:138
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:60
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:69
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:255
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:199
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:208
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern One Column"
msgstr "Moderne Eén Kolom"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:139
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:149
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:61
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:70
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:263
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:200
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:209
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "One Column"
msgstr "Eén kolom"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:140
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:150
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:62
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:71
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:267
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:210
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Column"
msgstr "Twee kolommen"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:141
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:64
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:203
msgid "MultiStep Checkout ( PRO )"
msgstr "MultiStep Checkout ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:142
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:63
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:202
msgid "Two Step ( PRO )"
msgstr "Twee Stappen ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:148
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:73
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:212
#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout"
msgstr "MultiStep Checkout"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:151
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:271
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:211
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step"
msgstr "Twee Stappen"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:168
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1212
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:446
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:229
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Labels"
msgstr "Moderne labels"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:132
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:109
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:68
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:82
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1012
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:157
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:294
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:160
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:152
#: modules/landing/classes/class-cartflows-landing-meta-data.php:106
#: modules/optin/classes/class-cartflows-optin-meta-data.php:561
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:389
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "General"
msgstr "Algemeen"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:190
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:253
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Layout"
msgstr "Selecteer indeling"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:192
#. translators: %s: link
msgid "The PRO layout options are available in the CartFlows Pro. %1$s  Upgrade Now! %2$s"
msgstr "De PRO-indelingsopties zijn beschikbaar in de CartFlows Pro. %1$s  Upgrade nu! %2$s"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:204
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:277
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:186
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:112
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:141
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:390
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:164
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:414
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:207
#: modules/optin/classes/class-cartflows-optin-meta-data.php:298
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Style"
msgstr "Stijl"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:207
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:115
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1226
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:70
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:289
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Global"
msgstr "Wereldwijd"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:233
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:262
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:341
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:529
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:458
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:473
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:127
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:189
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:334
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:236
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:264
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:310
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:346
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:399
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:689
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:228
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:347
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Typography"
msgstr "Typografie"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:243
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:218
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1234
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:78
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:362
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:301
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:523
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:616
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:705
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:168
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading"
msgstr "Kop"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:273
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:137
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1230
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:421
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:406
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:199
#: modules/optin/classes/class-cartflows-optin-meta-data.php:292
msgid "Input Fields"
msgstr "Invoervelden"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:287
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:460
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:913
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1114
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:443
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:878
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:226
#: modules/optin/classes/class-cartflows-optin-meta-data.php:375
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label Color"
msgstr "Labelkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:301
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:161
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:477
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:188
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:572
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:454
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:237
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Background Color"
msgstr "Achtergrondkleur van veld"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:320
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:503
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:465
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:248
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Text / Placeholder Color"
msgstr "Invoertekst / Placeholder-kleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:350
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:542
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:198
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:344
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:533
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:476
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:259
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Style"
msgstr "Randstijl"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:352
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:544
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:200
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:346
msgid "The type of border to use. Double borders must have a width of at least 3px to render properly."
msgstr "Het type rand dat je wilt gebruiken. Dubbele randen moeten een breedte van minimaal 3px hebben om correct weer te geven."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:355
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:547
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:203
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:349
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:539
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:482
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:265
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Solid"
msgstr "Solide"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:356
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:548
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:204
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:350
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:542
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:485
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:268
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dashed"
msgstr "Gestreept"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:357
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:549
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:205
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:351
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:541
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:484
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:267
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dotted"
msgstr "Gestippeld"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:358
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:550
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:206
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:352
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:540
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:483
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:266
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Double"
msgstr "Dubbel"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:377
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:582
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:230
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:377
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:575
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:500
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:278
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width"
msgstr "Randbreedte"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:398
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:605
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:840
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:245
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:393
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:610
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1024
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:517
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:940
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:290
#: modules/optin/classes/class-cartflows-optin-meta-data.php:396
#: modules/optin/classes/class-cartflows-optin-meta-data.php:528
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Color"
msgstr "Randkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:416
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:646
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:752
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:258
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:421
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius"
msgstr "Randstraal"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:437
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:274
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:556
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:287
#: modules/gutenberg/build/blocks.js:11
msgid "Buttons"
msgstr "Knoppen"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:465
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:411
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:292
#: modules/optin/classes/class-cartflows-optin-meta-data.php:507
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Hover Color"
msgstr "Tekst zweefkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:485
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:824
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:874
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:425
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:306
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:293
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:339
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:404
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:470
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:536
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:743
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1002
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1070
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:264
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:329
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:240
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:263
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:360
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:411
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:459
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:505
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:607
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:724
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:927
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:989
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:374
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:363
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:433
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:437
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:585
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:676
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:765
#: modules/optin/classes/class-cartflows-optin-meta-data.php:389
#: modules/optin/classes/class-cartflows-optin-meta-data.php:514
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Color"
msgstr "Achtergrondkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:509
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:433
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:320
#: modules/optin/classes/class-cartflows-optin-meta-data.php:521
#: modules/gutenberg/build/blocks.js:11
msgid "Background Hover Color"
msgstr "Achtergrond zweefkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:626
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:331
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:407
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:316
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:703
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:456
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:421
#: modules/optin/classes/class-cartflows-optin-meta-data.php:535
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Hover Color"
msgstr "Rand zweefkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:670
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1250
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:756
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Payment Section"
msgstr "Betaalsectie"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:688
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1131
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:776
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Description Color"
msgstr "Beschrijving Kleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:702
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:802
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Information Background Color"
msgstr "Achtergrondkleur van informatie"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:710
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1143
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:829
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:789
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:256
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Background Color"
msgstr "Achtergrondkleur van sectie"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:724
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1161
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:814
msgid "Section Padding"
msgstr "Sectie-opvulling"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:738
#: modules/gutenberg/build/blocks.js:11
msgid "Margin"
msgstr "Marge"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:768
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1246
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:861
msgid "Field Validation & Error Messages"
msgstr "Veldvalidatie & Foutmeldingen"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:772
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:602
msgid "Field Label Color"
msgstr "Veldlabelkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:788
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:936
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:587
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:890
msgid "Field Border Color"
msgstr "Randkleur van veld"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:808
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:980
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:913
msgid "Error Message Color"
msgstr "Foutberichtkleur"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:857
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1242
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:962
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Review"
msgstr "Bestellingsoverzicht"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:32
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:70
#: modules/gutenberg/classes/class-cartflows-block-config.php:54
#: modules/gutenberg/build/blocks-placeholder.js:10
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button"
msgstr "Volgende stap-knop"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:33
msgid "A simple next step button."
msgstr "Een eenvoudige volgende stap knop."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:139
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:192
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text"
msgstr "Tekst"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:140
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:61
msgid "Next Step"
msgstr "Volgende stap"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:149
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon"
msgstr "Icoon"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:160
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:195
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Position"
msgstr "Pictogrampositie"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:163
msgid "Before Text"
msgstr "Voor tekst"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:164
msgid "After Text"
msgstr "Na tekst"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:172
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:240
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Spacing"
msgstr "Pictogramafstand"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:190
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Type"
msgstr "Type"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:195
msgid "Flat"
msgstr "Plat"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:196
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Gradient"
msgstr "Gradiënt"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:197
msgid "Transparent"
msgstr "Transparant"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:198
msgid "3D"
msgstr "3D"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:208
msgid "Border Size"
msgstr "Randgrootte"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:217
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:233
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:246
msgid "Hover Styles"
msgstr "Zweefstijlen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:221
msgid "Fade Background"
msgstr "Achtergrond vervagen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:222
msgid "Fill Background From Top"
msgstr "Achtergrond van boven vullen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:223
msgid "Fill Background From Bottom"
msgstr "Achtergrond van onderen vullen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:224
msgid "Fill Background From Left"
msgstr "Achtergrond van links vullen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:225
msgid "Fill Background From Right"
msgstr "Achtergrond vullen vanaf rechts"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:226
msgid "Fill Background Vertical"
msgstr "Achtergrond verticaal vullen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:227
msgid "Fill Background Diagonal"
msgstr "Achtergrond diagonaal vullen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:228
msgid "Fill Background Horizontal"
msgstr "Achtergrond horizontaal vullen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:236
msgid "Move Down"
msgstr "Omlaag verplaatsen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:237
msgid "Move Up"
msgstr "Omhoog verplaatsen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:238
msgid "Move Left"
msgstr "Ga naar links"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:239
msgid "Move Right"
msgstr "Verplaats naar rechts"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:240
msgid "Animate Top"
msgstr "Top animeren"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:241
msgid "Animate Bottom"
msgstr "Onderkant animeren"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:250
msgid "Appear Icon From Right"
msgstr "Pictogram van rechts laten verschijnen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:251
msgid "Appear Icon From Left"
msgstr "Verschijn pictogram van links"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:252
msgid "Appear Icon From Top"
msgstr "Verschijn pictogram van boven"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:253
msgid "Appear Icon From Bottom"
msgstr "Verschijn pictogram van onderen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:259
msgid "Structure"
msgstr "Structuur"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:263
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Width"
msgstr "Breedte"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:267
#: modules/gutenberg/build/blocks.js:11
msgid "Full Width"
msgstr "Volledige breedte"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:268
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:509
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:680
#: modules/optin/classes/class-cartflows-optin-meta-data.php:355
#: modules/optin/classes/class-cartflows-optin-meta-data.php:459
msgid "Custom"
msgstr "Aangepast"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:284
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:302
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:309
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Alignment"
msgstr "Uitlijning"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:287
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:297
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:310
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:507
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:490
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center"
msgstr "Centrum"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:288
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:298
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:306
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:503
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:313
#: modules/optin/classes/class-cartflows-optin-meta-data.php:486
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Left"
msgstr "Links"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:289
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:299
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:314
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:511
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:321
#: modules/optin/classes/class-cartflows-optin-meta-data.php:494
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Right"
msgstr "Rechts"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:294
msgid "Mobile Alignment"
msgstr "Mobiele uitlijning"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:304
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:340
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Padding"
msgstr "Opvulling"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:318
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:447
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:792
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:215
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:277
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:626
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:395
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:373
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border"
msgstr "Grens"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:342
msgid "Custom Width"
msgstr "Aangepaste breedte"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:351
msgid "Custom Height"
msgstr "Aangepaste hoogte"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:360
msgid "Padding Top/Bottom"
msgstr "Opvulling boven/onder"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:369
msgid "Padding Left/Right"
msgstr "Padding Links/Rechts"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:378
msgid "Round Corners"
msgstr "Ronde Hoeken"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:393
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Colors"
msgstr "Kleuren"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:444
msgid "Apply Hover Color To"
msgstr "Hoverkleur toepassen op"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:448
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:322
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
msgid "Background"
msgstr "Achtergrond"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:461
msgid "Button Settings"
msgstr "Knopinstellingen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:465
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:260
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Size"
msgstr "Pictogramgrootte"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:46
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:578
#: modules/optin/classes/class-cartflows-optin-meta-data.php:181
#: modules/optin/classes/class-cartflows-optin-meta-data.php:260
#: modules/gutenberg/build/blocks-placeholder.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Optin Form"
msgstr "Opt-in formulier"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:34
msgid "Optin Form."
msgstr "Optin-formulier."

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:97
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:104
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:144
#: modules/optin/classes/class-cartflows-optin-meta-data.php:309
msgid "Floating Labels"
msgstr "Zwevende labels"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:32
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:46
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:65
#: modules/gutenberg/classes/class-cartflows-block-config.php:157
msgid "Order Details Form"
msgstr "Bestelformulier"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:33
msgid "Order Details Form."
msgstr "Bestelformulier."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:125
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:141
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:172
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank You Text"
msgstr "Bedankt tekst"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:136
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:321
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:86
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:150
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:458
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Overview"
msgstr "Bestellingsoverzicht"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:145
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:428
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:94
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:157
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:605
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:99
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Details"
msgstr "Bestelgegevens"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:154
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:164
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:209
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Billing Address"
msgstr "Factuuradres"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:163
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:171
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:221
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Shipping Address"
msgstr "Verzendadres"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:178
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:74
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:245
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing"
msgstr "Spatiëring"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:182
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:253
msgid "Heading Bottom Spacing"
msgstr "Koptekst Onderste Spatiëring"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:197
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:269
msgid "Spacing Between Sections"
msgstr "Afstand tussen secties"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:375
msgid "Sections Heading"
msgstr "Secties Kop"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:274
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:404
msgid "Sections Content"
msgstr "Secties Inhoud"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:353
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:418
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:484
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:550
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:398
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:446
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:492
msgid "Text Typography"
msgstr "Teksttypografie"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:363
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:90
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:515
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Downloads"
msgstr "Downloads"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:367
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:432
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:498
#: modules/gutenberg/build/blocks.js:11
msgid "Heading Color"
msgstr "Kleur van de kop"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:381
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:447
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:513
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:381
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:432
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:479
msgid "Heading Typography"
msgstr "Koptypografie"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:494
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:98
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:697
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Customer Details"
msgstr "Klantgegevens"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:116
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:117
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:128
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:159
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Layout"
msgstr "Indeling"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:134
#. translators: %s is the URL for upgrading
msgid "This feature is available in the CartFlows higher plan. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">%2$s</a>."
msgstr ""
"Deze functie is beschikbaar in het hogere plan van CartFlows. <a href=\"%1$s\" target=\"_blank\" "
"rel=\"noopener\">%2$s</a>."

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:136
msgid "Upgrade Now!"
msgstr "Nu upgraderen!"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:154
msgid " Global Text Typography"
msgstr "Wereldwijde Teksttypografie"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:252
msgid " Global Primary Color"
msgstr "Globale primaire kleur"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:538
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:481
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:264
msgid "Inherit"
msgstr "Erven"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:645
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:528
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:643
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:403
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:301
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:381
msgid "Rounded Corners"
msgstr "Afgeronde hoeken"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:841
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:290
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Box Shadow"
msgstr "Schaduw van de doos"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:906
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:869
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Validation"
msgstr "Veldvalidatie"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:904
msgid "Error Messages"
msgstr "Foutmeldingen"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1174
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:827
msgid "Section Margin"
msgstr "Sectiemarge"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1238
msgid "Buttons (Normal)"
msgstr "Knoppen (Normaal)"

#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:76
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button"
msgstr "Knop"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:276
msgid "Sections Heading Typography"
msgstr "Typografie van sectiekoppen"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:288
msgid "Sections Text Typography"
msgstr "Secties Tekst Typografie"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:304
msgid "Sections Background Color"
msgstr "Achtergrondkleur secties"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:145
msgid "Sorry there was a problem removing this coupon."
msgstr "Sorry, er was een probleem bij het verwijderen van deze coupon."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:148
msgid "Coupon has been removed."
msgstr "Coupon is verwijderd."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:166
msgid "Sorry there was a problem removing "
msgstr "Sorry, er was een probleem bij het verwijderen"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:169
msgid " has been removed."
msgstr "is verwijderd."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email Exist."
msgstr "E-mail bestaat."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email not exist"
msgstr "E-mail bestaat niet"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:126
#. Translators: %1$s & %2$s is replaced with Field Name
msgid "%1$s Add %2$s"
msgstr "%1$s Voeg %2$s toe"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:137
#. Translators: %s is replaced with Field Icon
msgid "%s Have a coupon?"
msgstr "%s Heb je een coupon?"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:146
#. Translators: %s is replaced with Field Icon
msgid "%s Add Order Notes"
msgstr "%s Ordernotities toevoegen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:474
#: modules/optin/classes/class-cartflows-optin-markup.php:228
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:232
msgid "WooCommerce functions do not exist. If you are in an IFrame, please reload it."
msgstr "WooCommerce-functies bestaan niet. Als je in een IFrame bent, laad het dan opnieuw."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:475
#: modules/optin/classes/class-cartflows-optin-markup.php:229
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:233
msgid "Click Here to Reload"
msgstr "Klik hier om te herladen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:503
msgid "Checkout ID not found"
msgstr "Checkout-ID niet gevonden"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:506
#. translators: %1$1s, %2$2s Link to article
msgid ""
"It seems that this is not the CartFlows Checkout page where you have added this shortcode. Please refer to this "
"%1$1sarticle%2$2s to know more."
msgstr ""
"Het lijkt erop dat dit niet de CartFlows Checkout-pagina is waar je deze shortcode hebt toegevoegd. Raadpleeg dit "
"%1$1sartikel%2$2s voor meer informatie."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:576
#: modules/checkout/templates/embed/checkout-template-simple.php:48
#: modules/checkout/templates/wcf-template.php:40
#: modules/optin/templates/optin-template-simple.php:29
msgid "Your cart is currently empty."
msgstr "Je winkelwagen is momenteel leeg."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:616
#. translators: %1$1s, %2$2s Link to meta
msgid "No product is selected. Please select products from the %1$1scheckout meta settings%2$2s to continue."
msgstr "Er is geen product geselecteerd. Selecteer producten uit de %1$1scheckout meta-instellingen%2$2s om door te gaan."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:724
msgid "Variations Not set"
msgstr "Variaties Niet ingesteld"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:735
msgid "This product can't be purchased"
msgstr "Dit product kan niet worden gekocht"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1467
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1511
#: modules/checkout/templates/checkout/collapsed-order-summary.php:46
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:82
msgid "Coupon Code"
msgstr "Couponcode"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1468
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1520
#: modules/checkout/templates/checkout/collapsed-order-summary.php:51
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:87
msgid "Apply"
msgstr "Toepassen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1544
msgid "Entered email address is not a valid email."
msgstr "Het ingevoerde e-mailadres is geen geldig e-mailadres."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1545
msgid "This email is already registered. Please enter the password to continue."
msgstr "Dit e-mailadres is al geregistreerd. Voer het wachtwoord in om door te gaan."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1548
msgid "Value must be between "
msgstr "Waarde moet tussen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1752
msgid "Show Order Summary"
msgstr "Toon bestellingssamenvatting"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1753
msgid "Hide Order Summary"
msgstr "Besteloverzicht verbergen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1876
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:498
#: modules/checkout/templates/checkout/shipping-methods.php:66
msgid "Enter your address to view shipping options."
msgstr "Voer uw adres in om de verzendopties te bekijken."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1885
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:405
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:407
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:796
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:110
#: modules/thankyou/templates/instant-thankyou-order-details.php:107
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping"
msgstr "Verzending"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:138
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:943
#: modules/optin/classes/class-cartflows-optin-meta-data.php:174
msgid "Products"
msgstr "Producten"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:144
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Order Bumps"
msgstr "Order Bumps"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:156
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Offers"
msgstr "Dynamische Aanbiedingen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:172
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:982
msgid "Checkout Offer"
msgstr "Afrekenaanbieding"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:205
msgid "Two Step (Available in higher plan) "
msgstr "Twee stappen (Beschikbaar in hoger plan)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:206
msgid "Multistep Checkout (Available in higher plan) "
msgstr "Meerstapsafrekenen (Beschikbaar in hoger plan)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:213
#: modules/landing/classes/class-cartflows-landing-meta-data.php:91
#: modules/optin/classes/class-cartflows-optin-meta-data.php:253
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:92
msgid "Shortcode"
msgstr "Shortcode"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:230
msgid "CartFlows Checkout"
msgstr "CartFlows Checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:232
msgid "Add this shortcode to your checkout page"
msgstr "Voeg deze shortcode toe aan je afrekenpagina"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:239
msgid "Checkout Design"
msgstr "Afrekenontwerp"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:245
msgid "Checkout Skin"
msgstr "Afrekenen Huid"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:259
#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout"
msgstr "Meerstapsafrekenen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:296
#: modules/optin/classes/class-cartflows-optin-meta-data.php:284
#: modules/optin/classes/class-cartflows-optin-meta-data.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:420
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:181
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:205
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:306
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Family"
msgstr "Lettertypefamilie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:304
msgid "Instant Checkout"
msgstr "Direct afrekenen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:319
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:328
msgid "Left Column Background Color"
msgstr "Achtergrondkleur van de linkerkolom"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:336
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:344
msgid "Right Column Background Color"
msgstr "Achtergrondkleur van de rechterkolom"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:355
msgid "Checkout Texts & Buttons"
msgstr "Afreken Teksten & Knoppen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:362
msgid "Enable Advance Options"
msgstr "Geavanceerde opties inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:370
msgid "Heading Font"
msgstr "Koplettertype"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:384
#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text Color"
msgstr "Koptekst Kleur"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:400
msgid "Heading Font Family"
msgstr "Koplettertypefamilie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:436
msgid "Input Field Style"
msgstr "Stijl van invoerveld"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:463
msgid "Input Field Font Family"
msgstr "Lettertypefamilie invoerveld"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:483
msgid "Field Size"
msgstr "Veldgrootte"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:489
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:660
#: modules/optin/classes/class-cartflows-optin-meta-data.php:335
#: modules/optin/classes/class-cartflows-optin-meta-data.php:439
msgid "Extra Small"
msgstr "Extra Small"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:493
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:664
#: modules/optin/classes/class-cartflows-optin-meta-data.php:339
#: modules/optin/classes/class-cartflows-optin-meta-data.php:443
msgid "Small"
msgstr "Klein"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:497
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:668
#: modules/optin/classes/class-cartflows-optin-meta-data.php:343
#: modules/optin/classes/class-cartflows-optin-meta-data.php:447
msgid "Medium"
msgstr "Middelgroot"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:501
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:672
#: modules/optin/classes/class-cartflows-optin-meta-data.php:347
#: modules/optin/classes/class-cartflows-optin-meta-data.php:451
msgid "Large"
msgstr "Groot"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:505
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:676
#: modules/optin/classes/class-cartflows-optin-meta-data.php:351
#: modules/optin/classes/class-cartflows-optin-meta-data.php:455
msgid "Extra Large"
msgstr "Extra Groot"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:525
msgid "Field Top-Bottom Spacing"
msgstr "Veld boven-onder afstand"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:541
msgid "Field Left-Right Spacing"
msgstr "Veld links-rechts afstand"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:557
msgid "Field Text / Placeholder Color"
msgstr "Veldtekst / Placeholder-kleur"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:618
msgid "Button Fields"
msgstr "Knopvelden"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:634
msgid "Button Font Family"
msgstr "Knop Lettertypefamilie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:654
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:330
msgid "Button Size"
msgstr "Knopgrootte"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:696
msgid "Button Top-Bottom Spacing"
msgstr "Knop boven-onder afstand"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:717
msgid "Button Left-Right Spacing"
msgstr "Knop Links-Rechts Afstand"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:738
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:294
msgid "Button Text Color"
msgstr "Kleur van de knoptekst"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:753
msgid "Button Text Hover Color"
msgstr "Tekstkleur bij zweven over knop"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:768
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:300
msgid "Button Background Color"
msgstr "Achtergrondkleur van de knop"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:783
msgid "Button Background Hover Color"
msgstr "Achtergrondkleur knop bij zweven"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:798
msgid "Button Border Color"
msgstr "Kleur van de knoprand"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:813
msgid "Button Border Hover Color"
msgstr "Kleur van de rand van de knop bij zweven"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:861
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:865
msgid "Enable Product Options"
msgstr "Productopties inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:870
msgid "Enable Conditions"
msgstr "Voorwaarden inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:874
msgid "Restrict user to purchase all products"
msgstr "Beperk gebruiker om alle producten te kopen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:878
msgid "Let user select one product from all options"
msgstr "Laat de gebruiker één product uit alle opties selecteren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:882
msgid "Let user select multiple products from all options"
msgstr "Laat de gebruiker meerdere producten uit alle opties selecteren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:888
msgid "Enable Variations"
msgstr "Variaties inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:897
msgid "Show variations inline"
msgstr "Variaties inline weergeven"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:901
msgid "Show variations in popup"
msgstr "Variaties in pop-up weergeven"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:907
msgid "Enable Quantity"
msgstr "Aantal inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:920
msgid "Select Coupon"
msgstr "Selecteer coupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:921
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a coupon"
msgstr "Zoek naar een coupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:925
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows coupon please %1$1s Click here.%2$2s"
msgstr "Voor meer informatie over de CartFlows-coupon, %1$1s klik hier.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:951
msgid "Select Product"
msgstr "Selecteer product"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:952
msgid "Search for a product..."
msgstr "Zoek naar een product..."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:962
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the checkout product settings please %1$1s Click here.%2$2s"
msgstr "Voor meer informatie over de productinstellingen bij het afrekenen, %1$1s klik hier.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:968
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Auto Apply Coupon"
msgstr "Automatisch coupon toepassen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:975
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Product Options"
msgstr "Productopties"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1026
#: modules/flow/classes/class-cartflows-step-meta-base.php:80
#: modules/landing/classes/class-cartflows-landing-meta-data.php:119
#: modules/optin/classes/class-cartflows-optin-meta-data.php:574
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:402
msgid "Custom Script"
msgstr "Aangepast script"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1035
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:475
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Advanced"
msgstr "Geavanceerd"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1041
msgid "Display product images"
msgstr "Productafbeeldingen weergeven"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1049
msgid "Enable cart editing on checkout"
msgstr "Winkelwagen bewerken inschakelen bij afrekenen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1064
#: modules/landing/classes/class-cartflows-landing-meta-data.php:134
#: modules/optin/classes/class-cartflows-optin-meta-data.php:634
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:523
msgid "Step Note"
msgstr "Stapnotitie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1148
msgid "Form Settings"
msgstr "Formulierinstellingen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1153
msgid "Enable Coupon Field"
msgstr "Couponveld inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1159
msgid "Collapsible Coupon Field"
msgstr "Inklapbaar kortingsveld"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1175
msgid "Enable Additional Field"
msgstr "Extra veld inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1181
msgid "Collapsible Additional Field"
msgstr "Inklapbaar extra veld"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1197
msgid "Enable Ship To Different Address"
msgstr "Verzenden naar een ander adres inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1204
msgid "Enable Google Address Autocomplete"
msgstr "Google-adres automatisch aanvullen inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1207
#. translators: %1$s: link html start, %2$s: link html end
msgid "Before enabling this option, make sure that you have added API key in Google Address Autocomplete Settings."
msgstr ""
"Voordat u deze optie inschakelt, zorg ervoor dat u de API-sleutel hebt toegevoegd in de Google Address "
"Autocomplete-instellingen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1212
msgid "Enable Custom Shipping Message"
msgstr "Aangepast verzendbericht inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1220
msgid "Shipping Message"
msgstr "Verzendbericht"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1224
msgid "This message will be displayed when no shipping method is available."
msgstr "Dit bericht wordt weergegeven wanneer er geen verzendmethode beschikbaar is."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1241
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:446
msgid "Order Summary Position"
msgstr "Positie van de bestellingssamenvatting"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1248
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:219
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:453
msgid "Top"
msgstr "Top"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1252
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:457
msgid "Bottom"
msgstr "Onderkant"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1266
msgid "Form Headings"
msgstr "Formuliervolgorden"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1273
msgid "Billing Details"
msgstr "Factuurgegevens"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1275
msgid "Billing details"
msgstr "Factureringsgegevens"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1280
msgid "Shipping Details"
msgstr "Verzendgegevens"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1282
msgid "Ship to a different address?"
msgstr "Verzenden naar een ander adres?"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1287
#: modules/thankyou/templates/instant-thankyou-your-product.php:23
msgid "Your Order"
msgstr "Uw bestelling"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1289
msgid "Your order"
msgstr "Uw bestelling"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1294
msgid "Customer Information"
msgstr "Klantinformatie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1296
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:159
msgid "Customer information"
msgstr "Klantinformatie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1302
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1304
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:823
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:276
#: modules/thankyou/templates/instant-thankyou-order-details.php:127
msgid "Payment"
msgstr "Betaling"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1310
msgid "Enable Field validation error message"
msgstr "Foutmelding voor veldvalidatie inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1318
msgid "Validation error message"
msgstr "Validatiefoutmelding"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1336
msgid "Place Order Button"
msgstr "Bestelknop"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1342
#: modules/optin/classes/class-cartflows-optin-meta-data.php:590
#: modules/gutenberg/build/blocks.js:11
msgid "Button Text"
msgstr "Knoptekst"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1351
msgid "Enable Lock Icon"
msgstr "Vergrendelingspictogram inschakelen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1360
msgid "Enable Price Display"
msgstr "Prijsweergave inschakelen"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:260
msgid "Home"
msgstr "Thuis"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:496
#: modules/checkout/templates/checkout/shipping-methods.php:64
msgid "Shipping costs are calculated during checkout."
msgstr "Verzendkosten worden berekend tijdens het afrekenen."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:515
#: modules/checkout/templates/checkout/shipping-methods.php:72
#. Translators: $s shipping destination.
msgid "No shipping options were found for %s."
msgstr "Er zijn geen verzendopties gevonden voor %s."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:519
#: modules/checkout/templates/checkout/shipping-methods.php:73
#. Translators: $s shipping destination.
msgid "Enter a different address"
msgstr "Voer een ander adres in"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:562
#: modules/thankyou/templates/instant-thankyou-order-details.php:67
msgid "Contact"
msgstr "Contact"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:564
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "%1$1s Log in%2$2s"
msgstr "%1$1s Inloggen%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:594
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:192
msgid "Password"
msgstr "Wachtwoord"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:596
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:194
#. translators: %s: asterisk mark
msgid "Password %s"
msgstr "Wachtwoord %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:602
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:200
msgid "Login"
msgstr "Inloggen"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:603
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:201
msgid "Lost your password?"
msgstr "Wachtwoord vergeten?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:608
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:206
msgid "Login is optional, you can continue with your order below."
msgstr "Inloggen is optioneel, je kunt hieronder doorgaan met je bestelling."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:620
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:218
msgid "Create an account?"
msgstr "Een account aanmaken?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:635
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:233
msgid "Account username"
msgstr "Accountgebruikersnaam"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:637
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:235
#. translators: %s: asterisk mark
msgid "Account username %s"
msgstr "Accountgebruikersnaam %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:649
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:247
msgid "Create account password"
msgstr "Accountwachtwoord aanmaken"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:651
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:249
#. translators: %s: asterisk mark
msgid "Create account password %s"
msgstr "Accountwachtwoord maken %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:660
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:258
#. translators: %1$s: username, %2$s emailid
msgid " Welcome Back %1$s ( %2$s )"
msgstr "Welkom terug %1$s ( %2$s )"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:915
msgid "Looks like you haven't added any items to cart yet — start shopping to fill it up!"
msgstr "Het lijkt erop dat je nog geen items aan je winkelwagen hebt toegevoegd — begin met winkelen om deze te vullen!"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:927
msgid "Your Cart is Currently Empty."
msgstr "Je winkelwagen is momenteel leeg."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:929
msgid "Start Shopping"
msgstr "Begin met winkelen"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:161
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "Already have an account? %1$1s Log in%2$2s"
msgstr "Heb je al een account? %1$1s Log in%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:177
#. translators: %s: asterisk mark
msgid "Email Address %s"
msgstr "E-mailadres %s"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:69
msgid "Coupon code applied successfully."
msgstr "Couponcode succesvol toegepast."

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:76
msgid "Have a coupon?"
msgstr "Heb je een kortingsbon?"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:95
#: modules/checkout/templates/checkout/order-review-table.php:17
#: modules/checkout/templates/checkout/order-review-table.php:43
#: modules/thankyou/templates/instant-thankyou-your-product.php:118
msgid "Subtotal"
msgstr "Subtotaal"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:147
#: modules/checkout/templates/checkout/order-review-table.php:79
#: modules/thankyou/templates/instant-thankyou-your-product.php:148
msgid "Total"
msgstr "Totaal"

#: modules/checkout/templates/checkout/order-review-table.php:16
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Product"
msgstr "Product"

#: modules/checkout/templates/checkout/shipping-methods.php:53
#. Translators: $s shipping destination.
msgid "Shipping to %s."
msgstr "Verzending naar %s."

#: modules/checkout/templates/checkout/shipping-methods.php:54
#. Translators: $s shipping destination.
msgid "Change address"
msgstr "Adres wijzigen"

#: modules/checkout/templates/checkout/shipping-methods.php:56
msgid "Shipping options will be updated during checkout."
msgstr "Verzendopties worden bijgewerkt tijdens het afrekenen."

#: modules/checkout/templates/wcf-template.php:51
msgid "Copyright &copy;"
msgstr "Auteursrecht &copy;"

#: modules/checkout/templates/wcf-template.php:56
msgid "All Rights Reserved"
msgstr "Alle rechten voorbehouden"

#: modules/elementor/class-cartflows-el-widgets-loader.php:177
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:588
msgid "Cartflows"
msgstr "Cartflows"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:267
#. translators: %s admin link
msgid ""
"This feature is available in the CartFlows higher plan. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Upgrade "
"Now!</a>."
msgstr ""
"Deze functie is beschikbaar in het hogere plan van CartFlows. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Nu "
"upgraden!</a>."

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:370
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:175
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color"
msgstr "Kleur"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:585
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:354
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:343
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Normal"
msgstr "Normaal"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:683
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:417
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:403
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover"
msgstr "Zweven"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:839
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Rounded Corners"
msgstr "Sectie Afgeronde Hoeken"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:164
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title"
msgstr "Titel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:166
msgid "BUY NOW"
msgstr "NU KOPEN"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:176
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sub Title"
msgstr "Ondertitel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title"
msgstr "Voor Titel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:200
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title"
msgstr "Na Titel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:201
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title & Sub Title"
msgstr "Voor Titel & Subtitel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:202
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title & Sub Title"
msgstr "Na Titel & Subtitel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:213
msgid "Icon Vertical Alignment"
msgstr "Pictogram verticale uitlijning"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:223
msgid "Middle"
msgstr "Midden"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:318
msgid "Justify"
msgstr "Rechtvaardigen"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:424
msgid "Hover Text Color"
msgstr "Tekstkleur bij zweven"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:437
msgid "Hover Background Color"
msgstr "Achtergrondkleur bij zweven"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:467
msgid "Hover Animation"
msgstr "Zweefanimatie"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:554
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:645
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:734
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Content"
msgstr "Inhoud"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:499
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Alignment"
msgstr "Tekstuitlijning"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:556
msgid "Title and Sub Title Spacing"
msgstr "Titel- en subtitelafstand"

#: modules/elementor/widgets/class-cartflows-el-optin-form.php:324
#: modules/optin/classes/class-cartflows-optin-meta-data.php:406
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button"
msgstr "Verzendknop"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:187
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:199
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:211
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:223
msgid "Show"
msgstr "Tonen"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:367
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sections"
msgstr "Secties"

#: modules/email-report/class-cartflows-admin-report-emails.php:104
msgid "There"
msgstr "Daar"

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "You have successfully unsubscribed from our weekly emails list."
msgstr "Je bent succesvol uitgeschreven van onze wekelijkse e-maillijst."

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "Unsubscribed"
msgstr "Uitgeschreven"

#: modules/email-report/class-cartflows-admin-report-emails.php:174
msgid "Here’s how your store performed last week!"
msgstr "Zo heeft uw winkel het vorige week gedaan!"

#: modules/email-report/templates/email-body.php:17
msgid "CartFlows Weekly Report"
msgstr "CartFlows Wekelijks Rapport"

#: modules/email-report/templates/email-cf-pro-block.php:26
msgid "CartFlows Pro can help you to increase conversion and maximize profits."
msgstr "CartFlows Pro kan je helpen om de conversie te verhogen en de winst te maximaliseren."

#: modules/email-report/templates/email-cf-pro-block.php:43
msgid ""
"Want to earn 30% more store revenue on autopilot? CartFlows order bumps and upsells help you do just that. Try "
"CartFlows Pro risk-free for 30 days!"
msgstr ""
"Wil je 30% meer winkelomzet verdienen op de automatische piloot? CartFlows order bumps en upsells helpen je daarbij. "
"Probeer CartFlows Pro 30 dagen risicoloos!"

#: modules/email-report/templates/email-cf-pro-block.php:62
msgid "GET CARTFLOWS NOW"
msgstr "HAAL CARTFLOWS NU"

#: modules/email-report/templates/email-content-section.php:27
#. translators: %s user name
msgid "Hey %s!"
msgstr "Hé %s!"

#: modules/email-report/templates/email-content-section.php:42
#. translators: %1$s: store name, %2$s: total revenue.  %3$s: total revenue
msgid ""
"%1$s has earned a total %2$s in revenue last week by using CartFlows to power your store! And in the last month, it "
"earned %3$s"
msgstr ""
"%1$s heeft vorige week een totale omzet van %2$s verdiend door CartFlows te gebruiken om uw winkel aan te drijven! En "
"in de afgelopen maand heeft het %3$s verdiend"

#: modules/email-report/templates/email-content-section.php:79
msgid "(Last 7 days)"
msgstr "(Laatste 7 dagen)"

#: modules/email-report/templates/email-content-section.php:93
msgid "(Last 30 days)"
msgstr "(Laatste 30 dagen)"

#: modules/email-report/templates/email-footer.php:63
#. translators: %1$s - link to a site;
msgid "This email was auto-generated and sent from %1$s."
msgstr "Deze e-mail is automatisch gegenereerd en verzonden vanaf %1$s."

#: modules/email-report/templates/email-footer.php:70
msgid "Unsubscribe"
msgstr "Afmelden"

#: modules/email-report/templates/email-header.php:27
msgid "Your weekly summary from CartFlows."
msgstr "Je wekelijkse samenvatting van CartFlows."

#: modules/email-report/templates/email-other-product-block.php:26
msgid "Would you like to try our other products that help WooCommerce stores sell more?"
msgstr "Zou je onze andere producten willen proberen die WooCommerce-winkels helpen meer te verkopen?"

#: modules/email-report/templates/email-other-product-block.php:41
msgid "TRY OUR OTHER PRODUCTS"
msgstr "PROBEER ONZE ANDERE PRODUCTEN"

#: modules/email-report/templates/email-stat-content.php:26
msgid "Full Overview"
msgstr "Volledig Overzicht"

#: modules/email-report/templates/email-stat-content.php:82
#: modules/email-report/templates/email-stat-content.php:220
msgid "Order Placed"
msgstr "Bestelling Geplaatst"

#: modules/email-report/templates/email-stat-content.php:112
#: modules/email-report/templates/email-stat-content.php:247
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total Visits"
msgstr "Totaal aantal bezoeken"

#: modules/email-report/templates/email-stat-content.php:142
#: modules/email-report/templates/email-stat-content.php:274
msgid "Order Bumps Revenue"
msgstr "Order Bumps Omzet"

#: modules/email-report/templates/email-stat-content.php:172
#: modules/email-report/templates/email-stat-content.php:304
msgid "Offers Revenue"
msgstr "Aanbiedingen Omzet"

#: modules/email-report/templates/email-stat-content.php:250
#: modules/email-report/templates/email-stat-content.php:282
#: modules/email-report/templates/email-stat-content.php:307
msgid "CartFlows Pro"
msgstr "CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Flow: "
msgstr "Stroom:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Name: "
msgstr "Naam:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:105
msgid "Search Flows"
msgstr "Zoekstromen"

#: modules/flow/classes/class-cartflows-flow-post-type.php:106
msgid "All Flows"
msgstr "Alle stromen"

#: modules/flow/classes/class-cartflows-flow-post-type.php:107
msgid "Edit Flow"
msgstr "Stroom bewerken"

#: modules/flow/classes/class-cartflows-flow-post-type.php:108
msgid "View Flow"
msgstr "Bekijk Flow"

#: modules/flow/classes/class-cartflows-flow-post-type.php:109
#: modules/flow/classes/class-cartflows-flow-post-type.php:111
#: modules/flow/classes/class-cartflows-step-post-type.php:176
#: modules/flow/classes/class-cartflows-step-post-type.php:178
#: admin-core/assets/build/settings-app.js:25
msgid "Add New"
msgstr "Nieuw toevoegen"

#: modules/flow/classes/class-cartflows-flow-post-type.php:110
msgid "Update Flow"
msgstr "Update Flow"

#: modules/flow/classes/class-cartflows-flow-post-type.php:112
msgid "New Flow Name"
msgstr "Nieuwe stroomnaam"

#: modules/flow/classes/class-cartflows-flow-post-type.php:194
#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Upgrade to CartFlows Pro"
msgstr "Upgrade naar CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:213
msgid "Slug"
msgstr "Naaktslak"

#: modules/flow/classes/class-cartflows-flow-post-type.php:332
#: modules/flow/classes/class-cartflows-flow-post-type.php:338
#: modules/flow/classes/class-cartflows-step-post-type.php:410
#: modules/flow/classes/class-cartflows-step-post-type.php:416
#. translators: %s: singular custom post type name
msgid "%s updated."
msgstr "%s bijgewerkt."

#: modules/flow/classes/class-cartflows-flow-post-type.php:334
#: modules/flow/classes/class-cartflows-step-post-type.php:412
#. translators: %s: singular custom post type name
msgid "Custom %s updated."
msgstr "Aangepaste %s bijgewerkt."

#: modules/flow/classes/class-cartflows-flow-post-type.php:336
#: modules/flow/classes/class-cartflows-step-post-type.php:414
#. translators: %s: singular custom post type name
msgid "Custom %s deleted."
msgstr "Aangepaste %s verwijderd."

#: modules/flow/classes/class-cartflows-flow-post-type.php:340
#: modules/flow/classes/class-cartflows-step-post-type.php:418
#. translators: %1$s: singular custom post type name ,%2$s: date and time of the revision
msgid "%1$s restored to revision from %2$s"
msgstr "%1$s hersteld naar revisie van %2$s"

#: modules/flow/classes/class-cartflows-flow-post-type.php:342
#: modules/flow/classes/class-cartflows-step-post-type.php:420
#. translators: %s: singular custom post type name
msgid "%s published."
msgstr "%s gepubliceerd."

#: modules/flow/classes/class-cartflows-flow-post-type.php:344
#: modules/flow/classes/class-cartflows-step-post-type.php:422
#. translators: %s: singular custom post type name
msgid "%s saved."
msgstr "%s opgeslagen."

#: modules/flow/classes/class-cartflows-flow-post-type.php:346
#: modules/flow/classes/class-cartflows-step-post-type.php:424
#. translators: %s: singular custom post type name
msgid "%s submitted."
msgstr "%s ingediend."

#: modules/flow/classes/class-cartflows-flow-post-type.php:348
#: modules/flow/classes/class-cartflows-step-post-type.php:426
#. translators: %s: singular custom post type name
msgid "%s scheduled for."
msgstr "%s gepland voor."

#: modules/flow/classes/class-cartflows-flow-post-type.php:350
#: modules/flow/classes/class-cartflows-step-post-type.php:428
#. translators: %s: singular custom post type name
msgid "%s draft updated."
msgstr "%s concept bijgewerkt."

#: modules/flow/classes/class-cartflows-step-meta-base.php:58
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:118
msgid "Design"
msgstr "Ontwerp"

#: modules/flow/classes/class-cartflows-step-meta-base.php:82
msgid "Custom script lets you add your own custom script on front end of this flow page."
msgstr "Aangepast script stelt je in staat om je eigen aangepaste script toe te voegen aan de voorkant van deze stroompagina."

#: modules/flow/classes/class-cartflows-step-post-type.php:172
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Search Steps"
msgstr "Zoekstappen"

#: modules/flow/classes/class-cartflows-step-post-type.php:173
msgid "All Steps"
msgstr "Alle stappen"

#: modules/flow/classes/class-cartflows-step-post-type.php:174
msgid "Edit Step"
msgstr "Bewerk stap"

#: modules/flow/classes/class-cartflows-step-post-type.php:175
msgid "View Step"
msgstr "Stap bekijken"

#: modules/flow/classes/class-cartflows-step-post-type.php:177
msgid "Update Step"
msgstr "Update Stap"

#: modules/flow/classes/class-cartflows-step-post-type.php:179
msgid "New Step Name"
msgstr "Nieuwe stapnaam"

#: modules/flow/classes/class-cartflows-step-post-type.php:220
msgid "Step Type"
msgstr "Staptype"

#: modules/flow/classes/class-cartflows-step-post-type.php:230
msgid "Step Flow"
msgstr "Stapstroom"

#: modules/flow/classes/class-cartflows-step-post-type.php:255
msgid "Optin"
msgstr "Opt-in"

#: modules/flow/classes/class-cartflows-step-post-type.php:276
msgid "Upsell"
msgstr "Upsell"

#: modules/flow/classes/class-cartflows-step-post-type.php:283
msgid "Downsell"
msgstr "Downsell"

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:146
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:201
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:311
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:86
msgid "Permission denied."
msgstr "Toestemming geweigerd."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:225
msgid "No product is selected. Please select products from the checkout meta settings to continue."
msgstr "Er is geen product geselecteerd. Selecteer producten in de afrekenmeta-instellingen om door te gaan."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:328
#: modules/optin/classes/class-cartflows-optin-markup.php:321
msgid "No product is selected. Please select a Simple, Virtual and Free product from the meta settings."
msgstr ""
"Er is geen product geselecteerd. Selecteer alstublieft een Eenvoudig, Virtueel en Gratis product uit de "
"meta-instellingen."

#: modules/landing/classes/class-cartflows-landing-meta-data.php:98
msgid "Next Step Link"
msgstr "Volgende stap link"

#: modules/optin/classes/class-cartflows-optin-markup.php:261
msgid "Please place shortcode on Optin step-type only."
msgstr "Plaats de shortcode alleen op de Optin-staptype."

#: modules/optin/classes/class-cartflows-optin-markup.php:338
msgid "Please update the selected product's price to zero (0)."
msgstr "Werk de prijs van het geselecteerde product bij naar nul (0)."

#: modules/optin/classes/class-cartflows-optin-markup.php:347
#: modules/optin/classes/class-cartflows-optin-markup.php:351
msgid "Please select a Simple, Virtual and Free product."
msgstr "Selecteer alstublieft een Eenvoudig, Virtueel en Gratis product."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:76
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enable Custom Field Editor"
msgstr "Aangepaste veldeneditor inschakelen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a Product"
msgstr "Zoek naar een product"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:229
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows Optin step please %1$sClick here.%2$s"
msgstr "Voor meer informatie over de CartFlows Optin-stap, %1$sklik hier.%2$s"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:262
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:101
msgid "Add this shortcode to your optin page"
msgstr "Voeg deze shortcode toe aan je opt-in pagina"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:270
msgid "Global Settings"
msgstr "Globale instellingen"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:328
#: modules/optin/classes/class-cartflows-optin-meta-data.php:432
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size"
msgstr "Grootte"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:361
#: modules/optin/classes/class-cartflows-optin-meta-data.php:465
msgid "Top Bottom Spacing"
msgstr "Afstand boven en onder"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:368
#: modules/optin/classes/class-cartflows-optin-meta-data.php:472
msgid "Left Right Spacing"
msgstr "Links Rechts Spatiëring"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:382
msgid "Text / Placeholder Color"
msgstr "Tekst / Placeholder Kleur"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:412
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Size"
msgstr "Lettergrootte"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:479
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Position"
msgstr "Positie"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:584
msgid "Optin Settings"
msgstr "Opt-in instellingen"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:598
msgid "Pass Fields as URL Parameters"
msgstr "Geef velden door als URL-parameters"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:601
msgid "You can pass specific fields from the form to next step as URL query parameters."
msgstr "Je kunt specifieke velden van het formulier doorgeven naar de volgende stap als URL-queryparameters."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:606
msgid "Enter form field"
msgstr "Voer formulier veld in"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:609
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:492
msgid "Enter comma seprated field name. E.g. first_name, last_name"
msgstr "Voer door komma's gescheiden veldnamen in. Bijv. first_name, last_name"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:610
msgid "Fields to pass, separated by commas"
msgstr "Velden om door te geven, gescheiden door komma's"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:612
#. translators: %s: link
msgid "You can pass field value as a URL parameter to the next step. %1$sLearn More >>%2$s"
msgstr "Je kunt de veldwaarde als een URL-parameter doorgeven naar de volgende stap. %1$sMeer informatie >>%2$s"

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:180
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:183
msgid "We can't seem to find an order for you."
msgstr "We kunnen geen bestelling voor u vinden."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:272
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:662
msgid "No completed or processing order found to show the order details form demo."
msgstr "Geen voltooide of verwerkte bestelling gevonden om het bestelformulier voor de demo te tonen."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:280
msgid "Order not found. You cannot access this page directly."
msgstr "Bestelling niet gevonden. U kunt deze pagina niet direct openen."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:673
msgid "Order Details Not Found."
msgstr "Bestelgegevens niet gevonden."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:675
msgid "Return to Shopping"
msgstr "Terug naar winkelen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:212
msgid "Font Size (In px)"
msgstr "Lettergrootte (in px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:221
msgid "Advanced Options"
msgstr "Geavanceerde opties"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:227
msgid "Enable Advanced Options"
msgstr "Geavanceerde opties inschakelen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:234
msgid "Container Width (In px)"
msgstr "Containerbreedte (in px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:331
msgid "Background color of left side column for Instant Thank You Layout."
msgstr "Achtergrondkleur van de linkerkolom voor de Instant Thank You-indeling."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:347
msgid "Background color of right side column for Instant Thank You Layout."
msgstr "Achtergrondkleur van de rechterkolom voor de Instant Thank You-indeling."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:417
msgid "Enable Order Overview"
msgstr "Orderoverzicht inschakelen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:424
msgid "Enable Order Details"
msgstr "Orderdetails inschakelen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:431
msgid "Enable Billing Details"
msgstr "Factureringsgegevens inschakelen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:438
msgid "Enable Shipping Details"
msgstr "Verzendgegevens inschakelen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:481
msgid "Thank You Page Text"
msgstr "Tekst van de bedankpagina"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:489
msgid "Redirect After Purchase"
msgstr "Doorverwijzen na aankoop"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:497
msgid "Redirect Link"
msgstr "Link omleiden"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:500
msgid "https://"
msgstr "https://"

#: modules/thankyou/templates/instant-thankyou-order-details.php:41
#. Translators: First name.
msgid "Thank you, %s!"
msgstr "Dank je, %s!"

#: modules/thankyou/templates/instant-thankyou-order-details.php:58
msgid "Order Updates"
msgstr "Bestelupdates"

#: modules/thankyou/templates/instant-thankyou-order-details.php:59
msgid "You will receive order and shipping updates via email."
msgstr "Je ontvangt updates over je bestelling en verzending via e-mail."

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
msgid "Address"
msgstr "Adres"

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Billing"
msgstr "Facturering"

#: modules/thankyou/templates/instant-thankyou-order-details.php:155
msgid "Continue Shopping"
msgstr "Verder winkelen"

#: modules/thankyou/templates/instant-thankyou.php:37
msgid ""
"Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please "
"attempt your purchase again."
msgstr ""
"Helaas kan uw bestelling niet worden verwerkt omdat de oorspronkelijke bank/handelaar uw transactie heeft geweigerd. "
"Probeer uw aankoop opnieuw."

#: modules/thankyou/templates/instant-thankyou.php:41
msgid "Pay"
msgstr "Betalen"

#: modules/thankyou/templates/instant-thankyou.php:43
msgid "My account"
msgstr "Mijn account"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:137
msgid "Select the Flow"
msgstr "Selecteer de Flow"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:147
msgid "Add to Cart text"
msgstr "Tekst toevoegen aan winkelwagen"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:149
msgid "Add to cart"
msgstr "Toevoegen aan winkelwagen"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:154
#. translators: %1$s,%2$s HTML content
msgid ""
"If you want to start the flow from the product page, select the appropriate flow & button text field if required. Refer "
"%1$sthis article%2$s for more information."
msgstr ""
"Als je de stroom vanaf de productpagina wilt starten, selecteer dan de juiste stroom en knoptekstveld indien nodig. "
"Raadpleeg %1$sdit artikel%2$s voor meer informatie."

#: wizard/ajax/wizard.php:207
msgid "Please enter your email ID."
msgstr "Voer alstublieft uw e-mailadres in."

#: wizard/ajax/wizard.php:262
msgid "Oops! Something went wrong. Please refresh the page and try again."
msgstr "Oeps! Er is iets misgegaan. Vernieuw de pagina en probeer het opnieuw."

#: wizard/ajax/wizard.php:363
msgid "Please select any of the page builder to display the ready templates."
msgstr "Selecteer een van de paginabouwers om de kant-en-klare sjablonen weer te geven."

#: wizard/ajax/wizard.php:502
msgid "No flow ID found. Please select atleast one flow to import."
msgstr "Geen flow-ID gevonden. Selecteer alstublieft ten minste één flow om te importeren."

#: admin-core/ajax/importer.php:893
#: wizard/ajax/wizard.php:516
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$sarticle%3$s."
msgstr ""
"Time-outfout bij verzoek. Controleer of de firewall of een beveiligingsplugin de uitgaande HTTP/HTTPS-verzoeken naar "
"templates.cartflows.com blokkeert of niet. %1$sOm dit probleem op te lossen, bekijk dit %2$sartikel%3$s."

#: wizard/ajax/wizard.php:539
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import this template, CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "Om deze sjabloon te importeren, is CartFlows Pro vereist! %1$sUpgrade naar CartFlows Pro%2$s"

#: wizard/ajax/wizard.php:540
#: wizard/ajax/wizard.php:542
msgid "CartFlows Pro Required"
msgstr "CartFlows Pro Vereist"

#: wizard/ajax/wizard.php:546
msgid "Invalid License Key"
msgstr "Ongeldige licentiesleutel"

#: wizard/ajax/wizard.php:548
#. translators: %1$s: link html start, %2$s: link html end
msgid "No valid license key found! %1$sActivate license%2$s"
msgstr "Geen geldige licentiesleutel gevonden! %1$sLicentie activeren%2$s"

#: wizard/inc/wizard-core.php:174
msgid "Thanks for installing and using CartFlows!"
msgstr "Bedankt voor het installeren en gebruiken van CartFlows!"

#: wizard/inc/wizard-core.php:175
msgid "It is easy to use the CartFlows. Please use the setup wizard to quick start setup."
msgstr "Het is gemakkelijk om CartFlows te gebruiken. Gebruik de setup wizard om snel te beginnen met de installatie."

#: wizard/inc/wizard-core.php:177
msgid "Start Wizard"
msgstr "Wizard starten"

#: wizard/inc/wizard-core.php:178
msgid "Skip Setup"
msgstr "Setup overslaan"

#: wizard/inc/wizard-core.php:394
msgid "Oops!! Unexpected error occoured"
msgstr "Oeps!! Onverwachte fout opgetreden"

#: wizard/inc/wizard-core.php:395
msgid "Import template API call failed. Please reload the page and try again!"
msgstr "Importeren van sjabloon-API-oproep is mislukt. Herlaad de pagina en probeer het opnieuw!"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Settings Saved"
msgstr "Instellingen opgeslagen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:53
msgid "Saving…"
msgstr "Opslaan…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:42
msgid "Save Settings"
msgstr "Instellingen opslaan"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Regular Price of the product"
msgstr "Reguliere prijs van het product"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Price after discount."
msgstr "Prijs na korting."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Product Name"
msgstr "Productnaam"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{product_name}} and {{quantity}} to dynamically fetch respective product details."
msgstr "Gebruik {{product_name}} en {{quantity}} om dynamisch de respectieve productdetails op te halen."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Subtext"
msgstr "Subtekst"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{quantity}}, {{discount_value}}, {{discount_percent}} to dynamically fetch respective product details."
msgstr "Gebruik {{quantity}}, {{discount_value}}, {{discount_percent}} om respectievelijk productdetails dynamisch op te halen."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Enable Highlight"
msgstr "Markeren inschakelen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Highlight Text"
msgstr "Tekst markeren"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Create New Product"
msgstr "Nieuw product maken"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add"
msgstr "Toevoegen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Once you have selected products, they will be displayed here."
msgstr "Zodra je producten hebt geselecteerd, worden ze hier weergegeven."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Items"
msgstr "Artikelen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Quantity"
msgstr "Hoeveelheid"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Discount"
msgstr "Korting"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Adding…"
msgstr "Toevoegen…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Please search and select at-lease one product to add."
msgstr "Zoek en selecteer ten minste één product om toe te voegen."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
#: wizard/assets/build/wizard-app.js:1
msgid "Reset"
msgstr "Opnieuw instellen"

#: admin-core/assets/build/editor-app.js:1
msgid "Image Preview"
msgstr "Voorbeeld van afbeelding"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "Upload a file"
msgstr "Een bestand uploaden"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "or drag and drop"
msgstr "of slepen en neerzetten"

#: admin-core/assets/build/editor-app.js:1
msgid "PNG, JPG, GIF up to 10MB"
msgstr "PNG, JPG, GIF tot 10MB"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Select Dates"
msgstr "Selecteer datums"

#: admin-core/assets/build/editor-app.js:4
#: admin-core/assets/build/settings-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Pro %2$s to use %3$s feature."
msgstr "Upgrade alstublieft naar de %1$s CartFlows Pro %2$s om de %3$s functie te gebruiken."

#: admin-core/assets/build/editor-app.js:7
#: admin-core/assets/build/settings-app.js:7
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Higher Plan %2$s to use %3$s feature."
msgstr "Upgrade alstublieft naar het %1$s CartFlows Higher Plan %2$s om de %3$s functie te gebruiken."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
#. translators: %s is replaced with feature name
msgid "Please upgrade to the CartFlows Higher Plan to use the %s feature."
msgstr "Upgrade alstublieft naar het CartFlows Hogere Plan om de %s functie te gebruiken."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Role"
msgstr "Rol"

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Access"
msgstr "Toegang"

#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/settings-app.js:11
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the user role management please %1$sClick here.%2$s"
msgstr "Voor meer informatie over het beheer van gebruikersrollen, %1$sklik hier.%2$s"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
#. translators: %1$s is the selected product of CartFlows, %2$s is the selected version of CartFlows.
msgid "Are you sure you want to rollback to %1$s v%2$s?"
msgstr "Weet je zeker dat je wilt teruggaan naar %1$s v%2$s?"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback"
msgstr "Terugdraaien"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:14
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Cancel"
msgstr "Annuleren"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid ""
"Experiencing an issue with the current version of CartFlows? Roll back to a previous version to help troubleshoot the "
"problem."
msgstr ""
"Ervaar je een probleem met de huidige versie van CartFlows? Ga terug naar een eerdere versie om het probleem te helpen "
"oplossen."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Now"
msgstr "Nu opnieuw genereren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:19
msgid "Reset Permalinks Settings"
msgstr "Permalink-instellingen opnieuw instellen"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate License"
msgstr "Licentie activeren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Deactivate License"
msgstr "Licentie deactiveren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Please enter a valid license key!"
msgstr "Voer een geldige licentiesleutel in!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
#: wizard/assets/build/wizard-app.js:4
msgid "Processing"
msgstr "Verwerken"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unknown error occurred while activating the license."
msgstr "Er is een onbekende fout opgetreden bij het activeren van de licentie."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Facebook Pixel"
msgstr "Facebook Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Analytics Pixel"
msgstr "Google Analytics Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Ads Pixel"
msgstr "Google Ads Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Tiktok Pixel"
msgstr "Tiktok Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Pinterest Tag"
msgstr "Pinterest-tag"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Snapchat Pixel"
msgstr "Snapchat Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Auto Address"
msgstr "Google Auto-adres"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Inline CSS"
msgstr "Inline CSS opnieuw genereren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid " Regenerating…."
msgstr "Opnieuw genereren…."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerated"
msgstr "Geregeneerd"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"If you are using the CartFlows Shortcode and using the Design Settings, then this option will regenerate the steps "
"inline CSS. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Als je de CartFlows Shortcode gebruikt en de Ontwerpinstellingen gebruikt, dan zal deze optie de stappen inline CSS "
"regenereren. Om meer te leren, %1$1s Klik hier %2$2s."

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Updating"
msgstr "Bijwerken"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Permalinks reset successfully"
msgstr "Permanente koppelingen succesvol opnieuw ingesteld"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the CartFlows Permalink settings please %1$sClick here.%2$s"
msgstr "Voor meer informatie over de CartFlows Permalink-instellingen, %1$sklik hier.%2$s"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Oops! You don't have access to this page."
msgstr "Oeps! Je hebt geen toegang tot deze pagina."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You don't have permission to access this page. Please reach out to your admin for help."
msgstr "Je hebt geen toestemming om deze pagina te openen. Neem contact op met je beheerder voor hulp."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Back to Dashboard"
msgstr "Terug naar Dashboard"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Integrations"
msgstr "Integraties"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "User Role Manager"
msgstr "Gebruikersrolbeheerder"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Permalink"
msgstr "Permanente link"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Version Control"
msgstr "Versiebeheer"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Order"
msgstr "Bestelling"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "License"
msgstr "Licentie"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Sync Knowledge Base"
msgstr "Synchroniseer kennisbank"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Knowledge Base"
msgstr "Kennisbank"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Search knowledge base"
msgstr "Zoek in de kennisbank"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "No Docs Founds"
msgstr "Geen documenten gevonden"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Please try syncing the docs library"
msgstr "Probeer de documentbibliotheek te synchroniseren"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Syncing…"
msgstr "Synchroniseren…"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Synced. Reloading.."
msgstr "Gesynchroniseerd. Opnieuw laden.."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Need Help?"
msgstr "Hulp nodig?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "We aim to answer all priority support requests within 2-3 hours."
msgstr "We streven ernaar om alle prioriteitsverzoeken binnen 2-3 uur te beantwoorden."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Get Support"
msgstr "Krijg ondersteuning"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "All Documentation"
msgstr "Alle documentatie"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse documentation, reference material, and tutorials for CartFlows."
msgstr "Blader door documentatie, referentiemateriaal en tutorials voor CartFlows."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "View documentation"
msgstr "Documentatie bekijken"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Videos"
msgstr "Video's"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse tutorial videos on our YouTube channel."
msgstr "Bekijk tutorialvideo's op ons YouTube-kanaal."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Youtube Channel"
msgstr "Youtube-kanaal"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Support"
msgstr "Ondersteuning"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "What's New?"
msgstr "Wat is er nieuw?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Unlicensed"
msgstr "Ongeautoriseerd"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Licensed"
msgstr "Gelicentieerd"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Open Global Settings"
msgstr "Open globale instellingen"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Tutorial Videos"
msgstr "Instructievideo's"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:35
msgid "More Options"
msgstr "Meer opties"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:35
msgid "Control"
msgstr "Beheer"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
#. translators: %d is replaced with the count
msgid "Variation-%d"
msgstr "Variatie-%d"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "No Product Assigned"
msgstr "Geen product toegewezen"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Store Checkout - Remove selected checkout product"
msgstr "Winkelafrekenen - Geselecteerd afrekenproduct verwijderen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "PRO"
msgstr "PRO"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Accepted"
msgstr "Aanbod geaccepteerd"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Rejected"
msgstr "Aanbod afgewezen"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Invalid Position"
msgstr "Ongeldige positie"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Views"
msgstr "Weergaven"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Conversions"
msgstr "Conversies"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Revenue"
msgstr "Omzet"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "Upgrade to Pro"
msgstr "Upgrade naar Pro"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:39
#: admin-core/assets/build/settings-app.js:42
#. translators: %s: step slug
msgid "%s Step"
msgstr "%s Stap"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid "Step Editing is Disabled"
msgstr "Stap bewerken is uitgeschakeld"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Open Settings"
msgstr "Instellingen openen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Duplicate Step"
msgstr "Dubbele stap"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to duplicate this step? Are you sure?"
msgstr "Wilt u deze stap dupliceren? Weet u het zeker?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Delete Step"
msgstr "Stap verwijderen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to delete this step? Are you sure?"
msgstr "Wil je deze stap verwijderen? Weet je het zeker?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Declare Winner"
msgstr "Winnaar aanwijzen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to declare this step as winner? Are you sure?"
msgstr "Wil je deze stap als winnaar aanwijzen? Weet je het zeker?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Archive Step"
msgstr "Archiefstap"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to archive this step? Are you sure?"
msgstr "Wil je deze stap archiveren? Weet je het zeker?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Saving.."
msgstr "Opslaan.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:53
msgid "Saved"
msgstr "Opgeslagen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading.."
msgstr "Opnieuw laden.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Testing"
msgstr "Split Test Testen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stop Split Test"
msgstr "Stop split-test"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Start Split Test"
msgstr "Start A/B-test"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test"
msgstr "Splittest"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stopping…"
msgstr "Stoppen…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Starting…"
msgstr "Beginnen…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading…"
msgstr "Herladen…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Settings"
msgstr "Instellingen voor gesplitste test"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Restore Archived Variation"
msgstr "Gearchiveerde Variatie Herstellen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to restore this archived variation? Are you sure?"
msgstr "Wil je deze gearchiveerde variant herstellen? Weet je het zeker?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:44
msgid "Trash Archived Variation"
msgstr "Prullenbak Gearchiveerde Variatie"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will trash this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Deze actie zal deze gearchiveerde variant en de bijbehorende analyseggegevens permanent verwijderen. %1$s Wilt u deze "
"gearchiveerde variant verwijderen?"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
msgid "Hide Archived Variation"
msgstr "Gearchiveerde variant verbergen"

#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:44
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will hide this archived variation from the list of steps, but its analytics will be visible. %1$s Do you "
"want to hide this archived variation?"
msgstr ""
"Deze actie verbergt deze gearchiveerde variant uit de lijst met stappen, maar de analyses blijven zichtbaar. %1$s Wilt "
"u deze gearchiveerde variant verbergen?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will delete this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Deze actie zal deze gearchiveerde variant en de bijbehorende analyseggegevens permanent verwijderen. %1$s Wilt u deze "
"gearchiveerde variant verwijderen?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Deleted On: "
msgstr "Verwijderd op:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived On: "
msgstr "Gearchiveerd op:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived Steps"
msgstr "Gearchiveerde stappen"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Step Name"
msgstr "Bewerk stapnaam"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "Save"
msgstr "Opslaan"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Update Template"
msgstr "Template bijwerken"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Changing the template will permanently delete the current design in this step. Would you still like to proceed?"
msgstr "Het wijzigen van de sjabloon zal het huidige ontwerp in deze stap permanent verwijderen. Wilt u toch doorgaan?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Change Template"
msgstr "Template wijzigen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If you are using shortcodes, enable this design settings to apply styles."
msgstr "Als je shortcodes gebruikt, schakel dan deze ontwerpinstellingen in om stijlen toe te passen."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Design Settings"
msgstr "Ontwerpinstellingen inschakelen"

#: admin-core/assets/build/editor-app.js:15
#: admin-core/assets/build/settings-app.js:15
#. translators: %s is replaced with plugin name
msgid "Activate %s"
msgstr "Activeer %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
#. translators: %s is replaced with plugin name
msgid "Activating %s"
msgstr "%s activeren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Activated!"
msgstr "Succesvol geactiveerd!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Failed! Activation!"
msgstr "Mislukt! Activering!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Upgrade to Cartflows Pro"
msgstr "Upgrade naar Cartflows Pro"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Activate the License"
msgstr "Activeer de licentie"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Step Type: "
msgstr "Staptype:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "License is required!"
msgstr "Licentie is vereist!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Activate the license to modify this offer step's settings"
msgstr "Activeer de licentie om de instellingen van deze aanbiedingsstap te wijzigen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product Selected"
msgstr "Geen product geselecteerd"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you select the product, they will be displayed here."
msgstr "Zodra u het product selecteert, worden ze hier weergegeven."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping Rate"
msgstr "Verzendtarief"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you have add product, it will be displayed here."
msgstr "Zodra je een product hebt toegevoegd, wordt het hier weergegeven."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Coupon will apply on checkout page"
msgstr "De coupon wordt toegepast op de afrekenpagina"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "AND"
msgstr "EN"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the conditional order bump feature."
msgstr "Werk CartFlows Pro bij naar de nieuwste versie om de voorwaardelijke order bump-functie te gebruiken."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable conditional order bump "
msgstr "Voorwaardelijke orderbump inschakelen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "By enabling this option, you can create the conditions to display the order bump."
msgstr "Door deze optie in te schakelen, kun je de voorwaarden creëren om de order bump weer te geven."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show this order bump if following conditions are true"
msgstr "Toon deze orderbump als de volgende voorwaarden waar zijn"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Condition"
msgstr "Voorwaarde toevoegen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:33
#: admin-core/assets/build/settings-app.js:48
msgid "OR"
msgstr "OF"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Conditions Group"
msgstr "Voorwaarden Groep Toevoegen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Bump Product Image"
msgstr "Bestel Bump Productafbeelding"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Styles"
msgstr "Stijlen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Conditions"
msgstr "Voorwaarden"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Save Changes"
msgstr "Wijzigingen opslaan"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "You have made changes. Do you want to save the changes?"
msgstr "U heeft wijzigingen aangebracht. Wilt u de wijzigingen opslaan?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product"
msgstr "Geen product"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Duplicate Order Bump"
msgstr "Dubbele Bestelverhoging"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Order Bump"
msgstr "Order Bump bewerken"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Trash Order Bump"
msgstr "Prullenbak Bestelling Bump"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Do you really want to trash this order bump permanently?"
msgstr "Wil je deze orderbump echt permanent verwijderen?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Delete Order Bump"
msgstr "Order Bump verwijderen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
msgid "Status"
msgstr "Status"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Actions"
msgstr "Acties"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Create an order bump."
msgstr "Maak een order bump."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enter order bump name"
msgstr "Voer de naam van de orderbump in"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please enter the order bump title"
msgstr "Voer de titel van de orderbump in"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Preview"
msgstr "Voorbeeld"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "View in Full Screen"
msgstr "Volledig scherm bekijken"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Exit Full Screen"
msgstr "Volledig scherm afsluiten"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Submitted"
msgstr "Bestelling Verzonden"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Special Offer"
msgstr "Speciale Aanbieding"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Receipt"
msgstr "Bestelbevestiging"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Checkout Offer"
msgstr "Aanbieding bij afrekenen inschakelen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Dynamic Conditions"
msgstr "Dynamische voorwaarden"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the dynamic offers feature."
msgstr "Werk alstublieft CartFlows Pro bij naar de nieuwste versie om de functie voor dynamische aanbiedingen te gebruiken."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Dynamic Offers"
msgstr "Dynamische aanbiedingen inschakelen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Redirect to: "
msgstr "Omleiden naar:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for step…"
msgstr "Zoek naar stap…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If the following conditions are true"
msgstr "Als de volgende voorwaarden waar zijn"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Dynamic Offer"
msgstr "Dynamisch Aanbod Toevoegen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for default step…"
msgstr "Zoek naar standaardstap…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If all of the above conditions failed."
msgstr "Als al de bovenstaande voorwaarden zijn mislukt."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label"
msgstr "Label"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "ID"
msgstr "ID"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable"
msgstr "Inschakelen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add New Field"
msgstr "Nieuw veld toevoegen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Date & Time"
msgstr "Datum & Tijd"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Custom Field"
msgstr "Aangepast veld toevoegen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "TextArea"
msgstr "Tekstgebied"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Number"
msgstr "Nummer"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Checkbox"
msgstr "Selectievakje"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Radio"
msgstr "Radio"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Select"
msgstr "Selecteer"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Hidden"
msgstr "Verborgen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add To"
msgstr "Toevoegen aan"

#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
#. translators: %$s is replaced with the HTML tag
msgid "Label %1$s*%2$s"
msgstr "Label %1$s*%2$s"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#. translators: %$s is replaced with the HTML tag
msgid ""
"Field value will store in this meta key. Add field id without prefix like \"billing_\" or \"shipping_\". %s Use \"_\" "
"instead of spaces."
msgstr ""
"Veldwaarde wordt opgeslagen in deze meta-sleutel. Voeg veld-id toe zonder voorvoegsel zoals \"billing_\" of "
"\"shipping_\". %s Gebruik \"_\" in plaats van spaties."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min Value"
msgstr "Minimale waarde"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max Value"
msgstr "Maximale waarde"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enter your options separated by (|)."
msgstr "Voer uw opties in, gescheiden door (|)."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "UnChecked"
msgstr "Niet aangevinkt"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Input Type"
msgstr "Veld invoertype"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:50
msgid "Date"
msgstr "Datum"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Time"
msgstr "Tijd"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min "
msgstr "Min"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max "
msgstr "Max"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Show in Email"
msgstr "Weergeven in e-mail"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Billing Fields"
msgstr "Factureringsvelden"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Shipping Fields"
msgstr "Verzendvelden"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Delete Field"
msgstr "Veld verwijderen"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Are you really want to delete field?"
msgstr "Weet je zeker dat je het veld wilt verwijderen?"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Editor"
msgstr "Veldeditor"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Label is required field"
msgstr "Label is een verplicht veld"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "WooCommerce is Required!"
msgstr "WooCommerce is vereist!"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
#. translators: %s: step type
msgid "To modify the %s step options, please install and activate the WooCommerce plugin."
msgstr "Om de %s stapopties te wijzigen, installeer en activeer de WooCommerce-plugin."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activating…"
msgstr "Activeren…"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activated"
msgstr "Geactiveerd"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Funnel Settings"
msgstr "Trechterinstellingen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Save Setting"
msgstr "Instelling opslaan"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Disable Store Checkout"
msgstr "Winkelafrekenen uitschakelen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Enable Store Checkout"
msgstr "Winkelafrekenen inschakelen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Edit Title"
msgstr "Titel bewerken"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Publish or Draft the Funnel"
msgstr "Publiceer of Concept de Trechter"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Publish"
msgstr "Publiceren"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to publish this funnel?"
msgstr "Wil je deze funnel echt publiceren?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Draft Funnel"
msgstr "Concepttrechter"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to draft this funnel?"
msgstr "Wil je deze funnel echt opstellen?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Unique Visits"
msgstr "Unieke bezoeken"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Conversion Rate"
msgstr "Conversieratio"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total number of orders."
msgstr "Totaal aantal bestellingen."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all orders."
msgstr "Totaalbedrag van alle bestellingen."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Average total of every order."
msgstr "Gemiddeld totaal van elke bestelling."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all order bumps."
msgstr "Totaalbedrag van alle orderverhogingen."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Export Flow"
msgstr "Exportstroom"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "View Funnel"
msgstr "Trechter bekijken"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Open Funnel Settings"
msgstr "Open trechterinstellingen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Delete Store Checkout"
msgstr "Winkelafrekenen verwijderen"

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
#. translators: %s new line break
msgid "Do you really want to delete store checkout?%1$1sNOTE: This action cannot be reversed."
msgstr "Weet u zeker dat u de winkelcheckout wilt verwijderen?%1$1sOPMERKING: Deze actie kan niet ongedaan worden gemaakt."

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "archived_date"
msgstr "archived_date"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
#. translators: %1$s: html tag, %2$s: html tag
msgid "%1$sNote:%2$s The orders which are placed by the admins are not considered while calculating the analytics."
msgstr ""
"%1$sOpmerking:%2$s De bestellingen die door de beheerders worden geplaatst, worden niet meegenomen bij het berekenen "
"van de analyses."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Reset Analytics"
msgstr "Analytics opnieuw instellen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Are you really want to reset funnel analytics?"
msgstr "Wil je de trechteranalyse echt opnieuw instellen?"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Resetting"
msgstr "Opnieuw instellen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Automation for"
msgstr "Automatisering voor"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Create a distraction free, high-converting checkout experience without needing a page builder."
msgstr "Creëer een afleidingsvrije, hoog converterende afrekenervaring zonder een paginabouwer nodig te hebben."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Funnel Steps"
msgstr "Trechterstappen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add New Step"
msgstr "Nieuwe stap toevoegen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "No Steps Added."
msgstr "Geen stappen toegevoegd."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Seems like there are no steps created or added in this flow"
msgstr "Het lijkt erop dat er geen stappen zijn gemaakt of toegevoegd in deze flow"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add new step"
msgstr "Nieuwe stap toevoegen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Select Step Type"
msgstr "Selecteer staptype"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Activating WooCommerce.."
msgstr "WooCommerce activeren.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installing WooCommerce.."
msgstr "WooCommerce installeren.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Installed!"
msgstr "Succesvol geïnstalleerd!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installation Failed!"
msgstr "Installatie mislukt!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Step"
msgstr "Importstap"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
msgid "Add multiple steps to your flows today with an upgraded CartFlows plan."
msgstr "Voeg vandaag nog meerdere stappen toe aan je flows met een geüpgraded CartFlows-plan."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
msgid "Get CartFlows Higher Plan"
msgstr "Verkrijg een hoger plan van CartFlows"

#: admin-core/assets/build/editor-app.js:44
#: admin-core/assets/build/editor-app.js:45
#: admin-core/assets/build/editor-app.js:54
#: admin-core/assets/build/editor-app.js:55
#: admin-core/assets/build/editor-app.js:59
#: admin-core/assets/build/editor-app.js:63
#: admin-core/assets/build/editor-app.js:64
#: admin-core/assets/build/settings-app.js:56
#: admin-core/assets/build/settings-app.js:57
#: admin-core/assets/build/settings-app.js:66
#: admin-core/assets/build/settings-app.js:67
#: admin-core/assets/build/settings-app.js:70
#: admin-core/assets/build/settings-app.js:74
#: admin-core/assets/build/settings-app.js:75
#. translators: %s is replaced with plugin name
msgid "Add multiple steps to your flows by activating %s."
msgstr "Voeg meerdere stappen toe aan je flows door %s te activeren."

#: admin-core/assets/build/editor-app.js:46
#: admin-core/assets/build/editor-app.js:47
#: admin-core/assets/build/editor-app.js:56
#: admin-core/assets/build/editor-app.js:60
#: admin-core/assets/build/editor-app.js:65
#: admin-core/assets/build/settings-app.js:58
#: admin-core/assets/build/settings-app.js:59
#: admin-core/assets/build/settings-app.js:68
#: admin-core/assets/build/settings-app.js:71
#: admin-core/assets/build/settings-app.js:76
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating %2$s."
msgstr "Voeg %1$s stap toe aan je flows door %2$s te activeren."

#: admin-core/assets/build/editor-app.js:48
#: admin-core/assets/build/editor-app.js:49
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:60
#: admin-core/assets/build/settings-app.js:61
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click %s to your flows when you upgrade to our CartFlows Higher plan today."
msgstr ""
"Voeg onbeperkte inkomensverhogende één-klik %s toe aan je flows wanneer je vandaag upgrade naar ons CartFlows "
"Higher-plan."

#: admin-core/assets/build/editor-app.js:50
#: admin-core/assets/build/settings-app.js:62
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flow by activating CartFlows license."
msgstr "Voeg %1$s stap toe aan je flow door de CartFlows-licentie te activeren."

#: admin-core/assets/build/editor-app.js:51
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:63
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s."
msgstr "Toegang tot al onze pro-sjablonen door %s te activeren."

#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Access all of our pro templates when you upgrade your plan to CartFlows Pro today."
msgstr "Toegang tot al onze pro-sjablonen wanneer je je plan vandaag nog upgrade naar CartFlows Pro."

#: admin-core/inc/admin-menu.php:318
#: admin-core/inc/admin-menu.php:319
#: classes/class-cartflows-admin.php:250
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Get CartFlows Pro"
msgstr "Koop CartFlows Pro"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating license."
msgstr "Voeg %1$s stap toe aan je flows door de licentie te activeren."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:78
msgid "You need WooCommerce plugin installed and activated to import this step."
msgstr "Je hebt de WooCommerce-plugin nodig die geïnstalleerd en geactiveerd is om deze stap te importeren."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Imported! Redirecting…"
msgstr "Geïmporteerd! Omleiden…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid " Please sync the library and try importing the template again."
msgstr "Synchroniseer alstublieft de bibliotheek en probeer de sjabloon opnieuw te importeren."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Failed! Try again."
msgstr "Importeren mislukt! Probeer het opnieuw."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create Step"
msgstr "Stap maken"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Creating Step.."
msgstr "Stap maken.."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Step Created! Redirecting…"
msgstr "Stap gemaakt! Doorverwijzen…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
msgid "Failed to Create Step!"
msgstr "Stap maken mislukt!"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate license for adding more steps and other features."
msgstr "Activeer licentie om meer stappen en andere functies toe te voegen."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Close the window"
msgstr "Sluit het raam"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Name Your Step"
msgstr "Noem Uw Stap"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Step Name"
msgstr "Stapnaam"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Enter Step Name"
msgstr "Voer stapnaam in"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Learn How"
msgstr "Leer hoe"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create from Scratch"
msgstr "Vanaf nul maken"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Step thumbnail image"
msgstr "Stap miniatuurafbeelding"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Import"
msgstr "Importeren"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
#: wizard/assets/build/wizard-app.js:1
msgid "Back"
msgstr "Terug"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/settings-app.js:34
msgid "Sync Library"
msgstr "Bibliotheek synchroniseren"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
#. translators: %d is replaced with the condition number
msgid "Importing page %d"
msgstr "Pagina %d importeren"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Sync Complete"
msgstr "Synchronisatie voltooid"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Syncing Library…"
msgstr "Bibliotheek synchroniseren…"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Library Synced"
msgstr "Bibliotheek gesynchroniseerd"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Steps Library"
msgstr "Stappenbibliotheek"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:69
msgid "Get CartFlows Higher plan"
msgstr "Verkrijg het hogere CartFlows-plan"

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Activate license for adding more flows and other features."
msgstr "Activeer licentie om meer flows en andere functies toe te voegen."

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Importing Step.."
msgstr "Stap importeren.."

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Close"
msgstr "Sluiten"

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Error"
msgstr "Fout"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click upsells to your flows by activating %s"
msgstr "Voeg onbeperkte inkomstenverhogende one-click upsells toe aan je flows door %s te activeren"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
msgid "Add unlimited income boosting one-click upsells to your flows when you upgrade to our CartFlows Plus or Pro plan today."
msgstr ""
"Voeg onbeperkte inkomstenverhogende one-click upsells toe aan je flows wanneer je vandaag nog upgrade naar ons "
"CartFlows Plus- of Pro-plan."

#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:79
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s"
msgstr "Toegang tot al onze pro-sjablonen door %s te activeren"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:80
#. translators: %s is replaced with the step title
msgid "Templates for %s"
msgstr "Templates voor %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait…"
msgstr "Even geduld…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating funnel…"
msgstr "Even geduld. Trechter dupliceren…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Drafting funnel…"
msgstr "Even geduld. Trechter wordt opgesteld…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting funnel…"
msgstr "Even geduld. Trechter wordt verwijderd…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Restoring funnel…"
msgstr "Even geduld. Trechter wordt hersteld…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Exporting…"
msgstr "Even geduld. Exporteren…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating step…"
msgstr "Even geduld. Stap dupliceren…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting step…"
msgstr "Even geduld. Stap wordt verwijderd…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Creating variation…"
msgstr "Even geduld. Variatie wordt gemaakt…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Archiving variation…"
msgstr "Even geduld. Variatie wordt gearchiveerd…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Declaring winner…"
msgstr "Even geduld. Winnaar wordt bepaald…"

#: admin-core/assets/build/settings-app.js:23
msgid "Getting Started"
msgstr "Aan de slag"

#: admin-core/assets/build/settings-app.js:23
msgid "Introduction to CartFlows"
msgstr "Inleiding tot CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Modernizing WordPress eCommerce!"
msgstr "WordPress eCommerce moderniseren!"

#: admin-core/assets/build/settings-app.js:23
msgid "Create Your First Flow"
msgstr "Maak uw eerste flow"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Setup Wizard"
msgstr "Ga naar de installatiewizard"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:34
msgid "Import Funnel"
msgstr "Importtrechter"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Click for more info"
msgstr "Klik voor meer info"

#: admin-core/assets/build/settings-app.js:23
msgid "You need WooCommerce plugin installed and activated to import this funnel."
msgstr "Je hebt de WooCommerce-plugin geïnstalleerd en geactiveerd nodig om deze funnel te importeren."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates by activating CartFlows Pro."
msgstr "Verkrijg toegang tot al onze pro-sjablonen door CartFlows Pro te activeren."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates when you activate CartFlows Pro license."
msgstr "Toegang tot al onze pro-sjablonen wanneer je de CartFlows Pro-licentie activeert."

#: admin-core/assets/build/settings-app.js:23
msgid "Importing Complete Funnel.."
msgstr "Importeren van volledige trechter voltooid.."

#: admin-core/assets/build/settings-app.js:23
msgid "Design Your Funnel"
msgstr "Ontwerp je trechter"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Created! Redirecting…"
msgstr "Aangemaakt! Bezig met doorverwijzen…"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Failed to Create Flow!"
msgstr "Maken van Flow mislukt!"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To CartFlows Pro"
msgstr "Upgrade naar CartFlows Pro"

#: admin-core/assets/build/settings-app.js:23
msgid "Name Your Funnel"
msgstr "Noem je trechter"

#: admin-core/assets/build/settings-app.js:23
msgid "Funnel Name"
msgstr "Trechternaam"

#: admin-core/assets/build/settings-app.js:23
msgid "Enter Funnel Name"
msgstr "Voer trechternaam in"

#: admin-core/assets/build/settings-app.js:24
msgid "Welcome to CartFlows "
msgstr "Welkom bij CartFlows"

#: admin-core/assets/build/settings-app.js:24
msgid "Sales funnel builder turns your WordPress website into an optimized selling machine."
msgstr "Met de sales funnel builder verandert u uw WordPress-website in een geoptimaliseerde verkoopmachine."

#: admin-core/assets/build/settings-app.js:24
msgid "Create Your First Funnel"
msgstr "Maak je eerste trechter"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"A sales funnel is the sequence of steps a buyer takes to make a purchase. CartFlows helps optimize funnels to turn "
"visitors into customers."
msgstr ""
"Een verkooptrechter is de reeks stappen die een koper neemt om een aankoop te doen. CartFlows helpt trechters te "
"optimaliseren om bezoekers in klanten te veranderen."

#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
msgid "Create New Funnel"
msgstr "Nieuwe Trechter Maken"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views"
msgstr "Totaal aantal paginaweergaven"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Revenue"
msgstr "Totale omzet"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Orders"
msgstr "Totaal aantal bestellingen"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue"
msgstr "Aanbiedingsinkomsten"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Views"
msgstr "Totaal aantal weergaven"

#: admin-core/assets/build/settings-app.js:24
msgid "Overview"
msgstr "Overzicht"

#: admin-core/assets/build/settings-app.js:24
msgid "WooCommerce plugin is required."
msgstr "WooCommerce-plug-in is vereist."

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the overview"
msgstr "Je hebt de WooCommerce-plugin nodig die geïnstalleerd en geactiveerd is om het overzicht te bekijken"

#: admin-core/assets/build/settings-app.js:24
msgid "Recent Orders"
msgstr "Recente bestellingen"

#: admin-core/assets/build/settings-app.js:24
msgid "View All"
msgstr "Alles bekijken"

#: admin-core/assets/build/settings-app.js:24
msgid "Customer"
msgstr "Klant"

#: admin-core/assets/build/settings-app.js:24
msgid "Payment Method"
msgstr "Betaalmethode"

#: admin-core/assets/build/settings-app.js:24
msgid "Value"
msgstr "Waarde"

#: admin-core/assets/build/settings-app.js:24
msgid "at"
msgstr "bij"

#: admin-core/assets/build/settings-app.js:24
msgid "Find recent order here"
msgstr "Vind recente bestelling hier"

#: admin-core/assets/build/settings-app.js:24
msgid "Once you have received orders, come back here to find it again easily"
msgstr "Zodra je bestellingen hebt ontvangen, kom hier terug om ze gemakkelijk weer te vinden"

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the recent orders"
msgstr "Je hebt de WooCommerce-plugin nodig die geïnstalleerd en geactiveerd is om de recente bestellingen te bekijken"

#: admin-core/assets/build/settings-app.js:24
msgid "Quick Actions"
msgstr "Snelle acties"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Funnel"
msgstr "Een trechter maken"

#: admin-core/assets/build/settings-app.js:80
msgid "Analytics"
msgstr "Analytics"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Product"
msgstr "Een product maken"

#: admin-core/assets/build/settings-app.js:24
msgid "Create new Product"
msgstr "Nieuw product maken"

#: admin-core/assets/build/settings-app.js:24
msgid "All Funnels"
msgstr "Alle trechters"

#: admin-core/assets/build/settings-app.js:24
msgid "View all funnels"
msgstr "Alle trechters bekijken"

#: admin-core/assets/build/settings-app.js:24
msgid "Previous"
msgstr "Vorige"

#: admin-core/assets/build/settings-app.js:24
#: wizard/assets/build/wizard-app.js:1
msgid "Next"
msgstr "Volgende"

#: admin-core/assets/build/settings-app.js:24
msgid "First"
msgstr "Eerste"

#: admin-core/assets/build/settings-app.js:24
msgid "Last"
msgstr "Laatst"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"You can specify a file to import by either dragging it into the drag and drop area.(Maximum file size of 5MB; .json "
"file extensions only.)"
msgstr ""
"U kunt een bestand specificeren om te importeren door het naar het sleepgebied te slepen. (Maximale bestandsgrootte van "
"5MB; alleen .json-bestandsextensies.)"

#: admin-core/assets/build/settings-app.js:24
msgid "Change a file"
msgstr "Wijzig een bestand"

#: admin-core/assets/build/settings-app.js:24
msgid "JSON file up to 5MB"
msgstr "JSON-bestand tot 5MB"

#: admin-core/assets/build/settings-app.js:25
#. translators: %s is replaced with the file name.
msgid "File Selected: %s"
msgstr "Bestand geselecteerd: %s"

#: admin-core/assets/build/settings-app.js:25
msgid "Please select the valid json file."
msgstr "Selecteer het geldige json-bestand."

#: admin-core/assets/build/settings-app.js:25
#: wizard/assets/build/wizard-app.js:5
msgid "Importing.."
msgstr "Importeren.."

#: admin-core/assets/build/settings-app.js:25
msgid "Export All"
msgstr "Alles exporteren"

#: admin-core/assets/build/settings-app.js:25
msgid "Exporting…"
msgstr "Exporteren…"

#: admin-core/assets/build/settings-app.js:25
msgid "Search Funnels"
msgstr "Zoektrechters"

#: admin-core/assets/build/settings-app.js:25
msgid "Filter Funnels by Date"
msgstr "Trechters filteren op datum"

#: admin-core/assets/build/settings-app.js:25
msgid "Publish "
msgstr "Publiceren"

#: admin-core/assets/build/settings-app.js:25
msgid "Draft "
msgstr "Ontwerp"

#: admin-core/assets/build/settings-app.js:25
msgid "Trash "
msgstr "Afval"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:33
msgid "Mode"
msgstr "Modus"

#: admin-core/assets/build/settings-app.js:25
msgid "All"
msgstr "Alle"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
msgid "Live"
msgstr "Live"

#: admin-core/assets/build/settings-app.js:25
msgid "SandBox"
msgstr "SandBox"

#: admin-core/assets/build/settings-app.js:25
msgid "Reset Filters"
msgstr "Filters opnieuw instellen"

#: admin-core/assets/build/settings-app.js:28
#. translators: %s: action name.
msgid "%s This Flow"
msgstr "%s Deze flow"

#: admin-core/assets/build/settings-app.js:31
#. translators: %s: action status name.
msgid "Do you want to %s this flow? Are you sure?"
msgstr "Wilt u deze flow %s? Weet u het zeker?"

#: admin-core/assets/build/settings-app.js:31
msgid "items selected"
msgstr "items geselecteerd"

#: admin-core/assets/build/settings-app.js:31
msgid "Applying changes…"
msgstr "Wijzigingen toepassen…"

#: admin-core/assets/build/settings-app.js:31
msgid " Published "
msgstr "Gepubliceerd"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate Funnel"
msgstr "Funnel dupliceren"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to duplicate this funnel?"
msgstr "Wilt u deze trechter echt dupliceren?"

#: admin-core/assets/build/settings-app.js:31
msgid "Trash Funnel"
msgstr "Afvaltrechter"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this funnel?"
msgstr "Wil je deze trechter echt weggooien?"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this Funnel?"
msgstr "Wil je deze trechter echt verwijderen?"

#: admin-core/assets/build/settings-app.js:31
msgid "Restore Funnel"
msgstr "Trechter herstellen"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to restore this funnel?"
msgstr "Wil je deze trechter echt herstellen?"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Draft"
msgstr "Concept"

#: admin-core/assets/build/settings-app.js:32
#. translators: %s date
msgid "Last Modified: %s"
msgstr "Laatst gewijzigd: %s"

#: admin-core/assets/build/settings-app.js:32
msgid "Updated "
msgstr "Bijgewerkt"

#: admin-core/assets/build/settings-app.js:32
msgid "Sandbox"
msgstr "Sandbox"

#: admin-core/assets/build/settings-app.js:32
msgid "WooCommerce Required to display the revenue."
msgstr "WooCommerce vereist om de omzet weer te geven."

#: admin-core/assets/build/settings-app.js:32
msgid "Restore Flow"
msgstr "Herstel Flow"

#: admin-core/assets/build/settings-app.js:32
msgid "Delete Flow"
msgstr "Flow verwijderen"

#: admin-core/assets/build/settings-app.js:32
msgid "Upgrade to Pro for this feature."
msgstr "Upgrade naar Pro voor deze functie."

#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate (Pro)"
msgstr "Dupliceren (Pro)"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash Flow"
msgstr "Afvalstroom"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash"
msgstr "Afval"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Name"
msgstr "Naam"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Sales"
msgstr "Verkoop"

#: admin-core/assets/build/settings-app.js:32
msgid "Move to Trash"
msgstr "Verplaatsen naar prullenbak"

#: admin-core/assets/build/settings-app.js:33
#. translators: %d Search term
msgid "No matching results found for the search term \"%s\"."
msgstr "Geen overeenkomende resultaten gevonden voor de zoekterm \"%s\"."

#: admin-core/assets/build/settings-app.js:33
msgid "No flows found for the selected filter."
msgstr "Geen stromen gevonden voor het geselecteerde filter."

#: admin-core/assets/build/settings-app.js:33
msgid "Please try using different keywords, date range, or filters to refine your results."
msgstr "Probeer verschillende trefwoorden, datumbereiken of filters te gebruiken om uw resultaten te verfijnen."

#: admin-core/assets/build/settings-app.js:33
msgid "Create New"
msgstr "Nieuw maken"

#: admin-core/assets/build/settings-app.js:34
#. translators: %d flow count
msgid " %d items"
msgstr "%d items"

#: admin-core/assets/build/settings-app.js:34
msgid "Create your first funnel"
msgstr "Maak je eerste trechter"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Build a sales funnel with everything you need to generate leads and grow sales."
msgstr "Bouw een verkooptrechter met alles wat je nodig hebt om leads te genereren en de verkoop te laten groeien."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "One Click Upsells"
msgstr "One Click Upsells"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "A/B Split Testing"
msgstr "A/B-splittesten"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Conversion Templates"
msgstr "Conversiesjablonen"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Editor"
msgstr "Afrekeneditor"

#: admin-core/assets/build/settings-app.js:34
msgid "Insights"
msgstr "Inzichten"

#: admin-core/assets/build/settings-app.js:34
msgid "Create Funnel"
msgstr "Trechter maken"

#: admin-core/assets/build/settings-app.js:34
msgid "Plugin Required"
msgstr "Plug-in vereist"

#: admin-core/assets/build/settings-app.js:34
msgid "You need WooCommerce plugin installed and activated to access this page."
msgstr "Je hebt de WooCommerce-plugin nodig die geïnstalleerd en geactiveerd is om deze pagina te openen."

#: admin-core/assets/build/settings-app.js:34
msgid "Installing"
msgstr "Installeren"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
msgid "Activating"
msgstr "Activeren"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Failed"
msgstr "Mislukt"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Redirecting"
msgstr "Omleiden"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Create Store Checkout"
msgstr "Afrekenen winkel maken"

#: admin-core/assets/build/settings-app.js:34
msgid "Name Your Store Checkout"
msgstr "Noem uw winkelafrekenpunt"

#: admin-core/assets/build/settings-app.js:34
msgid "You can't create more than 3 flows in free version. Upgrade to CartFlows Pro for adding more flows and other features."
msgstr ""
"Je kunt niet meer dan 3 flows maken in de gratis versie. Upgrade naar CartFlows Pro om meer flows en andere functies "
"toe te voegen."

#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To Cartflows Pro"
msgstr "Upgrade naar Cartflows Pro"

#: admin-core/assets/build/settings-app.js:34
msgid "Store Checkout Name"
msgstr "Naam winkelkassa"

#: admin-core/assets/build/settings-app.js:34
msgid "Enter Store Checkout Name"
msgstr "Voer de naam van de winkelkassa in"

#: admin-core/assets/build/settings-app.js:34
msgid "Create a global store checkout"
msgstr "Maak een wereldwijde winkelafrekening"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"A well-designed checkout page can help streamline the checkout process, reduce cart abandonment rates and increase "
"conversions."
msgstr ""
"Een goed ontworpen afrekenpagina kan helpen het afrekenproces te stroomlijnen, het aantal verlaten winkelwagentjes te "
"verminderen en de conversies te verhogen."

#: admin-core/assets/build/settings-app.js:34
msgid "Improved user experience"
msgstr "Verbeterde gebruikerservaring"

#: admin-core/assets/build/settings-app.js:34
msgid "Brand consistency"
msgstr "Merkconsistentie"

#: admin-core/assets/build/settings-app.js:34
msgid "Increased trust and credibility"
msgstr "Verhoogd vertrouwen en geloofwaardigheid"

#: admin-core/assets/build/settings-app.js:34
msgid "Flexibility and customization"
msgstr "Flexibiliteit en maatwerk"

#: admin-core/assets/build/settings-app.js:34
msgid "Competitive advantage"
msgstr "Concurrentievoordeel"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"By setting up the store checkout, your default checkout page will be replaced by the CartFlows modern checkout which "
"will lead to more conversion and leads."
msgstr ""
"Door de winkelcheckout in te stellen, wordt uw standaard afrekenpagina vervangen door de moderne checkout van "
"CartFlows, wat zal leiden tot meer conversie en leads."

#: admin-core/assets/build/settings-app.js:34
msgid "Get Started"
msgstr "Beginnen"

#: admin-core/assets/build/settings-app.js:34
msgid "Connect a Payment Gateway"
msgstr "Verbind een betalingsgateway"

#: admin-core/assets/build/settings-app.js:34
msgid "Stripe for WooCommerce delivers a simple, secure way to accept credit card payments in your WooCommerce store."
msgstr ""
"Stripe voor WooCommerce biedt een eenvoudige en veilige manier om creditcardbetalingen in je WooCommerce-winkel te "
"accepteren."

#: admin-core/assets/build/settings-app.js:34
msgid "Connect with Stripe"
msgstr "Verbind met Stripe"

#: admin-core/assets/build/settings-app.js:34
msgid "Setting up…"
msgstr "Bezig met instellen…"

#: admin-core/assets/build/settings-app.js:34
msgid "Recover Abandoned Carts"
msgstr "Herstel Verlaten Winkelwagens"

#: admin-core/assets/build/settings-app.js:34
msgid "Use our cart abandonment plugin and automatically recover your lost revenue absolutely free."
msgstr "Gebruik onze winkelwagen-verlatingsplugin en herstel automatisch uw verloren inkomsten helemaal gratis."

#: admin-core/assets/build/settings-app.js:34
msgid "Finishing…"
msgstr "Bezig met afronden…"

#: admin-core/assets/build/settings-app.js:34
msgid "Setup Email Reports"
msgstr "E-mailrapporten instellen"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send you an email report with "
"key metrics and insights."
msgstr ""
"Laat CartFlows het giswerk uit je afrekenresultaten halen. Elke week stuurt je winkel je een e-mailrapport met "
"belangrijke statistieken en inzichten."

#: admin-core/assets/build/settings-app.js:34
msgid "Add Email Address"
msgstr "E-mailadres toevoegen"

#: admin-core/assets/build/settings-app.js:34
msgid "Dismiss Setup"
msgstr "Setup afsluiten"

#: admin-core/assets/build/settings-app.js:34
msgid "Active"
msgstr "Actief"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Activate"
msgstr "Activeren"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Install"
msgstr "Installeren"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Installing…"
msgstr "Installeren…"

#: admin-core/assets/build/settings-app.js:34
msgid "Installed"
msgstr "Geïnstalleerd"

#: admin-core/assets/build/settings-app.js:34
msgid "Let's Go"
msgstr "Laten we gaan"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Plugins"
msgstr "Aanbevolen plug-ins"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Themes"
msgstr "Aanbevolen thema's"

#: admin-core/assets/build/settings-app.js:34
msgid "View All Steps"
msgstr "Alle stappen bekijken"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel thumbnail image"
msgstr "Trechter miniatuurafbeelding"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel Preview"
msgstr "Trechtervoorbeeld"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Live Preview"
msgstr "Livevoorbeeld"

#: admin-core/assets/build/settings-app.js:34
msgid "Funnel Templates"
msgstr "Trechtertemplates"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Start from scratch"
msgstr "Begin opnieuw"

#: admin-core/assets/build/settings-app.js:34
msgid "It seems that the page builder you selected is inactive."
msgstr "Het lijkt erop dat de paginabouwer die je hebt geselecteerd inactief is."

#: admin-core/assets/build/settings-app.js:34
msgid " to see CartFlows templates. If you prefer another page builder tool, you can "
msgstr "om CartFlows-sjablonen te bekijken. Als je een andere paginabuildertool verkiest, kun je"

#: admin-core/assets/build/settings-app.js:34
msgid "select it here"
msgstr "selecteer het hier"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using any other page builder? No worries. CartFlows works well with every other page builder. Right now we do "
"not have ready templates for every page builder but we are planning to add it very soon."
msgstr ""
"Gebruik je een andere paginabouwer? Geen zorgen. CartFlows werkt goed met elke andere paginabouwer. Op dit moment "
"hebben we nog geen kant-en-klare sjablonen voor elke paginabouwer, maar we zijn van plan om deze zeer binnenkort toe te "
"voegen."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Learn How "
msgstr "Leer hoe"

#: admin-core/assets/build/settings-app.js:35
msgid "No Results Found."
msgstr "Geen resultaten gevonden."

#: admin-core/assets/build/settings-app.js:35
msgid "Don't see a funnel that you would like to import?"
msgstr "Ziet u geen trechter die u wilt importeren?"

#: admin-core/assets/build/settings-app.js:35
msgid "Please suggest us "
msgstr "Stel ons alstublieft voor"

#: admin-core/assets/build/settings-app.js:35
msgid "Choose a Funnel Templates"
msgstr "Kies een trechtertemplate"

#: admin-core/assets/build/settings-app.js:35
msgid "Search Templates"
msgstr "Zoeksjablonen"

#: admin-core/assets/build/settings-app.js:35
msgid "Start from Scratch"
msgstr "Begin helemaal opnieuw"

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the Bricks Builder."
msgstr "Het lijkt erop dat je de Bricks Builder gebruikt."

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the page builder other than Elementor, Beaver Builder, Block Builder."
msgstr "Het lijkt erop dat je een andere paginabouwer gebruikt dan Elementor, Beaver Builder, Block Builder."

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using Bricks Builder? No worries. CartFlows works well with Bricks Builder. Right now we do not have ready "
"templates for Bricks Builder but we are planning to add it very soon."
msgstr ""
"Gebruik je Bricks Builder? Geen zorgen. CartFlows werkt goed met Bricks Builder. Op dit moment hebben we nog geen "
"kant-en-klare sjablonen voor Bricks Builder, maar we zijn van plan deze zeer binnenkort toe te voegen."

#: admin-core/assets/build/settings-app.js:35
msgid "Checkout Page"
msgstr "Afrekenpagina"

#: admin-core/assets/build/settings-app.js:35
msgid "Oops!!! No template Found."
msgstr "Oeps!!! Geen sjabloon gevonden."

#: admin-core/assets/build/settings-app.js:35
msgid "Seems like no template is available for chosen editor."
msgstr "Het lijkt erop dat er geen sjabloon beschikbaar is voor de gekozen editor."

#: admin-core/assets/build/settings-app.js:35
msgid "Store Checkout Templates"
msgstr "Winkelafrekeningssjablonen"

#: admin-core/assets/build/settings-app.js:80
msgid "No CartFlows Logs Found."
msgstr "Geen CartFlows-logboeken gevonden."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Logs"
msgstr "CartFlows-logboeken"

#: admin-core/assets/build/settings-app.js:80
msgid "Copied"
msgstr "Gekopieerd"

#: admin-core/assets/build/settings-app.js:80
msgid "Copy"
msgstr "Kopiëren"

#: admin-core/assets/build/settings-app.js:80
msgid "Downloading"
msgstr "Downloaden"

#: admin-core/assets/build/settings-app.js:80
msgid "Download"
msgstr "Downloaden"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleting"
msgstr "Verwijderen"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleted"
msgstr "Verwijderd"

#: admin-core/assets/build/settings-app.js:80
msgid "Email Marketing Automation"
msgstr "E-mailmarketingautomatisering"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate email marketing campaigns based on customer actions, such as abandoned carts or completed purchases in "
"WooCommerce."
msgstr ""
"Automatiseer e-mailmarketingcampagnes op basis van klantacties, zoals verlaten winkelwagentjes of voltooide aankopen in "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Customer Birthday Campaigns"
msgstr "Klantverjaardagscampagnes"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automatically send personalized birthday offers or discounts to customers based on their birthdate stored in "
"WooCommerce."
msgstr ""
"Stuur automatisch gepersonaliseerde verjaardagsaanbiedingen of kortingen naar klanten op basis van hun geboortedatum "
"die is opgeslagen in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Order Notification"
msgstr "Bestelmelding"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Receive instant notifications via SMS, Slack, WhatsApp, or messaging apps when new orders are placed in your "
"WooCommerce store."
msgstr ""
"Ontvang direct meldingen via SMS, Slack, WhatsApp of berichten-apps wanneer er nieuwe bestellingen worden geplaatst in "
"je WooCommerce-winkel."

#: admin-core/assets/build/settings-app.js:80
msgid "Payment and Accounting Integration"
msgstr "Betalings- en boekhoudintegratie"

#: admin-core/assets/build/settings-app.js:80
msgid "Sync WooCommerce sales data with your accounting software for streamlined financial management."
msgstr "Synchroniseer WooCommerce-verkoopgegevens met uw boekhoudsoftware voor gestroomlijnd financieel beheer."

#: admin-core/assets/build/settings-app.js:80
msgid "Coupon Code Marketing"
msgstr "Marketing met kortingscodes"

#: admin-core/assets/build/settings-app.js:80
msgid "Automate the creation and distribution of coupon codes based on specific conditions or customer actions in WooCommerce."
msgstr ""
"Automatiseer het aanmaken en verspreiden van kortingscodes op basis van specifieke voorwaarden of klantacties in "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Upsell and Cross-sell Campaigns"
msgstr "Upsell- en cross-sellcampagnes"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate targeted upsell and cross-sell offers based on customers' purchase history or product interactions in "
"WooCommerce."
msgstr ""
"Automatiseer gerichte upsell- en cross-sell-aanbiedingen op basis van de aankoopgeschiedenis of productinteracties van "
"klanten in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Connect Your Website"
msgstr "Verbind uw website"

#: admin-core/assets/build/settings-app.js:80
msgid "Reloading"
msgstr "Herladen"

#: admin-core/assets/build/settings-app.js:80
msgid "Connecting"
msgstr "Verbinden"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate WooCommerce and CartFlows with Anything"
msgstr "Integreer WooCommerce en CartFlows met Alles"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate all your apps, plugins, and services to automate repetitive tasks."
msgstr "Integreer al je apps, plugins en diensten om repetitieve taken te automatiseren."

#: admin-core/assets/build/settings-app.js:80
msgid "These are just some examples. The possibilities are truly endless!"
msgstr "Dit zijn slechts enkele voorbeelden. De mogelijkheden zijn werkelijk eindeloos!"

#: admin-core/assets/build/settings-app.js:80
msgid "Trusted by World's Top Brands to Connect Their Apps"
msgstr "Vertrouwd door 's werelds topmerken om hun apps te verbinden"

#: admin-core/assets/build/settings-app.js:80
msgid "Connect your apps and automate your business."
msgstr "Verbind je apps en automatiseer je bedrijf."

#: modules/gutenberg/build/blocks-placeholder.js:12
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail Form"
msgstr "Bestelformulier"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Overview."
msgstr "Orderoverzicht verbergen/weergeven."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail"
msgstr "Bestelgegevens"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Detail."
msgstr "Orderdetails verbergen/weergeven."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Billing Address."
msgstr "Factuuradres verbergen/weergeven."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Shipping Address."
msgstr "Verzendadres verbergen/weergeven."

#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text"
msgstr "Koptekst"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading Bottom Spacing(px)"
msgstr "Onderste ruimte van koptekst (px)"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Opacity"
msgstr "Ondoorzichtigheid"

#: modules/gutenberg/build/blocks.js:7
msgid "Section Spacing"
msgstr "Sectieafstand"

#: modules/gutenberg/build/blocks.js:7
msgid "Download Details"
msgstr "Downloadgegevens"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Loading"
msgstr "Laden"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Order Detail Form Block"
msgstr "CartFlows Bestelformulier Blok"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cartflows"
msgstr "cartflows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "order detail form"
msgstr "bestelformulier"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cf"
msgstr "cf"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Color"
msgstr "Pictogramkleur"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Hover Color"
msgstr "Pictogram zweefkleur"

#: modules/gutenberg/build/blocks.js:11
msgid "Gap Between Icon And Text"
msgstr "Afstand tussen pictogram en tekst"

#: modules/gutenberg/build/blocks.js:11
msgid "Subtitle"
msgstr "Ondertitel"

#: modules/gutenberg/build/blocks.js:11
msgid "Enable Subtitle"
msgstr "Ondertiteling inschakelen"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Bottom Spacing"
msgstr "Titel Onderste Spatiëring"

#: modules/gutenberg/build/blocks.js:11
msgid "SubTitle"
msgstr "Ondertitel"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Add text…"
msgstr "Tekst toevoegen…"

#: modules/gutenberg/build/blocks.js:11
msgid "CartFlows Next Step Button Block."
msgstr "CartFlows Volgende Stap Knopblok."

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "next step button"
msgstr "volgende stap knop"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step ( Pro )"
msgstr "Twee Stappen ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout ( Pro )"
msgstr "Meerstapsafrekenen ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This feature is available in the CartFlows higher plan. Upgrade Now!."
msgstr "Opmerking: Deze functie is beschikbaar in het hogere plan van CartFlows. Upgrade nu!."

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Skin"
msgstr "Invoerveldhuid"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Field"
msgstr "Invoerveld"

#: modules/gutenberg/build/blocks.js:11
msgid "Field Text Color"
msgstr "Veldtekstkleur"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Validation"
msgstr "Invoerveldvalidatie"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This styling can be only seen at frontend"
msgstr "Opmerking: Deze styling is alleen zichtbaar aan de voorkant"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Horizontal"
msgstr "Horizontaal"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Vertical"
msgstr "Verticaal"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Blur"
msgstr "Vervagen"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spread"
msgstr "Verspreiden"

#: modules/gutenberg/build/blocks.js:11
msgid "Buttons Text"
msgstr "Knoppen Tekst"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Color"
msgstr "Titelkleur"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Background Color"
msgstr "Achtergrondkleur van de titel"

#: modules/gutenberg/build/blocks.js:11
msgid "Desc Background Color"
msgstr "Achtergrondkleur beschrijven"

#: modules/gutenberg/build/blocks.js:11
msgid "Success/Error Message"
msgstr "Succes-/Foutmelding"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Message Color"
msgstr "Berichtkleur"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label"
msgstr "Zwevend label"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Skin"
msgstr "Invoerskin"

#: modules/gutenberg/build/blocks.js:11
msgid "Skin"
msgstr "Huid"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide Advanced"
msgstr "Geavanceerd verbergen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Line Height"
msgstr "Regelhoogte"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color Settings"
msgstr "Kleurinstellingen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Overlay Color"
msgstr "Overlaykleur"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Inset"
msgstr "Invoegen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Outset"
msgstr "Begin"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing Between Sections(px)"
msgstr "Afstand tussen secties (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Type"
msgstr "Achtergrondtype"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image"
msgstr "Afbeelding"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Image"
msgstr "Achtergrondafbeelding"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Background Image"
msgstr "Achtergrondafbeelding selecteren"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Replace image"
msgstr "Afbeelding vervangen"

#: admin-core/assets/build/editor-app.js:1
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Remove Image"
msgstr "Afbeelding verwijderen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image Position"
msgstr "Afbeeldingspositie"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Left"
msgstr "Boven links"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Center"
msgstr "Boven Midden"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Right"
msgstr "Boven rechts"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Left"
msgstr "Midden links"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Center"
msgstr "Centrum Centrum"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Right"
msgstr "Centrum Rechts"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Left"
msgstr "Linksonder"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Center"
msgstr "Onderkant midden"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Right"
msgstr "Rechtsonder"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Attachment"
msgstr "Bijlage"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Fixed"
msgstr "Vast"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Scroll"
msgstr "Scrollen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat"
msgstr "Herhaal"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "No Repeat"
msgstr "Geen herhaling"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-x"
msgstr "Herhaal-x"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-y"
msgstr "Herhaal-y"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Auto"
msgstr "Auto"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Cover"
msgstr "Omslag"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Contain"
msgstr "Bevatten"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Weight"
msgstr "Lettertypegewicht"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Subset"
msgstr "Lettertype subset"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "px"
msgstr "px"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "em"
msgstr "em"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size Type"
msgstr "Grootte Type"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button Block"
msgstr "Volgende stap knopblok"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Icon"
msgstr "Selecteer pictogram"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button Hover Color"
msgstr "Kleur bij zweven over knop"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Groove"
msgstr "Groef"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Ridge"
msgstr "Rug"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Bottom Spacing (px)"
msgstr "Titel Onder Spatiëring (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover Color"
msgstr "Zweefkleur"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Transform"
msgstr "Tekst Transformeren"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Capitalize"
msgstr "Hoofdlettergebruik"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Uppercase"
msgstr "Hoofdletters"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Lowercase"
msgstr "Lowercase"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Letter Spacing (px)"
msgstr "Letterafstand (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Checkout Block"
msgstr "CartFlows Checkout-blok"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "checkout form"
msgstr "afrekenformulier"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout ( Pro )"
msgstr "MultiStep Checkout ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width (px)"
msgstr "Randbreedte (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Note: This feature is available in the CartFlows Pro. Upgrade Now!."
msgstr "Opmerking: Deze functie is beschikbaar in de CartFlows Pro. Upgrade nu!."

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input/Text Placeholder Color"
msgstr "Invoer/Tekst Placeholder Kleur"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius (px)"
msgstr "Randstraal (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Padding (px)"
msgstr "Sectie-opvulling (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Margin (px)"
msgstr "Sectiemarge (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Success / Error Message"
msgstr "Succes- / Foutmelding"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Form success / Error validation"
msgstr "Formulier geslaagd / Foutvalidatie"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Error Message"
msgstr "Foutmelding"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Optin Form Block"
msgstr "CartFlows Optin Formulierblok"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "optin form"
msgstr "opt-in formulier"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label ( Pro )"
msgstr "Zwevend label ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button Text"
msgstr "Tekst van de verzendknop"

#: wizard/assets/build/wizard-app.js:1
msgid "Let's Start"
msgstr "Laten we beginnen"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 1 of 6"
msgstr "Stap 1 van 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome to CartFlows"
msgstr "Welkom bij CartFlows"

#: wizard/assets/build/wizard-app.js:1
msgid ""
"You're only minutes away from having a more profitable WooCommerce store! This short setup wizard will help you get "
"started with CartFlows."
msgstr ""
"Je bent nog maar een paar minuten verwijderd van een winstgevendere WooCommerce-winkel! Deze korte installatiewizard "
"helpt je op weg met CartFlows."

#: wizard/assets/build/wizard-app.js:1
msgid "Save & Continue"
msgstr "Opslaan & Doorgaan"

#: wizard/assets/build/wizard-app.js:1
msgid "Saving"
msgstr "Opslaan"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 2 of 6"
msgstr "Stap 2 van 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Hi there! Tell us which page builder you use."
msgstr "Hoi daar! Vertel ons welke paginabouwer je gebruikt."

#: wizard/assets/build/wizard-app.js:1
msgid "CartFlows works with all page builders, so don't worry if your page builder is not in the list. "
msgstr "CartFlows werkt met alle paginabouwers, dus maak je geen zorgen als jouw paginabouwer niet in de lijst staat."

#: wizard/assets/build/wizard-app.js:1
msgid "Install & Activate"
msgstr "Installeren & Activeren"

#: wizard/assets/build/wizard-app.js:1
msgid "Installing Required Plugins"
msgstr "Vereiste plug-ins installeren"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 3 of 6"
msgstr "Stap 3 van 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Great job!"
msgstr "Goed gedaan!"

#: wizard/assets/build/wizard-app.js:1
msgid "Now let's install some required plugins."
msgstr "Laten we nu enkele vereiste plug-ins installeren."

#: wizard/assets/build/wizard-app.js:1
msgid ""
"Since CartFlows uses WooCommerce, we'll set it up for you along with Cart Abandonment and Stripe Payments so you can "
"recover abandoned orders and easily accept payments."
msgstr ""
"Aangezien CartFlows gebruikmaakt van WooCommerce, zullen we het voor je instellen samen met Cart Abandonment en Stripe "
"Payments, zodat je verlaten bestellingen kunt terughalen en eenvoudig betalingen kunt accepteren."

#: wizard/assets/build/wizard-app.js:1
msgid "The following plugins will be installed and activated for you:"
msgstr "De volgende plugins worden voor u geïnstalleerd en geactiveerd:"

#: wizard/assets/build/wizard-app.js:1
msgid "Continuing…"
msgstr "Doorgaan…"

#: wizard/assets/build/wizard-app.js:1
msgid "Continue"
msgstr "Doorgaan"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 5 of 6"
msgstr "Stap 5 van 6"

#: wizard/assets/build/wizard-app.js:2
#. translators: %s: html tag
msgid "One last step. %s Let's setup email reports on how your store is doing."
msgstr "Eén laatste stap. %s Laten we e-mailrapporten instellen over hoe uw winkel presteert."

#: wizard/assets/build/wizard-app.js:3
#. translators: %1$s: html tag, %2$s: html tag
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send %1$s you an email report "
"with key metrics and insights. You also will receive emails from us to %2$s help your store sell more."
msgstr ""
"Laat CartFlows het giswerk uit je afrekenresultaten halen. Elke week zal je winkel %1$s je een e-mailrapport sturen met "
"belangrijke statistieken en inzichten. Je ontvangt ook e-mails van ons om %2$s je winkel te helpen meer te verkopen."

#: wizard/assets/build/wizard-app.js:3
msgid "First Name"
msgstr "Voornaam"

#: wizard/assets/build/wizard-app.js:3
msgid "Please enter your name"
msgstr "Voer uw naam in"

#: wizard/assets/build/wizard-app.js:3
msgid "Enter Your Email"
msgstr "Voer uw e-mailadres in"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Name"
msgstr "Voer naam in"

#: wizard/assets/build/wizard-app.js:4
msgid "Entered email address is not a valid email"
msgstr "Het ingevoerde e-mailadres is geen geldig e-mailadres"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Email ID"
msgstr "Voer e-mailadres in"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome"
msgstr "Welkom"

#: wizard/assets/build/wizard-app.js:1
msgid "Page Builder"
msgstr "Pagina Bouwer"

#: wizard/assets/build/wizard-app.js:1
msgid "Required Plugins"
msgstr "Vereiste plug-ins"

#: wizard/assets/build/wizard-app.js:1
msgid "Done"
msgstr "Klaar"

#: wizard/assets/build/wizard-app.js:1
msgid "Exit setup wizard"
msgstr "Setupwizard afsluiten"

#: wizard/assets/build/wizard-app.js:1
msgid "Redirecting.."
msgstr "Omleiden.."

#: wizard/assets/build/wizard-app.js:1
msgid "Skip"
msgstr "Overslaan"

#: wizard/assets/build/wizard-app.js:1
#: wizard/assets/build/wizard-app.js:5
msgid "Finish Store Setup"
msgstr "Voltooi winkelinstelling"

#: wizard/assets/build/wizard-app.js:1
msgid "Select Color"
msgstr "Selecteer kleur"

#: wizard/assets/build/wizard-app.js:5
msgid "Recommended"
msgstr "Aanbevolen"

#: wizard/assets/build/wizard-app.js:5
msgid "Upload a Logo"
msgstr "Een logo uploaden"

#: wizard/assets/build/wizard-app.js:5
msgid "Change a Logo"
msgstr "Een logo wijzigen"

#: wizard/assets/build/wizard-app.js:5
msgid "Remove logo"
msgstr "Logo verwijderen"

#: wizard/assets/build/wizard-app.js:5
msgid "Suggested Dimensions: 180x60 pixels"
msgstr "Voorgestelde afmetingen: 180x60 pixels"

#: wizard/assets/build/wizard-app.js:5
msgid "Oops!!! No templates found"
msgstr "Oeps!!! Geen sjablonen gevonden"

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Seems like no templates are available for chosen page editor. Don't worry, you can always import the store checkout "
"template from the CartFlows setting menu."
msgstr ""
"Het lijkt erop dat er geen sjablonen beschikbaar zijn voor de gekozen pagina-editor. Maak je geen zorgen, je kunt "
"altijd het winkelwagenafrekensjabloon importeren vanuit het CartFlows-instellingenmenu."

#: wizard/assets/build/wizard-app.js:5
msgid "Skip to Next"
msgstr "Overslaan naar volgende"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 4 of 6"
msgstr "Stap 4 van 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Awesome"
msgstr "Geweldig"

#: wizard/assets/build/wizard-app.js:5
msgid "Now let's setup your new store checkout."
msgstr "Laten we nu je nieuwe winkelafrekenproces instellen."

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Choose one of the store checkout designs below. After setup you can change the text and color or even choose an "
"entirely new store checkout design."
msgstr ""
"Kies een van de onderstaande winkelafrekenontwerpen. Na de installatie kun je de tekst en kleur wijzigen of zelfs een "
"geheel nieuw winkelafrekenontwerp kiezen."

#: wizard/assets/build/wizard-app.js:5
msgid "Import & Continue"
msgstr "Importeren en doorgaan"

#: wizard/assets/build/wizard-app.js:5
msgid "Processing.."
msgstr "Bezig met verwerken.."

#: wizard/assets/build/wizard-app.js:5
msgid "Importing Failed.."
msgstr "Importeren mislukt.."

#: wizard/assets/build/wizard-app.js:5
msgid "Selected Template:"
msgstr "Geselecteerde sjabloon:"

#: wizard/assets/build/wizard-app.js:5
msgid "Change Primary Color"
msgstr "Primaire kleur wijzigen"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 6 of 6"
msgstr "Stap 6 van 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Congratulations, You Did It!"
msgstr "Gefeliciteerd, je hebt het gedaan!"

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows is set up on your website! Please watch the short video below for your next steps."
msgstr "CartFlows is ingesteld op je website! Bekijk de korte video hieronder voor je volgende stappen."

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows Extended Walkthrough Tutorial"
msgstr "Uitgebreide CartFlows Handleiding"

#: wizard/assets/build/wizard-app.js:5
msgid "Finishing the Setup"
msgstr "De installatie voltooien"

#: admin-core/inc/admin-menu.php:1052
msgid "A simple yet powerful way to add content restriction to your website."
msgstr "Een eenvoudige maar krachtige manier om inhoudsbeperkingen aan je website toe te voegen."

#: classes/class-cartflows-loader.php:368
msgid "Quick Feedback"
msgstr "Snelle feedback"

#: classes/class-cartflows-loader.php:370
msgid "If you have a moment, please share why you are deactivating CartFlows:"
msgstr "Als je een moment hebt, deel dan alsjeblieft waarom je CartFlows deactiveert:"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unlock Premium Features with CartFlows PRO!"
msgstr "Ontgrendel Premium-functies met CartFlows PRO!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Get the tools you need to create powerful sales funnels, increase conversions, and grow your business with ease."
msgstr ""
"Verkrijg de tools die je nodig hebt om krachtige verkooptrechters te creëren, conversies te verhogen en je bedrijf "
"moeiteloos te laten groeien."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Select a Settings Tab"
msgstr "Selecteer een instellingen tabblad"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Free vs Pro"
msgstr "Gratis vs Pro"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Increase Your Revenue with Smart Order Bumps"
msgstr "Verhoog uw omzet met slimme orderverhogingen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Boost sales with CartFlows’ Order Bump! Offer personalized add-ons at checkout to increase revenue effortlessly. Quick "
"to set up, no coding needed!"
msgstr ""
"Verhoog de verkoop met de Order Bump van CartFlows! Bied gepersonaliseerde extra's aan bij het afrekenen om moeiteloos "
"de omzet te verhogen. Snel in te stellen, geen codering nodig!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show the Right Offer to the Right People – Automatically!"
msgstr "Toon de juiste aanbieding aan de juiste mensen – automatisch!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Personalize deals based on location, cart details, and more. Upgrade to CartFlows PRO and unlock this smart feature "
"today!"
msgstr ""
"Personaliseer aanbiedingen op basis van locatie, winkelwagengegevens en meer. Upgrade naar CartFlows PRO en ontgrendel "
"deze slimme functie vandaag nog!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Boost Sales Instantly with Auto-Applied Coupons!"
msgstr "Verhoog de verkoop direct met automatisch toegepaste coupons!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"No codes, no hassle—discounts apply instantly at checkout. Upgrade to CartFlows PRO and start converting more customers "
"today!"
msgstr ""
"Geen codes, geen gedoe—kortingen worden direct bij het afrekenen toegepast. Upgrade naar CartFlows PRO en begin vandaag "
"nog met het converteren van meer klanten!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Give Your Customers More Choices, Boost Your Sales"
msgstr "Geef uw klanten meer keuzes, verhoog uw verkoop"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"Make buying easier with flexible product options. Let customers make the right choices from your checkout. Upgrade to "
"CartFlows PRO and start customizing today!"
msgstr ""
"Maak kopen gemakkelijker met flexibele productopties. Laat klanten de juiste keuzes maken vanuit uw afrekenproces. "
"Upgrade naar CartFlows PRO en begin vandaag nog met aanpassen!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Optin Product"
msgstr "Optin-product"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Select free & virtual product only. Once you select a product, it will be displayed here."
msgstr "Selecteer alleen gratis en virtueel product. Zodra je een product selecteert, wordt het hier weergegeven."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Search for a Free & Virtual Product"
msgstr "Zoek naar een Gratis & Virtueel Product"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Please select a free & virtual product only."
msgstr "Selecteer alstublieft alleen een gratis en virtueel product."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:80
msgid "Free"
msgstr "Gratis"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "A/B Testing"
msgstr "A/B-test"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid ""
"Optimize your sales with A/B testing in CartFlows! Experiment with product pricing, page layouts, messaging, and "
"design. Create variants, analyze results, and discover new ways to boost revenue."
msgstr ""
"Optimaliseer je verkoop met A/B-testen in CartFlows! Experimenteer met productprijzen, paginalay-outs, berichten en "
"ontwerp. Maak varianten, analyseer resultaten en ontdek nieuwe manieren om de omzet te verhogen."

#: admin-core/assets/build/settings-app.js:23
msgid "Finish Setup"
msgstr "Voltooi de installatie"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup CartFlows"
msgstr "CartFlows instellen"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup Store Checkout"
msgstr "Winkelafrekenen instellen"

#: admin-core/assets/build/settings-app.js:23
msgid "Create"
msgstr "Maken"

#: admin-core/assets/build/settings-app.js:23
msgid "Build Your Funnel"
msgstr "Bouw je trechter"

#: admin-core/assets/build/settings-app.js:23
msgid "Start From Scratch"
msgstr "Begin opnieuw"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Library"
msgstr "Ga naar de bibliotheek"

#: admin-core/assets/build/settings-app.js:23
msgid "Offer add-ons with Order Bump."
msgstr "Bied add-ons aan met Order Bump."

#: admin-core/assets/build/settings-app.js:23
msgid "Increase Revenue with Upsells."
msgstr "Verhoog de omzet met upsells."

#: admin-core/assets/build/settings-app.js:23
msgid "Almost There! Let's Go Live."
msgstr "Bijna daar! Laten we live gaan."

#: admin-core/assets/build/settings-app.js:24
#. translators: %d is the number of completed steps.
msgid "%d out of 5 completed"
msgstr "%d van de 5 voltooid"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade to PRO"
msgstr "Upgrade naar PRO"

#: admin-core/assets/build/settings-app.js:24
msgid "Completed"
msgstr "Voltooid"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade your plan anytime and get more detailed analytics data."
msgstr "Upgrade je plan op elk moment en krijg meer gedetailleerde analyseggevens."

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views is a Premium Feature"
msgstr "Totaal aantal paginaweergaven is een premiumfunctie"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue is a Premium Feature"
msgstr "Aanbiedingsinkomsten is een premiumfunctie"

#: admin-core/assets/build/settings-app.js:24
msgid "Custom Filter:"
msgstr "Aangepast filter:"

#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Features"
msgstr "Afrekenfuncties"

#: admin-core/assets/build/settings-app.js:80
msgid "Modern Checkout Styles"
msgstr "Moderne afrekenstijlen"

#: admin-core/assets/build/settings-app.js:80
msgid "Optimized replacement for the standard WooCommerce checkout page designed for higher conversion"
msgstr "Geoptimaliseerde vervanging voor de standaard WooCommerce-afrekenpagina, ontworpen voor een hogere conversie"

#: admin-core/assets/build/settings-app.js:80
msgid "Custom Checkout Fields"
msgstr "Aangepaste afrekenvelden"

#: admin-core/assets/build/settings-app.js:80
msgid "Have complete control over the field editor to manage the fields as required"
msgstr "Heb volledige controle over de veldeditor om de velden naar wens te beheren"

#: admin-core/assets/build/settings-app.js:80
msgid "One-Click Upsells / Downsells"
msgstr "Upsells / Downsells met één klik"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic One-Click Upsells"
msgstr "Dynamische één-klik upsells"

#: admin-core/assets/build/settings-app.js:80
msgid "Use cart contents or customer data to display relevant upsells for maximum conversion"
msgstr "Gebruik de inhoud van de winkelwagen of klantgegevens om relevante upsells weer te geven voor maximale conversie"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Upsell Templates"
msgstr "Dynamische upsell-sjablonen"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional templates to help you sell more even if you’re not a designer"
msgstr "Professionele sjablonen om je te helpen meer te verkopen, zelfs als je geen ontwerper bent"

#: admin-core/assets/build/settings-app.js:80
msgid "Order Bump Features"
msgstr "Order Bump-functies"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Order Bumps"
msgstr "Dynamische Order Bumps"

#: admin-core/assets/build/settings-app.js:80
msgid "Smart order bumps using customer data to display most relevant products or offers"
msgstr "Slimme orderverhogingen met behulp van klantgegevens om de meest relevante producten of aanbiedingen weer te geven"

#: admin-core/assets/build/settings-app.js:80
msgid "Advanced Funnel Features"
msgstr "Geavanceerde trechterfuncties"

#: admin-core/assets/build/settings-app.js:80
msgid "A / B Split Testing"
msgstr "A / B-splittesten"

#: admin-core/assets/build/settings-app.js:80
msgid "Increase conversions and sales with CartFlows A/B Testing by running simple tests"
msgstr "Verhoog conversies en verkopen met CartFlows A/B-testen door eenvoudige tests uit te voeren"

#: admin-core/assets/build/settings-app.js:80
msgid "Analyze transactions and user behavior to refine conversions and make more profit"
msgstr "Analyseer transacties en gebruikersgedrag om conversies te verfijnen en meer winst te maken"

#: admin-core/assets/build/settings-app.js:80
msgid "Cloud-based automation tools that intelligently links your websites, stores, plugins and apps"
msgstr "Cloudgebaseerde automatiseringstools die intelligent uw websites, winkels, plugins en apps koppelen"

#: admin-core/assets/build/settings-app.js:80
msgid "SkillJet Academy Access"
msgstr "Toegang tot SkillJet Academy"

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows offers full training to help you make more profit with SkillJet academy"
msgstr "CartFlows biedt volledige training om je te helpen meer winst te maken met SkillJet academy"

#: admin-core/assets/build/settings-app.js:80
msgid "Others Benefits"
msgstr "Andere voordelen"

#: admin-core/assets/build/settings-app.js:80
msgid "Premium Support"
msgstr "Premium Ondersteuning"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional Support, Professional Support Team or Dedicated Support Team"
msgstr "Professionele Ondersteuning, Professioneel Ondersteuningsteam of Toegewijd Ondersteuningsteam"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community"
msgstr "Geweldige gebruikersgemeenschap"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community is already a great message unless you’re looking for a different meaning"
msgstr "Geweldige gebruikersgemeenschap is al een geweldige boodschap, tenzij je op zoek bent naar een andere betekenis"

#: admin-core/assets/build/settings-app.js:80
msgid "Great Documentation & Video Tutorials"
msgstr "Geweldige documentatie en videotutorials"

#: admin-core/assets/build/settings-app.js:80
msgid "Comprehensive Documentation and Video Tutorials or Comprehensive Documentation and Video Guides"
msgstr "Uitgebreide documentatie en videotutorials of uitgebreide documentatie en videogidsen"

#: admin-core/assets/build/settings-app.js:80
msgid "Free Plugins"
msgstr "Gratis plugins"

#: admin-core/assets/build/settings-app.js:80
msgid "Variation Swatches"
msgstr "Variatie Stalen"

#: admin-core/assets/build/settings-app.js:80
msgid "Give customers choice by including relevant product variations including size, color and more"
msgstr "Geef klanten keuze door relevante productvariaties op te nemen, waaronder maat, kleur en meer"

#: admin-core/assets/build/settings-app.js:80
msgid "Stripe Payment Gateway"
msgstr "Stripe-betalingsgateway"

#: admin-core/assets/build/settings-app.js:80
msgid "Accepting multiple payment methods gives customers choice and can significantly increase conversion"
msgstr "Het accepteren van meerdere betaalmethoden geeft klanten keuze en kan de conversie aanzienlijk verhogen"

#: admin-core/assets/build/settings-app.js:80
msgid "Features"
msgstr "Kenmerken"

#: admin-core/assets/build/settings-app.js:80
msgid "See all CartFlows Pro features"
msgstr "Bekijk alle CartFlows Pro-functies"

#: admin-core/assets/build/settings-app.js:80
msgid "Sell More with CartFlows Pro"
msgstr "Verkoop meer met CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Get access to powerful features for painless WordPress designing, without the high costs. With all the time you will "
"save, it’s a product that pays for itself!"
msgstr ""
"Krijg toegang tot krachtige functies voor moeiteloos WordPress-ontwerpen, zonder de hoge kosten. Met alle tijd die je "
"bespaart, is het een product dat zichzelf terugverdient!"

#: wizard/assets/build/wizard-app.js:1
msgid "Please complete the previous step before proceeding."
msgstr "Voltooi de vorige stap voordat u verder gaat."

#: cartflows.php
#. Author of the plugin
msgid "Brainstorm Force"
msgstr "Brainstorm Force"

#: cartflows.php
#. Author URI of the plugin
msgid "https://www.brainstormforce.com"
msgstr "https://www.brainstormforce.com"

#: admin-core/ajax/importer.php:1017
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$s article%3$s."
msgstr ""
"Time-outfout bij verzoek. Controleer of de firewall of een beveiligingsplugin de uitgaande HTTP/HTTPS-verzoeken naar "
"templates.cartflows.com blokkeert of niet. %1$sOm dit probleem op te lossen, bekijk dit %2$s artikel%3$s."

#: admin-core/inc/admin-menu.php:288
#: admin-core/inc/admin-menu.php:290
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Modern Cart"
msgstr "Moderne Kar"

#: admin-core/inc/admin-menu.php:1038
msgid "OttoKit"
msgstr "OttoKit"

#: admin-core/inc/admin-menu.php:1039
msgid ""
"OttoKit helps people automate their work by integrating multiple apps and plugins, allowing them to share data and "
"perform tasks automatically."
msgstr ""
"OttoKit helpt mensen hun werk te automatiseren door meerdere apps en plug-ins te integreren, waardoor ze gegevens "
"kunnen delen en taken automatisch kunnen uitvoeren."

#: admin-core/inc/admin-menu.php:1088
msgid "Modern Cart for WooCommerce"
msgstr "Moderne winkelwagen voor WooCommerce"

#: admin-core/inc/admin-menu.php:1089
msgid ""
"Modern Cart for WooCommerce that helps every shop owner improve their user experience, increase conversions & maximize "
"profits."
msgstr ""
"Moderne winkelwagen voor WooCommerce die elke winkeleigenaar helpt de gebruikerservaring te verbeteren, conversies te "
"verhogen en winst te maximaliseren."

#: admin-core/inc/flow-meta.php:59
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Enable Instant Layout"
msgstr "Directe lay-out inschakelen"

#: admin-core/inc/flow-meta.php:64
#. translators: %1$s: Break line, %2$s: link html Start, %3$s: Link html end.
msgid ""
"This layout will replace the default page template for the Checkout, Upsell/Downsell and Thank You steps. You can "
"customize the design %1$sin the Checkout, Upsell/Downsell and Thank You step's settings, under the design tab. %2$sRead "
"More.%3$s"
msgstr ""
"Deze lay-out vervangt de standaard paginasjabloon voor de Checkout-, Upsell/Downsell- en Bedankt-stappen. Je kunt het "
"ontwerp %1$saanpassen in de instellingen van de Checkout-, Upsell/Downsell- en Bedankt-stap, onder het ontwerptabblad. "
"%2$sLees meer.%3$s"

#: admin-core/inc/flow-meta.php:87
msgid "Custom Logo"
msgstr "Aangepast logo"

#: admin-core/inc/flow-meta.php:92
msgid "If you've added a custom logo, it will show up here. If not, a default logo from the theme will be used instead."
msgstr ""
"Als je een aangepast logo hebt toegevoegd, zal het hier verschijnen. Zo niet, dan wordt in plaats daarvan een "
"standaardlogo van het thema gebruikt."

#: admin-core/inc/flow-meta.php:103
msgid "Minimum image size should be 130 x 40 in pixes for ideal display."
msgstr "De minimale afbeeldingsgrootte moet 130 x 40 pixels zijn voor een ideale weergave."

#: admin-core/inc/flow-meta.php:143
msgid "Header Color"
msgstr "Kleur van de koptekst"

#: admin-core/inc/flow-meta.php:249
msgid ""
"The Test Mode automatically adds sample products to your funnel if you haven't selected any. This helps you preview and "
"test the checkout experience easily."
msgstr ""
"De testmodus voegt automatisch voorbeeldproducten toe aan je funnel als je er geen hebt geselecteerd. Dit helpt je om "
"de betaalervaring eenvoudig te bekijken en te testen."

#: admin-core/inc/flow-meta.php:250
msgid ""
"The Test Mode automatically adds sample products to your store checkout funnel if you haven't selected any. This helps "
"you preview and test the experience easily on all steps except the Checkout page."
msgstr ""
"De Testmodus voegt automatisch voorbeeldproducten toe aan je winkelwagen als je er geen hebt geselecteerd. Dit helpt je "
"om de ervaring gemakkelijk te bekijken en te testen in alle stappen behalve de afrekenpagina."

#: admin-core/inc/flow-meta.php:256
msgid "Disallow Indexing"
msgstr "Indexeren niet toestaan"

#: admin-core/inc/flow-meta.php:257
msgid "Changing this will replace the default global setting. To go back to the global setting, just select Default."
msgstr ""
"Als u dit wijzigt, wordt de standaard globale instelling vervangen. Om terug te gaan naar de globale instelling, "
"selecteert u gewoon Standaard."

#: admin-core/inc/flow-meta.php:280
msgid "Any code you add here will work across all the pages in this funnel."
msgstr "Alle code die je hier toevoegt, zal werken op alle pagina's in deze funnel."

#: admin-core/inc/global-settings.php:50
msgid "Allow full access to all settings to customize everything."
msgstr "Sta volledige toegang tot alle instellingen toe om alles aan te passen."

#: admin-core/inc/global-settings.php:55
msgid "Allow limited access to create, edit, delete, or import flows and steps."
msgstr "Sta beperkte toegang toe om stromen en stappen te maken, bewerken, verwijderen of importeren."

#: admin-core/inc/global-settings.php:349
#: admin-core/inc/global-settings.php:539
msgid "This event will trigger when someone subscribes or signs up on the opt-in page."
msgstr "Dit evenement wordt geactiveerd wanneer iemand zich abonneert of aanmeldt op de opt-in pagina."

#: admin-core/inc/global-settings.php:930
msgid "This option is only available for products that are part of a subscription."
msgstr "Deze optie is alleen beschikbaar voor producten die deel uitmaken van een abonnement."

#: admin-core/inc/global-settings.php:1415
msgid "Usage Tracking"
msgstr "Gebruikstracering"

#: admin-core/inc/global-settings.php:1417
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Allow CartFlows Inc products to track non-sensitive usage tracking data. %1$1s Learn More%2$2s."
msgstr "Sta CartFlows Inc-producten toe om niet-gevoelige gebruiksgegevens bij te houden. %1$1s Meer informatie%2$2s."

#: classes/class-cartflows-admin-notices.php:183
msgid "Hi there! You recently used CartFlows to build a sales funnel &mdash; Thanks a ton!"
msgstr "Hoi daar! Je hebt onlangs CartFlows gebruikt om een verkooptrechter te bouwen &mdash; Heel erg bedankt!"

#: classes/class-cartflows-admin-notices.php:184
msgid ""
"It would be awesome if you give us a 5-star review and share your experience on WordPress. Your reviews pump us up and "
"also help other WordPress users make a better decision when choosing CartFlows!"
msgstr ""
"Het zou geweldig zijn als je ons een 5-sterrenbeoordeling geeft en je ervaring deelt op WordPress. Jouw beoordelingen "
"geven ons energie en helpen ook andere WordPress-gebruikers een betere beslissing te nemen bij het kiezen van CartFlows!"

#: classes/class-cartflows-admin-notices.php:186
msgid "Ok, you deserve it"
msgstr "Oké, je verdient het"

#: classes/class-cartflows-admin-notices.php:188
msgid "Nope, maybe later"
msgstr "Nee, misschien later"

#: classes/class-cartflows-admin-notices.php:189
msgid "I already did"
msgstr "Ik heb het al gedaan"

#: classes/class-cartflows-flow-frontend.php:90
msgid ""
"Test mode is currently enabled to help you preview your funnel. You can turn it off anytime from the funnel's settings "
"in the admin dashboard."
msgstr ""
"De testmodus is momenteel ingeschakeld om je te helpen je funnel te bekijken. Je kunt het op elk moment uitschakelen "
"via de instellingen van de funnel in het beheerdersdashboard."

#: classes/class-cartflows-flow-frontend.php:91
msgid "Click here to disable it."
msgstr "Klik hier om het uit te schakelen."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:119
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:131
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:162
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:145
#: modules/gutenberg/build/blocks.js:7
msgid "Legacy"
msgstr "Erfenis"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:120
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:132
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:163
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:149
#: modules/gutenberg/build/blocks.js:7
msgid "Modern"
msgstr "Modern"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:132
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:146
#: modules/gutenberg/build/blocks.js:7
msgid "The Thank You Text is only applicable for the old layout."
msgstr "De bedanktekst is alleen van toepassing op de oude lay-out."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:322
msgid "Pick a background color for the left side of your Checkout page."
msgstr "Kies een achtergrondkleur voor de linkerkant van je afrekenpagina."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:339
msgid "Pick a background color for the right side of your Checkout page."
msgstr "Kies een achtergrondkleur voor de rechterkant van je afrekenpagina."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:841
msgid "Change the background color of the payment description box to match your style."
msgstr "Wijzig de achtergrondkleur van het betalingsbeschrijvingsvak zodat deze bij uw stijl past."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1022
msgid "This is the name (slug) of the current step. Changing it will update the URL for this step, so be cautious!"
msgstr ""
"Dit is de naam (slug) van de huidige stap. Als je deze wijzigt, wordt de URL voor deze stap bijgewerkt, dus wees "
"voorzichtig!"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1030
#: modules/landing/classes/class-cartflows-landing-meta-data.php:123
#: modules/optin/classes/class-cartflows-optin-meta-data.php:577
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:405
msgid "Add your own custom code here. If you're adding CSS, make sure to wrap it inside &lt;style&gt; tags."
msgstr "Voeg hier je eigen aangepaste code toe. Als je CSS toevoegt, zorg ervoor dat je het binnen &lt;style&gt;-tags plaatst."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1044
msgid "Turn this ON to show your product images in the order review section."
msgstr "Zet dit AAN om je productafbeeldingen in de besteloverzichtsectie te tonen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1052
msgid "Users can easily remove products from the checkout page if they decide not to purchase them."
msgstr "Gebruikers kunnen eenvoudig producten van de afrekenpagina verwijderen als ze besluiten deze niet te kopen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1216
msgid "Turn this on to show a custom message when no shipping options are available at checkout."
msgstr "Zet dit aan om een aangepast bericht weer te geven wanneer er geen verzendopties beschikbaar zijn bij het afrekenen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1244
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:449
msgid "Choose this option to adjust where the order summary appears on mobile devices."
msgstr "Kies deze optie om aan te passen waar het besteloverzicht op mobiele apparaten verschijnt."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1297
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1305
msgid "This heading will only appear when you use the Modern Checkout style."
msgstr "Deze kop verschijnt alleen wanneer je de Moderne Checkout-stijl gebruikt."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1313
msgid "This message will appear next to the field name to show an error if something goes wrong."
msgstr "Dit bericht verschijnt naast de veldnaam om een fout aan te geven als er iets misgaat."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1346
msgid ""
"Customizes the text on the 'Place Order' button during checkout, allowing you to make it more relevant to your "
"customers."
msgstr "Past de tekst op de knop 'Bestelling plaatsen' tijdens het afrekenen aan, zodat deze relevanter is voor uw klanten."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1354
msgid ""
"Enabling this will add a lock icon to the 'Place Order' button on the checkout page, indicating secure payment "
"processing."
msgstr ""
"Als u dit inschakelt, wordt er een slotpictogram toegevoegd aan de knop 'Bestelling plaatsen' op de afrekenpagina, wat "
"aangeeft dat de betaling veilig wordt verwerkt."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1363
msgid "This will display the total amount in the cart when you click the 'Place Order' button."
msgstr "Dit zal het totale bedrag in de winkelwagen weergeven wanneer je op de knop 'Bestelling plaatsen' klikt."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:408
msgid "This heading will appear only when the Instant Layout option is used."
msgstr "Deze kop verschijnt alleen wanneer de optie Instant Layout wordt gebruikt."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:139
msgid "Thank You Skin"
msgstr "Dankjewel huid"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:501
msgid "After submitting, users will be sent to this URL instead of the usual thank you page."
msgstr "Na het indienen worden gebruikers naar deze URL gestuurd in plaats van naar de gebruikelijke bedankpagina."

#: modules/thankyou/templates/instant-thankyou-order-details.php:35
#. Translators: Order ID.
msgid "Order #%s"
msgstr "Bestelling #%s"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:139
msgid "Type to search a funnel..."
msgstr "Typ om een trechter te zoeken..."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Disabled"
msgstr "Uitgeschakeld"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "upgrading to PRO"
msgstr "upgraden naar PRO"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "activating CartFlows Pro"
msgstr "CartFlows Pro activeren"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You're using"
msgstr "Je gebruikt"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "CartFlows Free"
msgstr "CartFlows Gratis"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "To unlock more features, consider"
msgstr "Overweeg om meer functies te ontgrendelen"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Activate CartFlows Pro"
msgstr "Activeer CartFlows Pro"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Activated!"
msgstr "Geactiveerd!"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid ""
"You can't edit this step directly because Instant Layout is turned on in the funnel settings. To make design changes, "
"go to the Design tab inside this step's settings."
msgstr ""
"Je kunt deze stap niet direct bewerken omdat Instant Layout is ingeschakeld in de trechterinstellingen. Om "
"ontwerpwijzigingen aan te brengen, ga naar het tabblad Ontwerp in de instellingen van deze stap."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#. translators: %s: The current step type.
msgid "Use this setting to customize the style of the Instant %s Layout."
msgstr "Gebruik deze instelling om de stijl van de Instant %s-indeling aan te passen."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Turn this on to set up rules that decide when visitors should be redirected to a special offer or the next step."
msgstr ""
"Zet dit aan om regels in te stellen die bepalen wanneer bezoekers moeten worden doorgestuurd naar een speciale "
"aanbieding of de volgende stap."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Your email address can't be edited when using the Modern Checkout Style."
msgstr "Je e-mailadres kan niet worden bewerkt wanneer je de Moderne Checkout-stijl gebruikt."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "The Company field won't be visible if you're using the Instant Layout Style."
msgstr "Het veld Bedrijf zal niet zichtbaar zijn als je de Instant Layout-stijl gebruikt."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from all your funnels combined."
msgstr "Dit toont het totale bedrag aan geld dat is verdiend met al je funnels samen."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of orders placed through your CartFlows checkout pages."
msgstr "Dit toont het totale aantal bestellingen dat via uw CartFlows-afrekenpagina's is geplaatst."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of times people visited any step in your funnel."
msgstr "Dit toont het totale aantal keren dat mensen een stap in je trechter hebben bezocht."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from your Upsell and Downsell offers."
msgstr "Dit toont het totale bedrag dat is verdiend met uw Upsell- en Downsell-aanbiedingen."

#: admin-core/assets/build/settings-app.js:35
msgid "Set up a Store Checkout in just one click:"
msgstr "Stel een winkelafrekenpunt in met slechts één klik:"

#: admin-core/assets/build/settings-app.js:35
msgid "Thank You Page"
msgstr "Bedankpagina"

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Use ready-made templates from the CartFlows Library, our custom widget, or shortcodes on each page to set this up "
"easily—no coding needed!"
msgstr ""
"Gebruik kant-en-klare sjablonen uit de CartFlows-bibliotheek, onze aangepaste widget of shortcodes op elke pagina om "
"dit eenvoudig in te stellen—geen codering nodig!"

#: admin-core/assets/build/settings-app.js:80
msgid "Install OttoKit for Free"
msgstr "Installeer OttoKit gratis"

#: admin-core/assets/build/settings-app.js:80
msgid "Visit OttoKit Website"
msgstr "Bezoek de OttoKit-website"

#: admin-core/assets/build/settings-app.js:80
msgid "Here are a few simple examples of what OttoKit can do on your WooCommerce store:"
msgstr "Hier zijn een paar eenvoudige voorbeelden van wat OttoKit kan doen in je WooCommerce-winkel:"

#: admin-core/assets/build/settings-app.js:80
msgid "Join Thousands of Entrepreneurs Already Using OttoKit."
msgstr "Sluit je aan bij duizenden ondernemers die OttoKit al gebruiken."

#: admin-core/assets/build/settings-app.js:80
msgid "Bonus ($200 Value)"
msgstr "Bonus (waarde $200)"

#: admin-core/assets/build/settings-app.js:80
msgid "Access to OttoKit Pro Plan"
msgstr "Toegang tot OttoKit Pro Plan"

#: admin-core/assets/build/settings-app.js:80
msgid "Plus - Annual"
msgstr "Plus - Jaarlijks"

#: admin-core/assets/build/settings-app.js:80
msgid "/ year"
msgstr "/ jaar"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Annual"
msgstr "Pro - Jaarlijks"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (One Time Pay)"
msgstr "Pro - Levenslang (Eenmalige Betaling)"

#: admin-core/assets/build/settings-app.js:80
msgid "for Lifetime"
msgstr "voor het leven"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (11 x Split Pay)"
msgstr "Pro - Levenslang (11 x Gespreide Betaling)"

#: admin-core/assets/build/settings-app.js:80
msgid "x 11 Months"
msgstr "x 11 maanden"

#: admin-core/assets/build/settings-app.js:80
msgid "Explore the key differences between Plus and Pro to find the perfect fit for your needs."
msgstr "Ontdek de belangrijkste verschillen tussen Plus en Pro om de perfecte match voor jouw behoeften te vinden."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Free vs Pro Image"
msgstr "CartFlows Free vs Pro Afbeelding"

#: admin-core/assets/build/settings-app.js:80
msgid "Unlock Pro Features"
msgstr "Ontgrendel Pro-functies"

#: admin-core/assets/build/settings-app.js:80
msgid "Generate More Sales With CartFlows Pro!"
msgstr "Genereer meer verkopen met CartFlows Pro!"

#: admin-core/assets/build/settings-app.js:80
msgid "And More…"
msgstr "En meer…"

#: admin-core/assets/build/settings-app.js:80
msgid "Buy Now"
msgstr "Koop nu"

#: admin-core/assets/build/settings-app.js:80
msgid "View plans"
msgstr "Plannen bekijken"

#: admin-core/assets/build/settings-app.js:80
msgid "Get Modern Cart Now"
msgstr "Koop nu de Moderne Winkelwagen"

#: admin-core/assets/build/settings-app.js:80
msgid "Moderncart"
msgstr "Moderncart"

#: admin-core/assets/build/settings-app.js:81
#. translators: %s: line break
msgid "Your Cart Can Do More — Let’s Make It %sa Sales Machine!"
msgstr "Uw winkelwagen kan meer doen — laten we er een %sverkoopmachine van maken!"

#: admin-core/assets/build/settings-app.js:81
msgid ""
"Transform your default WooCommerce cart into a high-converting, fast, and user-friendly shopping experience — designed "
"to keep customers engaged and ready to buy."
msgstr ""
"Transformeer je standaard WooCommerce-winkelwagen in een hoog converterende, snelle en gebruiksvriendelijke "
"winkelervaring — ontworpen om klanten betrokken en klaar om te kopen te houden."

#: admin-core/assets/build/settings-app.js:81
msgid "Visit Modern Cart"
msgstr "Bezoek Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Why Store Owners ❤️ Modern Cart"
msgstr "Waarom winkeleigenaren ❤️ Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Trusted by Top Brands to Boost Conversions Instantly"
msgstr "Vertrouwd door topmerken om conversies direct te verhogen"

#: admin-core/assets/build/settings-app.js:81
msgid "Brand logo"
msgstr "Merkenlogo"

#: admin-core/assets/build/settings-app.js:81
msgid "Stop Losing Sales at the Cart — Fix It in Minutes!"
msgstr "Stop met het verliezen van verkopen bij de kassa — Los het binnen enkele minuten op!"

#: admin-core/assets/build/settings-app.js:81
msgid "Modern Cart is your instant upgrade for more sales, bigger orders, and smoother checkouts."
msgstr "Modern Cart is jouw directe upgrade voor meer verkopen, grotere bestellingen en soepelere checkouts."

#: wizard/assets/build/wizard-app.js:1
msgid "Learn more about usage tracking"
msgstr "Meer informatie over gebruiksregistratie"

#: wizard/assets/build/wizard-app.js:3
msgid "I agree to share anonymous usage data to help improve CartFlows."
msgstr "Ik ga akkoord om anonieme gebruiksgegevens te delen om CartFlows te helpen verbeteren."

#: wizard/assets/build/wizard-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag close
msgid ""
"We never collect personal info, Only anonymized data like PHP version, admin language, and feature usage. To learn what "
"we collect and why, see this %1$sdocument%2$s."
msgstr ""
"We verzamelen nooit persoonlijke informatie, alleen geanonimiseerde gegevens zoals PHP-versie, beheertaal en gebruik "
"van functies. Om te leren wat we verzamelen en waarom, zie dit %1$sdocument%2$s."

#: wizard/assets/build/wizard-app.js:5
#. translators: %1$s: Anchor tag one start, %2$s: Anchor tag one close, %3$s: Anchor tag two start, %4$s: Anchor tag two close.
msgid "By continuing you agree to our %1$sTerms%2$s and %3$sPrivacy Policy%4$s."
msgstr "Door door te gaan, ga je akkoord met onze %1$sVoorwaarden%2$s en %3$sPrivacybeleid%4$s."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:266
msgctxt "Width."
msgid "Auto"
msgstr "Auto"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping %d"
msgstr "Verzending %d"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping"
msgstr "Verzending"

#: modules/flow/classes/class-cartflows-flow-post-type.php:103
msgctxt "flow general name"
msgid "Flows"
msgstr "Stromen"

#: modules/flow/classes/class-cartflows-flow-post-type.php:104
msgctxt "flow singular name"
msgid "Flow"
msgstr "Stroom"

#: modules/flow/classes/class-cartflows-step-post-type.php:170
msgctxt "flow step general name"
msgid "Steps"
msgstr "Stappen"

#: modules/flow/classes/class-cartflows-step-post-type.php:171
msgctxt "flow step singular name"
msgid "Step"
msgstr "Stap"
