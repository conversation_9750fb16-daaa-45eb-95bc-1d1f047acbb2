[{"ID": 20918, "title": "Store Checkout 04", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-04.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-04-600x861.jpg", "category": ["store-checkout"], "page_builder": "beaver-builder", "steps": [{"ID": 20920, "title": "Store Checkout 04", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-04.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-04-600x861.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-04/"}, {"ID": 20921, "title": "Store Checkout Thank You 04", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-04.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-04-600x590.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-thank-you-04/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 115, "name": "Store Checkout", "slug": "store-checkout", "term_group": 0, "term_taxonomy_id": 115, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 4, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 20914, "title": "Store Checkout 03", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-03.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-03-600x990.jpg", "category": ["store-checkout"], "page_builder": "beaver-builder", "steps": [{"ID": 20916, "title": "Store Checkout 03", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-03.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-03-600x990.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-03/"}, {"ID": 20917, "title": "Store Checkout Thank You 03", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-03.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-03-600x575.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-thank-you-03/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 115, "name": "Store Checkout", "slug": "store-checkout", "term_group": 0, "term_taxonomy_id": 115, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 4, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 20906, "title": "Store Checkout 02", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-02.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-02-600x1093.jpg", "category": ["store-checkout"], "page_builder": "beaver-builder", "steps": [{"ID": 20908, "title": "Store Checkout 02", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-02.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-02-600x1093.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-02/"}, {"ID": 20909, "title": "Store Checkout Thank You 02", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-02.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-02-600x531.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-thank-you-02/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 115, "name": "Store Checkout", "slug": "store-checkout", "term_group": 0, "term_taxonomy_id": 115, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 4, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 20901, "title": "Store Checkout 01", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-01-600x839.jpg", "category": ["store-checkout"], "page_builder": "beaver-builder", "steps": [{"ID": 20903, "title": "Store Checkout 01", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-01-600x839.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-01/"}, {"ID": 20904, "title": "Store Checkout Thank You 01", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2022/07/bb-global-store-checkout-thank-you-01-600x604.jpg", "link": "https://templates.cartflows.com/beaver-builder/store-checkout-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 115, "name": "Store Checkout", "slug": "store-checkout", "term_group": 0, "term_taxonomy_id": 115, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 4, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19873, "title": "Webinar", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/webinar-optin.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/webinar-optin-600x1487.jpg", "category": ["lead-generation"], "page_builder": "beaver-builder", "steps": [{"ID": 19874, "title": "<PERSON><PERSON><PERSON>", "type": "optin", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/webinar-optin.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/webinar-optin-600x1487.jpg", "link": "https://templates.cartflows.com/beaver-builder/webinar-optin-01/"}, {"ID": 19889, "title": "Webinar Thank you", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/webinar-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/webinar-thank-you-01-600x403.jpg", "link": "https://templates.cartflows.com/beaver-builder/webinar-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 35, "name": "Lead Generation", "slug": "lead-generation", "term_group": 0, "term_taxonomy_id": 35, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 3, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19815, "title": "Online Course", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-landing01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-landing01-600x2539.jpg", "category": ["product-landing", "sales-funnel"], "page_builder": "beaver-builder", "steps": [{"ID": 19816, "title": "Course Landing", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-landing01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-landing01-600x2539.jpg", "link": "https://templates.cartflows.com/beaver-builder/course-landing-01/"}, {"ID": 19847, "title": "Course Checkout", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-checkout01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-checkout01-600x904.jpg", "link": "https://templates.cartflows.com/beaver-builder/course-checkout-01/"}, {"ID": 19851, "title": "Course Thank You", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-thankyou01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2021/07/course-thankyou01-600x725.jpg", "link": "https://templates.cartflows.com/beaver-builder/course-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 29, "name": "Product Landing", "slug": "product-landing", "term_group": 0, "term_taxonomy_id": 29, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}, {"term_id": 31, "name": "Sales Funnel", "slug": "sales-funnel", "term_group": 0, "term_taxonomy_id": 31, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19424, "title": "Evergreen Product 01", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-landing-01-600x1600.jpg", "category": ["product-landing"], "page_builder": "beaver-builder", "steps": [{"ID": 19425, "title": "<PERSON>", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-landing-01-600x1600.jpg", "link": "https://templates.cartflows.com/beaver-builder/product01-landing-01/"}, {"ID": 19442, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-checkout-01-600x887.jpg", "link": "https://templates.cartflows.com/beaver-builder/product01-checkout-01/"}, {"ID": 19445, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product01-thank-you-01-600x592.jpg", "link": "https://templates.cartflows.com/beaver-builder/product01-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 29, "name": "Product Landing", "slug": "product-landing", "term_group": 0, "term_taxonomy_id": 29, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19395, "title": "Evergreen Product 02", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-landing-01-600x2020.jpg", "category": ["product-landing"], "page_builder": "beaver-builder", "steps": [{"ID": 19396, "title": "<PERSON>", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-landing-01-600x2020.jpg", "link": "https://templates.cartflows.com/beaver-builder/product02-landing-01/"}, {"ID": 19411, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-checkout-01-600x727.jpg", "link": "https://templates.cartflows.com/beaver-builder/product02-checkout-01/"}, {"ID": 19418, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product02-thank-you-01-600x565.jpg", "link": "https://templates.cartflows.com/beaver-builder/product02-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 29, "name": "Product Landing", "slug": "product-landing", "term_group": 0, "term_taxonomy_id": 29, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19372, "title": "Evergreen Product 03", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-landing-01-600x2644.jpg", "category": ["product-landing"], "page_builder": "beaver-builder", "steps": [{"ID": 19373, "title": "<PERSON>", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-checkout-01-600x607.jpg", "link": "https://templates.cartflows.com/beaver-builder/product03-landing-01/"}, {"ID": 19386, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-checkout-01-600x607.jpg", "link": "https://templates.cartflows.com/beaver-builder/product03-checkout-01/"}, {"ID": 19389, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/product03-thank-you-01-600x571.jpg", "link": "https://templates.cartflows.com/beaver-builder/product03-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 29, "name": "Product Landing", "slug": "product-landing", "term_group": 0, "term_taxonomy_id": 29, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19352, "title": "Inline Checkout", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/inline-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/inline-checkout-01-600x1818.jpg", "category": ["product-landing"], "page_builder": "beaver-builder", "steps": [{"ID": 19353, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/inline-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/inline-checkout-01-600x1818.jpg", "link": "https://templates.cartflows.com/beaver-builder/inline-checkout-01/"}, {"ID": 19367, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/inline-checkout-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/inline-checkout-thank-you-01-600x525.jpg", "link": "https://templates.cartflows.com/beaver-builder/inline-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 29, "name": "Product Landing", "slug": "product-landing", "term_group": 0, "term_taxonomy_id": 29, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19315, "title": "eBook", "type": "pro", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-landing-01-600x1592.jpg", "category": ["sales-funnel"], "page_builder": "beaver-builder", "steps": [{"ID": 19316, "title": "<PERSON>", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-landing-01-600x1592.jpg", "link": "https://templates.cartflows.com/beaver-builder/ebook-landing-01/"}, {"ID": 19334, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-checkout-01-600x1133.jpg", "link": "https://templates.cartflows.com/beaver-builder/ebook-checkout-01/"}, {"ID": 19336, "title": "EBook Upsell", "type": "upsell", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-upsell-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-upsell-01-600x422.jpg", "link": "https://templates.cartflows.com/beaver-builder/ebook-upsell-01/"}, {"ID": 19339, "title": "E<PERSON><PERSON>", "type": "downsell", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-downsell-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-downsell-01-600x432.jpg", "link": "https://templates.cartflows.com/beaver-builder/ebook-downsell-01/"}, {"ID": 19342, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/ebook-thank-you-01-600x769.jpg", "link": "https://templates.cartflows.com/beaver-builder/ebook-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 31, "name": "Sales Funnel", "slug": "sales-funnel", "term_group": 0, "term_taxonomy_id": 31, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 30, "name": "Pro", "slug": "pro", "term_group": 0, "term_taxonomy_id": 30, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}]}, {"ID": 19289, "title": "<PERSON>tein Supplement", "type": "pro", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-landing-01-600x1926.jpg", "category": ["sales-funnel"], "page_builder": "beaver-builder", "steps": [{"ID": 19290, "title": "<PERSON>", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-landing-01-600x1926.jpg", "link": "https://templates.cartflows.com/beaver-builder/protein-supplement-landing-01/"}, {"ID": 19299, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-checkout-01-600x914.jpg", "link": "https://templates.cartflows.com/beaver-builder/protein-supplement-checkout-01/"}, {"ID": 19302, "title": "Upsell", "type": "upsell", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-upsell-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-upsell-01-600x344.jpg", "link": "https://templates.cartflows.com/beaver-builder/protein-supplement-upsell-01/"}, {"ID": 19306, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/protein-supplement-thank-you-01-600x548.jpg", "link": "https://templates.cartflows.com/beaver-builder/protein-supplement-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 31, "name": "Sales Funnel", "slug": "sales-funnel", "term_group": 0, "term_taxonomy_id": 31, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 30, "name": "Pro", "slug": "pro", "term_group": 0, "term_taxonomy_id": 30, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}]}, {"ID": 19260, "title": "Beauty Product", "type": "pro", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-landing-01-600x1800.jpg", "category": ["sales-funnel"], "page_builder": "beaver-builder", "steps": [{"ID": 19261, "title": "<PERSON>", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-landing-01-600x1800.jpg", "link": "https://templates.cartflows.com/beaver-builder/beauty-product-landing-01/"}, {"ID": 19272, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-checkout-01-600x980.jpg", "link": "https://templates.cartflows.com/beaver-builder/beauty-product-checkout-01/"}, {"ID": 19275, "title": "Upsell", "type": "upsell", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-upsell-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-upsell-01-600x372.jpg", "link": "https://templates.cartflows.com/beaver-builder/beauty-product-upsell-01/"}, {"ID": 19277, "title": "<PERSON><PERSON>", "type": "downsell", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-downsell-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-downsell-01-600x313.jpg", "link": "https://templates.cartflows.com/beaver-builder/beauty-product-downsell-01/"}, {"ID": 19280, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/beauty-product-thank-you-01-600x590.jpg", "link": "https://templates.cartflows.com/beaver-builder/beauty-product-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 31, "name": "Sales Funnel", "slug": "sales-funnel", "term_group": 0, "term_taxonomy_id": 31, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 30, "name": "Pro", "slug": "pro", "term_group": 0, "term_taxonomy_id": 30, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}]}, {"ID": 19226, "title": "Organic Tea", "type": "pro", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-landing-01-600x1701.jpg", "category": ["sales-funnel"], "page_builder": "beaver-builder", "steps": [{"ID": 19227, "title": "<PERSON>", "type": "landing", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-landing-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-landing-01-600x1701.jpg", "link": "https://templates.cartflows.com/beaver-builder/organic-tea-landing-01/"}, {"ID": 19247, "title": "Checkout Page", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-checkout-01-600x967.jpg", "link": "https://templates.cartflows.com/beaver-builder/organic-tea-checkout-01/"}, {"ID": 19250, "title": "Upsell", "type": "upsell", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-upsell-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-upsell-01-600x389.jpg", "link": "https://templates.cartflows.com/beaver-builder/organic-tea-upsell-01/"}, {"ID": 19253, "title": "Thank You Page", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/organic-tea-thank-you-01-600x502.jpg", "link": "https://templates.cartflows.com/beaver-builder/organic-tea-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 31, "name": "Sales Funnel", "slug": "sales-funnel", "term_group": 0, "term_taxonomy_id": 31, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 30, "name": "Pro", "slug": "pro", "term_group": 0, "term_taxonomy_id": 30, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}]}, {"ID": 19179, "title": "Download eBook", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/download-the-ebook-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/download-the-ebook-01-600x519.jpg", "category": ["lead-generation"], "page_builder": "beaver-builder", "steps": [{"ID": 19180, "title": "Download The eBook", "type": "optin", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/download-the-ebook-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/download-the-ebook-01-600x519.jpg", "link": "https://templates.cartflows.com/beaver-builder/download-the-ebook-01/"}, {"ID": 19186, "title": "Thank You", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/download-the-ebook-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/download-the-ebook-thank-you-01-600x608.jpg", "link": "https://templates.cartflows.com/beaver-builder/download-ebook-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 35, "name": "Lead Generation", "slug": "lead-generation", "term_group": 0, "term_taxonomy_id": 35, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 3, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}, {"ID": 19164, "title": "Email Marketing Course", "type": "pro", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-lead-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-lead-01-600x1159.jpg", "category": ["lead-generation"], "page_builder": "beaver-builder", "steps": [{"ID": 19165, "title": "Email Marketing Free Course", "type": "optin", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-lead-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-lead-01-600x1159.jpg", "link": "https://templates.cartflows.com/beaver-builder/email-marketing-lead-01/"}, {"ID": 19169, "title": "Email Marketing Full Access", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-checkout-01-2.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-checkout-01-2-600x2038.jpg", "link": "https://templates.cartflows.com/beaver-builder/email-marketing-checkout-01/"}, {"ID": 19173, "title": "Email Marketing Thank You", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-thank-you-01-2.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/email-marketing-thank-you-01-2-600x447.jpg", "link": "https://templates.cartflows.com/beaver-builder/email-marketing-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 35, "name": "Lead Generation", "slug": "lead-generation", "term_group": 0, "term_taxonomy_id": 35, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 3, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 30, "name": "Pro", "slug": "pro", "term_group": 0, "term_taxonomy_id": 30, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}]}, {"ID": 19130, "title": "Photoshop Tutorial", "type": "pro", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-01-600x2480.jpg", "category": ["sales-funnel", "tripwire-funnel"], "page_builder": "beaver-builder", "steps": [{"ID": 19131, "title": "Learn Photoshop", "type": "optin", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-01-600x2480.jpg", "link": "https://templates.cartflows.com/beaver-builder/learn-photoshop-01/"}, {"ID": 19142, "title": "Learn Photoshop Checkout", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-checkout-01-600x2492.jpg", "link": "https://templates.cartflows.com/beaver-builder/learn-photoshop-checkout-01/"}, {"ID": 19151, "title": "Learn Photoshop Upsell", "type": "upsell", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-upsell-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-upsell-01-600x706.jpg", "link": "https://templates.cartflows.com/beaver-builder/learn-photoshop-upsell-01/"}, {"ID": 19153, "title": "Learn Photoshop Thank You", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-thsnk-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/learn-photoshop-thsnk-you-01-600x536.jpg", "link": "https://templates.cartflows.com/beaver-builder/learn-photoshop-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 31, "name": "Sales Funnel", "slug": "sales-funnel", "term_group": 0, "term_taxonomy_id": 31, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}, {"term_id": 36, "name": "Tripwire Funnel", "slug": "tripwire-funnel", "term_group": 0, "term_taxonomy_id": 36, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 1, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 30, "name": "Pro", "slug": "pro", "term_group": 0, "term_taxonomy_id": 30, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}]}, {"ID": 19060, "title": "Digital Marketing Guide", "type": "free", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/digital-marketing-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/digital-marketing-checkout-01-600x2057.jpg", "category": ["product-landing"], "page_builder": "beaver-builder", "steps": [{"ID": 19061, "title": "Digital Marketing Checkout", "type": "checkout", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/digital-marketing-checkout-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/digital-marketing-checkout-01-600x2057.jpg", "link": "https://templates.cartflows.com/beaver-builder/digital-marketing-checkout-01/"}, {"ID": 19063, "title": "Digital Marketing Thank You", "type": "thankyou", "featured_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/digital-marketing-thank-you-01.jpg", "thumbnail_image_url": "https://templates.cartflows.com/beaver-builder/wp-content/uploads/sites/3/2020/07/digital-marketing-thank-you-01-600x604.jpg", "link": "https://templates.cartflows.com/beaver-builder/digital-marketing-thank-you-01/"}], "cartflows_flow_page_builder": [{"term_id": 17, "name": "Beaver Builder", "slug": "beaver-builder", "term_group": 0, "term_taxonomy_id": 17, "taxonomy": "cartflows_flow_page_builder", "description": "", "parent": 0, "count": 19, "filter": "raw", "term_order": "0"}], "cartflows_flow_category": [{"term_id": 29, "name": "Product Landing", "slug": "product-landing", "term_group": 0, "term_taxonomy_id": 29, "taxonomy": "cartflows_flow_category", "description": "", "parent": 0, "count": 6, "filter": "raw", "term_order": "0"}], "cartflows_flow_type": [{"term_id": 28, "name": "Free", "slug": "free", "term_group": 0, "term_taxonomy_id": 28, "taxonomy": "cartflows_flow_type", "description": "", "parent": 0, "count": 12, "filter": "raw", "term_order": "0"}]}]