(c=>{let d,p,v={street_number:"long_name",route:"long_name",locality:"long_name",postal_town:"long_name",sublocality_level_2:"long_name",administrative_area_level_1:"short_name",administrative_area_level_2:"short_name",country:"short_name",postal_code:"short_name"};function h(e,n,i){e=e.replace(/\s+/g,"");return c("#"+n).is("select")&&0<c(`#${n} option[value=${e}]`).length?e:i}function e(e){var n={componentRestrictions:{country:[e=null==e?c("#billing_country :selected").val():e]},types:["geocode"]};""!==e&&void 0!==e||delete n.componentRestrictions,(p=new google.maps.places.Autocomplete(document.getElementById("billing_address_1"),n)).addListener("place_changed",i)}function n(e){var n={componentRestrictions:{country:[e=null==e?c("#shipping_country :selected").val():e]},types:["geocode"]};""!==e&&void 0!==e||delete n.componentRestrictions,(d=new google.maps.places.Autocomplete(document.getElementById("shipping_address_1"),n)).addListener("place_changed",t)}function i(){c("#billing_address_1").val(""),c("#billing_country").val(""),c("#billing_address_2").val(""),c("#billing_city").val(""),c("#billing_state").val(""),c("#billing_postcode").val("");var e=p.getPlace(),n=["street_number","route"],i=["sublocality_level_2"];let t="",l="",s="",a="",o="",g="";if(e&&0<e.address_components.length){for(var r of e.address_components){var _=r.types[0];v[_]&&(r=r[v[_]],"country"===_&&(t=r),"administrative_area_level_1"!==_&&"administrative_area_level_2"!==_||(l=h(r,"billing_state",l)),"locality"!==_&&"postal_town"!==_||(s=r),"postal_code"===_&&(g=r),-1!==n.indexOf(_)&&(a+=r+" "),-1!==i.indexOf(_))&&(o+=r+" ")}0<c("#billing_address_1").length&&0<a.length&&c("#billing_address_1").val(a.trimEnd()).trigger("focus"),0<c("#billing_address_2").length&&c("#billing_address_2").val(o.trimEnd()).trigger("focus"),0<c("#billing_postcode").length&&c("#billing_postcode").val(g).trigger("focus"),0<c("#billing_city").length&&c("#billing_city").val(s).trigger("focus"),0<c("#billing_state").length&&c("#billing_state").val(l).trigger("focus").trigger("change"),0<c("#billing_country").length&&c("#billing_country").val(t).trigger("focus").trigger("change"),!e.name||""===e.name&&void 0===e.name||c("#billing_address_1").val(e.name)}}function t(){if(c("#ship-to-different-address-checkbox").is(":checked")){c("#shipping_address_1").val(""),c("#shipping_country").val(""),c("#shipping_address_2").val(""),c("#shipping_city").val(""),c("#shipping_state").val(""),c("#shipping_postcode").val("");var a=d.getPlace(),o=["street_number","route"],g=["sublocality_level_2"];let e="",n="",i="",t="",l="",s="";if(a&&0<a.address_components.length){for(var r of a.address_components){var _=r.types[0];v[_]&&(r=r[v[_]],"country"===_&&(e=r),"administrative_area_level_1"!==_&&"administrative_area_level_2"!==_||(n=h(r,"shipping_state",n)),"locality"===_&&(i=r),"postal_code"===_&&(s=r),-1!==o.indexOf(_)&&(t+=r+" "),-1!==g.indexOf(_))&&(l+=r+" ")}0<c("#shipping_address_1").length&&0<t.length&&c("#shipping_address_1").val(t.trimEnd()).trigger("focus"),0<c("#shipping_address_2").length&&c("#shipping_address_2").val(l.trimEnd()).trigger("focus"),0<c("#shipping_postcode").length&&c("#shipping_postcode").val(s).trigger("focus"),0<c("#shipping_city").length&&c("#shipping_city").val(i).trigger("focus"),0<c("#shipping_state").length&&c("#shipping_state").val(n).trigger("focus").trigger("change"),0<c("#shipping_country").length&&c("#shipping_country").val(e).trigger("focus").trigger("change"),!a.name||""===a.name&&void 0===a.name||c("#shipping_address_1").val(a.name)}}}c(document).on("change","#billing_country",function(){e(c(this).val())}),c(document).on("change","#shipping_country",function(){n(c(this).val())}),c(document).on("ready",function(){e(),n()})})(jQuery);