var CartFlowsAjaxQueue=function(){var s=[];return{add:function(t){s.push(t)},remove:function(t){-1<jQuery.inArray(t,s)&&s.splice($.inArray(t,s),1)},run:function(){var t,e=this;s.length?(t=s[0].complete,s[0].complete=function(){"function"==typeof t&&t(),s.shift(),e.run.apply(e,[])},jQuery.ajax(s[0])):e.tid=setTimeout(function(){e.run.apply(e,[])},1e3)},stop:function(){s=[],clearTimeout(this.tid)}}}();!function(l){CartFlowsImport={doc:l(document),wrap:l(".wcf-flow-steps-data-wrap"),inner:l(".wcf-flow-steps-data-wrap-importer"),post_id:l("#post_ID").val(),_ref:null,_api_step_type:{},_api_params:{},all_steps:0,remaining_steps:0,remaining_install_plugins:0,remaining_active_plugins:0,woo_required_steps:["checkout","upsell","downsell","thankyou","optin"],step_order:["landing","checkout","upsell","downsell","thankyou","optin"],new_step_names:{landing:"Landing",checkout:"Checkout (Woo)",upsell:"Upsell (Woo)",downsell:"Downsell (Woo)",thankyou:"Thank You (Woo)",optin:"Optin (Woo)"},init:function(){var t;this._bind(),"other"!==CartFlowsImportVars.default_page_builder&&(l(".post-type-cartflows_flow").hasClass("edit-php")&&this._process_cache_remote_flows(),l(".post-type-cartflows_flow").hasClass("post-php")&&this._process_cache_remote_steps()),l(".post-type-cartflows_flow").hasClass("edit-php")&&null!==this._getParamFromURL("add-new-flow")&&this._render_remote_flows(),l(".post-type-cartflows_flow").hasClass("post-php")&&null!==this._getParamFromURL("add-new-step")&&this._render_remote_steps(),!this._getParamFromURL("highlight-step-id")||(t=l('.wcf-step-wrap[data-id="'+this._getParamFromURL("highlight-step-id")+'"]')).length&&l("html, body").animate({scrollTop:t.offset().top},1500),l(".post-type-cartflows_flow").hasClass("edit-php")&&((t=l(".edit-php.post-type-cartflows_flow").find(".page-title-action:first")).after('<a href="'+CartFlowsImportVars.export_url+'" class="page-title-action">Export</a>'),t.after('<a href="'+CartFlowsImportVars.import_url+'" class="page-title-action">Import</a>'))},_getParamFromURL:function(t,e){e=e||window.location.href,t=t.replace(/[\[\]]/g,"\\$&");e=new RegExp("[?&]"+t+"(=([^&#]*)|&|#|$)").exec(e);return e?e[2]?decodeURIComponent(e[2].replace(/\+/g," ")):"":null},_bind:function(){var t=CartFlowsImport;t.doc.on("click",".wcf-install-plugin",t._install_plugin),t.doc.on("cartflows-api-request-fail",t._api_request_failed),t.doc.on("click","#wcf-get-started-steps a, .wcf-create-from-scratch-link",t._toggle_ready_templates),t.doc.on("click",".cartflows-flow-import-blank",t._create_default_flow),t.doc.on("click","#wcf-remote-flow-importer .wcf-page-builder-links a",t._filterFlowPageBuilderClick),t.doc.on("click","#wcf-remote-step-importer #wcf-categories .step-type-filter-links a",t._filterBlankStepCategoryClick),t.doc.on("change","#wcf-remote-step-importer #wcf-scratch-steps-categories .step-type-filter-links",t._filterBlankStepCategoryChange),t.doc.on("click","#wcf-get-started-steps",t._filterBlankStepCategoryChange),t.doc.on("click","#wcf-remote-step-importer .wcf-page-builder-links a",t._filterStepPageBuilderClick),t.doc.on("click",".cartflows-step-import-blank:not(.get-pro)",t._create_blank_step),t.doc.on("click","#wcf-remote-step-importer .cartflows-step-import",t._process_import_step),t.doc.on("click","#wcf-remote-flow-importer .cartflows-step-import",t._process_import_flow),t.doc.on("click",".cartflows-preview-flow-step",t._preview_individual),t.doc.on("add_template_to_page-fail",t._add_template_to_page_fail),l("body").on("thickbox:iframe:loaded",t._previewLoaded),l(document).on("keyup input","#wcf-remote-step-importer .wcf-flow-search-input",t._remote_step_search),l(document).on("click",".actions a",t._previewResponsive),l(document).on("click",".page-title-action:first",t._render_remote_flows),l(document).on("click",".wcf-trigger-popup",t._render_remote_steps),l(document).on("click",".wcf-templates-popup-overlay",t._close_template_popup),l(document).on("click",".wcf-popup-close-wrap .close-icon",t._close_template_popup),l(document).on("wp-plugin-install-success",t._installSuccess),l(document).on("click",".wcf-activate-wc",t._installWc)},_install_plugin:function(t){t.preventDefault();var t=l(this);t.hasClass("updating-message")||(l("#wcf-remote-flow-importer").addClass("request-process"),l("#wcf-remote-step-importer").addClass("request-process"),t.addClass("updating-message button"),t=CartFlowsImportVars.required_plugins[CartFlowsImportVars.default_page_builder].plugins,l.each(t,function(t,e){"install"===e.status&&CartFlowsImport.remaining_install_plugins++,"activate"===e.status&&CartFlowsImport.remaining_active_plugins++}),CartFlowsImport.remaining_install_plugins?CartFlowsImport._install_all_plugins():CartFlowsImport.remaining_active_plugins?CartFlowsImport._activate_all_plugins():l("#wcf-remote-flow-importer").length?CartFlowsImport._cache_remote_flows():l("#wcf-remote-step-importer").length&&CartFlowsImport._cache_remote_steps())},_install_all_plugins:function(){var t=CartFlowsImportVars.required_plugins[CartFlowsImportVars.default_page_builder].plugins;l.each(t,function(t,e){"install"===e.status&&wp.updates.queue.push({action:"install-plugin",data:{slug:e.slug}})}),wp.updates.queueChecker()},_activate_all_plugins:function(){var t;CartFlowsImport.remaining_active_plugins||CartFlowsImport.remaining_install_plugins?(t=CartFlowsImportVars.required_plugins[CartFlowsImportVars.default_page_builder].plugins,CartFlowsAjaxQueue.stop(),CartFlowsAjaxQueue.run(),l.each(t,function(t,e){"activate"===e.status&&CartFlowsAjaxQueue.add({url:CartFlowsImportVars.ajaxurl,type:"POST",data:{action:"cartflows_activate_plugin",plugin_init:e.init,security:CartFlowsImportVars.cartflows_activate_plugin_nonce},success:function(t){CartFlowsImport.remaining_active_plugins--,CartFlowsImport.remaining_active_plugins||CartFlowsImport.remaining_install_plugins||(l("#wcf-remote-flow-importer").length?CartFlowsImport._cache_remote_flows():l("#wcf-remote-step-importer").length&&CartFlowsImport._cache_remote_steps())}})})):l("#wcf-remote-flow-importer").length?CartFlowsImport._cache_remote_flows():l("#wcf-remote-step-importer").length&&CartFlowsImport._cache_remote_steps()},_installSuccess:function(t,s){t.preventDefault(),"no"===CartFlowsImportVars.is_wc_activated&&CartFlowsImport._activateWc();t=CartFlowsImportVars.required_plugins[CartFlowsImportVars.default_page_builder].plugins;l.each(t,function(t,e){"install"===e.status&&s.slug===e.slug&&l.ajax({url:ajaxurl,type:"POST",data:{action:"cartflows_activate_plugin",plugin_init:e.init,security:CartFlowsImportVars.cartflows_activate_plugin_nonce}}).done(function(t,e,s){CartFlowsImport.remaining_install_plugins--,CartFlowsImport.remaining_install_plugins||CartFlowsImport._activate_all_plugins()})})},_api_request_failed:function(t,e,s,a){"error"==a&&(l("#wcf-remote-content-failed").length||l("#wcf-ready-templates").html(wp.template("cartflows-website-unreachable")))},_toggle_ready_templates:function(t){t.preventDefault();t=l(this).data("slug")||"";l("#wcf-get-started-steps").find("a").removeClass("current"),l("#wcf-get-started-steps").find('a[data-slug="'+t+'"]').addClass("current"),"canvas"==t?(l("#wcf-ready-templates").hide(),l("#wcf-start-from-scratch").show()):(l("#wcf-ready-templates").show(),l("#wcf-start-from-scratch").hide()),!l(".wcf-page-builder-notice").length&&l("#wcf-remote-step-importer").length&&CartFlowsImport._showSteps()},_switch_step_tab:function(t){t.preventDefault();t=l(".wcf-tab > li.active > a").attr("href");l(".wcf-tab > li.active").removeClass("active"),l(this).parents("li").addClass("active"),l(t).removeClass("active"),l(t).addClass("hide");t=l(this).attr("href");l(t).removeClass("hide"),l(t).addClass("active")},_remote_step_search:function(t){t.preventDefault(),l(".step-type-filter-links").find("option").removeClass("current"),l(".step-type-filter-links").find("option:first-child").addClass("current"),window.clearTimeout(CartFlowsImport._ref),CartFlowsImport._ref=window.setTimeout(function(){CartFlowsImport._ref=null,CartFlowsImport._showSteps()},500)},_previewResponsive:function(t){t.preventDefault();var e=l(this).find(".dashicons"),t=e.attr("data-view")||"";l("#TB_window").removeClass("desktop tablet mobile"),l("#TB_window").addClass(t),l(".actions .dashicons").removeClass("active"),e.addClass("active"),l("#TB_iframeContent").removeClass(),l("#TB_iframeContent").addClass(t)},_filterStepPageBuilderClick:function(t){t.preventDefault(),l(this).parents("ul").find("a").removeClass("current"),l(this).addClass("current");t=l(".step-type-filter-links .current").data("slug")||"";"upsell"===t||"downsell"===t?l(".wcf-template-notice").show():l(".wcf-template-notice").hide(),l(".wcf-page-builder-notice").html(""),l("#wcf-remote-step-list").html('<span class="spinner is-active"></span>'),CartFlowsImport._showSteps()},_filterBlankStepCategoryClick:function(t){t.preventDefault(),l(".wcf-page-builder-notice").html("");t=l(this).data("group")||"";t&&(l("#wcf-scratch-steps-categories .step-type-filter-links").val(t),l("#wcf-scratch-steps-categories .step-type-filter-links option").removeClass("current"),l('#wcf-scratch-steps-categories .step-type-filter-links option[data-group="'+t+'"]').addClass("current")),l(".step-type-filter-links").find("a").removeClass("current"),l(this).addClass("current"),$step_type=l(this).data("slug"),"upsell"===$step_type||"downsell"===$step_type?l(".wcf-template-notice").show():l(".wcf-template-notice").hide(),""!=CartFlowsImportVars._is_pro_active||"upsell"!=$step_type&&"downsell"!=$step_type?(l(".cartflows-step-import-blank").text("Create Step"),l(".cartflows-step-import-blank").removeClass("get-pro"),l(".cartflows-step-import-blank").removeAttr("target")):(l(".cartflows-step-import-blank").text("Get Pro"),l(".cartflows-step-import-blank").attr("href",CartFlowsImportVars.domain_url),l(".cartflows-step-import-blank").attr("target","_blank"),l(".cartflows-step-import-blank").addClass("get-pro")),l("#wcf-remote-step-list").html('<span class="spinner is-active"></span>'),CartFlowsImport._showSteps()},_filterBlankStepCategoryChange:function(t){t.preventDefault(),l(".wcf-notice-wrap").remove(),l(".cartflows-step-import-blank").css("pointer-events","auto").removeClass("disabled");t=l(".step-type-filter-links").find("option:selected").val()||"";t&&(l(".step-type-filter-links").val(t),l(".step-type-filter-links").find("a").removeClass("current"),l(".step-type-filter-links").find('a[data-group="'+t+'"]').addClass("current")),l(".step-type-filter-links").find("option").removeClass("current"),l(".step-type-filter-links").find("option:selected").addClass("current"),$step_type=l(".step-type-filter-links").find("option:selected").data("slug");if(("no"===CartFlowsImportVars.is_wc_installed||"no"===CartFlowsImportVars.is_wc_activated)&&["optin","checkout","upsell","downsell","thankyou"].includes($step_type))return l(".cartflows-step-import-blank").after("<p class='wcf-notice-wrap' style='text-align: center'>You need WooCommerce plugin installed and activated to use this product flow. <br/><br/> <a href='#' class='wcf-activate-wc button-secondary'> Click here to install and activate WooCommerce </a> </p>"),void l(".cartflows-step-import-blank").addClass("disabled").css("pointer-events","none");"upsell"===$step_type||"downsell"===$step_type?l(".wcf-template-notice").show():l(".wcf-template-notice").hide(),""!=CartFlowsImportVars._is_pro_active||"upsell"!=$step_type&&"downsell"!=$step_type?(l(".cartflows-step-import-blank").text("Create Step"),l(".cartflows-step-import-blank").removeClass("get-pro"),l(".cartflows-step-import-blank").removeAttr("target")):(l(".cartflows-step-import-blank").text("Get Pro"),l(".cartflows-step-import-blank").attr("href",CartFlowsImportVars.domain_url),l(".cartflows-step-import-blank").attr("target","_blank"),l(".cartflows-step-import-blank").addClass("get-pro")),!l(".wcf-page-builder-notice").length&&l("#wcf-remote-step-importer").length&&(l("#wcf-remote-step-list").html('<span class="spinner is-active"></span>'),CartFlowsImport._showSteps())},_showSteps:function(){var t={licence_args:CartFlowsImportVars.licence_args,per_page:100,_fields:CartFlowsImportVars.step_fields.toString()},e=l("#wcf-categories .step-type-filter-links").find(".current").data("group")||"";l("#wcf-categories .step-type-filter-links").find(".current").data("slug");""!==e&&"all"!==e&&(t[CartFlowsImportVars.step_type]=e);var e=l("#wcf-page-builders .wcf-page-builder-links").find(".current").data("group")||"",a=l("#wcf-page-builders .wcf-page-builder-links").find(".current").data("slug")||"",r=l("#wcf-page-builders .wcf-page-builder-links").find(".current").data("title")||"Page Builder";""!==e&&"all"!==e&&(t[CartFlowsImportVars.step_page_builder]=e);t={remote_slug:CartFlowsImportVars.step,slug:CartFlowsImportVars.step+"?"+decodeURIComponent(l.param(t))};CartFlowsAPI._api_request(t,function(s){s.current_step_type=a,"yes"!==CartFlowsImportVars.is_wc_activated&&l.each(s.items,function(t,e){s.items[t].woo_required=!1,0<=l.inArray(e.step_type.slug,CartFlowsImport.woo_required_steps)&&(s.items[t].woo_required=!0)});var t=wp.template("cartflows-steps-list");parseInt(s.items_count)?l("#wcf-remote-step-list").html(t(s)):(l("#wcf-remote-step-list").html(wp.template("cartflows-no-steps")),l(".cartflows-no-steps").find(".description").html("We are working on ready templates designed with "+r+'.<br/>Meanwhile you can <a href="#" data-slug="canvas" class="wcf-create-from-scratch-link">create your own designs</a> easily.')),l(".wcf-page-builder-notice").remove(),l("#wcf-remote-step-importer").removeClass("request-process")})},_apiAddParam_per_page:function(){CartFlowsImport._api_params.per_page=100},_apiAddParam_licence_args:function(){CartFlowsImport._api_params.licence_args=CartFlowsImportVars.licence_args},_apiAddParam_search:function(){var t=l(".wcf-flow-search-input").val()||"";""!==t&&(CartFlowsImport._api_params.search=t)},_close_popup:function(){l("#cartflows-steps").fadeOut(),l("body").removeClass("cartflows-popup-is-open")},_post_auto_save:function(){var t=l("#title"),e=l("#title-prompt-text"),s=CartFlowsImport;t.val()||(t.val("CartFlows #"+s.post_id),e.length&&e.remove()),wp.autosave&&wp.autosave.server.triggerSave()},_process_cache_remote_flows:function(){var t=CartFlowsImportVars.required_plugins[CartFlowsImportVars.default_page_builder].plugins,s=!0;l.each(t,function(t,e){s&&("install"!==e.status&&"activate"!==e.status||(s=!1))}),!1===s?(l(".wcf-page-builder-notice").html(wp.template("cartflows-page-builder-notice")),l("#wcf-remote-flow-list").find(".spinner").remove()):CartFlowsImport._cache_remote_flows()},_cache_remote_flows:function(){CartFlowsImport;var t={search:CartFlowsImportVars.default_page_builder,licence_args:CartFlowsImportVars.licence_args,hide_empty:!1,_fields:CartFlowsImportVars.flow_page_builder_fields.toString()},t={remote_slug:CartFlowsImportVars.flow_page_builder,slug:CartFlowsImportVars.flow_page_builder+"?"+decodeURIComponent(l.param(t)),wrapper_class:"wcf-page-builder-links filter-links",show_all:!1};CartFlowsAPI._api_request(t,function(t){var e=wp.template("cartflows-term-filters");l("#wcf-page-builders").html(e(t)),l("#wcf-page-builders").find("li:first a").addClass("current");t={licence_args:CartFlowsImportVars.licence_args,hide_empty:!0,_fields:CartFlowsImportVars.flow_type_fields.toString()},t={remote_slug:CartFlowsImportVars.flow_type,slug:CartFlowsImportVars.flow_type+"?"+decodeURIComponent(l.param(t)),wrapper_class:"flow-type-filter-links filter-links",show_all:!1};CartFlowsAPI._api_request(t,function(t){var e=wp.template("cartflows-term-filters");l("#wcf-categories").html(e(t)),l("#wcf-categories").find("li:first a").addClass("current"),CartFlowsImport._showFlows()})})},_render_remote_flows:function(t){t&&t.preventDefault(),l("#wcf-remote-flow-importer").addClass("open"),l("html").addClass("wcf-popup-open")},_process_cache_remote_steps:function(){var t=CartFlowsImportVars.required_plugins[CartFlowsImportVars.default_page_builder].plugins,s=!0;l.each(t,function(t,e){s&&("install"!==e.status&&"activate"!==e.status||(s=!1))}),!1===s?(l(".wcf-page-builder-notice").html(wp.template("cartflows-page-builder-notice")),l("#wcf-remote-step-list").find(".spinner").remove()):CartFlowsImport._cache_remote_steps()},mapOrder:function(t,s,a){return t.sort(function(t,e){t=t[a],e=e[a];return-1===s.indexOf(t)?0:s.indexOf(t)>s.indexOf(e)?1:-1}),t},_cache_remote_steps:function(){CartFlowsImport;l("html").addClass("wcf-steps-loading");var t={search:CartFlowsImportVars.default_page_builder,licence_args:CartFlowsImportVars.licence_args,hide_empty:!1,_fields:CartFlowsImportVars.step_page_builder_fields.toString()},e={remote_slug:CartFlowsImportVars.step_page_builder,slug:CartFlowsImportVars.step_page_builder+"?"+decodeURIComponent(l.param(t)),wrapper_class:"wcf-page-builder-links filter-links",show_all:!1};CartFlowsAPI._api_request(e,function(t){var e=wp.template("cartflows-term-filters");l("#wcf-page-builders").html(e(t)),l("#wcf-page-builders").find("li:first a").addClass("current")});t={licence_args:CartFlowsImportVars.licence_args,_fields:CartFlowsImportVars.step_type_fields.toString()},e={remote_slug:CartFlowsImportVars.step_type,slug:CartFlowsImportVars.step_type+"?"+decodeURIComponent(l.param(t)),wrapper_class:"step-type-filter-links filter-links",show_all:!1};CartFlowsAPI._api_request(e,function(t){t.items=CartFlowsImport.mapOrder(t.items,CartFlowsImport.step_order,"slug");var s=t,a=t.items_count;if(t.items)for(key in t.items){t.items[key].name=CartFlowsImport.new_step_names[t.items[key].slug];var e={licence_args:CartFlowsImportVars.licence_args,per_page:100,_fields:CartFlowsImportVars.step_fields.toString()};e[CartFlowsImportVars.step_type]=t.items[key].id;e={remote_slug:CartFlowsImportVars.step,slug:CartFlowsImportVars.step+"?"+decodeURIComponent(l.param(e))};CartFlowsAPI._api_request(e,function(t){var e=wp.template("cartflows-steps-list");parseInt(t.items_count)?l("#wcf-remote-step-list").html(e(t)):l("#wcf-remote-step-list").html(wp.template("cartflows-no-steps")),0==--a&&(t=wp.template("cartflows-term-filters-dropdown"),e=wp.template("cartflows-term-filters"),l("#wcf-categories").html(e(s)),l("#wcf-scratch-steps-categories").html(t(s)),l("#wcf-scratch-steps-categories").find("option:first").addClass("current"),l("#wcf-categories").find("li a[data-slug=landing]").addClass("current"),l(".wcf-page-builder-notice").remove(),l("#wcf-remote-content").find(".spinner").remove(),CartFlowsImport._showSteps(),l("html").removeClass("wcf-steps-loading"))})}})},_render_remote_steps:function(t){t&&t.preventDefault(),l("#wcf-remote-step-importer").addClass("open"),l("html").addClass("wcf-popup-open")},_categorize_data:function(t){var a=[];return l.each(t,function(t,e){var s=e.step_type.slug;void 0===a[s]&&(a[s]=[]),a[s].push(e)}),a},_close_template_popup:function(t){(l(t.target).hasClass("wcf-templates-popup-overlay")||l(t.target).hasClass("close-icon"))&&(l(".wcf-templates-popup-overlay").hasClass("request-process")||(l("html").removeClass("wcf-popup-open"),l(".wcf-templates-popup-overlay").removeClass("open")))},_ajax:function(t,a,r){var o=CartFlowsImport;l.ajax({url:ajaxurl,type:"POST",data:t}).done(function(t,e,s){a&&"function"==typeof a&&a({request:t,status:e,XHR:s}),o.doc.trigger(r+"-done",[t,e,s])}).fail(function(t,e){o.doc.trigger(r+"-fail",[t,e])}).always(function(){o.doc.trigger(r+"-always")})},_empty:function(t){if("number"==typeof t||"boolean"==typeof t)return!1;if(null==t)return!0;if(void 0!==t.length)return 0==t.length;var e,s=0;for(e in t)t.hasOwnProperty(e)&&s++;return 0==s},_preview_individual:function(){var t=l(this).data("id")||"",e=l(this).data("href")||"";l(".cartflows-preview-flow-step").removeClass("active"),l('.cartflows-preview-flow-step[data-id="'+t+'"]').addClass("active"),l("#TB_window").addClass("cartflows-thickbox-loading"),l("#TB_iframeContent").removeAttr("onload"),l("#TB_iframeContent").removeAttr("src"),l("#TB_iframeContent").attr("src",e),l("#TB_iframeContent").attr("onload","CartFlowsImport.showIframe()")},showIframe:function(){l("#TB_load").remove(),l("#TB_window").css({visibility:"visible"}),l("#TB_window").removeClass("cartflows-thickbox-loading")},_previewLoaded:function(t){t.preventDefault(),l("#TB_window").removeClass("cartflows-thickbox-loading")},_filterFlowPageBuilderClick:function(t){t.preventDefault(),l(this).parents("ul").find("a").removeClass("current"),l(this).addClass("current"),l(".wcf-page-builder-notice").html(""),l("#wcf-remote-flow-list").html('<span class="spinner is-active"></span>'),CartFlowsImport._showFlows()},_showFlows:function(){var t={licence_args:CartFlowsImportVars.licence_args,_fields:CartFlowsImportVars.flow_fields.toString(),per_page:100},e=l("#wcf-page-builders .wcf-page-builder-links").find(".current").data("group")||"";l("#wcf-page-builders .wcf-page-builder-links").find(".current").data("slug"),l("#wcf-page-builders .wcf-page-builder-links").find(".current").data("title");""!==e&&"all"!==e&&(t[CartFlowsImportVars.flow_page_builder]=e);t={remote_slug:CartFlowsImportVars.flow,slug:CartFlowsImportVars.flow+"?"+decodeURIComponent(l.param(t))};CartFlowsAPI._api_request(t,function(a){"yes"!==CartFlowsImportVars.is_wc_activated&&l.each(a.items,function(s,t){a.items[s].woo_required=!1,l.each(t.flow_steps,function(t,e){0<=l.inArray(e.type,CartFlowsImport.woo_required_steps)&&(a.items[s].woo_required=!0)})});var t=wp.template("cartflows-flows-list");parseInt(a.items_count)?l("#wcf-remote-flow-list").html(t(a)):l("#wcf-remote-flow-list").html(wp.template("cartflows-no-flows")),l(".wcf-page-builder-notice").remove(),l("#wcf-remote-flow-importer").removeClass("request-process")})},_create_default_flow:function(t){t.preventDefault();var e=CartFlowsImport,s=l(this);s.hasClass("updating-message")||(l("#wcf-remote-flow-importer").addClass("request-process"),s.addClass("updating-message").text("Creating Flow.."),s.parents(".template").addClass("importing"),t={action:"cartflows_default_flow",security:cartflows_admin.cf_default_flow_nonce},e._ajax(t,function(t){var e;t.request.success&&(e=t.request.data,setTimeout(function(){s.removeClass("updating-message").text("Flow Created! Redirecting.."),window.location=CartFlowsImportVars.admin_url+"post.php?post="+e+"&action=edit"},3e3))}))},_activate_plugin:function(t){var e=CartFlowsImport,t={action:"cartflows_activate_plugin",plugin_init:t,security:CartFlowsImportVars.cartflows_activate_plugin_nonce};e._ajax(t,function(t){})},_process_import_flow:function(t){t.preventDefault();var e,a,r=l(this);r.hasClass("updating-message")||(l("#wcf-remote-flow-importer").addClass("request-process"),e=CartFlowsImport,r.text("Creating Flow.."),r.addClass("updating-message"),r.parents(".template").addClass("importing"),t=r.data("flow-steps")||"",a=""!==t?JSON.parse("["+t+"]"):[],t={action:"cartflows_create_flow",security:cartflows_admin.cf_create_flow_nonce},e._ajax(t,function(t){var s;t.request.success&&(s=t.request.data,a&&(CartFlowsAjaxQueue.stop(),CartFlowsAjaxQueue.run(),CartFlowsImport.all_steps=a.length,r.addClass("updating-message").text("Importing Step 1 of "+CartFlowsImport.all_steps),l.each(a,function(t,e){CartFlowsAjaxQueue.add({url:CartFlowsImportVars.ajaxurl,type:"POST",data:{action:"cartflows_import_flow_step",flow_id:s,template_id:e,security:cartflows_admin.cf_import_flow_step_nonce},success:function(t){CartFlowsImport.remaining_steps=CartFlowsImport.remaining_steps+1,CartFlowsImport.remaining_steps===CartFlowsImport.all_steps?(r.addClass("updating-message").text("Imported Step "+CartFlowsImport.remaining_steps+" of "+CartFlowsImport.all_steps),setTimeout(function(){r.removeClass("updating-message").text("All Imported! Redirecting.."),window.location=CartFlowsImportVars.admin_url+"post.php?post="+s+"&action=edit"},3e3)):r.addClass("updating-message").text("Importing Step "+CartFlowsImport.remaining_steps+" of "+CartFlowsImport.all_steps)},error:function(t){CartFlowsImport.remaining_steps=CartFlowsImport.remaining_steps+1,template=r.parents(".template.importing"),template.find(".preview").addClass("notice notice-warning").removeClass("preview").text(t.statusText),CartFlowsImport.remaining_steps===CartFlowsImport.all_steps?(r.addClass("updating-message").text("Failed "+CartFlowsImport.remaining_steps+" of "+CartFlowsImport.all_steps),setTimeout(function(){r.removeClass("updating-message button-primary").addClass("disabled")},3e3)):r.addClass("updating-message").text("Failed "+CartFlowsImport.remaining_steps+" of "+CartFlowsImport.all_steps)},fail:function(t){CartFlowsImport.remaining_steps=CartFlowsImport.remaining_steps+1,CartFlowsImport.remaining_steps===CartFlowsImport.all_steps?(r.addClass("updating-message").text("Imported "+CartFlowsImport.remaining_steps+" of "+CartFlowsImport.all_steps),setTimeout(function(){r.removeClass("updating-message").text("All Step Imported! Reloading.."),location.reload()},3e3)):r.addClass("updating-message").text("Importing "+CartFlowsImport.remaining_steps+" of "+CartFlowsImport.all_steps)}})})))}))},_handle_error:function(t){l(".cartflows-step-import.updating-message").addClass("updating-message").text(t.errorMessage)},_create_blank_step:function(t){t.preventDefault();var s=l(this),e=CartFlowsImport,a=l("#post_ID").val(),r=l("#wcf-scratch-steps-categories .step-type-filter-links .current").data("slug")||"",o=l("#wcf-scratch-steps-categories .step-type-filter-links .current").data("title")||"",t=r,r=l('.wcf-step-wrap[data-term-slug="'+r+'"]').length||1,r=o+" "+(parseInt(r)+1);if(all_step_type=["landing","checkout","thankyou"],l("#wcf-start-from-scratch .wcf-notice-wrap ").remove(),""!==t){if(CartFlowsImportVars._is_pro_active||"upsell"!==t&&"downsell"!==t){if(s.parents(".template").addClass("importing"),!CartFlowsImportVars._is_pro_active)if("-1"!=jQuery.inArray(t,all_step_type))if(0<l('.wcf-step-wrap[data-term-slug="'+t+'"]').length){s.parents(".template.importing");return s.removeClass("importing updating-message").text("Import Failed!"),void l("#wcf-start-from-scratch .inner").append('<div class="wcf-notice-wrap"><div class="notice notice-warning wcf-notice"><p>Upgrade to Pro for adding more than one '+t.charAt(0).toUpperCase()+t.slice(1)+" step.</p></div></div>")}l("#wcf-remote-step-importer").addClass("request-process"),l(".cartflows-step-import").addClass("disabled"),s.addClass("importing updating-message").text("Creating.."),l("body").hasClass("post-type-cartflows_flow")&&(r={action:"cartflows_step_create_blank",flow_id:a,step_type:t,step_title:r,security:cartflows_admin.cf_step_create_blank_nonce},e._ajax(r,function(t){CartFlowsImport;var e=s.parents(".template.importing");t.request.success?(s.text("Created. Reloading.."),setTimeout(function(){window.location.href=window.location.href+"&highlight-step-id="+t.request.data},3e3)):(s.removeClass("importing updating-message").text("Creating Failed!"),l("#wcf-remote-step-importer").removeClass("request-process"),e.find(".cartflows-step-preview").append("<div class='preview'></div>"),e.find(".preview").addClass("notice notice-warning").removeClass("preview").text(t.request.data))}))}}else l("#wcf-start-from-scratch .inner").append('<div class="wcf-notice-wrap"><div class="notice notice-info"><p>Please select the step type.</p></div></div>')},_process_import_step:function(t){t.preventDefault();var s=l(this),e=s.data("slug")||"",a=l('.wcf-step-wrap[data-term-slug="'+e+'"]').length||1;if((step_title=s.data("title")||"",step_custom_title=step_title+" "+(parseInt(a)+1),t=CartFlowsImport,all_step_slug=["landing","checkout","thankyou"],!CartFlowsImportVars._is_pro_active)&&("-1"!=jQuery.inArray(e,all_step_slug)&&0<l('.wcf-step-wrap[data-term-slug="'+e+'"]').length)){var r=s.parents(".template");return s.removeClass("importing updating-message").text("Import Failed!"),r.find(".preview").hide(),r.find("#wcf_create_notice").show().find("a").addClass("notice notice-warning ").text("Upgrade to Pro for adding more than one "+e.charAt(0).toUpperCase()+e.slice(1)+" step"),void l("#wcf-remote-step-importer").removeClass("request-process")}s.hasClass("updating-message")||(l("#wcf-remote-step-importer").addClass("request-process"),t=CartFlowsImport,s.addClass("updating-message"),s.parents(".template").addClass("importing"),a=s.data("template-id")||"",r=l("#post_ID").val(),e=e,t=CartFlowsImport,l(".cartflows-step-import-blank").addClass("disabled"),l(".cartflows-step-import").addClass("disabled"),s.addClass("importing updating-message").text("Importing.."),l("body").hasClass("post-type-cartflows_flow")&&(e={action:"cartflows_step_import",flow_id:r,template_id:a,step_title:step_custom_title,step_type:e,security:cartflows_admin.cf_step_import_nonce},t._ajax(e,function(t){CartFlowsImport;var e=s.parents(".template.importing");t.request.success?(s.text("Imported. Reloading.."),setTimeout(function(){window.location.href=window.location.href+"&highlight-step-id="+t.request.data},3e3)):(l(".wcf-templates-popup-overlay").removeClass("request-process"),s.removeClass("importing updating-message").text("Import Failed!"),e.find(".preview").addClass("notice notice-warning").removeClass("preview").text(t.request.data))},"add_template_to_page")))},_add_template_to_page_fail:function(t,e,s){t.preventDefault();CartFlowsImport;t=l(".wcf-flow-steps-data-wrap-importer .template.importing");t.find(".cartflows-step-import").removeClass("importing updating-message").text("Import Failed!"),t.find(".preview").addClass("notice notice-warning").removeClass("preview").text(e.responseText)},_activateWc:function(t){l.ajax({url:ajaxurl,type:"POST",data:{action:"cartflows_activate_plugin",plugin_init:"woocommerce/woocommerce.php",security:CartFlowsImportVars.cartflows_activate_plugin_nonce}}).done(function(t,e,s){l(".wcf-notice-wrap").addClass("wcf-hidden"),l(".cartflows-template-selector").removeAttr("disabled"),CartFlowsImport.wc_installed=!0,CartFlowsImportVars.is_wc_installed="yes",CartFlowsImportVars.is_wc_activated="yes",location.reload(),window.location.search+="&add-new-flow"})},_installWc:function(t){l(this).addClass("updating-message button"),l(this).text(cartflows_admin.wc_activating_message),0==cartflows_admin.wc_status.installed?(wp.updates.shouldRequestFilesystemCredentials&&!wp.updates.ajaxLocked&&(wp.updates.requestFilesystemCredentials(t),$document.on("credential-modal-cancel",function(){l(".install-now.updating-message").removeClass("updating-message").text(wp.updates.l10n.installNow),wp.a11y.speak(wp.updates.l10n.updateCancel,"polite")})),wp.updates.installPlugin({slug:"woocommerce"})):CartFlowsImport._activateWc()}},l(function(){CartFlowsImport.init()})}(jQuery);