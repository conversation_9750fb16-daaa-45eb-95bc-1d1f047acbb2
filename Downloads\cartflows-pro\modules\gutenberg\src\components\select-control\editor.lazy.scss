@import "../../styles/variables";

.uagb-select-control {

	&--layout-inline {

		.components-select-control {
			align-items: center;
			display: grid;
			grid-template-columns: 50% 50%;
			margin-bottom: $spectra-control-vertical-gap;
			color: $spectra-color-body;
		}

		.uagb-control__header,
		.uag-control-label,
		.components-select-control label.components-input-control__label {
			margin-bottom: 0;
		}

		&:last-child .components-select-control {
			margin-bottom: 0;
		}
	}

}
