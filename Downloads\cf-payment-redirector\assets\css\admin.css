/**
 * Admin CSS para CartFlows Payment Redirector
 */

.cfpr-admin-wrap {
    margin: 20px;
}

.cfpr-admin-content {
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    margin-top: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cfpr-admin-description {
    margin-bottom: 20px;
    font-size: 14px;
}

/* Estilo das abas */
#cfpr-flow-tabs {
    margin-top: 20px;
}

.cfpr-tabs-nav {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 1px solid #ccc;
}

.cfpr-tabs-nav li {
    margin: 0;
}

.cfpr-tabs-nav li a {
    display: block;
    padding: 10px 15px;
    text-decoration: none;
    color: #333;
    background: #f7f7f7;
    border: 1px solid #ccc;
    border-bottom: none;
    margin-right: 5px;
    border-radius: 3px 3px 0 0;
}

.cfpr-tabs-nav li a.ui-state-active {
    background: #fff;
    border-bottom-color: #fff;
    position: relative;
    top: 1px;
}

.cfpr-tab-content {
    padding: 20px;
    border: 1px solid #ccc;
    border-top: none;
    background: #fff;
}

/* Tabela de configurações */
.cfpr-settings-table {
    width: 100%;
    border-collapse: collapse;
}

.cfpr-settings-table th,
.cfpr-settings-table td {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.cfpr-settings-table th {
    text-align: left;
    font-weight: 600;
    background: #f7f7f7;
}

.cfpr-settings-table select {
    width: 100%;
    max-width: 350px;
} 