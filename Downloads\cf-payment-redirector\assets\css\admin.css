/**
 * Admin CSS para CartFlows Payment Redirector
 * Integrado com o layout do CartFlows Pro
 */

/* Fonte Inter para consistência com CartFlows */
* {
    font-family: Inter, sans-serif;
}

/* Remover padding padrão do WordPress para páginas do CartFlows */
.toplevel_page_cfpr-settings #wpcontent {
    padding-left: 0;
}

/* Wrapper principal seguindo padrão CartFlows */
.wcf-menu-page-wrapper a:focus {
    box-shadow: none;
    outline: none;
}

/* Cabeçalho personalizado */
.cfpr-page-title {
    margin-left: 15px;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

/* Conteúdo principal */
.cfpr-admin-content {
    background: #fff;
    padding: 30px 20px;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.cfpr-admin-description {
    margin-bottom: 30px;
    font-size: 16px;
    line-height: 1.5;
    color: #6b7280;
}

/* Estilo das abas seguindo padrão CartFlows */
#cfpr-flow-tabs {
    margin-top: 30px;
}

.cfpr-tabs-nav {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: 2px solid #e5e7eb;
    background: #f9fafb;
    border-radius: 8px 8px 0 0;
}

.cfpr-tabs-nav li {
    margin: 0;
}

.cfpr-tabs-nav li a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #6b7280;
    background: transparent;
    border: none;
    margin-right: 2px;
    border-radius: 8px 8px 0 0;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
}

.cfpr-tabs-nav li a:hover {
    color: #f06335;
    background: rgba(240, 99, 53, 0.05);
}

.cfpr-tabs-nav li a.ui-state-active {
    background: #fff;
    color: #f06335;
    border-bottom: 2px solid #f06335;
    position: relative;
    top: 2px;
    font-weight: 600;
}

.cfpr-tab-content {
    padding: 30px;
    border: 1px solid #e5e7eb;
    border-top: none;
    background: #fff;
    border-radius: 0 0 8px 8px;
}

/* Tabela de configurações seguindo padrão CartFlows */
.cfpr-settings-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cfpr-settings-table th,
.cfpr-settings-table td {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
}

.cfpr-settings-table th {
    text-align: left;
    font-weight: 600;
    background: #f9fafb;
    color: #374151;
    font-size: 14px;
    letter-spacing: 0.025em;
}

.cfpr-settings-table td {
    background: #fff;
}

.cfpr-settings-table tr:last-child th,
.cfpr-settings-table tr:last-child td {
    border-bottom: none;
}

.cfpr-settings-table select {
    width: 100%;
    max-width: 350px;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: #fff;
    font-size: 14px;
    color: #374151;
    transition: border-color 0.2s ease;
}

.cfpr-settings-table select:focus {
    outline: none;
    border-color: #f06335;
    box-shadow: 0 0 0 3px rgba(240, 99, 53, 0.1);
}

/* Botão de submit seguindo padrão CartFlows */
.cfpr-admin-settings .button-primary {
    background: #f06335;
    border-color: #f06335;
    color: #fff;
    padding: 10px 24px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.cfpr-admin-settings .button-primary:hover {
    background: #e55527;
    border-color: #e55527;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(240, 99, 53, 0.3);
}

/* Títulos das seções */
.cfpr-tab-content h3 {
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e5e7eb;
}