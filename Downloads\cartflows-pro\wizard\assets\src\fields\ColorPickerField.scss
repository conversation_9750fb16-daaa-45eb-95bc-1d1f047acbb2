.wcf-color-field {
	display: flex;
	align-items: center;
	line-height: 1;

	.wcf-colorpicker-swatch-wrap {
		display: flex;
		align-items: center;
		cursor: pointer;
	}

	.wcf-colorpicker-swatch {
		padding: 0;
		background: #fafafa;
		display: inline-block;
		margin: 0;
		border-right: 1px solid #ddd;
		box-shadow: inset 0 0 0 3px #fff;
	}

	.wcf-color-picker-popover {
		position: absolute;
		z-index: 2;
		margin: 8px 0;
		left: -50px;
	}

	.wcf-color-picker-cover {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}

	.sketch-picker {
		font-weight: 400;
	}

	.wcf-field__data--content {
		.wcf-colorpicker-selector {
			display: flex;
			align-items: center;
			flex-direction: row-reverse;
		}

		.wcf-colorpicker-swatch-wrap {
			border: 1px solid #ddd;
			border-radius: 2px;
			background: #fff;
			color: #444;
		}

		.wcf-colorpicker-label {
			font-weight: 400;
			font-size: 12px;
			padding: 5px 7px;
		}

		.wcf-colorpicker-reset {
			padding: 6px;
			cursor: pointer;
		}
	}

	.wcf-color-picker {
		position: relative;

		input {
			margin: 0;
		}

		input[id^="rc-editable-input-"] {
			width: 100% !important;
		}
	}
}
