msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: gpt-po v1.1.1\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-04T15:33:29+00:00\n"
"PO-Revision-Date: 2025-02-04T15:33:29+00:00\n"
"Language: \n"

#: cartflows.php
#: classes/class-cartflows-admin-notices.php:217
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:62
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:71
#. Plugin Name of the plugin
msgid "CartFlows"
msgstr "CartFlows"

#: cartflows.php
#. Plugin URI of the plugin
msgid "https://cartflows.com/"
msgstr "https://cartflows.com/"

#: cartflows.php
#. Description of the plugin
msgid "Create beautiful checkout pages & sales flows for WooCommerce."
msgstr "Erstellen Sie schöne Checkout-Seiten und Verkaufsabläufe für WooCommerce."

#: admin-core/ajax/ab-steps.php:89
#. translators: %s step id
msgid "Can't create a variation for this step - %s, Invalid Step ID."
msgstr "Kann keine Variation für diesen Schritt erstellen - %s, ungültige Schritt-ID."

#: admin-core/ajax/ab-steps.php:105
#. translators: %s flow id
msgid "Step successfully hidden - %s"
msgstr "Schritt erfolgreich ausgeblendet - %s"

#: admin-core/ajax/ab-steps.php:140
#. translators: %s step id
msgid "Can't delete a variation for this step - %s, Invalid Step Id or Funnel Id."
msgstr "Kann die Variation für diesen Schritt nicht löschen - %s, ungültige Schritt-ID oder Trichter-ID."

#: admin-core/ajax/ab-steps.php:188
#. translators: %s flow id
msgid "Step deleted - %s"
msgstr "Schritt gelöscht - %s"

#: admin-core/ajax/ab-steps.php:223
#. translators: %s step id
msgid "Can't create a variation for this step - %s"
msgstr "Kann keine Variation für diesen Schritt erstellen - %s"

#: admin-core/ajax/ab-steps.php:279
#. translators: %s step id
msgid "A/B test settings updated for this step - %s"
msgstr "A/B-Testeinstellungen für diesen Schritt aktualisiert - %s"

#: admin-core/ajax/ajax-errors.php:59
#: wizard/ajax/ajax-errors.php:59
msgid "Sorry, you are not allowed to do this operation."
msgstr "Entschuldigung, Sie dürfen diese Operation nicht durchführen."

#: admin-core/ajax/ajax-errors.php:60
#: admin-core/ajax/common-settings.php:217
#: admin-core/ajax/common-settings.php:279
#: admin-core/ajax/common-settings.php:385
#: admin-core/ajax/common-settings.php:418
#: admin-core/inc/meta-ops.php:32
#: modules/checkout/classes/class-cartflows-checkout-ajax.php:110
#: wizard/ajax/ajax-errors.php:60
msgid "Nonce validation failed"
msgstr "Nonce-Überprüfung fehlgeschlagen"

#: admin-core/ajax/ajax-errors.php:61
#: wizard/ajax/ajax-errors.php:61
msgid "Sorry, something went wrong."
msgstr "Entschuldigung, etwas ist schiefgelaufen."

#: admin-core/ajax/ajax-errors.php:62
msgid "Required parameter is missing from the posted data."
msgstr "Ein erforderlicher Parameter fehlt in den gesendeten Daten."

#: admin-core/ajax/common-settings.php:85
msgid "Successfully deleted the dynamic CSS keys!"
msgstr "Die dynamischen CSS-Schlüssel wurden erfolgreich gelöscht!"

#: admin-core/ajax/common-settings.php:105
msgid "No post data found!"
msgstr "Keine Postdaten gefunden!"

#: admin-core/ajax/common-settings.php:152
msgid "Successfully saved data!"
msgstr "Daten erfolgreich gespeichert!"

#: admin-core/ajax/debugger.php:82
#: admin-core/ajax/debugger.php:133
#: admin-core/ajax/debugger.php:157
msgid "You don't have permission to perform this action."
msgstr "Sie haben keine Berechtigung, diese Aktion auszuführen."

#: admin-core/ajax/debugger.php:91
msgid "Sync Success."
msgstr "Synchronisierung erfolgreich."

#: admin-core/ajax/debugger.php:105
#: admin-core/inc/log-status.php:79
msgid "You don't have permission to view this page."
msgstr "Sie haben keine Berechtigung, diese Seite anzusehen."

#: admin-core/ajax/debugger.php:139
#: admin-core/inc/log-status.php:175
msgid "Filename is empty. Please refresh the page and retry."
msgstr "Dateiname ist leer. Bitte aktualisieren Sie die Seite und versuchen Sie es erneut."

#: admin-core/ajax/debugger.php:174
#: admin-core/inc/log-status.php:210
msgid "Invalid file."
msgstr "Ungültige Datei."

#: admin-core/ajax/debugger.php:181
msgid "Export logs successfully"
msgstr "Protokolle erfolgreich exportiert"

#: admin-core/ajax/flows.php:97
msgid "No Funnel IDs has been supplied to export!"
msgstr "Es wurden keine Trichter-IDs zum Exportieren angegeben!"

#: admin-core/ajax/flows.php:110
#: admin-core/ajax/importer.php:109
#: admin-core/ajax/importer.php:212
msgid "Funnel exported successfully"
msgstr "Trichter erfolgreich exportiert"

#: admin-core/ajax/flows.php:142
msgid "Can't update the flow data"
msgstr "Kann die Flussdaten nicht aktualisieren"

#: admin-core/ajax/flows.php:159
#: admin-core/ajax/steps.php:415
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "(no title)"
msgstr "(kein Titel)"

#: admin-core/ajax/flows.php:184
msgid "Successfully saved the flow data!"
msgstr "Die Flussdaten wurden erfolgreich gespeichert!"

#: admin-core/ajax/flows.php:246
msgid "Successfully deleted the Funnels!"
msgstr "Die Trichter wurden erfolgreich gelöscht!"

#: admin-core/ajax/flows.php:274
#: admin-core/ajax/flows.php:352
#: admin-core/ajax/flows.php:988
msgid "No Funnel IDs has been supplied to delete!"
msgstr "Es wurden keine Trichter-IDs zum Löschen angegeben!"

#: admin-core/ajax/flows.php:323
#: admin-core/ajax/flows.php:391
msgid "Successfully trashed the Funnels!"
msgstr "Die Funnels wurden erfolgreich in den Papierkorb verschoben!"

#: admin-core/ajax/flows.php:422
msgid "Invalid Funnel ID has been supplied to update title."
msgstr "Ungültige Trichter-ID wurde zur Aktualisierung des Titels angegeben."

#: admin-core/ajax/flows.php:427
msgid "Can't update the flow title"
msgstr "Der Titel des Flusses kann nicht aktualisiert werden"

#: admin-core/ajax/flows.php:443
#. translators: %s flow id
msgid "Funnel title updated - %s"
msgstr "Trichtertitel aktualisiert - %s"

#: admin-core/ajax/flows.php:468
msgid "Invalid Funnel ID has been supplied to clone!"
msgstr "Ungültige Trichter-ID wurde zum Klonen angegeben!"

#: admin-core/ajax/flows.php:502
msgid "Invalid Funnel ID has been supplied to duplicate!"
msgstr "Ungültige Trichter-ID wurde zum Duplizieren angegeben!"

#: admin-core/ajax/flows.php:679
msgid "Successfully cloned the Funnel!"
msgstr "Der Funnel wurde erfolgreich geklont!"

#: admin-core/ajax/flows.php:708
msgid "Invalid Funnel ID has been supplied to restore!"
msgstr "Ungültige Trichter-ID wurde zur Wiederherstellung angegeben!"

#: admin-core/ajax/flows.php:748
msgid "Successfully restored the Funnel!"
msgstr "Der Trichter wurde erfolgreich wiederhergestellt!"

#: admin-core/ajax/flows.php:775
msgid "Invalid Funnel ID has been supplied to trash!"
msgstr "Ungültige Trichter-ID wurde zum Löschen angegeben!"

#: admin-core/ajax/flows.php:814
msgid "Successfully trashed the Funnel!"
msgstr "Der Trichter wurde erfolgreich in den Papierkorb verschoben!"

#: admin-core/ajax/flows.php:841
msgid "Invalid Funnel ID has been supplied to delete!"
msgstr "Ungültige Trichter-ID wurde zum Löschen angegeben!"

#: admin-core/ajax/flows.php:882
msgid "Successfully deleted the Funnel!"
msgstr "Der Funnel wurde erfolgreich gelöscht!"

#: admin-core/ajax/flows.php:909
msgid "Invalid Funnel IDs has been supplied to update status!"
msgstr "Ungültige Trichter-IDs wurden zur Aktualisierung des Status bereitgestellt!"

#: admin-core/ajax/flows.php:958
#: admin-core/ajax/flows.php:1017
msgid "Successfully updated the Funnel status!"
msgstr "Der Funnel-Status wurde erfolgreich aktualisiert!"

#: admin-core/ajax/flows.php:1057
msgid "Invalid flow ID has been provided."
msgstr "Es wurde eine ungültige Flow-ID angegeben."

#: admin-core/ajax/flows.php:1073
#. translators: %s flow id
msgid "Steps not sorted for flow - %s"
msgstr "Schritte nicht sortiert für Ablauf - %s"

#: admin-core/ajax/flows.php:1113
#. translators: %s flow id
msgid "Steps sorted for flow - %s"
msgstr "Schritte sortiert für Ablauf - %s"

#: admin-core/ajax/flows.php:1146
msgid "No Funnel ID is been supplied"
msgstr "Es wurde keine Trichter-ID angegeben"

#: admin-core/ajax/flows.php:1159
#. translators: %s flow id
msgid "Notice Dismissed"
msgstr "Benachrichtigung verworfen"

#: admin-core/ajax/importer.php:116
msgid "No Funnels to export"
msgstr "Keine Trichter zum Exportieren"

#: admin-core/ajax/importer.php:205
msgid "Invalid flow ID."
msgstr "Ungültige Flow-ID."

#: admin-core/ajax/importer.php:392
msgid "Invalid Funnel Id has been provided."
msgstr "Es wurde eine ungültige Trichter-ID angegeben."

#: admin-core/ajax/importer.php:407
#. translators: %s: step ID
msgid "Invalid step id %1$s."
msgstr "Ungültige Schritt-ID %1$s."

#: admin-core/ajax/importer.php:414
msgid "Successfully created the step!"
msgstr "Der Schritt wurde erfolgreich erstellt!"

#: admin-core/ajax/importer.php:516
msgid "Theme Activated"
msgstr "Thema aktiviert"

#: admin-core/ajax/importer.php:575
#: admin-core/ajax/importer.php:590
#: modules/flow/classes/class-cartflows-step-post-type.php:262
#: wizard/ajax/wizard.php:717
msgid "Checkout"
msgstr "Kasse"

#: admin-core/ajax/importer.php:579
#: admin-core/ajax/importer.php:594
#: admin-core/ajax/importer.php:606
#: modules/flow/classes/class-cartflows-step-post-type.php:269
#: wizard/ajax/wizard.php:721
#: wizard/ajax/wizard.php:732
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Thank You"
msgstr "Danke"

#: admin-core/ajax/importer.php:586
msgid "Sales Landing"
msgstr "Verkaufsseite"

#: admin-core/ajax/importer.php:602
#: modules/flow/classes/class-cartflows-step-post-type.php:248
#: wizard/ajax/wizard.php:728
msgid "Landing"
msgstr "Landung"

#: admin-core/ajax/importer.php:661
#: wizard/ajax/wizard.php:805
msgid "Successfully created the Funnel!"
msgstr "Der Trichter wurde erfolgreich erstellt!"

#: admin-core/ajax/importer.php:719
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "CartFlows Pro erforderlich! %1$sUpgrade auf CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:721
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$supgrade to CartFlows Pro%2$s."
msgstr "Um den Premium-Flow zu importieren, %1$supgrade auf CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:724
#: wizard/ajax/wizard.php:544
#. translators: %1$s: link html start, %2$s: link html end
msgid "Activate the CartFlows Pro to import the flow! %1$sActivate CartFlows Pro%2$s"
msgstr "Aktivieren Sie CartFlows Pro, um den Flow zu importieren! %1$sCartFlows Pro aktivieren%2$s"

#: admin-core/ajax/importer.php:726
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate Cartflows Pro%2$s and validate the license key."
msgstr "Um den Premium-Flow zu importieren, %1$saktivieren Sie Cartflows Pro%2$s und validieren Sie den Lizenzschlüssel."

#: admin-core/ajax/importer.php:729
#. translators: %1$s: link html start, %2$s: link html end
msgid "Invalid License Key! %1$sActivate CartFlows Pro%2$s"
msgstr "Ungültiger Lizenzschlüssel! %1$sCartFlows Pro aktivieren%2$s"

#: admin-core/ajax/importer.php:731
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate CartFlows Pro%2$s."
msgstr "Um den Premium-Flow zu importieren, %1$saktivieren Sie CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:744
#: admin-core/ajax/importer.php:1056
msgid "Funnel data not found."
msgstr "Trichterdaten nicht gefunden."

#: admin-core/ajax/importer.php:791
msgid "Steps not found."
msgstr "Schritte nicht gefunden."

#: admin-core/ajax/importer.php:824
#: wizard/ajax/wizard.php:642
msgid "Successfully imported the Flow!"
msgstr "Der Flow wurde erfolgreich importiert!"

#: admin-core/ajax/importer.php:873
msgid "Step data ID not found for import."
msgstr "Schrittdaten-ID für den Import nicht gefunden."

#: admin-core/ajax/importer.php:885
msgid "Funnel ID not found in the request."
msgstr "Trichter-ID in der Anfrage nicht gefunden."

#: admin-core/inc/admin-helper.php:1127
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$1sTo resolve this issue, please check this %2$2sarticle%3$3s."
msgstr ""
"Anforderungszeitüberschreitungsfehler. Bitte überprüfen Sie, ob die Firewall oder ein Sicherheits-Plugin die "
"ausgehenden HTTP/HTTPS-Anfragen an templates.cartflows.com blockiert. %1$1sUm dieses Problem zu beheben, lesen Sie "
"bitte diesen %2$2sArtikel%3$3s."

#: admin-core/ajax/importer.php:915
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sUpgrade to CartFlows Pro.%2$s"
msgstr "%1$sUpgrade auf CartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:916
msgid "To import the premium step, please upgrade to CartFlows Pro"
msgstr "Um den Premium-Schritt zu importieren, aktualisieren Sie bitte auf CartFlows Pro"

#: admin-core/ajax/importer.php:919
#: admin-core/ajax/importer.php:1041
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro%2$s"
msgstr "%1$sAktiviere CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:920
msgid "To import the premium step activate Cartflows Pro and validate the license key."
msgstr "Um den Premium-Schritt zu importieren, aktivieren Sie Cartflows Pro und validieren Sie den Lizenzschlüssel."

#: admin-core/ajax/importer.php:923
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro License %2$s"
msgstr "%1$sAktiviere CartFlows Pro Lizenz %2$s"

#: admin-core/ajax/importer.php:924
msgid "To import the premium step activate the CartFlows Pro."
msgstr "Um den Premium-Schritt zu importieren, aktivieren Sie CartFlows Pro."

#: admin-core/ajax/importer.php:959
#: admin-core/ajax/importer.php:1080
msgid "Step data not found."
msgstr "Schrittdaten nicht gefunden."

#: admin-core/ajax/importer.php:967
#: admin-core/ajax/importer.php:1088
msgid "Successfully imported the Step!"
msgstr "Der Schritt wurde erfolgreich importiert!"

#: admin-core/ajax/importer.php:1038
#. translators: %1$s: link html start, %2$s: link html end
msgid "Upgrade to %1$sCartFlows Pro.%2$s"
msgstr "Upgrade zu %1$sCartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:1044
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro license is not active. Activate %1$sCartFlows Pro License %2$s"
msgstr "Die CartFlows Pro-Lizenz ist nicht aktiv. Aktivieren Sie %1$sCartFlows Pro-Lizenz %2$s"

#: admin-core/ajax/importer.php:1112
#: admin-core/ajax/importer.php:1198
#. translators: %s: step ID
msgid "Invalid step id %1$s or post id %2$s."
msgstr "Ungültige Schritt-ID %1$s oder Post-ID %2$s."

#: admin-core/ajax/importer.php:1175
#: admin-core/inc/admin-menu.php:1194
#: admin-core/inc/store-checkout.php:110
msgid "Nonce verification failed."
msgstr "Überprüfung des Nonce ist fehlgeschlagen."

#: admin-core/ajax/importer.php:1454
#: wizard/ajax/wizard.php:388
msgid "Successful!"
msgstr "Erfolgreich!"

#: admin-core/ajax/meta-data.php:143
#. Translators: %d stock amount
msgid "Stock: %d"
msgstr "Bestand: %d"

#: admin-core/ajax/meta-data.php:271
msgid "On backorder"
msgstr "Nachbestellt"

#: admin-core/ajax/meta-data.php:274
msgid "In stock"
msgstr "Auf Lager"

#: admin-core/ajax/meta-data.php:277
msgid "Out of stock"
msgstr "Nicht auf Lager"

#: admin-core/ajax/setup-page.php:84
msgid "Setup page dismissed successfully."
msgstr "Einrichtungsseite erfolgreich geschlossen."

#: admin-core/ajax/steps.php:91
msgid "Can't update the step title"
msgstr "Der Schritt-Titel kann nicht aktualisiert werden"

#: admin-core/ajax/steps.php:112
#. translators: %s flow id
msgid "Step title updated - %s"
msgstr "Schritt-Titel aktualisiert - %s"

#: admin-core/ajax/steps.php:148
#. translators: %s flow id
msgid "Can't clone this step - %1$s. Flow - %2$s"
msgstr "Kann diesen Schritt nicht klonen - %1$s. Ablauf - %2$s"

#: admin-core/ajax/steps.php:267
#. translators: %s flow id
msgid "Step - %1$s cloned. Flow - %2$s"
msgstr "Schritt - %1$s geklont. Ablauf - %2$s"

#: admin-core/ajax/steps.php:315
#. translators: %s flow id
msgid "Step not deleted for flow - %s"
msgstr "Schritt nicht gelöscht für Flow - %s"

#: admin-core/ajax/steps.php:358
#. translators: %s flow id
msgid "Step deleted for flow - %s"
msgstr "Schritt gelöscht für Ablauf - %s"

#: admin-core/ajax/steps.php:367
#. translators: %s flow id
msgid "This step can not be deleted."
msgstr "Dieser Schritt kann nicht gelöscht werden."

#: admin-core/ajax/steps.php:400
#. translators: %s flow id
msgid "Invalid Step Id has been provided."
msgstr "Ungültige Schritt-ID wurde angegeben."

#: admin-core/ajax/steps.php:451
#. translators: %s flow id
msgid "Data saved successfully for step id %s"
msgstr "Daten erfolgreich für Schritt-ID %s gespeichert"

#: admin-core/api/common-settings.php:129
#: admin-core/api/flow-data.php:139
#: admin-core/api/flows.php:287
#: admin-core/api/home-page.php:172
#: admin-core/api/product/product-data.php:121
#: admin-core/api/step-data.php:144
msgid "Sorry, you cannot list resources."
msgstr "Entschuldigung, Sie können keine Ressourcen auflisten."

#: admin-core/api/flow-data.php:70
msgid "Flow ID."
msgstr "Fluss-ID."

#: admin-core/api/flows.php:194
#: modules/flow/classes/class-cartflows-flow-post-type.php:229
#: admin-core/assets/build/settings-app.js:80
msgid "View"
msgstr "Ansicht"

#: admin-core/api/flows.php:202
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Edit"
msgstr "Bearbeiten"

#: admin-core/api/flows.php:210
#: admin-core/inc/admin-helper.php:763
#: admin-core/inc/admin-helper.php:845
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate"
msgstr "Duplikat"

#: admin-core/api/flows.php:217
#: admin-core/assets/build/settings-app.js:32
msgid "Export"
msgstr "Exportieren"

#: admin-core/api/flows.php:224
#: admin-core/inc/admin-helper.php:780
#: admin-core/inc/admin-helper.php:854
#: admin-core/inc/admin-helper.php:922
#: admin-core/inc/admin-helper.php:942
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Delete"
msgstr "Löschen"

#: admin-core/api/product/product-data.php:68
#: admin-core/api/step-data.php:69
msgid "Step ID."
msgstr "Schritt-ID."

#: admin-core/inc/admin-helper.php:580
#: admin-core/inc/flow-meta.php:262
#: classes/class-cartflows-helper.php:1412
#: classes/class-cartflows-helper.php:1429
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:167
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:194
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:96
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1211
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:103
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:442
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:228
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:143
#: modules/optin/classes/class-cartflows-optin-meta-data.php:305
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:50
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Default"
msgstr "Standard"

#: admin-core/inc/admin-helper.php:597
msgid "System Fonts"
msgstr "Systemschriftarten"

#: admin-core/inc/admin-helper.php:615
msgid "Google Fonts"
msgstr "Google Fonts"

#: admin-core/inc/admin-helper.php:772
msgid "A/B Test"
msgstr "A/B-Test"

#: admin-core/inc/admin-helper.php:797
msgid "Automation"
msgstr "Automatisierung"

#: admin-core/inc/admin-helper.php:800
msgid "(Connect)"
msgstr "(Verbinden)"

#: admin-core/inc/admin-helper.php:862
msgid "Archive"
msgstr "Archiv"

#: admin-core/inc/admin-helper.php:869
msgid "Declare as Winner"
msgstr "Als Gewinner erklären"

#: admin-core/inc/admin-helper.php:913
msgid "Deleted variation can't be restored."
msgstr "Gelöschte Variante kann nicht wiederhergestellt werden."

#: admin-core/inc/admin-helper.php:914
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:188
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:200
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:212
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:224
msgid "Hide"
msgstr "Verbergen"

#: admin-core/inc/admin-helper.php:934
#: admin-core/assets/build/settings-app.js:32
msgid "Restore"
msgstr "Wiederherstellen"

#: admin-core/inc/admin-helper.php:1115
msgid "Ooops! Something went wrong. Please open a support ticket from the website."
msgstr "Ups! Etwas ist schiefgelaufen. Bitte eröffnen Sie ein Support-Ticket über die Website."

#: admin-core/inc/admin-helper.php:1116
msgid "No error found."
msgstr "Kein Fehler gefunden."

#: admin-core/inc/admin-helper.php:1141
#. translators: %1$s: HTML, %2$s: HTML, %3$s: HTML
msgid ""
"Sorry for the inconvenience, but your website seems to be having trouble connecting to our server. %1$s Please open a "
"technical %2$ssupport ticket%3$s and share the server's outgoing IP address."
msgstr ""
"Entschuldigen Sie die Unannehmlichkeiten, aber Ihre Website scheint Probleme zu haben, eine Verbindung zu unserem "
"Server herzustellen. %1$s Bitte öffnen Sie ein technisches %2$sSupport-Ticket%3$s und teilen Sie die ausgehende "
"IP-Adresse des Servers mit."

#: admin-core/inc/admin-helper.php:1143
msgid "Server's outgoing IP address: "
msgstr "Ausgehende IP-Adresse des Servers:"

#: admin-core/inc/admin-menu.php:123
#: admin-core/inc/admin-menu.php:174
#: classes/class-cartflows-flow-frontend.php:70
#: admin-core/assets/build/settings-app.js:32
msgid "Edit Funnel"
msgstr "Trichter bearbeiten"

#: admin-core/inc/admin-menu.php:210
msgid "Go to Funnel Editing"
msgstr "Gehe zur Trichterbearbeitung"

#: admin-core/inc/admin-menu.php:258
#: admin-core/inc/admin-menu.php:259
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:25
msgid "Funnels"
msgstr "Trichter"

#: admin-core/inc/admin-menu.php:269
#: admin-core/inc/admin-menu.php:270
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: wizard/assets/build/wizard-app.js:1
msgid "Store Checkout"
msgstr "Kasse"

#: admin-core/inc/admin-menu.php:278
#: admin-core/inc/admin-menu.php:280
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Automations"
msgstr "Automatisierungen"

#: admin-core/inc/admin-menu.php:280
#: admin-core/inc/admin-menu.php:290
msgid "New"
msgstr "Neu"

#: admin-core/inc/admin-menu.php:298
#: admin-core/inc/admin-menu.php:299
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Add-ons"
msgstr "Add-ons"

#: admin-core/inc/admin-menu.php:308
#: admin-core/inc/admin-menu.php:309
msgid "Setup"
msgstr "Einrichtung"

#: admin-core/inc/admin-menu.php:328
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Dashboard"
msgstr "Armaturenbrett"

#: admin-core/inc/admin-menu.php:541
msgid "Thin 100"
msgstr "Dünn 100"

#: admin-core/inc/admin-menu.php:542
msgid "Extra-Light 200"
msgstr "Extra-Leicht 200"

#: admin-core/inc/admin-menu.php:543
msgid "Light 300"
msgstr "Licht 300"

#: admin-core/inc/admin-menu.php:544
msgid "Normal 400"
msgstr "Normal 400"

#: admin-core/inc/admin-menu.php:545
msgid "Medium 500"
msgstr "Mittel 500"

#: admin-core/inc/admin-menu.php:546
msgid "Semi-Bold 600"
msgstr "Halbfett 600"

#: admin-core/inc/admin-menu.php:547
msgid "Bold 700"
msgstr "Fett 700"

#: admin-core/inc/admin-menu.php:548
msgid "Extra-Bold 800"
msgstr "Extra-Fett 800"

#: admin-core/inc/admin-menu.php:549
msgid "Ultra-Bold 900"
msgstr "Ultra-Fett 900"

#: admin-core/inc/admin-menu.php:634
#. Translators: %1$s is the required page builder title, %2$s is the opening anchor tag to plugins.php, %3$s is the closing anchor tag, %4$s is the plugin title.
msgid "The default page builder is set to %1$s. Please %2$sinstall & activate%3$s the %4$s to start editing the steps."
msgstr ""
"Der Standard-Seitenersteller ist auf %1$s eingestellt. Bitte %2$sinstallieren und aktivieren%3$s Sie den %4$s, um mit "
"der Bearbeitung der Schritte zu beginnen."

#: admin-core/inc/admin-menu.php:1002
msgid "Stripe Payments For WooCommerce"
msgstr "Stripe-Zahlungen für WooCommerce"

#: admin-core/inc/admin-menu.php:1003
msgid "Accept credit card payments in your store with Stripe for WooCommerce."
msgstr "Akzeptieren Sie Kreditkartenzahlungen in Ihrem Geschäft mit Stripe für WooCommerce."

#: admin-core/inc/admin-menu.php:1014
msgid "PayPal Payments For WooCommerce"
msgstr "PayPal-Zahlungen für WooCommerce"

#: admin-core/inc/admin-menu.php:1015
msgid "Accept payments in your store with PayPal for WooCommerce."
msgstr "Akzeptieren Sie Zahlungen in Ihrem Geschäft mit PayPal für WooCommerce."

#: admin-core/inc/admin-menu.php:1026
msgid "WooCommerce"
msgstr "WooCommerce"

#: admin-core/inc/admin-menu.php:1027
msgid "WooCommerce is a customizable, open-source ecommerce platform built on WordPress."
msgstr "WooCommerce ist eine anpassbare, quelloffene E-Commerce-Plattform, die auf WordPress basiert."

#: admin-core/inc/admin-menu.php:1051
msgid "SureMembers"
msgstr "SureMembers"

#: admin-core/inc/admin-menu.php:1065
msgid "Transform your WordPress form-building experience with stunning designs, ai integration, and no-code flexibility."
msgstr ""
"Verwandeln Sie Ihr WordPress-Formularerstellungserlebnis mit atemberaubenden Designs, KI-Integration und "
"No-Code-Flexibilität."

#: admin-core/inc/admin-menu.php:1064
msgid "SureForms"
msgstr "SureForms"

#: admin-core/inc/admin-menu.php:1076
#: wizard/assets/build/wizard-app.js:1
msgid "Spectra"
msgstr "Spektren"

#: admin-core/inc/admin-menu.php:1077
msgid "Power-up the Gutenberg editor with advanced and powerful blocks."
msgstr "Rüsten Sie den Gutenberg-Editor mit fortschrittlichen und leistungsstarken Blöcken auf."

#: admin-core/inc/admin-menu.php:1108
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Cart Abandonment"
msgstr "Warenkorbabbruch"

#: admin-core/inc/admin-menu.php:1109
msgid "Recover abandonded carts with ease in less than 10 minutes."
msgstr "Verlassene Warenkörbe mühelos in weniger als 10 Minuten wiederherstellen."

#: admin-core/inc/admin-menu.php:1120
msgid "Variation Swatches for WooCommerce"
msgstr "Variationsmuster für WooCommerce"

#: admin-core/inc/admin-menu.php:1121
msgid "Convert dropdown boxes into highly engaging variation swatches."
msgstr "Dropdown-Boxen in hochgradig ansprechende Variationsmuster umwandeln."

#: admin-core/inc/admin-menu.php:1137
msgid "Astra"
msgstr "Astra"

#: admin-core/inc/admin-menu.php:1138
msgid ""
"Astra is fast, fully customizable & beautiful WordPress theme suitable for blog, personal portfolio, business website "
"and WooCommerce storefront."
msgstr ""
"Astra ist ein schnelles, vollständig anpassbares und schönes WordPress-Theme, das sich für Blogs, persönliche "
"Portfolios, Unternehmenswebsites und WooCommerce-Shops eignet."

#: admin-core/inc/admin-menu.php:1148
msgid "Spectra One"
msgstr "Spectra One"

#: admin-core/inc/admin-menu.php:1149
msgid ""
"Spectra One is a beautiful and modern WordPress theme built with the Full Site Editing (FSE) feature. It's a versatile "
"theme that can be used for blogs, portfolios, businesses, and more."
msgstr ""
"Spectra One ist ein schönes und modernes WordPress-Theme, das mit der Full Site Editing (FSE) Funktion erstellt wurde. "
"Es ist ein vielseitiges Theme, das für Blogs, Portfolios, Unternehmen und mehr verwendet werden kann."

#: admin-core/inc/flow-meta.php:54
msgid "Instant Layout "
msgstr "Sofortiges Layout"

#: admin-core/inc/flow-meta.php:73
msgid "Logo"
msgstr "Logo"

#: admin-core/inc/flow-meta.php:108
msgid "Width (In px)"
msgstr "Breite (in px)"

#: admin-core/inc/flow-meta.php:125
msgid "Height (In px)"
msgstr "Höhe (in px)"

#: admin-core/inc/flow-meta.php:159
msgid "Global Styling"
msgstr "Globale Gestaltung"

#: admin-core/inc/flow-meta.php:164
msgid "Enable Global Styling"
msgstr "Globales Styling aktivieren"

#: admin-core/inc/flow-meta.php:172
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:211
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:119
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:119
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:291
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:297
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:168
#: modules/optin/classes/class-cartflows-optin-meta-data.php:277
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:125
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Primary Color"
msgstr "Primärfarbe"

#: admin-core/inc/flow-meta.php:187
msgid "Secondary Color"
msgstr "Sekundärfarbe"

#: admin-core/inc/flow-meta.php:202
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:219
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:247
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:441
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:674
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:861
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:397
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:222
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:250
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:325
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:390
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:456
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:522
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1052
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:172
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:251
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:303
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:329
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:592
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:690
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:763
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:361
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:350
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:410
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:334
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:383
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:413
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:478
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:541
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:572
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:633
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:663
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:722
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:752
#: modules/optin/classes/class-cartflows-optin-meta-data.php:500
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Color"
msgstr "Textfarbe"

#: admin-core/inc/flow-meta.php:217
msgid "Heading/Accent Color"
msgstr "Überschrift/Akzentfarbe"

#: admin-core/inc/flow-meta.php:233
msgid "General "
msgstr "Allgemein"

#: admin-core/inc/flow-meta.php:239
#: admin-core/inc/global-settings.php:149
msgid "Funnel Slug"
msgstr "Trichter-Slug"

#: admin-core/inc/flow-meta.php:245
msgid "Enable Test Mode"
msgstr "Testmodus aktivieren"

#: admin-core/inc/flow-meta.php:266
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:139
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:148
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:157
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:166
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "Yes"
msgstr "Ja"

#: admin-core/inc/flow-meta.php:270
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:140
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:149
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:158
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:167
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "No"
msgstr "Nein"

#: admin-core/inc/flow-meta.php:277
msgid "Funnel Custom Script"
msgstr "Benutzerdefiniertes Trichter-Skript"

#: admin-core/inc/global-settings.php:45
msgid "No Access"
msgstr "Kein Zugang"

#: admin-core/inc/global-settings.php:49
msgid "Full Access"
msgstr "Voller Zugriff"

#: admin-core/inc/global-settings.php:54
msgid "Limited Access"
msgstr "Eingeschränkter Zugriff"

#: admin-core/inc/global-settings.php:71
msgid "Show Ready Templates for"
msgstr "Bereitvorlagen anzeigen für"

#: admin-core/inc/global-settings.php:73
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"Please choose your preferred page builder from the list so you will only see templates that are made using that page "
"builder. %1$sLearn More >>%2$s"
msgstr ""
"Bitte wählen Sie Ihren bevorzugten Page Builder aus der Liste aus, damit Sie nur Vorlagen sehen, die mit diesem Page "
"Builder erstellt wurden. %1$sErfahren Sie mehr >>%2$s"

#: admin-core/inc/global-settings.php:77
msgid "Block Editor"
msgstr "Block-Editor"

#: admin-core/inc/global-settings.php:82
msgid "Elementor"
msgstr "Elementor"

#: admin-core/inc/global-settings.php:87
msgid "Bricks"
msgstr "Ziegel"

#: admin-core/inc/global-settings.php:92
msgid "Beaver"
msgstr "Biber"

#: admin-core/inc/global-settings.php:97
msgid "Other"
msgstr "Andere"

#: admin-core/inc/global-settings.php:110
msgid "Override Store Checkout"
msgstr "Store-Checkout überschreiben"

#: admin-core/inc/global-settings.php:112
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the Store Checkout settings please %1$sClick here%2$s."
msgstr "Für weitere Informationen zu den Store Checkout-Einstellungen bitte %1$sHier klicken%2$s."

#: admin-core/inc/global-settings.php:120
msgid "Disallow search engine from indexing funnels."
msgstr "Suchmaschinen daran hindern, Funnels zu indexieren."

#: admin-core/inc/global-settings.php:122
msgid "Prevent search engines from including funnels in their search results."
msgstr "Verhindern Sie, dass Suchmaschinen Trichter in ihre Suchergebnisse aufnehmen."

#: admin-core/inc/global-settings.php:139
msgid "Default Permalinks"
msgstr "Standard-Permalinks"

#: admin-core/inc/global-settings.php:140
msgid "Default WordPress Permalink"
msgstr "Standard-WordPress-Permalink"

#: admin-core/inc/global-settings.php:144
msgid "Funnel and Step Slug"
msgstr "Trichter und Schritt-Slug"

#: admin-core/inc/global-settings.php:154
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1019
#: modules/landing/classes/class-cartflows-landing-meta-data.php:113
#: modules/optin/classes/class-cartflows-optin-meta-data.php:568
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:396
msgid "Step Slug"
msgstr "Schritt Slug"

#: admin-core/inc/global-settings.php:164
msgid "Post Type Permalink Base"
msgstr "Permalink-Basis des Beitragstyps"

#: admin-core/inc/global-settings.php:168
msgid "Step Base"
msgstr "Schrittbasis"

#: admin-core/inc/global-settings.php:174
msgid "Funnel Base"
msgstr "Trichterbasis"

#: admin-core/inc/global-settings.php:185
#: admin-core/inc/global-settings.php:376
#: admin-core/inc/global-settings.php:582
#: admin-core/inc/global-settings.php:956
msgid "Enable For CartFlows Pages"
msgstr "Für CartFlows-Seiten aktivieren"

#: admin-core/inc/global-settings.php:203
#: admin-core/inc/global-settings.php:394
#: admin-core/inc/global-settings.php:600
#: admin-core/inc/global-settings.php:788
#: admin-core/inc/global-settings.php:974
#: admin-core/inc/global-settings.php:1180
msgid "Enable for the whole site"
msgstr "Für die gesamte Website aktivieren"

#: admin-core/inc/global-settings.php:205
msgid "If checked, page view and view content event will also be triggered for other pages/posts of site."
msgstr ""
"Wenn aktiviert, wird das Seitenansichts- und Inhaltsansichtsereignis auch für andere Seiten/Beiträge der Website "
"ausgelöst."

#: admin-core/inc/global-settings.php:231
msgid "Enter Facebook pixel ID"
msgstr "Facebook-Pixel-ID eingeben"

#: admin-core/inc/global-settings.php:258
msgid "Facebook Pixel Events"
msgstr "Facebook-Pixel-Ereignisse"

#: admin-core/inc/global-settings.php:272
#: admin-core/inc/global-settings.php:701
#: admin-core/inc/global-settings.php:888
#: admin-core/inc/global-settings.php:1093
msgid "View Content"
msgstr "Inhalt anzeigen"

#: admin-core/inc/global-settings.php:288
msgid "Initiate Checkout"
msgstr "Checkout starten"

#: admin-core/inc/global-settings.php:304
#: admin-core/inc/global-settings.php:495
#: admin-core/inc/global-settings.php:716
#: admin-core/inc/global-settings.php:1108
#: admin-core/inc/global-settings.php:1309
msgid "Add Payment Info"
msgstr "Zahlungsinformationen hinzufügen"

#: admin-core/inc/global-settings.php:321
msgid "Purchase Complete"
msgstr "Kauf abgeschlossen"

#: admin-core/inc/global-settings.php:337
#: admin-core/inc/global-settings.php:527
#: admin-core/inc/global-settings.php:748
#: admin-core/inc/global-settings.php:934
#: admin-core/inc/global-settings.php:1140
#: admin-core/inc/global-settings.php:1357
msgid "Optin Lead"
msgstr "Optin Lead"

#: admin-core/inc/global-settings.php:358
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Facebook Pixel not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Funktioniert das Facebook-Pixel nicht richtig? %1$1s Klicken Sie hier %2$2s, um mehr zu erfahren."

#: admin-core/inc/global-settings.php:396
msgid "If checked, page view event will also be triggered for other pages/posts of site."
msgstr "Wenn aktiviert, wird das Seitenansichtsereignis auch für andere Seiten/Beiträge der Website ausgelöst."

#: admin-core/inc/global-settings.php:422
msgid "Enter Google Analytics ID"
msgstr "Google Analytics-ID eingeben"

#: admin-core/inc/global-settings.php:426
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s google analytics account %2$2s to find your ID. e.g. G-XXXXX or UA-XXXXX-X"
msgstr "Melden Sie sich in Ihrem %1$1s Google Analytics-Konto %2$2s an, um Ihre ID zu finden. z.B. G-XXXXX oder UA-XXXXX-X"

#: admin-core/inc/global-settings.php:451
msgid "Google Analytics Events"
msgstr "Google Analytics-Ereignisse"

#: admin-core/inc/global-settings.php:464
#: admin-core/inc/global-settings.php:670
#: admin-core/inc/global-settings.php:858
#: admin-core/inc/global-settings.php:1062
#: admin-core/inc/global-settings.php:1279
msgid "Begin Checkout"
msgstr "Zur Kasse gehen"

#: admin-core/inc/global-settings.php:480
#: admin-core/inc/global-settings.php:686
#: admin-core/inc/global-settings.php:873
#: admin-core/inc/global-settings.php:1078
#: admin-core/inc/global-settings.php:1294
msgid "Add To Cart"
msgstr "In den Warenkorb"

#: admin-core/inc/global-settings.php:511
#: admin-core/inc/global-settings.php:732
#: admin-core/inc/global-settings.php:903
#: admin-core/inc/global-settings.php:1124
#: admin-core/inc/global-settings.php:1325
msgid "Purchase"
msgstr "Kaufen"

#: admin-core/inc/global-settings.php:548
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Google Analytics not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Funktioniert Google Analytics nicht richtig? %1$1s Klicken Sie hier %2$2s, um mehr zu erfahren."

#: admin-core/inc/global-settings.php:566
msgid "Enter Google Map API key"
msgstr "Google Maps API-Schlüssel eingeben"

#: admin-core/inc/global-settings.php:573
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Check this %1$1s article %2$2s to setup and find an API key."
msgstr "Überprüfen Sie diesen %1$1s Artikel %2$2s, um einen API-Schlüssel einzurichten und zu finden."

#: admin-core/inc/global-settings.php:602
#: admin-core/inc/global-settings.php:790
#: admin-core/inc/global-settings.php:976
msgid "If checked, PageView event will also be triggered for other pages/posts of site."
msgstr "Wenn aktiviert, wird das PageView-Ereignis auch für andere Seiten/Beiträge der Website ausgelöst."

#: admin-core/inc/global-settings.php:628
msgid "Enter TikTok ID"
msgstr "Geben Sie die TikTok-ID ein"

#: admin-core/inc/global-settings.php:632
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s TikTok business account %2$2s to find your ID."
msgstr "Melden Sie sich in Ihrem %1$1s TikTok-Geschäftskonto %2$2s an, um Ihre ID zu finden."

#: admin-core/inc/global-settings.php:657
msgid "TikTok Events"
msgstr "TikTok-Veranstaltungen"

#: admin-core/inc/global-settings.php:760
#: admin-core/inc/global-settings.php:946
#: admin-core/inc/global-settings.php:1152
#: admin-core/inc/global-settings.php:1369
msgid "Optin Lead event will be triggered for optin page."
msgstr "Das Optin-Lead-Ereignis wird für die Optin-Seite ausgelöst."

#: admin-core/inc/global-settings.php:770
#: admin-core/inc/global-settings.php:1162
msgid "Enable for CartFlows pages"
msgstr "Für CartFlows-Seiten aktivieren"

#: admin-core/inc/global-settings.php:816
msgid "Enter Snapchat pixel ID"
msgstr "Geben Sie die Snapchat-Pixel-ID ein"

#: admin-core/inc/global-settings.php:820
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Snapchat business account %2$2s to find your ID."
msgstr "Melden Sie sich in Ihrem %1$1s Snapchat-Geschäftskonto %2$2s an, um Ihre ID zu finden."

#: admin-core/inc/global-settings.php:845
msgid "Snapchat Events"
msgstr "Snapchat-Veranstaltungen"

#: admin-core/inc/global-settings.php:918
#: wizard/assets/build/wizard-app.js:1
msgid "Subscribe"
msgstr "Abonnieren"

#: admin-core/inc/global-settings.php:1002
msgid "Enter Google Ads Conversion ID"
msgstr "Geben Sie die Google Ads Conversion-ID ein"

#: admin-core/inc/global-settings.php:1006
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion ID."
msgstr "Melden Sie sich in Ihrem %1$1s Google Ads-Konto %2$2s an, um Ihre Conversion-ID zu finden."

#: admin-core/inc/global-settings.php:1019
msgid "Enter Google Ads Conversion Label"
msgstr "Geben Sie das Google Ads Conversion-Label ein"

#: admin-core/inc/global-settings.php:1023
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion label."
msgstr "Melden Sie sich in Ihrem %1$1s Google Ads-Konto %2$2s an, um Ihr Conversion-Label zu finden."

#: admin-core/inc/global-settings.php:1049
msgid "Google Ads Events"
msgstr "Google Ads-Ereignisse"

#: admin-core/inc/global-settings.php:1182
msgid "If checked, PageVisit event will also be triggered for other pages/posts of site."
msgstr "Wenn aktiviert, wird das PageVisit-Ereignis auch für andere Seiten/Beiträge der Website ausgelöst."

#: admin-core/inc/global-settings.php:1208
msgid "Enter Pinterest Tag ID"
msgstr "Geben Sie die Pinterest-Tag-ID ein"

#: admin-core/inc/global-settings.php:1212
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Pinterest business account %2$2s to find your ID."
msgstr "Melden Sie sich in Ihrem %1$1s Pinterest-Geschäftskonto %2$2s an, um Ihre ID zu finden."

#: admin-core/inc/global-settings.php:1237
msgid "Enable Pinterest tag tracking consent notice"
msgstr "Aktivieren Sie die Einwilligungsbenachrichtigung für das Pinterest-Tag-Tracking"

#: admin-core/inc/global-settings.php:1250
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"This setting enables a consent notice for Pinterest Tag tracking on your website. For more information check "
"%1$1sPinterest documentation%2$2s."
msgstr ""
"Diese Einstellung aktiviert einen Zustimmungshinweis für das Pinterest-Tag-Tracking auf Ihrer Website. Für weitere "
"Informationen lesen Sie die %1$1sPinterest-Dokumentation%2$2s."

#: admin-core/inc/global-settings.php:1266
msgid "Pinterest Events"
msgstr "Pinterest-Veranstaltungen"

#: admin-core/inc/global-settings.php:1340
msgid "Signup"
msgstr "Registrieren"

#: admin-core/inc/global-settings.php:1352
msgid "Signup event will be triggered for optin page."
msgstr "Das Anmeldeereignis wird für die Opt-in-Seite ausgelöst."

#: admin-core/inc/global-settings.php:1382
msgid "Store Revenue Report Emails"
msgstr "E-Mails zum Umsatzbericht des Geschäfts"

#: admin-core/inc/global-settings.php:1387
msgid "Enable Store Report Email."
msgstr "Store-Bericht-E-Mail aktivieren."

#: admin-core/inc/global-settings.php:1390
#. translators: %1$1s: link html start, %2$12: link html end
msgid "If enabled, you will receive the weekly report emails of your store for the revenue stats generated by CartFlows."
msgstr ""
"Wenn aktiviert, erhalten Sie die wöchentlichen Bericht-E-Mails Ihres Geschäfts für die Umsatzstatistiken, die von "
"CartFlows generiert werden."

#: admin-core/inc/global-settings.php:1397
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:577
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:579
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:174
msgid "Email Address"
msgstr "E-Mail-Adresse"

#: admin-core/inc/global-settings.php:1398
msgid "Email address to receive the weekly sales report emails. For multiple emails, add each email address per line."
msgstr ""
"E-Mail-Adresse zum Empfang der wöchentlichen Verkaufsberichte. Für mehrere E-Mails geben Sie jede E-Mail-Adresse in "
"einer neuen Zeile an."

#: admin-core/inc/global-settings.php:1425
msgid "Delete plugin data on plugin deletion"
msgstr "Plugindaten bei Plugin-Löschung löschen"

#: admin-core/inc/global-settings.php:1430
msgid "Are you sure? Do you want to delete plugin data while deleting the plugin? Type \"DELETE\" to confirm!"
msgstr ""
"Sind Sie sicher? Möchten Sie die Plugin-Daten löschen, während Sie das Plugin löschen? Geben Sie \"DELETE\" ein, um zu "
"bestätigen!"

#: admin-core/inc/global-settings.php:1433
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"This option will delete all the CartFlows options data on plugin deletion. If you enable this and deletes the plugin, "
"you can't restore your saved data. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Diese Option löscht alle CartFlows-Optionsdaten bei der Plugin-Löschung. Wenn Sie dies aktivieren und das Plugin "
"löschen, können Sie Ihre gespeicherten Daten nicht wiederherstellen. Um mehr zu erfahren, %1$1s klicken Sie hier %2$2s."

#: admin-core/inc/log-status.php:108
msgid "Log deleted successfully!"
msgstr "Protokoll erfolgreich gelöscht!"

#: admin-core/inc/log-status.php:171
#: admin-core/inc/log-status.php:195
msgid "Nonce verification failed. Please refresh the page and retry."
msgstr "Die Überprüfung des Nonce ist fehlgeschlagen. Bitte aktualisieren Sie die Seite und versuchen Sie es erneut."

#: admin-core/inc/store-checkout.php:63
msgid "Checkout (Store)"
msgstr "Kasse (Geschäft)"

#: admin-core/inc/store-checkout.php:67
msgid "Thank You (Store)"
msgstr "Danke (Geschäft)"

#: admin-core/views/404-error.php:36
msgid "404 ERROR"
msgstr "404 FEHLER"

#: admin-core/views/404-error.php:37
msgid "Page Not Found."
msgstr "Seite nicht gefunden."

#: admin-core/views/404-error.php:38
msgid "Sorry, we couldn’t find the page you’re looking for."
msgstr "Entschuldigung, wir konnten die Seite, die Sie suchen, nicht finden."

#: admin-core/views/404-error.php:39
msgid "Go back home"
msgstr "Geh nach Hause zurück"

#: admin-core/views/header.php:22
msgid "Generate More Leads & More Sales"
msgstr "Generieren Sie mehr Leads und mehr Verkäufe"

#: classes/class-cartflows-admin-notices.php:88
#. translators: %1$s Software Title, %2$s Plugin, %3$s Anchor opening tag, %4$s Anchor closing tag, %5$s Software Title.
msgid ""
"%1$sCartFlows:%2$s We just introduced an awesome new feature, weekly store revenue reports via email. Now you can see "
"how many revenue we are generating for your store each week, without having to log into your website. You can set the "
"email address for these email from %3$shere.%4$s"
msgstr ""
"%1$sCartFlows:%2$s Wir haben gerade eine großartige neue Funktion eingeführt: wöchentliche Umsatzberichte per E-Mail. "
"Jetzt können Sie sehen, wie viel Umsatz wir jede Woche für Ihren Shop generieren, ohne sich auf Ihrer Website anmelden "
"zu müssen. Sie können die E-Mail-Adresse für diese E-Mails %3$shier%4$s festlegen."

#: classes/class-cartflows-admin-notices.php:218
msgid "How likely are you to recommend #pluginname to your friends or colleagues?"
msgstr "Wie wahrscheinlich ist es, dass Sie #pluginname Ihren Freunden oder Kollegen empfehlen?"

#: classes/class-cartflows-admin-notices.php:221
msgid ""
"Could you please do us a favor and give us a 5-star rating on WordPress? It would help others choose CartFlows with "
"confidence. Thank you!"
msgstr ""
"Könnten Sie uns bitte einen Gefallen tun und uns eine 5-Sterne-Bewertung auf WordPress geben? Es würde anderen helfen, "
"CartFlows mit Vertrauen zu wählen. Vielen Dank!"

#: classes/class-cartflows-admin-notices.php:225
msgid "Thank you for your feedback"
msgstr "Danke für Ihr Feedback"

#: classes/class-cartflows-admin-notices.php:226
msgid "We value your input. How can we improve your experience?"
msgstr "Wir schätzen Ihr Feedback. Wie können wir Ihre Erfahrung verbessern?"

#: classes/class-cartflows-admin-notices.php:249
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Heads up! The Gutenberg plugin is not recommended on production sites as it may contain non-final features that cause "
"compatibility issues with CartFlows and other plugins. %1$s Please deactivate the Gutenberg plugin %2$s to ensure the "
"proper functioning of your website."
msgstr ""
"Achtung! Das Gutenberg-Plugin wird auf Produktionsseiten nicht empfohlen, da es nicht finale Funktionen enthalten kann, "
"die Kompatibilitätsprobleme mit CartFlows und anderen Plugins verursachen. %1$s Bitte deaktivieren Sie das "
"Gutenberg-Plugin %2$s, um das ordnungsgemäße Funktionieren Ihrer Website sicherzustellen."

#: classes/class-cartflows-admin.php:122
#: wizard/views/wizard-base.php:19
msgid "CartFlows Setup"
msgstr "CartFlows Einrichtung"

#: classes/class-cartflows-admin.php:167
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "Step"
msgstr "Schritt"

#: classes/class-cartflows-admin.php:167
msgid "of"
msgstr "von"

#: classes/class-cartflows-admin.php:173
msgid "You're almost there! Once you complete CartFlows setup you can start receiving orders from flows."
msgstr ""
"Sie sind fast am Ziel! Sobald Sie die CartFlows-Einrichtung abgeschlossen haben, können Sie Bestellungen aus Flows "
"erhalten."

#: classes/class-cartflows-admin.php:175
#: admin-core/assets/build/settings-app.js:34
msgid "Complete Setup"
msgstr "Einrichtung abschließen"

#: classes/class-cartflows-admin.php:233
msgid "Docs"
msgstr "Dokumente"

#: classes/class-cartflows-admin.php:246
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:162
#: modules/landing/classes/class-cartflows-landing-meta-data.php:57
#: modules/optin/classes/class-cartflows-optin-meta-data.php:188
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:58
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Settings"
msgstr "Einstellungen"

#: classes/class-cartflows-admin.php:362
msgid "You do not have permission to access this page."
msgstr "Sie haben keine Berechtigung, auf diese Seite zuzugreifen."

#: classes/class-cartflows-admin.php:363
#: classes/class-cartflows-admin.php:394
#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:11
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback to Previous Version"
msgstr "Zurück zur vorherigen Version"

#: classes/class-cartflows-admin.php:376
msgid "Error occurred, The version selected is invalid. Try selecting different version."
msgstr "Ein Fehler ist aufgetreten, die ausgewählte Version ist ungültig. Versuchen Sie, eine andere Version auszuwählen."

#: classes/class-cartflows-default-meta.php:163
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1880
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1889
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1223
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:501
#: modules/checkout/templates/checkout/shipping-methods.php:69
msgid ""
"There are no shipping options available. Please ensure that your address has been entered correctly, or contact us if "
"you need any help."
msgstr ""
"Es sind keine Versandoptionen verfügbar. Bitte stellen Sie sicher, dass Ihre Adresse korrekt eingegeben wurde, oder "
"kontaktieren Sie uns, wenn Sie Hilfe benötigen."

#: classes/class-cartflows-default-meta.php:176
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1345
msgid "Place Order"
msgstr "Bestellung aufgeben"

#: classes/class-cartflows-default-meta.php:367
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1320
msgid "is required"
msgstr "wird benötigt"

#: classes/class-cartflows-default-meta.php:629
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:126
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:143
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:174
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:484
#: modules/thankyou/templates/instant-thankyou.php:76
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank you. Your order has been received."
msgstr "Vielen Dank. Ihre Bestellung wurde erhalten."

#: classes/class-cartflows-default-meta.php:820
#: modules/optin/classes/class-cartflows-optin-meta-data.php:593
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit"
msgstr "Einreichen"

#: classes/class-cartflows-flow-frontend.php:284
msgid "Edit Design"
msgstr "Design bearbeiten"

#: classes/class-cartflows-functions.php:595
#. translators: %1$s page builder name "string"
msgid ""
"We have introduced %1$1s widgets for CartFlows shortcodes. Now, you can add/change/update design settings directly from "
"the page builder as well."
msgstr ""
"Wir haben %1$1s Widgets für CartFlows-Shortcodes eingeführt. Jetzt können Sie Design-Einstellungen auch direkt im Page "
"Builder hinzufügen/ändern/aktualisieren."

#: classes/class-cartflows-functions.php:596
msgid "Learn More »"
msgstr "Mehr erfahren »"

#: classes/class-cartflows-helper.php:568
msgid "First name"
msgstr "Vorname"

#: classes/class-cartflows-helper.php:577
msgid "Last name"
msgstr "Nachname"

#: classes/class-cartflows-helper.php:586
#: wizard/assets/build/wizard-app.js:3
msgid "Email address"
msgstr "E-Mail-Adresse"

#: classes/class-cartflows-helper.php:1370
msgid "Enable Field"
msgstr "Feld aktivieren"

#: classes/class-cartflows-helper.php:1376
msgid "Field Width"
msgstr "Feldbreite"

#: classes/class-cartflows-helper.php:1382
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "33%"
msgstr "33%"

#: classes/class-cartflows-helper.php:1386
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "50%"
msgstr "50%"

#: classes/class-cartflows-helper.php:1390
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "100%"
msgstr "100%"

#: classes/class-cartflows-helper.php:1397
msgid "Field Label"
msgstr "Feldbezeichnung"

#: classes/class-cartflows-helper.php:1402
#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
msgid "Field ID"
msgstr "Feld-ID"

#: classes/class-cartflows-helper.php:1406
msgid "Copy this field id to use in Order Custom Field rule of dynamic offers."
msgstr "Kopieren Sie diese Feld-ID, um sie in der Regel für benutzerdefinierte Bestellfelder dynamischer Angebote zu verwenden."

#: classes/class-cartflows-helper.php:1418
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Checked"
msgstr "Geprüft"

#: classes/class-cartflows-helper.php:1422
msgid "Un-Checked"
msgstr "Deaktiviert"

#: classes/class-cartflows-helper.php:1439
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:411
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Options"
msgstr "Optionen"

#: classes/class-cartflows-helper.php:1463
msgid "Min Date"
msgstr "Min. Datum"

#: classes/class-cartflows-helper.php:1470
msgid "Max Date"
msgstr "Max-Datum"

#: classes/class-cartflows-helper.php:1482
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Placeholder"
msgstr "Platzhalter"

#: classes/class-cartflows-helper.php:1491
msgid "Min Number"
msgstr "Mindestanzahl"

#: classes/class-cartflows-helper.php:1497
msgid "Max Number"
msgstr "Maximale Anzahl"

#: classes/class-cartflows-helper.php:1506
msgid "Show In Email"
msgstr "In E-Mail anzeigen"

#: classes/class-cartflows-helper.php:1513
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Required"
msgstr "Erforderlich"

#: classes/class-cartflows-helper.php:1521
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Collapsible"
msgstr "Zusammenklappbar"

#: classes/class-cartflows-helper.php:1571
msgid "CartFlows Primary Color"
msgstr "CartFlows Primärfarbe"

#: classes/class-cartflows-helper.php:1572
msgid "CartFlows Secondary Color"
msgstr "CartFlows Sekundärfarbe"

#: classes/class-cartflows-helper.php:1573
msgid "CartFlows Text Color"
msgstr "CartFlows Textfarbe"

#: classes/class-cartflows-helper.php:1574
msgid "CartFlows Heading/Accent Color"
msgstr "CartFlows Überschrift/Akzentfarbe"

#: classes/class-cartflows-loader.php:292
#. translators: %s: html tags
msgid ""
"The new version of  %1$s%3$s%2$s is released. Please download the latest zip to install the new updates. Click here to "
"%4$sdownload%5$s."
msgstr ""
"Die neue Version von %1$s%3$s%2$s ist veröffentlicht. Bitte laden Sie die neueste Zip-Datei herunter, um die neuen "
"Updates zu installieren. Klicken Sie hier, um %4$sherunterzuladen%5$s."

#: classes/class-cartflows-loader.php:309
#. translators: %s: html tags
msgid "You are using an older version of %1$s%3$s%2$s. Please update %1$s%3$s%2$s plugin to version %1$s%4$s%2$s or higher."
msgstr ""
"Sie verwenden eine ältere Version von %1$s%3$s%2$s. Bitte aktualisieren Sie das %1$s%3$s%2$s-Plugin auf Version "
"%1$s%4$s%2$s oder höher."

#: classes/class-cartflows-loader.php:612
#. translators: %s: html tags
msgid "This %1$sCartFlows%2$s page requires %1$sWooCommerce%2$s plugin installed & activated."
msgstr "Diese %1$sCartFlows%2$s-Seite erfordert das %1$sWooCommerce%2$s-Plugin, das installiert und aktiviert ist."

#: classes/class-cartflows-loader.php:622
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
msgid "Activate WooCommerce"
msgstr "WooCommerce aktivieren"

#: classes/class-cartflows-loader.php:629
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Install WooCommerce"
msgstr "WooCommerce installieren"

#: classes/class-cartflows-rollback.php:167
msgid "CartFlows <p>Rollback to Previous Version</p>"
msgstr "CartFlows <p>Auf vorherige Version zurücksetzen</p>"

#: classes/class-cartflows-tracking.php:1396
msgid "We use Pinterest tags to improve your experience. Do you consent to our use of Pinterest tags?"
msgstr "Wir verwenden Pinterest-Tags, um Ihre Erfahrung zu verbessern. Stimmen Sie der Verwendung von Pinterest-Tags zu?"

#: classes/class-cartflows-tracking.php:1397
msgid "Accept"
msgstr "Akzeptieren"

#: classes/class-cartflows-tracking.php:1398
msgid "Decline"
msgstr "Ablehnen"

#: classes/class-cartflows-tracking.php:1403
msgid "Pinterest Consent"
msgstr "Pinterest-Zustimmung"

#: classes/importer/batch-process/class-cartflows-batch-process.php:482
msgid ""
"ERROR! Cron schedules are disabled by setting constant DISABLE_WP_CRON to true.<br/>To start the import process please "
"enable the cron by setting the constant to false. E.g. define( 'DISABLE_WP_CRON', false );"
msgstr ""
"FEHLER! Cron-Zeitpläne sind deaktiviert, indem die Konstante DISABLE_WP_CRON auf true gesetzt wird.<br/>Um den "
"Importprozess zu starten, aktivieren Sie bitte den Cron, indem Sie die Konstante auf false setzen. Z.B. define( "
"'DISABLE_WP_CRON', false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:486
msgid ""
"ERROR! Cron schedules are disabled by setting constant ALTERNATE_WP_CRON to true.<br/>To start the import process "
"please enable the cron by setting the constant to false. E.g. define( 'ALTERNATE_WP_CRON', false );"
msgstr ""
"FEHLER! Cron-Zeitpläne sind deaktiviert, da die Konstante ALTERNATE_WP_CRON auf true gesetzt ist.<br/>Um den "
"Importprozess zu starten, aktivieren Sie bitte den Cron, indem Sie die Konstante auf false setzen. Z.B. define( "
"'ALTERNATE_WP_CRON', false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:522
#. translators: 1: The HTTP response code.
msgid "Unexpected HTTP response code: %s"
msgstr "Unerwarteter HTTP-Antwortcode: %s"

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:46
msgid "(✕) Empty content."
msgstr "(✕) Inhalt leer."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:51
msgid "(✕) Invalid content."
msgstr "(✕) Ungültiger Inhalt."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:62
msgid "Invalid content. Expected an array."
msgstr "Ungültiger Inhalt. Ein Array wurde erwartet."

#: classes/importer/batch-process/helpers/class-wp-background-process-cartflows-sync-library.php:69
msgid "All processes are complete"
msgstr "Alle Prozesse sind abgeschlossen"

#: classes/importer/batch-process/helpers/class-wp-background-process.php:440
#. Translators: %d: interval
msgid "Every %d Minutes"
msgstr "Alle %d Minuten"

#: classes/importer/class-cartflows-api.php:428
msgid "Request successfully processed!"
msgstr "Anfrage erfolgreich bearbeitet!"

#: classes/logger/class-cartflows-log-handler-file.php:355
#: classes/logger/class-cartflows-log-handler-file.php:375
msgid "This method should not be called before plugins_loaded."
msgstr "Diese Methode sollte nicht vor plugins_loaded aufgerufen werden."

#: classes/logger/class-cartflows-wc-logger.php:58
#. translators: 1: class name 2: Cartflows_Log_Handler_Interface
msgid "The provided handler %1$s does not implement %2$s."
msgstr "Der bereitgestellte Handler %1$s implementiert nicht %2$s."

#: classes/logger/class-cartflows-wc-logger.php:136
#. translators: 1: Cartflows_WC_Logger::log 2: level
msgid "%1$s was called with an invalid level \"%2$s\"."
msgstr "%1$s wurde mit einem ungültigen Level \"%2$s\" aufgerufen."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:85
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:354
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:546
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:220
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:249
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:202
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:348
#: modules/gutenberg/dist/blocks.build.js:1
msgid "None"
msgstr "Keine"

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:112
#. translators: 1: anchor start, 2: anchor close
msgid ""
"Non-enrolled students will redirect to the selected CartFlows template. If you have not created any Flow already, add "
"new Flow from %1$shere%2$s."
msgstr ""
"Nicht eingeschriebene Studierende werden zur ausgewählten CartFlows-Vorlage weitergeleitet. Wenn Sie noch keinen Flow "
"erstellt haben, fügen Sie einen neuen Flow von %1$shier%2$s hinzu."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:118
msgid "Select CartFlows Template for this Course"
msgstr "Wählen Sie die CartFlows-Vorlage für diesen Kurs aus"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:44
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:150
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:373
#: modules/gutenberg/build/blocks-placeholder.js:9
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Checkout Form"
msgstr "Kassenformular"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:34
msgid "Checkout Form."
msgstr "Kassenformular."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:35
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:36
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:34
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:36
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:34
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:35
msgid "Cartflows Modules"
msgstr "Cartflows-Module"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:137
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:146
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:59
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:68
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:251
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:198
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:207
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Checkout"
msgstr "Moderner Checkout"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:138
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:60
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:69
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:255
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:199
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:208
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern One Column"
msgstr "Moderne Einspaltig"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:139
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:149
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:61
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:70
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:263
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:200
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:209
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "One Column"
msgstr "Eine Spalte"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:140
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:150
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:62
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:71
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:267
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:210
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Column"
msgstr "Zwei Spalten"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:141
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:64
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:203
msgid "MultiStep Checkout ( PRO )"
msgstr "MultiStep Checkout ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:142
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:63
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:202
msgid "Two Step ( PRO )"
msgstr "Zwei Schritte ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:148
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:73
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:212
#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout"
msgstr "Mehrstufiger Checkout"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:151
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:271
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:211
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step"
msgstr "Zwei Schritte"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:168
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1212
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:446
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:229
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Labels"
msgstr "Moderne Etiketten"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:132
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:109
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:68
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:82
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1012
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:157
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:294
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:160
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:152
#: modules/landing/classes/class-cartflows-landing-meta-data.php:106
#: modules/optin/classes/class-cartflows-optin-meta-data.php:561
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:389
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "General"
msgstr "Allgemein"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:190
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:253
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Layout"
msgstr "Layout auswählen"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:192
#. translators: %s: link
msgid "The PRO layout options are available in the CartFlows Pro. %1$s  Upgrade Now! %2$s"
msgstr "Die PRO-Layout-Optionen sind in CartFlows Pro verfügbar. %1$s  Jetzt upgraden! %2$s"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:204
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:277
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:186
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:112
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:141
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:390
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:164
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:414
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:207
#: modules/optin/classes/class-cartflows-optin-meta-data.php:298
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Style"
msgstr "Stil"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:207
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:115
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1226
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:70
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:289
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Global"
msgstr "Global"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:233
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:262
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:341
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:529
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:458
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:473
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:127
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:189
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:334
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:236
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:264
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:310
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:346
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:399
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:689
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:228
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:347
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Typography"
msgstr "Typografie"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:243
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:218
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1234
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:78
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:362
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:301
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:523
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:616
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:705
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:168
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading"
msgstr "Überschrift"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:273
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:137
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1230
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:421
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:406
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:199
#: modules/optin/classes/class-cartflows-optin-meta-data.php:292
msgid "Input Fields"
msgstr "Eingabefelder"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:287
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:460
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:913
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1114
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:443
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:878
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:226
#: modules/optin/classes/class-cartflows-optin-meta-data.php:375
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label Color"
msgstr "Etikettenfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:301
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:161
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:477
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:188
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:572
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:454
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:237
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Background Color"
msgstr "Feldhintergrundfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:320
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:503
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:465
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:248
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Text / Placeholder Color"
msgstr "Eingabetext / Platzhalterfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:350
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:542
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:198
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:344
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:533
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:476
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:259
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Style"
msgstr "Rahmenstil"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:352
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:544
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:200
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:346
msgid "The type of border to use. Double borders must have a width of at least 3px to render properly."
msgstr ""
"Die Art des zu verwendenden Rahmens. Doppelte Rahmen müssen eine Breite von mindestens 3px haben, um korrekt "
"dargestellt zu werden."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:355
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:547
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:203
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:349
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:539
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:482
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:265
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Solid"
msgstr "Fest"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:356
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:548
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:204
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:350
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:542
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:485
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:268
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dashed"
msgstr "Gestrichelt"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:357
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:549
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:205
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:351
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:541
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:484
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:267
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dotted"
msgstr "Gepunktet"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:358
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:550
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:206
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:352
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:540
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:483
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:266
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Double"
msgstr "Doppelt"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:377
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:582
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:230
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:377
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:575
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:500
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:278
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width"
msgstr "Randbreite"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:398
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:605
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:840
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:245
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:393
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:610
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1024
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:517
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:940
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:290
#: modules/optin/classes/class-cartflows-optin-meta-data.php:396
#: modules/optin/classes/class-cartflows-optin-meta-data.php:528
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Color"
msgstr "Randfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:416
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:646
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:752
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:258
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:421
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius"
msgstr "Randradius"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:437
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:274
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:556
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:287
#: modules/gutenberg/build/blocks.js:11
msgid "Buttons"
msgstr "Knöpfe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:465
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:411
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:292
#: modules/optin/classes/class-cartflows-optin-meta-data.php:507
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Hover Color"
msgstr "Text-Schwebefarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:485
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:824
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:874
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:425
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:306
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:293
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:339
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:404
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:470
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:536
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:743
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1002
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1070
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:264
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:329
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:240
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:263
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:360
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:411
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:459
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:505
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:607
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:724
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:927
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:989
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:374
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:363
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:433
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:437
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:585
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:676
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:765
#: modules/optin/classes/class-cartflows-optin-meta-data.php:389
#: modules/optin/classes/class-cartflows-optin-meta-data.php:514
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Color"
msgstr "Hintergrundfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:509
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:433
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:320
#: modules/optin/classes/class-cartflows-optin-meta-data.php:521
#: modules/gutenberg/build/blocks.js:11
msgid "Background Hover Color"
msgstr "Hintergrund-Hover-Farbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:626
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:331
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:407
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:316
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:703
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:456
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:421
#: modules/optin/classes/class-cartflows-optin-meta-data.php:535
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Hover Color"
msgstr "Rand-Schwebefarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:670
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1250
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:756
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Payment Section"
msgstr "Zahlungsbereich"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:688
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1131
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:776
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Description Color"
msgstr "Beschreibung Farbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:702
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:802
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Information Background Color"
msgstr "Hintergrundfarbe der Information"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:710
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1143
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:829
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:789
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:256
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Background Color"
msgstr "Abschnitt Hintergrundfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:724
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1161
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:814
msgid "Section Padding"
msgstr "Abschnittsabstand"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:738
#: modules/gutenberg/build/blocks.js:11
msgid "Margin"
msgstr "Marge"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:768
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1246
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:861
msgid "Field Validation & Error Messages"
msgstr "Feldvalidierung & Fehlermeldungen"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:772
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:602
msgid "Field Label Color"
msgstr "Feldbeschriftungsfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:788
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:936
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:587
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:890
msgid "Field Border Color"
msgstr "Feldrandfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:808
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:980
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:913
msgid "Error Message Color"
msgstr "Fehlermeldungsfarbe"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:857
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1242
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:962
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Review"
msgstr "Bestellüberprüfung"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:32
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:70
#: modules/gutenberg/classes/class-cartflows-block-config.php:54
#: modules/gutenberg/build/blocks-placeholder.js:10
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button"
msgstr "Schaltfläche Nächster Schritt"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:33
msgid "A simple next step button."
msgstr "Eine einfache Weiter-Schaltfläche."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:139
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:192
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text"
msgstr "Text"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:140
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:61
msgid "Next Step"
msgstr "Nächster Schritt"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:149
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon"
msgstr "Symbol"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:160
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:195
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Position"
msgstr "Symbolposition"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:163
msgid "Before Text"
msgstr "Vor dem Text"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:164
msgid "After Text"
msgstr "Nach dem Text"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:172
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:240
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Spacing"
msgstr "Symbolabstand"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:190
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Type"
msgstr "Typ"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:195
msgid "Flat"
msgstr "Flach"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:196
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Gradient"
msgstr "Gradient"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:197
msgid "Transparent"
msgstr "Transparent"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:198
msgid "3D"
msgstr "3D"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:208
msgid "Border Size"
msgstr "Randgröße"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:217
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:233
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:246
msgid "Hover Styles"
msgstr "Hover-Stile"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:221
msgid "Fade Background"
msgstr "Hintergrund verblassen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:222
msgid "Fill Background From Top"
msgstr "Hintergrund von oben füllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:223
msgid "Fill Background From Bottom"
msgstr "Hintergrund von unten füllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:224
msgid "Fill Background From Left"
msgstr "Hintergrund von links füllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:225
msgid "Fill Background From Right"
msgstr "Hintergrund von rechts füllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:226
msgid "Fill Background Vertical"
msgstr "Hintergrund vertikal füllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:227
msgid "Fill Background Diagonal"
msgstr "Hintergrund diagonal füllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:228
msgid "Fill Background Horizontal"
msgstr "Hintergrund horizontal füllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:236
msgid "Move Down"
msgstr "Nach unten bewegen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:237
msgid "Move Up"
msgstr "Nach oben bewegen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:238
msgid "Move Left"
msgstr "Nach links bewegen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:239
msgid "Move Right"
msgstr "Nach rechts bewegen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:240
msgid "Animate Top"
msgstr "Nach oben animieren"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:241
msgid "Animate Bottom"
msgstr "Unten animieren"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:250
msgid "Appear Icon From Right"
msgstr "Symbol von rechts erscheinen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:251
msgid "Appear Icon From Left"
msgstr "Symbol von links erscheinen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:252
msgid "Appear Icon From Top"
msgstr "Symbol von oben erscheinen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:253
msgid "Appear Icon From Bottom"
msgstr "Symbol von unten erscheinen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:259
msgid "Structure"
msgstr "Struktur"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:263
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Width"
msgstr "Breite"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:267
#: modules/gutenberg/build/blocks.js:11
msgid "Full Width"
msgstr "Volle Breite"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:268
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:509
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:680
#: modules/optin/classes/class-cartflows-optin-meta-data.php:355
#: modules/optin/classes/class-cartflows-optin-meta-data.php:459
msgid "Custom"
msgstr "Benutzerdefiniert"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:284
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:302
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:309
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Alignment"
msgstr "Ausrichtung"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:287
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:297
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:310
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:507
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:490
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center"
msgstr "Zentrum"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:288
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:298
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:306
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:503
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:313
#: modules/optin/classes/class-cartflows-optin-meta-data.php:486
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Left"
msgstr "Links"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:289
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:299
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:314
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:511
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:321
#: modules/optin/classes/class-cartflows-optin-meta-data.php:494
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Right"
msgstr "Richtig"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:294
msgid "Mobile Alignment"
msgstr "Mobile Ausrichtung"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:304
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:340
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Padding"
msgstr "Polsterung"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:318
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:447
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:792
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:215
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:277
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:626
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:395
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:373
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border"
msgstr "Grenze"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:342
msgid "Custom Width"
msgstr "Benutzerdefinierte Breite"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:351
msgid "Custom Height"
msgstr "Benutzerdefinierte Höhe"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:360
msgid "Padding Top/Bottom"
msgstr "Abstand oben/unten"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:369
msgid "Padding Left/Right"
msgstr "Links/Rechts auffüllen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:378
msgid "Round Corners"
msgstr "Ecken abrunden"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:393
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Colors"
msgstr "Farben"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:444
msgid "Apply Hover Color To"
msgstr "Hover-Farbe anwenden auf"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:448
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:322
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
msgid "Background"
msgstr "Hintergrund"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:461
msgid "Button Settings"
msgstr "Schaltflächeneinstellungen"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:465
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:260
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Size"
msgstr "Symbolgröße"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:46
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:578
#: modules/optin/classes/class-cartflows-optin-meta-data.php:181
#: modules/optin/classes/class-cartflows-optin-meta-data.php:260
#: modules/gutenberg/build/blocks-placeholder.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Optin Form"
msgstr "Optin-Formular"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:34
msgid "Optin Form."
msgstr "Optin-Formular."

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:97
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:104
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:144
#: modules/optin/classes/class-cartflows-optin-meta-data.php:309
msgid "Floating Labels"
msgstr "Schwebende Beschriftungen"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:32
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:46
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:65
#: modules/gutenberg/classes/class-cartflows-block-config.php:157
msgid "Order Details Form"
msgstr "Bestellformular"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:33
msgid "Order Details Form."
msgstr "Bestellformular."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:125
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:141
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:172
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank You Text"
msgstr "Danke-Text"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:136
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:321
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:86
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:150
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:458
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Overview"
msgstr "Bestellübersicht"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:145
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:428
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:94
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:157
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:605
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:99
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Details"
msgstr "Bestelldetails"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:154
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:164
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:209
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Billing Address"
msgstr "Rechnungsadresse"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:163
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:171
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:221
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Shipping Address"
msgstr "Lieferadresse"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:178
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:74
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:245
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing"
msgstr "Abstand"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:182
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:253
msgid "Heading Bottom Spacing"
msgstr "Abstand unten bei Überschrift"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:197
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:269
msgid "Spacing Between Sections"
msgstr "Abstand zwischen den Abschnitten"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:375
msgid "Sections Heading"
msgstr "Abschnittsüberschrift"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:274
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:404
msgid "Sections Content"
msgstr "Abschnittsinhalte"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:353
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:418
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:484
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:550
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:398
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:446
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:492
msgid "Text Typography"
msgstr "Texttypografie"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:363
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:90
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:515
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Downloads"
msgstr "Downloads"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:367
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:432
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:498
#: modules/gutenberg/build/blocks.js:11
msgid "Heading Color"
msgstr "Überschriftfarbe"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:381
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:447
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:513
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:381
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:432
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:479
msgid "Heading Typography"
msgstr "Überschriftentypografie"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:494
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:98
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:697
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Customer Details"
msgstr "Kundendetails"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:116
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:117
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:128
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:159
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Layout"
msgstr "Layout"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:134
#. translators: %s is the URL for upgrading
msgid "This feature is available in the CartFlows higher plan. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">%2$s</a>."
msgstr ""
"Diese Funktion ist im höheren Plan von CartFlows verfügbar. <a href=\"%1$s\" target=\"_blank\" "
"rel=\"noopener\">%2$s</a>."

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:136
msgid "Upgrade Now!"
msgstr "Jetzt upgraden!"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:154
msgid " Global Text Typography"
msgstr "Globale Texttypografie"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:252
msgid " Global Primary Color"
msgstr "Globale Primärfarbe"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:538
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:481
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:264
msgid "Inherit"
msgstr "Erben"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:645
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:528
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:643
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:403
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:301
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:381
msgid "Rounded Corners"
msgstr "Abgerundete Ecken"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:841
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:290
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Box Shadow"
msgstr "Box-Schatten"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:906
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:869
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Validation"
msgstr "Feldvalidierung"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:904
msgid "Error Messages"
msgstr "Fehlermeldungen"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1174
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:827
msgid "Section Margin"
msgstr "Abschnittsmarge"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1238
msgid "Buttons (Normal)"
msgstr "Schaltflächen (Normal)"

#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:76
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button"
msgstr "Knopf"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:276
msgid "Sections Heading Typography"
msgstr "Abschnittsüberschrift Typografie"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:288
msgid "Sections Text Typography"
msgstr "Abschnitte Text Typografie"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:304
msgid "Sections Background Color"
msgstr "Abschnittshintergrundfarbe"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:145
msgid "Sorry there was a problem removing this coupon."
msgstr "Entschuldigung, es gab ein Problem beim Entfernen dieses Gutscheins."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:148
msgid "Coupon has been removed."
msgstr "Der Gutschein wurde entfernt."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:166
msgid "Sorry there was a problem removing "
msgstr "Entschuldigung, es gab ein Problem beim Entfernen"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:169
msgid " has been removed."
msgstr "wurde entfernt."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email Exist."
msgstr "E-Mail existiert."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email not exist"
msgstr "E-Mail existiert nicht"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:126
#. Translators: %1$s & %2$s is replaced with Field Name
msgid "%1$s Add %2$s"
msgstr "%1$s %2$s hinzufügen"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:137
#. Translators: %s is replaced with Field Icon
msgid "%s Have a coupon?"
msgstr "%s Haben Sie einen Gutschein?"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:146
#. Translators: %s is replaced with Field Icon
msgid "%s Add Order Notes"
msgstr "%s Bestellnotizen hinzufügen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:474
#: modules/optin/classes/class-cartflows-optin-markup.php:228
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:232
msgid "WooCommerce functions do not exist. If you are in an IFrame, please reload it."
msgstr "WooCommerce-Funktionen existieren nicht. Wenn Sie sich in einem IFrame befinden, laden Sie es bitte neu."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:475
#: modules/optin/classes/class-cartflows-optin-markup.php:229
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:233
msgid "Click Here to Reload"
msgstr "Klicken Sie hier, um neu zu laden"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:503
msgid "Checkout ID not found"
msgstr "Checkout-ID nicht gefunden"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:506
#. translators: %1$1s, %2$2s Link to article
msgid ""
"It seems that this is not the CartFlows Checkout page where you have added this shortcode. Please refer to this "
"%1$1sarticle%2$2s to know more."
msgstr ""
"Es scheint, dass dies nicht die CartFlows-Checkout-Seite ist, auf der Sie diesen Shortcode hinzugefügt haben. Bitte "
"lesen Sie diesen %1$1sArtikel%2$2s, um mehr zu erfahren."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:576
#: modules/checkout/templates/embed/checkout-template-simple.php:48
#: modules/checkout/templates/wcf-template.php:40
#: modules/optin/templates/optin-template-simple.php:29
msgid "Your cart is currently empty."
msgstr "Ihr Warenkorb ist derzeit leer."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:616
#. translators: %1$1s, %2$2s Link to meta
msgid "No product is selected. Please select products from the %1$1scheckout meta settings%2$2s to continue."
msgstr ""
"Es ist kein Produkt ausgewählt. Bitte wählen Sie Produkte aus den %1$1sCheckout-Meta-Einstellungen%2$2s aus, um "
"fortzufahren."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:724
msgid "Variations Not set"
msgstr "Variationen Nicht festgelegt"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:735
msgid "This product can't be purchased"
msgstr "Dieses Produkt kann nicht gekauft werden"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1467
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1511
#: modules/checkout/templates/checkout/collapsed-order-summary.php:46
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:82
msgid "Coupon Code"
msgstr "Gutscheincode"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1468
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1520
#: modules/checkout/templates/checkout/collapsed-order-summary.php:51
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:87
msgid "Apply"
msgstr "Anwenden"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1544
msgid "Entered email address is not a valid email."
msgstr "Die eingegebene E-Mail-Adresse ist keine gültige E-Mail."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1545
msgid "This email is already registered. Please enter the password to continue."
msgstr "Diese E-Mail ist bereits registriert. Bitte geben Sie das Passwort ein, um fortzufahren."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1548
msgid "Value must be between "
msgstr "Der Wert muss zwischen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1752
msgid "Show Order Summary"
msgstr "Bestellübersicht anzeigen"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1753
msgid "Hide Order Summary"
msgstr "Bestellübersicht ausblenden"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1876
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:498
#: modules/checkout/templates/checkout/shipping-methods.php:66
msgid "Enter your address to view shipping options."
msgstr "Geben Sie Ihre Adresse ein, um die Versandoptionen anzuzeigen."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1885
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:405
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:407
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:796
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:110
#: modules/thankyou/templates/instant-thankyou-order-details.php:107
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping"
msgstr "Versand"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:138
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:943
#: modules/optin/classes/class-cartflows-optin-meta-data.php:174
msgid "Products"
msgstr "Produkte"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:144
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Order Bumps"
msgstr "Bestell-Ergänzungen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:156
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Offers"
msgstr "Dynamische Angebote"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:172
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:982
msgid "Checkout Offer"
msgstr "Kassenangebot"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:205
msgid "Two Step (Available in higher plan) "
msgstr "Zwei Schritte (Verfügbar im höheren Plan)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:206
msgid "Multistep Checkout (Available in higher plan) "
msgstr "Mehrstufiger Checkout (Verfügbar im höheren Tarif)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:213
#: modules/landing/classes/class-cartflows-landing-meta-data.php:91
#: modules/optin/classes/class-cartflows-optin-meta-data.php:253
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:92
msgid "Shortcode"
msgstr "Kurzwahl"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:230
msgid "CartFlows Checkout"
msgstr "CartFlows Kasse"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:232
msgid "Add this shortcode to your checkout page"
msgstr "Fügen Sie diesen Shortcode zu Ihrer Kassenseite hinzu"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:239
msgid "Checkout Design"
msgstr "Kassendesign"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:245
msgid "Checkout Skin"
msgstr "Kasse Skin"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:259
#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout"
msgstr "Mehrstufiger Checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:296
#: modules/optin/classes/class-cartflows-optin-meta-data.php:284
#: modules/optin/classes/class-cartflows-optin-meta-data.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:420
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:181
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:205
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:306
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Family"
msgstr "Schriftfamilie"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:304
msgid "Instant Checkout"
msgstr "Sofortige Kasse"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:319
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:328
msgid "Left Column Background Color"
msgstr "Hintergrundfarbe der linken Spalte"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:336
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:344
msgid "Right Column Background Color"
msgstr "Hintergrundfarbe der rechten Spalte"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:355
msgid "Checkout Texts & Buttons"
msgstr "Kassentexte & -schaltflächen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:362
msgid "Enable Advance Options"
msgstr "Erweiterte Optionen aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:370
msgid "Heading Font"
msgstr "Überschrift Schriftart"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:384
#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text Color"
msgstr "Überschrift Textfarbe"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:400
msgid "Heading Font Family"
msgstr "Schriftfamilie der Überschrift"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:436
msgid "Input Field Style"
msgstr "Eingabefeldstil"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:463
msgid "Input Field Font Family"
msgstr "Schriftfamilie des Eingabefelds"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:483
msgid "Field Size"
msgstr "Feldgröße"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:489
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:660
#: modules/optin/classes/class-cartflows-optin-meta-data.php:335
#: modules/optin/classes/class-cartflows-optin-meta-data.php:439
msgid "Extra Small"
msgstr "Extra Klein"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:493
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:664
#: modules/optin/classes/class-cartflows-optin-meta-data.php:339
#: modules/optin/classes/class-cartflows-optin-meta-data.php:443
msgid "Small"
msgstr "Klein"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:497
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:668
#: modules/optin/classes/class-cartflows-optin-meta-data.php:343
#: modules/optin/classes/class-cartflows-optin-meta-data.php:447
msgid "Medium"
msgstr "Mittel"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:501
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:672
#: modules/optin/classes/class-cartflows-optin-meta-data.php:347
#: modules/optin/classes/class-cartflows-optin-meta-data.php:451
msgid "Large"
msgstr "Groß"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:505
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:676
#: modules/optin/classes/class-cartflows-optin-meta-data.php:351
#: modules/optin/classes/class-cartflows-optin-meta-data.php:455
msgid "Extra Large"
msgstr "Extra Groß"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:525
msgid "Field Top-Bottom Spacing"
msgstr "Feldabstand oben-unten"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:541
msgid "Field Left-Right Spacing"
msgstr "Feld Links-Rechts-Abstand"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:557
msgid "Field Text / Placeholder Color"
msgstr "Feldtext / Platzhalterfarbe"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:618
msgid "Button Fields"
msgstr "Schaltflächenfelder"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:634
msgid "Button Font Family"
msgstr "Schriftfamilie der Schaltfläche"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:654
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:330
msgid "Button Size"
msgstr "Schaltflächengröße"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:696
msgid "Button Top-Bottom Spacing"
msgstr "Schaltflächenabstand oben-unten"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:717
msgid "Button Left-Right Spacing"
msgstr "Abstand zwischen den Schaltflächen links und rechts"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:738
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:294
msgid "Button Text Color"
msgstr "Schriftfarbe der Schaltfläche"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:753
msgid "Button Text Hover Color"
msgstr "Schaltflächentext-Hover-Farbe"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:768
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:300
msgid "Button Background Color"
msgstr "Schaltflächenhintergrundfarbe"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:783
msgid "Button Background Hover Color"
msgstr "Schaltflächen-Hintergrund-Hover-Farbe"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:798
msgid "Button Border Color"
msgstr "Schaltflächenrandfarbe"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:813
msgid "Button Border Hover Color"
msgstr "Schaltflächenrand-Hover-Farbe"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:861
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:865
msgid "Enable Product Options"
msgstr "Produktoptionen aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:870
msgid "Enable Conditions"
msgstr "Bedingungen aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:874
msgid "Restrict user to purchase all products"
msgstr "Benutzer daran hindern, alle Produkte zu kaufen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:878
msgid "Let user select one product from all options"
msgstr "Lassen Sie den Benutzer ein Produkt aus allen Optionen auswählen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:882
msgid "Let user select multiple products from all options"
msgstr "Lassen Sie den Benutzer mehrere Produkte aus allen Optionen auswählen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:888
msgid "Enable Variations"
msgstr "Variationen aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:897
msgid "Show variations inline"
msgstr "Variationen inline anzeigen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:901
msgid "Show variations in popup"
msgstr "Variationen im Popup anzeigen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:907
msgid "Enable Quantity"
msgstr "Menge aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:920
msgid "Select Coupon"
msgstr "Gutschein auswählen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:921
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a coupon"
msgstr "Suche nach einem Gutschein"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:925
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows coupon please %1$1s Click here.%2$2s"
msgstr "Für weitere Informationen über den CartFlows-Gutschein bitte %1$1s hier klicken.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:951
msgid "Select Product"
msgstr "Produkt auswählen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:952
msgid "Search for a product..."
msgstr "Nach einem Produkt suchen..."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:962
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the checkout product settings please %1$1s Click here.%2$2s"
msgstr "Für weitere Informationen zu den Produkteinstellungen im Checkout bitte %1$1s hier klicken.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:968
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Auto Apply Coupon"
msgstr "Gutschein automatisch anwenden"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:975
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Product Options"
msgstr "Produktoptionen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1026
#: modules/flow/classes/class-cartflows-step-meta-base.php:80
#: modules/landing/classes/class-cartflows-landing-meta-data.php:119
#: modules/optin/classes/class-cartflows-optin-meta-data.php:574
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:402
msgid "Custom Script"
msgstr "Benutzerdefiniertes Skript"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1035
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:475
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Advanced"
msgstr "Fortgeschritten"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1041
msgid "Display product images"
msgstr "Produktbilder anzeigen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1049
msgid "Enable cart editing on checkout"
msgstr "Bearbeiten des Warenkorbs beim Checkout aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1064
#: modules/landing/classes/class-cartflows-landing-meta-data.php:134
#: modules/optin/classes/class-cartflows-optin-meta-data.php:634
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:523
msgid "Step Note"
msgstr "Schrittnotiz"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1148
msgid "Form Settings"
msgstr "Formulareinstellungen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1153
msgid "Enable Coupon Field"
msgstr "Gutschein-Feld aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1159
msgid "Collapsible Coupon Field"
msgstr "Einklappbares Gutscheinfeld"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1175
msgid "Enable Additional Field"
msgstr "Zusätzliches Feld aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1181
msgid "Collapsible Additional Field"
msgstr "Einklappbares Zusatzfeld"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1197
msgid "Enable Ship To Different Address"
msgstr "Versand an eine andere Adresse aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1204
msgid "Enable Google Address Autocomplete"
msgstr "Google-Adress-Autovervollständigung aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1207
#. translators: %1$s: link html start, %2$s: link html end
msgid "Before enabling this option, make sure that you have added API key in Google Address Autocomplete Settings."
msgstr ""
"Bevor Sie diese Option aktivieren, stellen Sie sicher, dass Sie den API-Schlüssel in den Google Address "
"Autocomplete-Einstellungen hinzugefügt haben."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1212
msgid "Enable Custom Shipping Message"
msgstr "Benutzerdefinierte Versandnachricht aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1220
msgid "Shipping Message"
msgstr "Versandnachricht"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1224
msgid "This message will be displayed when no shipping method is available."
msgstr "Diese Nachricht wird angezeigt, wenn keine Versandmethode verfügbar ist."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1241
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:446
msgid "Order Summary Position"
msgstr "Bestellübersicht Position"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1248
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:219
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:453
msgid "Top"
msgstr "Oben"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1252
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:457
msgid "Bottom"
msgstr "Unten"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1266
msgid "Form Headings"
msgstr "Formularüberschriften"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1273
msgid "Billing Details"
msgstr "Rechnungsdetails"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1275
msgid "Billing details"
msgstr "Rechnungsdetails"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1280
msgid "Shipping Details"
msgstr "Versanddetails"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1282
msgid "Ship to a different address?"
msgstr "An eine andere Adresse versenden?"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1287
#: modules/thankyou/templates/instant-thankyou-your-product.php:23
msgid "Your Order"
msgstr "Ihre Bestellung"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1289
msgid "Your order"
msgstr "Ihre Bestellung"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1294
msgid "Customer Information"
msgstr "Kundeninformation"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1296
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:159
msgid "Customer information"
msgstr "Kundeninformationen"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1302
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1304
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:823
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:276
#: modules/thankyou/templates/instant-thankyou-order-details.php:127
msgid "Payment"
msgstr "Zahlung"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1310
msgid "Enable Field validation error message"
msgstr "Fehlermeldung zur Feldvalidierung aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1318
msgid "Validation error message"
msgstr "Validierungsfehlermeldung"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1336
msgid "Place Order Button"
msgstr "Bestellknopf"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1342
#: modules/optin/classes/class-cartflows-optin-meta-data.php:590
#: modules/gutenberg/build/blocks.js:11
msgid "Button Text"
msgstr "Schaltflächentext"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1351
msgid "Enable Lock Icon"
msgstr "Sperrsymbol aktivieren"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1360
msgid "Enable Price Display"
msgstr "Preisanzeige aktivieren"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:260
msgid "Home"
msgstr "Startseite"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:496
#: modules/checkout/templates/checkout/shipping-methods.php:64
msgid "Shipping costs are calculated during checkout."
msgstr "Die Versandkosten werden während des Bezahlvorgangs berechnet."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:515
#: modules/checkout/templates/checkout/shipping-methods.php:72
#. Translators: $s shipping destination.
msgid "No shipping options were found for %s."
msgstr "Keine Versandoptionen wurden für %s gefunden."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:519
#: modules/checkout/templates/checkout/shipping-methods.php:73
#. Translators: $s shipping destination.
msgid "Enter a different address"
msgstr "Geben Sie eine andere Adresse ein"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:562
#: modules/thankyou/templates/instant-thankyou-order-details.php:67
msgid "Contact"
msgstr "Kontakt"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:564
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "%1$1s Log in%2$2s"
msgstr "%1$1s Anmelden%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:594
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:192
msgid "Password"
msgstr "Passwort"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:596
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:194
#. translators: %s: asterisk mark
msgid "Password %s"
msgstr "Passwort %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:602
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:200
msgid "Login"
msgstr "Anmelden"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:603
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:201
msgid "Lost your password?"
msgstr "Passwort vergessen?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:608
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:206
msgid "Login is optional, you can continue with your order below."
msgstr "Die Anmeldung ist optional, Sie können mit Ihrer Bestellung unten fortfahren."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:620
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:218
msgid "Create an account?"
msgstr "Ein Konto erstellen?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:635
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:233
msgid "Account username"
msgstr "Kontobenutzername"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:637
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:235
#. translators: %s: asterisk mark
msgid "Account username %s"
msgstr "Kontobenutzername %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:649
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:247
msgid "Create account password"
msgstr "Erstellen Sie ein Kontopasswort"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:651
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:249
#. translators: %s: asterisk mark
msgid "Create account password %s"
msgstr "Erstellen Sie ein Kontopasswort %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:660
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:258
#. translators: %1$s: username, %2$s emailid
msgid " Welcome Back %1$s ( %2$s )"
msgstr "Willkommen zurück %1$s ( %2$s )"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:915
msgid "Looks like you haven't added any items to cart yet — start shopping to fill it up!"
msgstr "Es sieht so aus, als hättest du noch keine Artikel in den Warenkorb gelegt – fang an zu shoppen, um ihn zu füllen!"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:927
msgid "Your Cart is Currently Empty."
msgstr "Ihr Warenkorb ist derzeit leer."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:929
msgid "Start Shopping"
msgstr "Einkauf beginnen"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:161
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "Already have an account? %1$1s Log in%2$2s"
msgstr "Haben Sie bereits ein Konto? %1$1s Anmelden%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:177
#. translators: %s: asterisk mark
msgid "Email Address %s"
msgstr "E-Mail-Adresse %s"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:69
msgid "Coupon code applied successfully."
msgstr "Gutscheincode erfolgreich angewendet."

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:76
msgid "Have a coupon?"
msgstr "Haben Sie einen Gutschein?"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:95
#: modules/checkout/templates/checkout/order-review-table.php:17
#: modules/checkout/templates/checkout/order-review-table.php:43
#: modules/thankyou/templates/instant-thankyou-your-product.php:118
msgid "Subtotal"
msgstr "Zwischensumme"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:147
#: modules/checkout/templates/checkout/order-review-table.php:79
#: modules/thankyou/templates/instant-thankyou-your-product.php:148
msgid "Total"
msgstr "Gesamt"

#: modules/checkout/templates/checkout/order-review-table.php:16
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Product"
msgstr "Produkt"

#: modules/checkout/templates/checkout/shipping-methods.php:53
#. Translators: $s shipping destination.
msgid "Shipping to %s."
msgstr "Versand nach %s."

#: modules/checkout/templates/checkout/shipping-methods.php:54
#. Translators: $s shipping destination.
msgid "Change address"
msgstr "Adresse ändern"

#: modules/checkout/templates/checkout/shipping-methods.php:56
msgid "Shipping options will be updated during checkout."
msgstr "Versandoptionen werden während des Bezahlvorgangs aktualisiert."

#: modules/checkout/templates/wcf-template.php:51
msgid "Copyright &copy;"
msgstr "Urheberrecht &copy;"

#: modules/checkout/templates/wcf-template.php:56
msgid "All Rights Reserved"
msgstr "Alle Rechte vorbehalten"

#: modules/elementor/class-cartflows-el-widgets-loader.php:177
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:588
msgid "Cartflows"
msgstr "Cartflows"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:267
#. translators: %s admin link
msgid ""
"This feature is available in the CartFlows higher plan. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Upgrade "
"Now!</a>."
msgstr ""
"Diese Funktion ist im höheren CartFlows-Plan verfügbar. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Jetzt "
"upgraden!</a>."

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:370
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:175
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color"
msgstr "Farbe"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:585
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:354
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:343
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Normal"
msgstr "Normal"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:683
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:417
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:403
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover"
msgstr "Schweben"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:839
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Rounded Corners"
msgstr "Abschnitt abgerundete Ecken"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:164
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title"
msgstr "Titel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:166
msgid "BUY NOW"
msgstr "JETZT KAUFEN"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:176
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sub Title"
msgstr "Untertitel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title"
msgstr "Vor dem Titel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:200
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title"
msgstr "Nach dem Titel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:201
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title & Sub Title"
msgstr "Vor Titel & Untertitel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:202
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title & Sub Title"
msgstr "Nach Titel & Untertitel"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:213
msgid "Icon Vertical Alignment"
msgstr "Symbol vertikale Ausrichtung"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:223
msgid "Middle"
msgstr "Mitte"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:318
msgid "Justify"
msgstr "Rechtfertigen"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:424
msgid "Hover Text Color"
msgstr "Textfarbe beim Überfahren"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:437
msgid "Hover Background Color"
msgstr "Hintergrundfarbe beim Überfahren"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:467
msgid "Hover Animation"
msgstr "Hover-Animation"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:554
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:645
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:734
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Content"
msgstr "Inhalt"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:499
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Alignment"
msgstr "Textausrichtung"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:556
msgid "Title and Sub Title Spacing"
msgstr "Abstand zwischen Titel und Untertitel"

#: modules/elementor/widgets/class-cartflows-el-optin-form.php:324
#: modules/optin/classes/class-cartflows-optin-meta-data.php:406
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button"
msgstr "Absenden-Button"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:187
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:199
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:211
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:223
msgid "Show"
msgstr "Zeigen"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:367
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sections"
msgstr "Abschnitte"

#: modules/email-report/class-cartflows-admin-report-emails.php:104
msgid "There"
msgstr "Dort"

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "You have successfully unsubscribed from our weekly emails list."
msgstr "Sie haben sich erfolgreich von unserer wöchentlichen E-Mail-Liste abgemeldet."

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "Unsubscribed"
msgstr "Abgemeldet"

#: modules/email-report/class-cartflows-admin-report-emails.php:174
msgid "Here’s how your store performed last week!"
msgstr "So hat Ihr Geschäft letzte Woche abgeschnitten!"

#: modules/email-report/templates/email-body.php:17
msgid "CartFlows Weekly Report"
msgstr "CartFlows Wochenbericht"

#: modules/email-report/templates/email-cf-pro-block.php:26
msgid "CartFlows Pro can help you to increase conversion and maximize profits."
msgstr "CartFlows Pro kann Ihnen helfen, die Konversion zu steigern und den Gewinn zu maximieren."

#: modules/email-report/templates/email-cf-pro-block.php:43
msgid ""
"Want to earn 30% more store revenue on autopilot? CartFlows order bumps and upsells help you do just that. Try "
"CartFlows Pro risk-free for 30 days!"
msgstr ""
"Möchten Sie 30 % mehr Umsatz im Geschäft im Autopilot-Modus erzielen? CartFlows Bestell-Erhöhungen und Upsells helfen "
"Ihnen dabei. Probieren Sie CartFlows Pro 30 Tage lang risikofrei aus!"

#: modules/email-report/templates/email-cf-pro-block.php:62
msgid "GET CARTFLOWS NOW"
msgstr "JETZT CARTFLOWS HOLEN"

#: modules/email-report/templates/email-content-section.php:27
#. translators: %s user name
msgid "Hey %s!"
msgstr "Hey %s!"

#: modules/email-report/templates/email-content-section.php:42
#. translators: %1$s: store name, %2$s: total revenue.  %3$s: total revenue
msgid ""
"%1$s has earned a total %2$s in revenue last week by using CartFlows to power your store! And in the last month, it "
"earned %3$s"
msgstr ""
"%1$s hat letzte Woche insgesamt %2$s an Einnahmen erzielt, indem es CartFlows genutzt hat, um Ihren Laden zu betreiben! "
"Und im letzten Monat hat es %3$s verdient"

#: modules/email-report/templates/email-content-section.php:79
msgid "(Last 7 days)"
msgstr "(Letzte 7 Tage)"

#: modules/email-report/templates/email-content-section.php:93
msgid "(Last 30 days)"
msgstr "(Letzte 30 Tage)"

#: modules/email-report/templates/email-footer.php:63
#. translators: %1$s - link to a site;
msgid "This email was auto-generated and sent from %1$s."
msgstr "Diese E-Mail wurde automatisch erstellt und von %1$s gesendet."

#: modules/email-report/templates/email-footer.php:70
msgid "Unsubscribe"
msgstr "Abbestellen"

#: modules/email-report/templates/email-header.php:27
msgid "Your weekly summary from CartFlows."
msgstr "Ihre wöchentliche Zusammenfassung von CartFlows."

#: modules/email-report/templates/email-other-product-block.php:26
msgid "Would you like to try our other products that help WooCommerce stores sell more?"
msgstr "Möchten Sie unsere anderen Produkte ausprobieren, die WooCommerce-Shops helfen, mehr zu verkaufen?"

#: modules/email-report/templates/email-other-product-block.php:41
msgid "TRY OUR OTHER PRODUCTS"
msgstr "PROBIEREN SIE UNSERE ANDEREN PRODUKTE AUS"

#: modules/email-report/templates/email-stat-content.php:26
msgid "Full Overview"
msgstr "Vollständiger Überblick"

#: modules/email-report/templates/email-stat-content.php:82
#: modules/email-report/templates/email-stat-content.php:220
msgid "Order Placed"
msgstr "Bestellung aufgegeben"

#: modules/email-report/templates/email-stat-content.php:112
#: modules/email-report/templates/email-stat-content.php:247
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total Visits"
msgstr "Gesamtbesuche"

#: modules/email-report/templates/email-stat-content.php:142
#: modules/email-report/templates/email-stat-content.php:274
msgid "Order Bumps Revenue"
msgstr "Umsatz durch Bestellaufschläge"

#: modules/email-report/templates/email-stat-content.php:172
#: modules/email-report/templates/email-stat-content.php:304
msgid "Offers Revenue"
msgstr "Angebotsumsatz"

#: modules/email-report/templates/email-stat-content.php:250
#: modules/email-report/templates/email-stat-content.php:282
#: modules/email-report/templates/email-stat-content.php:307
msgid "CartFlows Pro"
msgstr "CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Flow: "
msgstr "Fluss:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Name: "
msgstr "Name:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:105
msgid "Search Flows"
msgstr "Suchabläufe"

#: modules/flow/classes/class-cartflows-flow-post-type.php:106
msgid "All Flows"
msgstr "Alle Flows"

#: modules/flow/classes/class-cartflows-flow-post-type.php:107
msgid "Edit Flow"
msgstr "Bearbeitungsfluss"

#: modules/flow/classes/class-cartflows-flow-post-type.php:108
msgid "View Flow"
msgstr "Fluss anzeigen"

#: modules/flow/classes/class-cartflows-flow-post-type.php:109
#: modules/flow/classes/class-cartflows-flow-post-type.php:111
#: modules/flow/classes/class-cartflows-step-post-type.php:176
#: modules/flow/classes/class-cartflows-step-post-type.php:178
#: admin-core/assets/build/settings-app.js:25
msgid "Add New"
msgstr "Neu hinzufügen"

#: modules/flow/classes/class-cartflows-flow-post-type.php:110
msgid "Update Flow"
msgstr "Fluss aktualisieren"

#: modules/flow/classes/class-cartflows-flow-post-type.php:112
msgid "New Flow Name"
msgstr "Neuer Flussname"

#: modules/flow/classes/class-cartflows-flow-post-type.php:194
#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Upgrade to CartFlows Pro"
msgstr "Upgrade auf CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:213
msgid "Slug"
msgstr "Schnecke"

#: modules/flow/classes/class-cartflows-flow-post-type.php:332
#: modules/flow/classes/class-cartflows-flow-post-type.php:338
#: modules/flow/classes/class-cartflows-step-post-type.php:410
#: modules/flow/classes/class-cartflows-step-post-type.php:416
#. translators: %s: singular custom post type name
msgid "%s updated."
msgstr "%s aktualisiert."

#: modules/flow/classes/class-cartflows-flow-post-type.php:334
#: modules/flow/classes/class-cartflows-step-post-type.php:412
#. translators: %s: singular custom post type name
msgid "Custom %s updated."
msgstr "Benutzerdefinierter %s aktualisiert."

#: modules/flow/classes/class-cartflows-flow-post-type.php:336
#: modules/flow/classes/class-cartflows-step-post-type.php:414
#. translators: %s: singular custom post type name
msgid "Custom %s deleted."
msgstr "Benutzerdefinierter %s gelöscht."

#: modules/flow/classes/class-cartflows-flow-post-type.php:340
#: modules/flow/classes/class-cartflows-step-post-type.php:418
#. translators: %1$s: singular custom post type name ,%2$s: date and time of the revision
msgid "%1$s restored to revision from %2$s"
msgstr "%1$s wurde auf die Revision vom %2$s zurückgesetzt"

#: modules/flow/classes/class-cartflows-flow-post-type.php:342
#: modules/flow/classes/class-cartflows-step-post-type.php:420
#. translators: %s: singular custom post type name
msgid "%s published."
msgstr "%s veröffentlicht."

#: modules/flow/classes/class-cartflows-flow-post-type.php:344
#: modules/flow/classes/class-cartflows-step-post-type.php:422
#. translators: %s: singular custom post type name
msgid "%s saved."
msgstr "%s gespeichert."

#: modules/flow/classes/class-cartflows-flow-post-type.php:346
#: modules/flow/classes/class-cartflows-step-post-type.php:424
#. translators: %s: singular custom post type name
msgid "%s submitted."
msgstr "%s eingereicht."

#: modules/flow/classes/class-cartflows-flow-post-type.php:348
#: modules/flow/classes/class-cartflows-step-post-type.php:426
#. translators: %s: singular custom post type name
msgid "%s scheduled for."
msgstr "%s geplant für."

#: modules/flow/classes/class-cartflows-flow-post-type.php:350
#: modules/flow/classes/class-cartflows-step-post-type.php:428
#. translators: %s: singular custom post type name
msgid "%s draft updated."
msgstr "%s-Entwurf aktualisiert."

#: modules/flow/classes/class-cartflows-step-meta-base.php:58
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:118
msgid "Design"
msgstr "Design"

#: modules/flow/classes/class-cartflows-step-meta-base.php:82
msgid "Custom script lets you add your own custom script on front end of this flow page."
msgstr ""
"Mit einem benutzerdefinierten Skript können Sie Ihr eigenes benutzerdefiniertes Skript auf der Vorderseite dieser "
"Flussseite hinzufügen."

#: modules/flow/classes/class-cartflows-step-post-type.php:172
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Search Steps"
msgstr "Suchschritte"

#: modules/flow/classes/class-cartflows-step-post-type.php:173
msgid "All Steps"
msgstr "Alle Schritte"

#: modules/flow/classes/class-cartflows-step-post-type.php:174
msgid "Edit Step"
msgstr "Schritt bearbeiten"

#: modules/flow/classes/class-cartflows-step-post-type.php:175
msgid "View Step"
msgstr "Schritt anzeigen"

#: modules/flow/classes/class-cartflows-step-post-type.php:177
msgid "Update Step"
msgstr "Aktualisierungsschritt"

#: modules/flow/classes/class-cartflows-step-post-type.php:179
msgid "New Step Name"
msgstr "Neuer Schrittname"

#: modules/flow/classes/class-cartflows-step-post-type.php:220
msgid "Step Type"
msgstr "Schritttyp"

#: modules/flow/classes/class-cartflows-step-post-type.php:230
msgid "Step Flow"
msgstr "Schrittfluss"

#: modules/flow/classes/class-cartflows-step-post-type.php:255
msgid "Optin"
msgstr "Opt-in"

#: modules/flow/classes/class-cartflows-step-post-type.php:276
msgid "Upsell"
msgstr "Upsell"

#: modules/flow/classes/class-cartflows-step-post-type.php:283
msgid "Downsell"
msgstr "Downsell"

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:146
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:201
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:311
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:86
msgid "Permission denied."
msgstr "Zugriff verweigert."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:225
msgid "No product is selected. Please select products from the checkout meta settings to continue."
msgstr "Es ist kein Produkt ausgewählt. Bitte wählen Sie Produkte aus den Checkout-Meta-Einstellungen aus, um fortzufahren."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:328
#: modules/optin/classes/class-cartflows-optin-markup.php:321
msgid "No product is selected. Please select a Simple, Virtual and Free product from the meta settings."
msgstr ""
"Es ist kein Produkt ausgewählt. Bitte wählen Sie ein Einfaches, Virtuelles und Kostenloses Produkt aus den "
"Meta-Einstellungen aus."

#: modules/landing/classes/class-cartflows-landing-meta-data.php:98
msgid "Next Step Link"
msgstr "Nächster Schritt Link"

#: modules/optin/classes/class-cartflows-optin-markup.php:261
msgid "Please place shortcode on Optin step-type only."
msgstr "Bitte platzieren Sie den Shortcode nur auf dem Optin-Schritttyp."

#: modules/optin/classes/class-cartflows-optin-markup.php:338
msgid "Please update the selected product's price to zero (0)."
msgstr "Bitte aktualisieren Sie den Preis des ausgewählten Produkts auf null (0)."

#: modules/optin/classes/class-cartflows-optin-markup.php:347
#: modules/optin/classes/class-cartflows-optin-markup.php:351
msgid "Please select a Simple, Virtual and Free product."
msgstr "Bitte wählen Sie ein Einfaches, Virtuelles und Kostenloses Produkt aus."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:76
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enable Custom Field Editor"
msgstr "Benutzerdefinierten Felde-Editor aktivieren"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a Product"
msgstr "Nach einem Produkt suchen"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:229
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows Optin step please %1$sClick here.%2$s"
msgstr "Für weitere Informationen über den CartFlows Optin-Schritt bitte %1$shier klicken.%2$s"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:262
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:101
msgid "Add this shortcode to your optin page"
msgstr "Fügen Sie diesen Shortcode zu Ihrer Optin-Seite hinzu"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:270
msgid "Global Settings"
msgstr "Globale Einstellungen"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:328
#: modules/optin/classes/class-cartflows-optin-meta-data.php:432
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size"
msgstr "Größe"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:361
#: modules/optin/classes/class-cartflows-optin-meta-data.php:465
msgid "Top Bottom Spacing"
msgstr "Abstand oben unten"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:368
#: modules/optin/classes/class-cartflows-optin-meta-data.php:472
msgid "Left Right Spacing"
msgstr "Links Rechts Abstand"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:382
msgid "Text / Placeholder Color"
msgstr "Text- / Platzhalterfarbe"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:412
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Size"
msgstr "Schriftgröße"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:479
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Position"
msgstr "Position"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:584
msgid "Optin Settings"
msgstr "Opt-in-Einstellungen"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:598
msgid "Pass Fields as URL Parameters"
msgstr "Felder als URL-Parameter übergeben"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:601
msgid "You can pass specific fields from the form to next step as URL query parameters."
msgstr "Sie können bestimmte Felder aus dem Formular als URL-Abfrageparameter an den nächsten Schritt übergeben."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:606
msgid "Enter form field"
msgstr "Formularfeld eingeben"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:609
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:492
msgid "Enter comma seprated field name. E.g. first_name, last_name"
msgstr "Geben Sie den Feldnamen durch Kommas getrennt ein. Z.B. first_name, last_name"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:610
msgid "Fields to pass, separated by commas"
msgstr "Felder, die übergeben werden sollen, durch Kommas getrennt"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:612
#. translators: %s: link
msgid "You can pass field value as a URL parameter to the next step. %1$sLearn More >>%2$s"
msgstr "Sie können den Feldwert als URL-Parameter an den nächsten Schritt übergeben. %1$sMehr erfahren >>%2$s"

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:180
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:183
msgid "We can't seem to find an order for you."
msgstr "Wir können anscheinend keine Bestellung für Sie finden."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:272
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:662
msgid "No completed or processing order found to show the order details form demo."
msgstr "Keine abgeschlossene oder in Bearbeitung befindliche Bestellung gefunden, um das Bestelldetails-Formular anzuzeigen."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:280
msgid "Order not found. You cannot access this page directly."
msgstr "Bestellung nicht gefunden. Sie können nicht direkt auf diese Seite zugreifen."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:673
msgid "Order Details Not Found."
msgstr "Bestelldetails nicht gefunden."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:675
msgid "Return to Shopping"
msgstr "Zurück zum Einkaufen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:212
msgid "Font Size (In px)"
msgstr "Schriftgröße (in px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:221
msgid "Advanced Options"
msgstr "Erweiterte Optionen"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:227
msgid "Enable Advanced Options"
msgstr "Erweiterte Optionen aktivieren"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:234
msgid "Container Width (In px)"
msgstr "Containerbreite (in px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:331
msgid "Background color of left side column for Instant Thank You Layout."
msgstr "Hintergrundfarbe der linken Spalte für das Layout \"Sofortiges Dankeschön\"."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:347
msgid "Background color of right side column for Instant Thank You Layout."
msgstr "Hintergrundfarbe der rechten Spalte für das Layout \"Sofortiges Dankeschön\"."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:417
msgid "Enable Order Overview"
msgstr "Bestellübersicht aktivieren"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:424
msgid "Enable Order Details"
msgstr "Bestelldetails aktivieren"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:431
msgid "Enable Billing Details"
msgstr "Abrechnungsdetails aktivieren"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:438
msgid "Enable Shipping Details"
msgstr "Versanddetails aktivieren"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:481
msgid "Thank You Page Text"
msgstr "Text der Dankeschön-Seite"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:489
msgid "Redirect After Purchase"
msgstr "Weiterleitung nach dem Kauf"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:497
msgid "Redirect Link"
msgstr "Link umleiten"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:500
msgid "https://"
msgstr "https://"

#: modules/thankyou/templates/instant-thankyou-order-details.php:41
#. Translators: First name.
msgid "Thank you, %s!"
msgstr "Danke, %s!"

#: modules/thankyou/templates/instant-thankyou-order-details.php:58
msgid "Order Updates"
msgstr "Bestellaktualisierungen"

#: modules/thankyou/templates/instant-thankyou-order-details.php:59
msgid "You will receive order and shipping updates via email."
msgstr "Sie erhalten Bestell- und Versandaktualisierungen per E-Mail."

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
msgid "Address"
msgstr "Adresse"

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Billing"
msgstr "Abrechnung"

#: modules/thankyou/templates/instant-thankyou-order-details.php:155
msgid "Continue Shopping"
msgstr "Weiter einkaufen"

#: modules/thankyou/templates/instant-thankyou.php:37
msgid ""
"Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please "
"attempt your purchase again."
msgstr ""
"Leider kann Ihre Bestellung nicht bearbeitet werden, da die ausstellende Bank/der Händler Ihre Transaktion abgelehnt "
"hat. Bitte versuchen Sie Ihren Kauf erneut."

#: modules/thankyou/templates/instant-thankyou.php:41
msgid "Pay"
msgstr "Zahlen"

#: modules/thankyou/templates/instant-thankyou.php:43
msgid "My account"
msgstr "Mein Konto"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:137
msgid "Select the Flow"
msgstr "Wählen Sie den Flow aus"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:147
msgid "Add to Cart text"
msgstr "In den Warenkorb Text"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:149
msgid "Add to cart"
msgstr "In den Warenkorb legen"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:154
#. translators: %1$s,%2$s HTML content
msgid ""
"If you want to start the flow from the product page, select the appropriate flow & button text field if required. Refer "
"%1$sthis article%2$s for more information."
msgstr ""
"Wenn Sie den Ablauf von der Produktseite aus starten möchten, wählen Sie den entsprechenden Ablauf und das "
"Schaltflächentextfeld aus, falls erforderlich. Weitere Informationen finden Sie in %1$sdiesem Artikel%2$s."

#: wizard/ajax/wizard.php:207
msgid "Please enter your email ID."
msgstr "Bitte geben Sie Ihre E-Mail-ID ein."

#: wizard/ajax/wizard.php:262
msgid "Oops! Something went wrong. Please refresh the page and try again."
msgstr "Ups! Etwas ist schiefgelaufen. Bitte aktualisieren Sie die Seite und versuchen Sie es erneut."

#: wizard/ajax/wizard.php:363
msgid "Please select any of the page builder to display the ready templates."
msgstr "Bitte wählen Sie einen der Page Builder aus, um die fertigen Vorlagen anzuzeigen."

#: wizard/ajax/wizard.php:502
msgid "No flow ID found. Please select atleast one flow to import."
msgstr "Keine Flow-ID gefunden. Bitte wählen Sie mindestens einen Flow zum Importieren aus."

#: admin-core/ajax/importer.php:893
#: wizard/ajax/wizard.php:516
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$sarticle%3$s."
msgstr ""
"Anforderungszeitüberschreitungsfehler. Bitte überprüfen Sie, ob die Firewall oder ein Sicherheits-Plugin die "
"ausgehenden HTTP/HTTPS-Anfragen an templates.cartflows.com blockiert. %1$sUm dieses Problem zu lösen, lesen Sie bitte "
"diesen %2$sArtikel%3$s."

#: wizard/ajax/wizard.php:539
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import this template, CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "Um diese Vorlage zu importieren, ist CartFlows Pro erforderlich! %1$sUpgrade auf CartFlows Pro%2$s"

#: wizard/ajax/wizard.php:540
#: wizard/ajax/wizard.php:542
msgid "CartFlows Pro Required"
msgstr "CartFlows Pro erforderlich"

#: wizard/ajax/wizard.php:546
msgid "Invalid License Key"
msgstr "Ungültiger Lizenzschlüssel"

#: wizard/ajax/wizard.php:548
#. translators: %1$s: link html start, %2$s: link html end
msgid "No valid license key found! %1$sActivate license%2$s"
msgstr "Kein gültiger Lizenzschlüssel gefunden! %1$sLizenz aktivieren%2$s"

#: wizard/inc/wizard-core.php:174
msgid "Thanks for installing and using CartFlows!"
msgstr "Danke, dass Sie CartFlows installiert und verwendet haben!"

#: wizard/inc/wizard-core.php:175
msgid "It is easy to use the CartFlows. Please use the setup wizard to quick start setup."
msgstr ""
"Es ist einfach, CartFlows zu verwenden. Bitte verwenden Sie den Einrichtungsassistenten, um die Einrichtung schnell zu "
"starten."

#: wizard/inc/wizard-core.php:177
msgid "Start Wizard"
msgstr "Assistent starten"

#: wizard/inc/wizard-core.php:178
msgid "Skip Setup"
msgstr "Setup überspringen"

#: wizard/inc/wizard-core.php:394
msgid "Oops!! Unexpected error occoured"
msgstr "Ups!! Unerwarteter Fehler ist aufgetreten"

#: wizard/inc/wizard-core.php:395
msgid "Import template API call failed. Please reload the page and try again!"
msgstr "Importvorlage-API-Aufruf fehlgeschlagen. Bitte laden Sie die Seite neu und versuchen Sie es erneut!"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Settings Saved"
msgstr "Einstellungen gespeichert"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:53
msgid "Saving…"
msgstr "Speichern…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:42
msgid "Save Settings"
msgstr "Einstellungen speichern"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Regular Price of the product"
msgstr "Regulärer Preis des Produkts"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Price after discount."
msgstr "Preis nach Rabatt."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Product Name"
msgstr "Produktname"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{product_name}} and {{quantity}} to dynamically fetch respective product details."
msgstr "Verwenden Sie {{product_name}} und {{quantity}}, um die jeweiligen Produktdetails dynamisch abzurufen."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Subtext"
msgstr "Untertext"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{quantity}}, {{discount_value}}, {{discount_percent}} to dynamically fetch respective product details."
msgstr ""
"Verwenden Sie {{quantity}}, {{discount_value}}, {{discount_percent}}, um die jeweiligen Produktdetails dynamisch "
"abzurufen."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Enable Highlight"
msgstr "Hervorheben aktivieren"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Highlight Text"
msgstr "Text hervorheben"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Create New Product"
msgstr "Neues Produkt erstellen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add"
msgstr "Hinzufügen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Once you have selected products, they will be displayed here."
msgstr "Sobald Sie Produkte ausgewählt haben, werden sie hier angezeigt."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Items"
msgstr "Artikel"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Quantity"
msgstr "Menge"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Discount"
msgstr "Rabatt"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Adding…"
msgstr "Hinzufügen…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Please search and select at-lease one product to add."
msgstr "Bitte suchen und wählen Sie mindestens ein Produkt aus, das hinzugefügt werden soll."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
#: wizard/assets/build/wizard-app.js:1
msgid "Reset"
msgstr "Zurücksetzen"

#: admin-core/assets/build/editor-app.js:1
msgid "Image Preview"
msgstr "Bildvorschau"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "Upload a file"
msgstr "Datei hochladen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "or drag and drop"
msgstr "oder ziehen und ablegen"

#: admin-core/assets/build/editor-app.js:1
msgid "PNG, JPG, GIF up to 10MB"
msgstr "PNG, JPG, GIF bis zu 10 MB"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Select Dates"
msgstr "Datum auswählen"

#: admin-core/assets/build/editor-app.js:4
#: admin-core/assets/build/settings-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Pro %2$s to use %3$s feature."
msgstr "Bitte aktualisieren Sie auf das %1$s CartFlows Pro %2$s, um die %3$s Funktion zu nutzen."

#: admin-core/assets/build/editor-app.js:7
#: admin-core/assets/build/settings-app.js:7
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Higher Plan %2$s to use %3$s feature."
msgstr "Bitte upgraden Sie auf den %1$s CartFlows Higher Plan %2$s, um die %3$s Funktion zu nutzen."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
#. translators: %s is replaced with feature name
msgid "Please upgrade to the CartFlows Higher Plan to use the %s feature."
msgstr "Bitte upgraden Sie auf den CartFlows Higher Plan, um die %s Funktion zu nutzen."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Role"
msgstr "Rolle"

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Access"
msgstr "Zugang"

#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/settings-app.js:11
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the user role management please %1$sClick here.%2$s"
msgstr "Für weitere Informationen zur Verwaltung von Benutzerrollen bitte %1$shier klicken.%2$s"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
#. translators: %1$s is the selected product of CartFlows, %2$s is the selected version of CartFlows.
msgid "Are you sure you want to rollback to %1$s v%2$s?"
msgstr "Sind Sie sicher, dass Sie auf %1$s v%2$s zurücksetzen möchten?"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback"
msgstr "Zurücksetzen"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:14
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Cancel"
msgstr "Abbrechen"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid ""
"Experiencing an issue with the current version of CartFlows? Roll back to a previous version to help troubleshoot the "
"problem."
msgstr ""
"Haben Sie ein Problem mit der aktuellen Version von CartFlows? Gehen Sie zu einer früheren Version zurück, um das "
"Problem zu beheben."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Now"
msgstr "Jetzt regenerieren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:19
msgid "Reset Permalinks Settings"
msgstr "Permalink-Einstellungen zurücksetzen"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate License"
msgstr "Lizenz aktivieren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Deactivate License"
msgstr "Lizenz deaktivieren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Please enter a valid license key!"
msgstr "Bitte geben Sie einen gültigen Lizenzschlüssel ein!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
#: wizard/assets/build/wizard-app.js:4
msgid "Processing"
msgstr "Verarbeitung"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unknown error occurred while activating the license."
msgstr "Unbekannter Fehler beim Aktivieren der Lizenz aufgetreten."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Facebook Pixel"
msgstr "Facebook-Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Analytics Pixel"
msgstr "Google Analytics Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Ads Pixel"
msgstr "Google Ads Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Tiktok Pixel"
msgstr "Tiktok-Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Pinterest Tag"
msgstr "Pinterest-Tag"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Snapchat Pixel"
msgstr "Snapchat-Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Auto Address"
msgstr "Google Auto-Adresse"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Inline CSS"
msgstr "Inline-CSS regenerieren"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid " Regenerating…."
msgstr "Wird regeneriert…."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerated"
msgstr "Regeneriert"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"If you are using the CartFlows Shortcode and using the Design Settings, then this option will regenerate the steps "
"inline CSS. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Wenn Sie den CartFlows-Shortcode und die Design-Einstellungen verwenden, wird diese Option das Inline-CSS der Schritte "
"neu generieren. Um mehr zu erfahren, %1$1s klicken Sie hier %2$2s."

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Updating"
msgstr "Aktualisierung"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Permalinks reset successfully"
msgstr "Permanente Links wurden erfolgreich zurückgesetzt"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the CartFlows Permalink settings please %1$sClick here.%2$s"
msgstr "Für weitere Informationen zu den CartFlows Permalink-Einstellungen bitte %1$shier klicken.%2$s"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Oops! You don't have access to this page."
msgstr "Hoppla! Sie haben keinen Zugriff auf diese Seite."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You don't have permission to access this page. Please reach out to your admin for help."
msgstr ""
"Sie haben keine Berechtigung, auf diese Seite zuzugreifen. Bitte wenden Sie sich an Ihren Administrator, um Hilfe zu "
"erhalten."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Back to Dashboard"
msgstr "Zurück zum Dashboard"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Integrations"
msgstr "Integrationen"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "User Role Manager"
msgstr "Benutzerrollen-Manager"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Permalink"
msgstr "Permalink"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Version Control"
msgstr "Versionskontrolle"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Order"
msgstr "Bestellung"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "License"
msgstr "Lizenz"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Sync Knowledge Base"
msgstr "Wissensdatenbank synchronisieren"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Knowledge Base"
msgstr "Wissensdatenbank"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Search knowledge base"
msgstr "Wissensdatenbank durchsuchen"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "No Docs Founds"
msgstr "Keine Dokumente gefunden"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Please try syncing the docs library"
msgstr "Bitte versuchen Sie, die Dokumentenbibliothek zu synchronisieren"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Syncing…"
msgstr "Synchronisieren…"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Synced. Reloading.."
msgstr "Synchronisiert. Neu laden.."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Need Help?"
msgstr "Brauchen Sie Hilfe?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "We aim to answer all priority support requests within 2-3 hours."
msgstr "Wir streben an, alle vorrangigen Supportanfragen innerhalb von 2-3 Stunden zu beantworten."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Get Support"
msgstr "Unterstützung erhalten"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "All Documentation"
msgstr "Alle Dokumentation"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse documentation, reference material, and tutorials for CartFlows."
msgstr "Durchsuchen Sie die Dokumentation, Referenzmaterialien und Tutorials für CartFlows."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "View documentation"
msgstr "Dokumentation anzeigen"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Videos"
msgstr "Videos"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse tutorial videos on our YouTube channel."
msgstr "Durchsuchen Sie Tutorial-Videos auf unserem YouTube-Kanal."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Youtube Channel"
msgstr "Youtube-Kanal"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Support"
msgstr "Unterstützung"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "What's New?"
msgstr "Was gibt's Neues?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Unlicensed"
msgstr "Ohne Lizenz"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Licensed"
msgstr "Lizenziert"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Open Global Settings"
msgstr "Globale Einstellungen öffnen"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Tutorial Videos"
msgstr "Lernvideos"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:35
msgid "More Options"
msgstr "Weitere Optionen"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:35
msgid "Control"
msgstr "Steuerung"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
#. translators: %d is replaced with the count
msgid "Variation-%d"
msgstr "Variation-%d"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "No Product Assigned"
msgstr "Kein Produkt zugewiesen"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Store Checkout - Remove selected checkout product"
msgstr "Kasse - Ausgewähltes Produkt aus dem Warenkorb entfernen"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "PRO"
msgstr "PRO"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Accepted"
msgstr "Angebot angenommen"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Rejected"
msgstr "Angebot abgelehnt"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Invalid Position"
msgstr "Ungültige Position"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Views"
msgstr "Ansichten"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Conversions"
msgstr "Konversionen"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Revenue"
msgstr "Einnahmen"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "Upgrade to Pro"
msgstr "Upgrade auf Pro"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:39
#: admin-core/assets/build/settings-app.js:42
#. translators: %s: step slug
msgid "%s Step"
msgstr "%s Schritt"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid "Step Editing is Disabled"
msgstr "Die Bearbeitung von Schritten ist deaktiviert"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Open Settings"
msgstr "Einstellungen öffnen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Duplicate Step"
msgstr "Doppelter Schritt"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to duplicate this step? Are you sure?"
msgstr "Möchten Sie diesen Schritt duplizieren? Sind Sie sicher?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Delete Step"
msgstr "Schritt löschen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to delete this step? Are you sure?"
msgstr "Möchten Sie diesen Schritt löschen? Sind Sie sicher?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Declare Winner"
msgstr "Gewinner erklären"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to declare this step as winner? Are you sure?"
msgstr "Möchten Sie diesen Schritt als Gewinner erklären? Sind Sie sicher?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Archive Step"
msgstr "Archivierungsschritt"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to archive this step? Are you sure?"
msgstr "Möchten Sie diesen Schritt archivieren? Sind Sie sicher?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Saving.."
msgstr "Speichern.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:53
msgid "Saved"
msgstr "Gespeichert"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading.."
msgstr "Neu laden.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Testing"
msgstr "Split-Test-Tests"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stop Split Test"
msgstr "Split-Test stoppen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Start Split Test"
msgstr "Split-Test starten"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test"
msgstr "Split-Test"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stopping…"
msgstr "Stoppen…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Starting…"
msgstr "Starten…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading…"
msgstr "Neu laden…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Settings"
msgstr "Einstellungen für den Split-Test"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Restore Archived Variation"
msgstr "Archivierte Variante wiederherstellen"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to restore this archived variation? Are you sure?"
msgstr "Möchten Sie diese archivierte Variante wiederherstellen? Sind Sie sicher?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:44
msgid "Trash Archived Variation"
msgstr "Müll archivierte Variation"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will trash this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Diese Aktion wird diese archivierte Variante und ihre Analysedaten dauerhaft löschen. %1$s Möchten Sie diese "
"archivierte Variante löschen?"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
msgid "Hide Archived Variation"
msgstr "Archivierte Variante ausblenden"

#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:44
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will hide this archived variation from the list of steps, but its analytics will be visible. %1$s Do you "
"want to hide this archived variation?"
msgstr ""
"Diese Aktion wird diese archivierte Variante aus der Liste der Schritte ausblenden, aber ihre Analysen werden sichtbar "
"sein. %1$s Möchten Sie diese archivierte Variante ausblenden?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will delete this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Diese Aktion wird diese archivierte Variante und ihre Analysedaten dauerhaft löschen. %1$s Möchten Sie diese "
"archivierte Variante löschen?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Deleted On: "
msgstr "Gelöscht am:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived On: "
msgstr "Archiviert am:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived Steps"
msgstr "Archivierte Schritte"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Step Name"
msgstr "Schrittnamen bearbeiten"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "Save"
msgstr "Speichern"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Update Template"
msgstr "Vorlage aktualisieren"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Changing the template will permanently delete the current design in this step. Would you still like to proceed?"
msgstr "Das Ändern der Vorlage wird das aktuelle Design in diesem Schritt dauerhaft löschen. Möchten Sie trotzdem fortfahren?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Change Template"
msgstr "Vorlage ändern"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If you are using shortcodes, enable this design settings to apply styles."
msgstr "Wenn Sie Shortcodes verwenden, aktivieren Sie diese Designeinstellungen, um Stile anzuwenden."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Design Settings"
msgstr "Design-Einstellungen aktivieren"

#: admin-core/assets/build/editor-app.js:15
#: admin-core/assets/build/settings-app.js:15
#. translators: %s is replaced with plugin name
msgid "Activate %s"
msgstr "Aktiviere %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
#. translators: %s is replaced with plugin name
msgid "Activating %s"
msgstr "Aktivierung von %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Activated!"
msgstr "Erfolgreich aktiviert!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Failed! Activation!"
msgstr "Fehlgeschlagen! Aktivierung!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Upgrade to Cartflows Pro"
msgstr "Upgrade zu Cartflows Pro"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Activate the License"
msgstr "Aktivieren Sie die Lizenz"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Step Type: "
msgstr "Schritttyp:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "License is required!"
msgstr "Lizenz ist erforderlich!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Activate the license to modify this offer step's settings"
msgstr "Aktivieren Sie die Lizenz, um die Einstellungen dieses Angebotschritts zu ändern"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product Selected"
msgstr "Kein Produkt ausgewählt"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you select the product, they will be displayed here."
msgstr "Sobald Sie das Produkt ausgewählt haben, wird es hier angezeigt."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping Rate"
msgstr "Versandkosten"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you have add product, it will be displayed here."
msgstr "Sobald Sie ein Produkt hinzugefügt haben, wird es hier angezeigt."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Coupon will apply on checkout page"
msgstr "Der Gutschein wird auf der Kassenseite angewendet"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "AND"
msgstr "UND"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the conditional order bump feature."
msgstr "Bitte aktualisieren Sie CartFlows Pro auf die neueste Version, um die bedingte Order-Bump-Funktion zu nutzen."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable conditional order bump "
msgstr "Bedingte Bestell-Erhöhung aktivieren"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "By enabling this option, you can create the conditions to display the order bump."
msgstr "Wenn Sie diese Option aktivieren, können Sie die Bedingungen schaffen, um den Bestellaufschlag anzuzeigen."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show this order bump if following conditions are true"
msgstr "Zeige diesen Bestellaufschlag, wenn die folgenden Bedingungen erfüllt sind"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Condition"
msgstr "Bedingung hinzufügen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:33
#: admin-core/assets/build/settings-app.js:48
msgid "OR"
msgstr "ODER"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Conditions Group"
msgstr "Bedingungsgruppe hinzufügen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Bump Product Image"
msgstr "Produktbild für Bestellaufschlag"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Styles"
msgstr "Stile"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Conditions"
msgstr "Bedingungen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Save Changes"
msgstr "Änderungen speichern"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "You have made changes. Do you want to save the changes?"
msgstr "Sie haben Änderungen vorgenommen. Möchten Sie die Änderungen speichern?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product"
msgstr "Kein Produkt"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Duplicate Order Bump"
msgstr "Doppelter Bestellstoß"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Order Bump"
msgstr "Bestell-Bump bearbeiten"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Trash Order Bump"
msgstr "Müll-Bestellstoß"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Do you really want to trash this order bump permanently?"
msgstr "Möchten Sie diesen Bestellaufschlag wirklich dauerhaft löschen?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Delete Order Bump"
msgstr "Bestellaufschlag löschen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
msgid "Status"
msgstr "Status"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Actions"
msgstr "Aktionen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Create an order bump."
msgstr "Erstellen Sie einen Bestellaufschlag."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enter order bump name"
msgstr "Geben Sie den Namen des Bestellaufschlags ein"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please enter the order bump title"
msgstr "Bitte geben Sie den Titel des Bestellzusatzes ein"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Preview"
msgstr "Vorschau"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "View in Full Screen"
msgstr "Im Vollbild anzeigen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Exit Full Screen"
msgstr "Vollbildmodus beenden"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Submitted"
msgstr "Bestellung abgeschickt"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Special Offer"
msgstr "Sonderangebot"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Receipt"
msgstr "Bestellbeleg"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Checkout Offer"
msgstr "Checkout-Angebot aktivieren"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Dynamic Conditions"
msgstr "Dynamische Bedingungen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the dynamic offers feature."
msgstr "Bitte aktualisieren Sie CartFlows Pro auf die neueste Version, um die Funktion für dynamische Angebote zu nutzen."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Dynamic Offers"
msgstr "Dynamische Angebote aktivieren"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Redirect to: "
msgstr "Weiterleiten an:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for step…"
msgstr "Nach Schritt suchen…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If the following conditions are true"
msgstr "Wenn die folgenden Bedingungen zutreffen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Dynamic Offer"
msgstr "Dynamisches Angebot hinzufügen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for default step…"
msgstr "Suche nach dem Standard-Schritt…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If all of the above conditions failed."
msgstr "Wenn alle oben genannten Bedingungen fehlgeschlagen sind."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label"
msgstr "Etikett"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "ID"
msgstr "ID"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable"
msgstr "Aktivieren"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add New Field"
msgstr "Neues Feld hinzufügen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Date & Time"
msgstr "Datum & Uhrzeit"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Custom Field"
msgstr "Benutzerdefiniertes Feld hinzufügen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "TextArea"
msgstr "Textbereich"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Number"
msgstr "Zahl"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Checkbox"
msgstr "Kontrollkästchen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Radio"
msgstr "Radio"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Select"
msgstr "Auswählen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Hidden"
msgstr "Versteckt"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add To"
msgstr "Hinzufügen zu"

#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
#. translators: %$s is replaced with the HTML tag
msgid "Label %1$s*%2$s"
msgstr "Beschriftung %1$s*%2$s"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#. translators: %$s is replaced with the HTML tag
msgid ""
"Field value will store in this meta key. Add field id without prefix like \"billing_\" or \"shipping_\". %s Use \"_\" "
"instead of spaces."
msgstr ""
"Der Feldwert wird in diesem Metaschlüssel gespeichert. Fügen Sie die Feld-ID ohne Präfix wie \"billing_\" oder "
"\"shipping_\" hinzu. %s Verwenden Sie \"_\" anstelle von Leerzeichen."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min Value"
msgstr "Mindestwert"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max Value"
msgstr "Maximalwert"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enter your options separated by (|)."
msgstr "Geben Sie Ihre Optionen getrennt durch (|) ein."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "UnChecked"
msgstr "Deaktiviert"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Input Type"
msgstr "Feld-Eingabetyp"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:50
msgid "Date"
msgstr "Datum"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Time"
msgstr "Zeit"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min "
msgstr "Min"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max "
msgstr "Max"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Show in Email"
msgstr "In E-Mail anzeigen"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Billing Fields"
msgstr "Abrechnungsfelder"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Shipping Fields"
msgstr "Versandfelder"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Delete Field"
msgstr "Feld löschen"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Are you really want to delete field?"
msgstr "Möchten Sie das Feld wirklich löschen?"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Editor"
msgstr "Feldeditor"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Label is required field"
msgstr "Etikett ist ein Pflichtfeld"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "WooCommerce is Required!"
msgstr "WooCommerce ist erforderlich!"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
#. translators: %s: step type
msgid "To modify the %s step options, please install and activate the WooCommerce plugin."
msgstr "Um die %s Schrittoptionen zu ändern, installieren und aktivieren Sie bitte das WooCommerce-Plugin."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activating…"
msgstr "Aktivierung…"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activated"
msgstr "Aktiviert"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Funnel Settings"
msgstr "Trichtereinstellungen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Save Setting"
msgstr "Einstellung speichern"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Disable Store Checkout"
msgstr "Store-Checkout deaktivieren"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Enable Store Checkout"
msgstr "Store-Checkout aktivieren"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Edit Title"
msgstr "Titel bearbeiten"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Publish or Draft the Funnel"
msgstr "Trichter veröffentlichen oder als Entwurf speichern"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Publish"
msgstr "Veröffentlichen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to publish this funnel?"
msgstr "Möchten Sie diesen Funnel wirklich veröffentlichen?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Draft Funnel"
msgstr "Entwurfs-Trichter"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to draft this funnel?"
msgstr "Möchten Sie diesen Trichter wirklich entwerfen?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Unique Visits"
msgstr "Einzigartige Besuche"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Conversion Rate"
msgstr "Konversionsrate"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total number of orders."
msgstr "Gesamtanzahl der Bestellungen."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all orders."
msgstr "Gesamtsumme aller Bestellungen."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Average total of every order."
msgstr "Durchschnittlicher Gesamtbetrag jeder Bestellung."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all order bumps."
msgstr "Gesamtsumme aller Bestellaufschläge."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Export Flow"
msgstr "Exportfluss"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "View Funnel"
msgstr "Trichter anzeigen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Open Funnel Settings"
msgstr "Trichtereinstellungen öffnen"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Delete Store Checkout"
msgstr "Kassenbereich des Geschäfts löschen"

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
#. translators: %s new line break
msgid "Do you really want to delete store checkout?%1$1sNOTE: This action cannot be reversed."
msgstr ""
"Möchten Sie den Kassenbereich des Geschäfts wirklich löschen?%1$1sHINWEIS: Diese Aktion kann nicht rückgängig gemacht "
"werden."

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "archived_date"
msgstr "archived_date"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
#. translators: %1$s: html tag, %2$s: html tag
msgid "%1$sNote:%2$s The orders which are placed by the admins are not considered while calculating the analytics."
msgstr ""
"%1$sHinweis:%2$s Die Bestellungen, die von den Administratoren aufgegeben werden, werden bei der Berechnung der "
"Analysen nicht berücksichtigt."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Reset Analytics"
msgstr "Analysen zurücksetzen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Are you really want to reset funnel analytics?"
msgstr "Möchten Sie die Trichteranalysen wirklich zurücksetzen?"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Resetting"
msgstr "Zurücksetzen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Automation for"
msgstr "Automatisierung für"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Create a distraction free, high-converting checkout experience without needing a page builder."
msgstr "Erstellen Sie ein ablenkungsfreies, hochkonvertierendes Checkout-Erlebnis, ohne einen Seitenersteller zu benötigen."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Funnel Steps"
msgstr "Trichterstufen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add New Step"
msgstr "Neuen Schritt hinzufügen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "No Steps Added."
msgstr "Keine Schritte hinzugefügt."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Seems like there are no steps created or added in this flow"
msgstr "Es scheint, dass in diesem Ablauf keine Schritte erstellt oder hinzugefügt wurden"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add new step"
msgstr "Neuen Schritt hinzufügen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Select Step Type"
msgstr "Schrittart auswählen"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Activating WooCommerce.."
msgstr "WooCommerce wird aktiviert.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installing WooCommerce.."
msgstr "WooCommerce wird installiert.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Installed!"
msgstr "Erfolgreich installiert!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installation Failed!"
msgstr "Installation fehlgeschlagen!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Step"
msgstr "Importschritt"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
msgid "Add multiple steps to your flows today with an upgraded CartFlows plan."
msgstr "Fügen Sie heute mehrere Schritte zu Ihren Abläufen mit einem erweiterten CartFlows-Plan hinzu."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
msgid "Get CartFlows Higher Plan"
msgstr "Holen Sie sich den höheren Plan von CartFlows"

#: admin-core/assets/build/editor-app.js:44
#: admin-core/assets/build/editor-app.js:45
#: admin-core/assets/build/editor-app.js:54
#: admin-core/assets/build/editor-app.js:55
#: admin-core/assets/build/editor-app.js:59
#: admin-core/assets/build/editor-app.js:63
#: admin-core/assets/build/editor-app.js:64
#: admin-core/assets/build/settings-app.js:56
#: admin-core/assets/build/settings-app.js:57
#: admin-core/assets/build/settings-app.js:66
#: admin-core/assets/build/settings-app.js:67
#: admin-core/assets/build/settings-app.js:70
#: admin-core/assets/build/settings-app.js:74
#: admin-core/assets/build/settings-app.js:75
#. translators: %s is replaced with plugin name
msgid "Add multiple steps to your flows by activating %s."
msgstr "Fügen Sie mehrere Schritte zu Ihren Abläufen hinzu, indem Sie %s aktivieren."

#: admin-core/assets/build/editor-app.js:46
#: admin-core/assets/build/editor-app.js:47
#: admin-core/assets/build/editor-app.js:56
#: admin-core/assets/build/editor-app.js:60
#: admin-core/assets/build/editor-app.js:65
#: admin-core/assets/build/settings-app.js:58
#: admin-core/assets/build/settings-app.js:59
#: admin-core/assets/build/settings-app.js:68
#: admin-core/assets/build/settings-app.js:71
#: admin-core/assets/build/settings-app.js:76
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating %2$s."
msgstr "Fügen Sie %1$s Schritt zu Ihren Abläufen hinzu, indem Sie %2$s aktivieren."

#: admin-core/assets/build/editor-app.js:48
#: admin-core/assets/build/editor-app.js:49
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:60
#: admin-core/assets/build/settings-app.js:61
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click %s to your flows when you upgrade to our CartFlows Higher plan today."
msgstr ""
"Fügen Sie unbegrenzt einkommenssteigernde Ein-Klick-%s zu Ihren Abläufen hinzu, wenn Sie heute auf unseren CartFlows "
"Higher-Plan upgraden."

#: admin-core/assets/build/editor-app.js:50
#: admin-core/assets/build/settings-app.js:62
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flow by activating CartFlows license."
msgstr "Fügen Sie %1$s Schritt zu Ihrem Ablauf hinzu, indem Sie die CartFlows-Lizenz aktivieren."

#: admin-core/assets/build/editor-app.js:51
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:63
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s."
msgstr "Greifen Sie auf alle unsere Pro-Vorlagen zu, indem Sie %s aktivieren."

#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Access all of our pro templates when you upgrade your plan to CartFlows Pro today."
msgstr "Greifen Sie auf alle unsere Pro-Vorlagen zu, wenn Sie Ihr Abonnement noch heute auf CartFlows Pro upgraden."

#: admin-core/inc/admin-menu.php:318
#: admin-core/inc/admin-menu.php:319
#: classes/class-cartflows-admin.php:250
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Get CartFlows Pro"
msgstr "Holen Sie sich CartFlows Pro"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating license."
msgstr "Fügen Sie %1$s Schritt zu Ihren Abläufen hinzu, indem Sie die Lizenz aktivieren."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:78
msgid "You need WooCommerce plugin installed and activated to import this step."
msgstr "Sie benötigen das WooCommerce-Plugin, das installiert und aktiviert ist, um diesen Schritt zu importieren."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Imported! Redirecting…"
msgstr "Importiert! Weiterleitung…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid " Please sync the library and try importing the template again."
msgstr "Bitte synchronisieren Sie die Bibliothek und versuchen Sie, die Vorlage erneut zu importieren."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Failed! Try again."
msgstr "Import fehlgeschlagen! Versuchen Sie es erneut."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create Step"
msgstr "Schritt erstellen"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Creating Step.."
msgstr "Schritt wird erstellt.."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Step Created! Redirecting…"
msgstr "Schritt erstellt! Weiterleitung…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
msgid "Failed to Create Step!"
msgstr "Erstellen des Schritts fehlgeschlagen!"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate license for adding more steps and other features."
msgstr "Aktivieren Sie die Lizenz, um weitere Schritte und andere Funktionen hinzuzufügen."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Close the window"
msgstr "Schließe das Fenster"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Name Your Step"
msgstr "Nennen Sie Ihren Schritt"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Step Name"
msgstr "Schrittname"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Enter Step Name"
msgstr "Schrittname eingeben"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Learn How"
msgstr "Erfahren Sie, wie"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create from Scratch"
msgstr "Von Grund auf neu erstellen"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Step thumbnail image"
msgstr "Schritt Miniaturbild"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Import"
msgstr "Importieren"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
#: wizard/assets/build/wizard-app.js:1
msgid "Back"
msgstr "Zurück"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/settings-app.js:34
msgid "Sync Library"
msgstr "Bibliothek synchronisieren"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
#. translators: %d is replaced with the condition number
msgid "Importing page %d"
msgstr "Seite %d wird importiert"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Sync Complete"
msgstr "Synchronisierung abgeschlossen"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Syncing Library…"
msgstr "Bibliothek wird synchronisiert…"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Library Synced"
msgstr "Bibliothek synchronisiert"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Steps Library"
msgstr "Schritte-Bibliothek"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:69
msgid "Get CartFlows Higher plan"
msgstr "Holen Sie sich den höheren CartFlows-Plan"

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Activate license for adding more flows and other features."
msgstr "Lizenz aktivieren, um weitere Flows und andere Funktionen hinzuzufügen."

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Importing Step.."
msgstr "Schritt wird importiert.."

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Close"
msgstr "Schließen"

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Error"
msgstr "Fehler"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click upsells to your flows by activating %s"
msgstr "Fügen Sie Ihren Abläufen unbegrenzte einkommenssteigernde One-Click-Upsells hinzu, indem Sie %s aktivieren"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
msgid "Add unlimited income boosting one-click upsells to your flows when you upgrade to our CartFlows Plus or Pro plan today."
msgstr ""
"Fügen Sie unbegrenzt einkommenssteigernde One-Click-Upsells zu Ihren Abläufen hinzu, wenn Sie heute auf unseren "
"CartFlows Plus- oder Pro-Plan upgraden."

#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:79
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s"
msgstr "Greifen Sie auf alle unsere Pro-Vorlagen zu, indem Sie %s aktivieren"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:80
#. translators: %s is replaced with the step title
msgid "Templates for %s"
msgstr "Vorlagen für %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait…"
msgstr "Bitte warten…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating funnel…"
msgstr "Bitte warten. Trichter wird dupliziert…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Drafting funnel…"
msgstr "Bitte warten. Trichter wird entworfen…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting funnel…"
msgstr "Bitte warten. Trichter wird gelöscht…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Restoring funnel…"
msgstr "Bitte warten. Trichter wird wiederhergestellt…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Exporting…"
msgstr "Bitte warten. Exportieren…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating step…"
msgstr "Bitte warten. Schritt wird dupliziert…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting step…"
msgstr "Bitte warten. Schritt wird gelöscht…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Creating variation…"
msgstr "Bitte warten. Variation wird erstellt…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Archiving variation…"
msgstr "Bitte warten. Variation wird archiviert…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Declaring winner…"
msgstr "Bitte warten. Gewinner wird ermittelt…"

#: admin-core/assets/build/settings-app.js:23
msgid "Getting Started"
msgstr "Erste Schritte"

#: admin-core/assets/build/settings-app.js:23
msgid "Introduction to CartFlows"
msgstr "Einführung in CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Modernizing WordPress eCommerce!"
msgstr "Modernisierung des WordPress-E-Commerce!"

#: admin-core/assets/build/settings-app.js:23
msgid "Create Your First Flow"
msgstr "Erstellen Sie Ihren ersten Flow"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Setup Wizard"
msgstr "Zum Einrichtungsassistenten gehen"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:34
msgid "Import Funnel"
msgstr "Importtrichter"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Click for more info"
msgstr "Klicken Sie für mehr Informationen"

#: admin-core/assets/build/settings-app.js:23
msgid "You need WooCommerce plugin installed and activated to import this funnel."
msgstr "Sie müssen das WooCommerce-Plugin installiert und aktiviert haben, um diesen Funnel zu importieren."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates by activating CartFlows Pro."
msgstr "Greifen Sie auf alle unsere Pro-Vorlagen zu, indem Sie CartFlows Pro aktivieren."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates when you activate CartFlows Pro license."
msgstr "Greifen Sie auf alle unsere Pro-Vorlagen zu, wenn Sie die CartFlows Pro-Lizenz aktivieren."

#: admin-core/assets/build/settings-app.js:23
msgid "Importing Complete Funnel.."
msgstr "Importieren des vollständigen Trichters.."

#: admin-core/assets/build/settings-app.js:23
msgid "Design Your Funnel"
msgstr "Gestalten Sie Ihren Trichter"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Created! Redirecting…"
msgstr "Erstellt! Weiterleitung…"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Failed to Create Flow!"
msgstr "Erstellung des Flows fehlgeschlagen!"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To CartFlows Pro"
msgstr "Auf CartFlows Pro upgraden"

#: admin-core/assets/build/settings-app.js:23
msgid "Name Your Funnel"
msgstr "Bennenne deinen Trichter"

#: admin-core/assets/build/settings-app.js:23
msgid "Funnel Name"
msgstr "Trichtername"

#: admin-core/assets/build/settings-app.js:23
msgid "Enter Funnel Name"
msgstr "Trichtername eingeben"

#: admin-core/assets/build/settings-app.js:24
msgid "Welcome to CartFlows "
msgstr "Willkommen bei CartFlows"

#: admin-core/assets/build/settings-app.js:24
msgid "Sales funnel builder turns your WordPress website into an optimized selling machine."
msgstr "Der Sales-Funnel-Builder verwandelt Ihre WordPress-Website in eine optimierte Verkaufsmaschine."

#: admin-core/assets/build/settings-app.js:24
msgid "Create Your First Funnel"
msgstr "Erstellen Sie Ihren ersten Funnel"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"A sales funnel is the sequence of steps a buyer takes to make a purchase. CartFlows helps optimize funnels to turn "
"visitors into customers."
msgstr ""
"Ein Verkaufstrichter ist die Abfolge von Schritten, die ein Käufer unternimmt, um einen Kauf zu tätigen. CartFlows "
"hilft dabei, Trichter zu optimieren, um Besucher in Kunden zu verwandeln."

#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
msgid "Create New Funnel"
msgstr "Neuen Funnel erstellen"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views"
msgstr "Gesamtseitenaufrufe"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Revenue"
msgstr "Gesamteinnahmen"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Orders"
msgstr "Gesamtbestellungen"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue"
msgstr "Angebotsumsatz"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Views"
msgstr "Gesamtansichten"

#: admin-core/assets/build/settings-app.js:24
msgid "Overview"
msgstr "Übersicht"

#: admin-core/assets/build/settings-app.js:24
msgid "WooCommerce plugin is required."
msgstr "WooCommerce-Plugin ist erforderlich."

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the overview"
msgstr "Sie benötigen das WooCommerce-Plugin, das installiert und aktiviert ist, um die Übersicht anzuzeigen"

#: admin-core/assets/build/settings-app.js:24
msgid "Recent Orders"
msgstr "Neueste Bestellungen"

#: admin-core/assets/build/settings-app.js:24
msgid "View All"
msgstr "Alle anzeigen"

#: admin-core/assets/build/settings-app.js:24
msgid "Customer"
msgstr "Kunde"

#: admin-core/assets/build/settings-app.js:24
msgid "Payment Method"
msgstr "Zahlungsmethode"

#: admin-core/assets/build/settings-app.js:24
msgid "Value"
msgstr "Wert"

#: admin-core/assets/build/settings-app.js:24
msgid "at"
msgstr "bei"

#: admin-core/assets/build/settings-app.js:24
msgid "Find recent order here"
msgstr "Finden Sie die letzte Bestellung hier"

#: admin-core/assets/build/settings-app.js:24
msgid "Once you have received orders, come back here to find it again easily"
msgstr "Sobald Sie Bestellungen erhalten haben, kommen Sie hierher zurück, um sie leicht wiederzufinden"

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the recent orders"
msgstr "Sie benötigen das WooCommerce-Plugin, das installiert und aktiviert ist, um die neuesten Bestellungen anzuzeigen"

#: admin-core/assets/build/settings-app.js:24
msgid "Quick Actions"
msgstr "Schnellaktionen"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Funnel"
msgstr "Erstellen Sie einen Trichter"

#: admin-core/assets/build/settings-app.js:80
msgid "Analytics"
msgstr "Analytik"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Product"
msgstr "Ein Produkt erstellen"

#: admin-core/assets/build/settings-app.js:24
msgid "Create new Product"
msgstr "Neues Produkt erstellen"

#: admin-core/assets/build/settings-app.js:24
msgid "All Funnels"
msgstr "Alle Trichter"

#: admin-core/assets/build/settings-app.js:24
msgid "View all funnels"
msgstr "Alle Trichter anzeigen"

#: admin-core/assets/build/settings-app.js:24
msgid "Previous"
msgstr "Zurück"

#: admin-core/assets/build/settings-app.js:24
#: wizard/assets/build/wizard-app.js:1
msgid "Next"
msgstr "Weiter"

#: admin-core/assets/build/settings-app.js:24
msgid "First"
msgstr "Erste"

#: admin-core/assets/build/settings-app.js:24
msgid "Last"
msgstr "Letzte"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"You can specify a file to import by either dragging it into the drag and drop area.(Maximum file size of 5MB; .json "
"file extensions only.)"
msgstr ""
"Sie können eine Datei zum Importieren angeben, indem Sie sie in den Drag-and-Drop-Bereich ziehen. (Maximale Dateigröße "
"von 5 MB; nur .json-Dateierweiterungen.)"

#: admin-core/assets/build/settings-app.js:24
msgid "Change a file"
msgstr "Eine Datei ändern"

#: admin-core/assets/build/settings-app.js:24
msgid "JSON file up to 5MB"
msgstr "JSON-Datei bis zu 5 MB"

#: admin-core/assets/build/settings-app.js:25
#. translators: %s is replaced with the file name.
msgid "File Selected: %s"
msgstr "Datei ausgewählt: %s"

#: admin-core/assets/build/settings-app.js:25
msgid "Please select the valid json file."
msgstr "Bitte wählen Sie die gültige JSON-Datei aus."

#: admin-core/assets/build/settings-app.js:25
#: wizard/assets/build/wizard-app.js:5
msgid "Importing.."
msgstr "Importieren.."

#: admin-core/assets/build/settings-app.js:25
msgid "Export All"
msgstr "Alle exportieren"

#: admin-core/assets/build/settings-app.js:25
msgid "Exporting…"
msgstr "Exportieren…"

#: admin-core/assets/build/settings-app.js:25
msgid "Search Funnels"
msgstr "Suchtrichter"

#: admin-core/assets/build/settings-app.js:25
msgid "Filter Funnels by Date"
msgstr "Trichter nach Datum filtern"

#: admin-core/assets/build/settings-app.js:25
msgid "Publish "
msgstr "Veröffentlichen"

#: admin-core/assets/build/settings-app.js:25
msgid "Draft "
msgstr "Entwurf"

#: admin-core/assets/build/settings-app.js:25
msgid "Trash "
msgstr "Müll"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:33
msgid "Mode"
msgstr "Modus"

#: admin-core/assets/build/settings-app.js:25
msgid "All"
msgstr "Alle"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
msgid "Live"
msgstr "Leben"

#: admin-core/assets/build/settings-app.js:25
msgid "SandBox"
msgstr "SandBox"

#: admin-core/assets/build/settings-app.js:25
msgid "Reset Filters"
msgstr "Filter zurücksetzen"

#: admin-core/assets/build/settings-app.js:28
#. translators: %s: action name.
msgid "%s This Flow"
msgstr "%s Dieser Flow"

#: admin-core/assets/build/settings-app.js:31
#. translators: %s: action status name.
msgid "Do you want to %s this flow? Are you sure?"
msgstr "Möchten Sie diesen Ablauf %s? Sind Sie sicher?"

#: admin-core/assets/build/settings-app.js:31
msgid "items selected"
msgstr "ausgewählte Artikel"

#: admin-core/assets/build/settings-app.js:31
msgid "Applying changes…"
msgstr "Änderungen werden angewendet…"

#: admin-core/assets/build/settings-app.js:31
msgid " Published "
msgstr "Veröffentlicht"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate Funnel"
msgstr "Trichter duplizieren"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to duplicate this funnel?"
msgstr "Möchten Sie diesen Funnel wirklich duplizieren?"

#: admin-core/assets/build/settings-app.js:31
msgid "Trash Funnel"
msgstr "Mülltrichter"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this funnel?"
msgstr "Möchten Sie diesen Trichter wirklich löschen?"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this Funnel?"
msgstr "Möchten Sie diesen Funnel wirklich löschen?"

#: admin-core/assets/build/settings-app.js:31
msgid "Restore Funnel"
msgstr "Trichter wiederherstellen"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to restore this funnel?"
msgstr "Möchten Sie diesen Trichter wirklich wiederherstellen?"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Draft"
msgstr "Entwurf"

#: admin-core/assets/build/settings-app.js:32
#. translators: %s date
msgid "Last Modified: %s"
msgstr "Zuletzt geändert: %s"

#: admin-core/assets/build/settings-app.js:32
msgid "Updated "
msgstr "Aktualisiert"

#: admin-core/assets/build/settings-app.js:32
msgid "Sandbox"
msgstr "Sandkasten"

#: admin-core/assets/build/settings-app.js:32
msgid "WooCommerce Required to display the revenue."
msgstr "WooCommerce erforderlich, um den Umsatz anzuzeigen."

#: admin-core/assets/build/settings-app.js:32
msgid "Restore Flow"
msgstr "Fluss wiederherstellen"

#: admin-core/assets/build/settings-app.js:32
msgid "Delete Flow"
msgstr "Flow löschen"

#: admin-core/assets/build/settings-app.js:32
msgid "Upgrade to Pro for this feature."
msgstr "Upgrade auf Pro für diese Funktion."

#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate (Pro)"
msgstr "Duplizieren (Pro)"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash Flow"
msgstr "Müllfluss"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash"
msgstr "Müll"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Name"
msgstr "Name"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Sales"
msgstr "Vertrieb"

#: admin-core/assets/build/settings-app.js:32
msgid "Move to Trash"
msgstr "In den Papierkorb verschieben"

#: admin-core/assets/build/settings-app.js:33
#. translators: %d Search term
msgid "No matching results found for the search term \"%s\"."
msgstr "Keine übereinstimmenden Ergebnisse für den Suchbegriff „%s“ gefunden."

#: admin-core/assets/build/settings-app.js:33
msgid "No flows found for the selected filter."
msgstr "Keine Flows für den ausgewählten Filter gefunden."

#: admin-core/assets/build/settings-app.js:33
msgid "Please try using different keywords, date range, or filters to refine your results."
msgstr ""
"Bitte versuchen Sie, andere Schlüsselwörter, einen anderen Datumsbereich oder Filter zu verwenden, um Ihre Ergebnisse "
"zu verfeinern."

#: admin-core/assets/build/settings-app.js:33
msgid "Create New"
msgstr "Neu erstellen"

#: admin-core/assets/build/settings-app.js:34
#. translators: %d flow count
msgid " %d items"
msgstr "%d Artikel"

#: admin-core/assets/build/settings-app.js:34
msgid "Create your first funnel"
msgstr "Erstellen Sie Ihren ersten Trichter"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Build a sales funnel with everything you need to generate leads and grow sales."
msgstr "Erstellen Sie einen Verkaufstrichter mit allem, was Sie benötigen, um Leads zu generieren und den Umsatz zu steigern."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "One Click Upsells"
msgstr "Ein-Klick-Upsells"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "A/B Split Testing"
msgstr "A/B-Split-Tests"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Conversion Templates"
msgstr "Konvertierungsvorlagen"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Editor"
msgstr "Kasseneditor"

#: admin-core/assets/build/settings-app.js:34
msgid "Insights"
msgstr "Einblicke"

#: admin-core/assets/build/settings-app.js:34
msgid "Create Funnel"
msgstr "Trichter erstellen"

#: admin-core/assets/build/settings-app.js:34
msgid "Plugin Required"
msgstr "Plugin erforderlich"

#: admin-core/assets/build/settings-app.js:34
msgid "You need WooCommerce plugin installed and activated to access this page."
msgstr "Sie müssen das WooCommerce-Plugin installiert und aktiviert haben, um auf diese Seite zuzugreifen."

#: admin-core/assets/build/settings-app.js:34
msgid "Installing"
msgstr "Installieren"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
msgid "Activating"
msgstr "Aktivieren"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Failed"
msgstr "Fehlgeschlagen"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Redirecting"
msgstr "Weiterleitung"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Create Store Checkout"
msgstr "Store-Checkout erstellen"

#: admin-core/assets/build/settings-app.js:34
msgid "Name Your Store Checkout"
msgstr "Nennen Sie Ihren Store Checkout"

#: admin-core/assets/build/settings-app.js:34
msgid "You can't create more than 3 flows in free version. Upgrade to CartFlows Pro for adding more flows and other features."
msgstr ""
"Sie können in der kostenlosen Version nicht mehr als 3 Flows erstellen. Upgraden Sie auf CartFlows Pro, um mehr Flows "
"und andere Funktionen hinzuzufügen."

#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To Cartflows Pro"
msgstr "Auf Cartflows Pro upgraden"

#: admin-core/assets/build/settings-app.js:34
msgid "Store Checkout Name"
msgstr "Speicher-Kassenname"

#: admin-core/assets/build/settings-app.js:34
msgid "Enter Store Checkout Name"
msgstr "Geben Sie den Namen des Kassenbereichs ein"

#: admin-core/assets/build/settings-app.js:34
msgid "Create a global store checkout"
msgstr "Erstellen Sie einen globalen Kassenbereich"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"A well-designed checkout page can help streamline the checkout process, reduce cart abandonment rates and increase "
"conversions."
msgstr ""
"Eine gut gestaltete Checkout-Seite kann den Checkout-Prozess optimieren, die Abbruchrate verringern und die "
"Konversionsrate erhöhen."

#: admin-core/assets/build/settings-app.js:34
msgid "Improved user experience"
msgstr "Verbesserte Benutzererfahrung"

#: admin-core/assets/build/settings-app.js:34
msgid "Brand consistency"
msgstr "Markenkonsistenz"

#: admin-core/assets/build/settings-app.js:34
msgid "Increased trust and credibility"
msgstr "Erhöhtes Vertrauen und Glaubwürdigkeit"

#: admin-core/assets/build/settings-app.js:34
msgid "Flexibility and customization"
msgstr "Flexibilität und Anpassung"

#: admin-core/assets/build/settings-app.js:34
msgid "Competitive advantage"
msgstr "Wettbewerbsvorteil"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"By setting up the store checkout, your default checkout page will be replaced by the CartFlows modern checkout which "
"will lead to more conversion and leads."
msgstr ""
"Durch das Einrichten des Store-Checkouts wird Ihre Standard-Checkout-Seite durch den modernen CartFlows-Checkout "
"ersetzt, was zu mehr Konversionen und Leads führt."

#: admin-core/assets/build/settings-app.js:34
msgid "Get Started"
msgstr "Loslegen"

#: admin-core/assets/build/settings-app.js:34
msgid "Connect a Payment Gateway"
msgstr "Ein Zahlungsgateway verbinden"

#: admin-core/assets/build/settings-app.js:34
msgid "Stripe for WooCommerce delivers a simple, secure way to accept credit card payments in your WooCommerce store."
msgstr ""
"Stripe für WooCommerce bietet eine einfache und sichere Möglichkeit, Kreditkartenzahlungen in Ihrem WooCommerce-Shop zu "
"akzeptieren."

#: admin-core/assets/build/settings-app.js:34
msgid "Connect with Stripe"
msgstr "Mit Stripe verbinden"

#: admin-core/assets/build/settings-app.js:34
msgid "Setting up…"
msgstr "Einrichten…"

#: admin-core/assets/build/settings-app.js:34
msgid "Recover Abandoned Carts"
msgstr "Verlassene Warenkörbe wiederherstellen"

#: admin-core/assets/build/settings-app.js:34
msgid "Use our cart abandonment plugin and automatically recover your lost revenue absolutely free."
msgstr ""
"Verwenden Sie unser Plugin zur Warenkorbabbruch-Vermeidung und gewinnen Sie Ihren verlorenen Umsatz automatisch und "
"völlig kostenlos zurück."

#: admin-core/assets/build/settings-app.js:34
msgid "Finishing…"
msgstr "Fertigstellen…"

#: admin-core/assets/build/settings-app.js:34
msgid "Setup Email Reports"
msgstr "E-Mail-Berichte einrichten"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send you an email report with "
"key metrics and insights."
msgstr ""
"Lassen Sie CartFlows die Vermutungen aus Ihren Checkout-Ergebnissen entfernen. Jede Woche wird Ihr Geschäft Ihnen einen "
"E-Mail-Bericht mit wichtigen Kennzahlen und Erkenntnissen senden."

#: admin-core/assets/build/settings-app.js:34
msgid "Add Email Address"
msgstr "E-Mail-Adresse hinzufügen"

#: admin-core/assets/build/settings-app.js:34
msgid "Dismiss Setup"
msgstr "Einrichtung verwerfen"

#: admin-core/assets/build/settings-app.js:34
msgid "Active"
msgstr "Aktiv"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Activate"
msgstr "Aktivieren"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Install"
msgstr "Installieren"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Installing…"
msgstr "Installieren…"

#: admin-core/assets/build/settings-app.js:34
msgid "Installed"
msgstr "Installiert"

#: admin-core/assets/build/settings-app.js:34
msgid "Let's Go"
msgstr "Lass uns gehen"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Plugins"
msgstr "Empfohlene Plugins"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Themes"
msgstr "Empfohlene Themen"

#: admin-core/assets/build/settings-app.js:34
msgid "View All Steps"
msgstr "Alle Schritte anzeigen"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel thumbnail image"
msgstr "Trichter-Vorschaubild"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel Preview"
msgstr "Trichtervorschau"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Live Preview"
msgstr "Live-Vorschau"

#: admin-core/assets/build/settings-app.js:34
msgid "Funnel Templates"
msgstr "Trichtervorlagen"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Start from scratch"
msgstr "Von Grund auf neu beginnen"

#: admin-core/assets/build/settings-app.js:34
msgid "It seems that the page builder you selected is inactive."
msgstr "Es scheint, dass der von Ihnen ausgewählte Seitenersteller inaktiv ist."

#: admin-core/assets/build/settings-app.js:34
msgid " to see CartFlows templates. If you prefer another page builder tool, you can "
msgstr "um CartFlows-Vorlagen zu sehen. Wenn Sie ein anderes Page-Builder-Tool bevorzugen, können Sie"

#: admin-core/assets/build/settings-app.js:34
msgid "select it here"
msgstr "wählen Sie es hier aus"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using any other page builder? No worries. CartFlows works well with every other page builder. Right now we do "
"not have ready templates for every page builder but we are planning to add it very soon."
msgstr ""
"Verwenden Sie einen anderen Page Builder? Keine Sorge. CartFlows funktioniert gut mit jedem anderen Page Builder. "
"Derzeit haben wir noch keine fertigen Vorlagen für jeden Page Builder, aber wir planen, diese sehr bald hinzuzufügen."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Learn How "
msgstr "Erfahren Sie, wie"

#: admin-core/assets/build/settings-app.js:35
msgid "No Results Found."
msgstr "Keine Ergebnisse gefunden."

#: admin-core/assets/build/settings-app.js:35
msgid "Don't see a funnel that you would like to import?"
msgstr "Sie sehen keinen Trichter, den Sie importieren möchten?"

#: admin-core/assets/build/settings-app.js:35
msgid "Please suggest us "
msgstr "Bitte schlagen Sie uns vor"

#: admin-core/assets/build/settings-app.js:35
msgid "Choose a Funnel Templates"
msgstr "Wählen Sie eine Trichtervorlage"

#: admin-core/assets/build/settings-app.js:35
msgid "Search Templates"
msgstr "Vorlagen durchsuchen"

#: admin-core/assets/build/settings-app.js:35
msgid "Start from Scratch"
msgstr "Von vorne anfangen"

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the Bricks Builder."
msgstr "Es scheint, dass Sie den Bricks Builder verwenden."

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the page builder other than Elementor, Beaver Builder, Block Builder."
msgstr "Es scheint, dass Sie einen anderen Seiten-Builder als Elementor, Beaver Builder, Block Builder verwenden."

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using Bricks Builder? No worries. CartFlows works well with Bricks Builder. Right now we do not have ready "
"templates for Bricks Builder but we are planning to add it very soon."
msgstr ""
"Verwenden Sie Bricks Builder? Keine Sorge. CartFlows funktioniert gut mit Bricks Builder. Derzeit haben wir keine "
"fertigen Vorlagen für Bricks Builder, aber wir planen, diese sehr bald hinzuzufügen."

#: admin-core/assets/build/settings-app.js:35
msgid "Checkout Page"
msgstr "Kassenseite"

#: admin-core/assets/build/settings-app.js:35
msgid "Oops!!! No template Found."
msgstr "Hoppla!!! Keine Vorlage gefunden."

#: admin-core/assets/build/settings-app.js:35
msgid "Seems like no template is available for chosen editor."
msgstr "Es scheint, dass keine Vorlage für den gewählten Editor verfügbar ist."

#: admin-core/assets/build/settings-app.js:35
msgid "Store Checkout Templates"
msgstr "Vorlagen für den Kassenbereich"

#: admin-core/assets/build/settings-app.js:80
msgid "No CartFlows Logs Found."
msgstr "Keine CartFlows-Protokolle gefunden."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Logs"
msgstr "CartFlows-Protokolle"

#: admin-core/assets/build/settings-app.js:80
msgid "Copied"
msgstr "Kopiert"

#: admin-core/assets/build/settings-app.js:80
msgid "Copy"
msgstr "Kopieren"

#: admin-core/assets/build/settings-app.js:80
msgid "Downloading"
msgstr "Herunterladen"

#: admin-core/assets/build/settings-app.js:80
msgid "Download"
msgstr "Herunterladen"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleting"
msgstr "Löschen"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleted"
msgstr "Gelöscht"

#: admin-core/assets/build/settings-app.js:80
msgid "Email Marketing Automation"
msgstr "E-Mail-Marketing-Automatisierung"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate email marketing campaigns based on customer actions, such as abandoned carts or completed purchases in "
"WooCommerce."
msgstr ""
"Automatisiere E-Mail-Marketingkampagnen basierend auf Kundenaktionen, wie z. B. abgebrochene Warenkörbe oder "
"abgeschlossene Käufe in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Customer Birthday Campaigns"
msgstr "Kunden-Geburtstagskampagnen"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automatically send personalized birthday offers or discounts to customers based on their birthdate stored in "
"WooCommerce."
msgstr ""
"Senden Sie automatisch personalisierte Geburtstagsangebote oder Rabatte an Kunden basierend auf ihrem in WooCommerce "
"gespeicherten Geburtsdatum."

#: admin-core/assets/build/settings-app.js:80
msgid "Order Notification"
msgstr "Bestellbenachrichtigung"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Receive instant notifications via SMS, Slack, WhatsApp, or messaging apps when new orders are placed in your "
"WooCommerce store."
msgstr ""
"Erhalten Sie sofortige Benachrichtigungen per SMS, Slack, WhatsApp oder Messaging-Apps, wenn neue Bestellungen in Ihrem "
"WooCommerce-Shop eingehen."

#: admin-core/assets/build/settings-app.js:80
msgid "Payment and Accounting Integration"
msgstr "Zahlungs- und Buchhaltungsintegration"

#: admin-core/assets/build/settings-app.js:80
msgid "Sync WooCommerce sales data with your accounting software for streamlined financial management."
msgstr "Synchronisieren Sie WooCommerce-Verkaufsdaten mit Ihrer Buchhaltungssoftware für ein reibungsloses Finanzmanagement."

#: admin-core/assets/build/settings-app.js:80
msgid "Coupon Code Marketing"
msgstr "Gutscheincode-Marketing"

#: admin-core/assets/build/settings-app.js:80
msgid "Automate the creation and distribution of coupon codes based on specific conditions or customer actions in WooCommerce."
msgstr ""
"Automatisieren Sie die Erstellung und Verteilung von Gutscheincodes basierend auf bestimmten Bedingungen oder "
"Kundenaktionen in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Upsell and Cross-sell Campaigns"
msgstr "Upsell- und Cross-Sell-Kampagnen"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate targeted upsell and cross-sell offers based on customers' purchase history or product interactions in "
"WooCommerce."
msgstr ""
"Automatisieren Sie gezielte Upsell- und Cross-Sell-Angebote basierend auf dem Kaufverhalten der Kunden oder "
"Produktinteraktionen in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Connect Your Website"
msgstr "Verbinden Sie Ihre Website"

#: admin-core/assets/build/settings-app.js:80
msgid "Reloading"
msgstr "Neuladen"

#: admin-core/assets/build/settings-app.js:80
msgid "Connecting"
msgstr "Verbinden"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate WooCommerce and CartFlows with Anything"
msgstr "Integrieren Sie WooCommerce und CartFlows mit allem"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate all your apps, plugins, and services to automate repetitive tasks."
msgstr "Integrieren Sie alle Ihre Apps, Plugins und Dienste, um sich wiederholende Aufgaben zu automatisieren."

#: admin-core/assets/build/settings-app.js:80
msgid "These are just some examples. The possibilities are truly endless!"
msgstr "Dies sind nur einige Beispiele. Die Möglichkeiten sind wirklich endlos!"

#: admin-core/assets/build/settings-app.js:80
msgid "Trusted by World's Top Brands to Connect Their Apps"
msgstr "Vertraut von den weltweit führenden Marken, um ihre Apps zu verbinden"

#: admin-core/assets/build/settings-app.js:80
msgid "Connect your apps and automate your business."
msgstr "Verbinden Sie Ihre Apps und automatisieren Sie Ihr Geschäft."

#: modules/gutenberg/build/blocks-placeholder.js:12
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail Form"
msgstr "Bestellformular"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Overview."
msgstr "Bestellübersicht ausblenden/anzeigen."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail"
msgstr "Bestelldetails"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Detail."
msgstr "Bestelldetails ausblenden/anzeigen."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Billing Address."
msgstr "Rechnungsadresse ausblenden/anzeigen."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Shipping Address."
msgstr "Versandadresse ausblenden/anzeigen."

#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text"
msgstr "Überschrift Text"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading Bottom Spacing(px)"
msgstr "Abstand unten der Überschrift (px)"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Opacity"
msgstr "Deckkraft"

#: modules/gutenberg/build/blocks.js:7
msgid "Section Spacing"
msgstr "Abschnittsabstand"

#: modules/gutenberg/build/blocks.js:7
msgid "Download Details"
msgstr "Download-Details"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Loading"
msgstr "Laden"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Order Detail Form Block"
msgstr "CartFlows Bestelldetailformular-Block"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cartflows"
msgstr "cartflows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "order detail form"
msgstr "Bestellformular"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cf"
msgstr "cf"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Color"
msgstr "Symbolfarbe"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Hover Color"
msgstr "Symbol-Schwebefarbe"

#: modules/gutenberg/build/blocks.js:11
msgid "Gap Between Icon And Text"
msgstr "Lücke zwischen Symbol und Text"

#: modules/gutenberg/build/blocks.js:11
msgid "Subtitle"
msgstr "Untertitel"

#: modules/gutenberg/build/blocks.js:11
msgid "Enable Subtitle"
msgstr "Untertitel aktivieren"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Bottom Spacing"
msgstr "Abstand unten bei Titel"

#: modules/gutenberg/build/blocks.js:11
msgid "SubTitle"
msgstr "Untertitel"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Add text…"
msgstr "Text hinzufügen…"

#: modules/gutenberg/build/blocks.js:11
msgid "CartFlows Next Step Button Block."
msgstr "CartFlows Nächster Schritt Button-Block."

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "next step button"
msgstr "Schaltfläche „Nächster Schritt“"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step ( Pro )"
msgstr "Zwei Schritte ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout ( Pro )"
msgstr "Mehrstufiger Checkout (Pro)"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This feature is available in the CartFlows higher plan. Upgrade Now!."
msgstr "Hinweis: Diese Funktion ist im höheren CartFlows-Plan verfügbar. Jetzt upgraden!."

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Skin"
msgstr "Eingabefeld Skin"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Field"
msgstr "Eingabefeld"

#: modules/gutenberg/build/blocks.js:11
msgid "Field Text Color"
msgstr "Feldtextfarbe"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Validation"
msgstr "Eingabefeldvalidierung"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This styling can be only seen at frontend"
msgstr "Hinweis: Diese Gestaltung ist nur im Frontend sichtbar"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Horizontal"
msgstr "Horizontal"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Vertical"
msgstr "Vertikal"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Blur"
msgstr "Unschärfe"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spread"
msgstr "Verbreiten"

#: modules/gutenberg/build/blocks.js:11
msgid "Buttons Text"
msgstr "Schaltflächentext"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Color"
msgstr "Titelfarbe"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Background Color"
msgstr "Titelhintergrundfarbe"

#: modules/gutenberg/build/blocks.js:11
msgid "Desc Background Color"
msgstr "Hintergrundfarbe beschreiben"

#: modules/gutenberg/build/blocks.js:11
msgid "Success/Error Message"
msgstr "Erfolgs-/Fehlermeldung"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Message Color"
msgstr "Nachrichtenfarbe"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label"
msgstr "Schwebendes Etikett"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Skin"
msgstr "Eingabehaut"

#: modules/gutenberg/build/blocks.js:11
msgid "Skin"
msgstr "Haut"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide Advanced"
msgstr "Erweitert ausblenden"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Line Height"
msgstr "Zeilenhöhe"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color Settings"
msgstr "Farbeinstellungen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Overlay Color"
msgstr "Überlagerungsfarbe"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Inset"
msgstr "Einfügen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Outset"
msgstr "Anfang"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing Between Sections(px)"
msgstr "Abstand zwischen Abschnitten (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Type"
msgstr "Hintergrundtyp"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image"
msgstr "Bild"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Image"
msgstr "Hintergrundbild"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Background Image"
msgstr "Hintergrundbild auswählen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Replace image"
msgstr "Bild ersetzen"

#: admin-core/assets/build/editor-app.js:1
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Remove Image"
msgstr "Bild entfernen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image Position"
msgstr "Bildposition"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Left"
msgstr "Oben links"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Center"
msgstr "Oben Mitte"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Right"
msgstr "Oben rechts"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Left"
msgstr "Linke Mitte"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Center"
msgstr "Zentrum Zentrum"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Right"
msgstr "Zentrum rechts"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Left"
msgstr "Unten links"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Center"
msgstr "Unten Mitte"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Right"
msgstr "Unten rechts"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Attachment"
msgstr "Anhang"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Fixed"
msgstr "Fest"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Scroll"
msgstr "Scrollen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat"
msgstr "Wiederholen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "No Repeat"
msgstr "Keine Wiederholung"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-x"
msgstr "Wiederholen-x"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-y"
msgstr "Wiederhole-y"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Auto"
msgstr "Auto"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Cover"
msgstr "Abdeckung"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Contain"
msgstr "Enthalten"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Weight"
msgstr "Schriftstärke"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Subset"
msgstr "Schriftart-Subset"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "px"
msgstr "px"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "em"
msgstr "em"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size Type"
msgstr "Größentyp"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button Block"
msgstr "Nächster Schritt Schaltflächenblock"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Icon"
msgstr "Symbol auswählen"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button Hover Color"
msgstr "Schaltflächen-Hover-Farbe"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Groove"
msgstr "Rille"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Ridge"
msgstr "Grat"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Bottom Spacing (px)"
msgstr "Abstand unten Titel (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover Color"
msgstr "Hover-Farbe"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Transform"
msgstr "Textumwandlung"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Capitalize"
msgstr "Großschreiben"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Uppercase"
msgstr "Großbuchstaben"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Lowercase"
msgstr "Kleinbuchstaben"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Letter Spacing (px)"
msgstr "Buchstabenabstand (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Checkout Block"
msgstr "CartFlows Checkout Block"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "checkout form"
msgstr "Kassenformular"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout ( Pro )"
msgstr "MultiStep Checkout (Pro)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width (px)"
msgstr "Randbreite (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Note: This feature is available in the CartFlows Pro. Upgrade Now!."
msgstr "Hinweis: Diese Funktion ist in CartFlows Pro verfügbar. Jetzt upgraden!."

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input/Text Placeholder Color"
msgstr "Eingabe-/Textplatzhalterfarbe"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius (px)"
msgstr "Randradius (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Padding (px)"
msgstr "Abschnittspolsterung (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Margin (px)"
msgstr "Abschnittsmarge (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Success / Error Message"
msgstr "Erfolgs- / Fehlermeldung"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Form success / Error validation"
msgstr "Formularerfolg / Fehlerüberprüfung"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Error Message"
msgstr "Fehlermeldung"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Optin Form Block"
msgstr "CartFlows Optin-Formularblock"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "optin form"
msgstr "Opt-in-Formular"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label ( Pro )"
msgstr "Schwebendes Etikett ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button Text"
msgstr "Text der Schaltfläche \"Absenden\""

#: wizard/assets/build/wizard-app.js:1
msgid "Let's Start"
msgstr "Lass uns anfangen"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 1 of 6"
msgstr "Schritt 1 von 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome to CartFlows"
msgstr "Willkommen bei CartFlows"

#: wizard/assets/build/wizard-app.js:1
msgid ""
"You're only minutes away from having a more profitable WooCommerce store! This short setup wizard will help you get "
"started with CartFlows."
msgstr ""
"Sie sind nur wenige Minuten davon entfernt, einen profitableren WooCommerce-Shop zu haben! Dieser kurze "
"Einrichtungsassistent hilft Ihnen, mit CartFlows zu beginnen."

#: wizard/assets/build/wizard-app.js:1
msgid "Save & Continue"
msgstr "Speichern & Fortfahren"

#: wizard/assets/build/wizard-app.js:1
msgid "Saving"
msgstr "Speichern"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 2 of 6"
msgstr "Schritt 2 von 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Hi there! Tell us which page builder you use."
msgstr "Hallo! Sag uns, welchen Page Builder du benutzt."

#: wizard/assets/build/wizard-app.js:1
msgid "CartFlows works with all page builders, so don't worry if your page builder is not in the list. "
msgstr ""
"CartFlows funktioniert mit allen Page-Buildern, also machen Sie sich keine Sorgen, wenn Ihr Page-Builder nicht in der "
"Liste steht."

#: wizard/assets/build/wizard-app.js:1
msgid "Install & Activate"
msgstr "Installieren & Aktivieren"

#: wizard/assets/build/wizard-app.js:1
msgid "Installing Required Plugins"
msgstr "Installation erforderlicher Plugins"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 3 of 6"
msgstr "Schritt 3 von 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Great job!"
msgstr "Tolle Arbeit!"

#: wizard/assets/build/wizard-app.js:1
msgid "Now let's install some required plugins."
msgstr "Jetzt lass uns einige erforderliche Plugins installieren."

#: wizard/assets/build/wizard-app.js:1
msgid ""
"Since CartFlows uses WooCommerce, we'll set it up for you along with Cart Abandonment and Stripe Payments so you can "
"recover abandoned orders and easily accept payments."
msgstr ""
"Da CartFlows WooCommerce verwendet, richten wir es für Sie zusammen mit Warenkorbabbruch und Stripe-Zahlungen ein, "
"damit Sie verlassene Bestellungen zurückgewinnen und Zahlungen einfach akzeptieren können."

#: wizard/assets/build/wizard-app.js:1
msgid "The following plugins will be installed and activated for you:"
msgstr "Die folgenden Plugins werden für Sie installiert und aktiviert:"

#: wizard/assets/build/wizard-app.js:1
msgid "Continuing…"
msgstr "Fortsetzung…"

#: wizard/assets/build/wizard-app.js:1
msgid "Continue"
msgstr "Fortfahren"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 5 of 6"
msgstr "Schritt 5 von 6"

#: wizard/assets/build/wizard-app.js:2
#. translators: %s: html tag
msgid "One last step. %s Let's setup email reports on how your store is doing."
msgstr "Ein letzter Schritt. %s Lassen Sie uns E-Mail-Berichte einrichten, um zu sehen, wie Ihr Geschäft läuft."

#: wizard/assets/build/wizard-app.js:3
#. translators: %1$s: html tag, %2$s: html tag
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send %1$s you an email report "
"with key metrics and insights. You also will receive emails from us to %2$s help your store sell more."
msgstr ""
"Lassen Sie CartFlows das Rätselraten bei Ihren Checkout-Ergebnissen übernehmen. Jede Woche wird Ihr Geschäft %1$s Ihnen "
"einen E-Mail-Bericht mit wichtigen Kennzahlen und Erkenntnissen senden. Sie erhalten auch E-Mails von uns, um %2$s "
"Ihrem Geschäft zu helfen, mehr zu verkaufen."

#: wizard/assets/build/wizard-app.js:3
msgid "First Name"
msgstr "Vorname"

#: wizard/assets/build/wizard-app.js:3
msgid "Please enter your name"
msgstr "Bitte geben Sie Ihren Namen ein"

#: wizard/assets/build/wizard-app.js:3
msgid "Enter Your Email"
msgstr "Geben Sie Ihre E-Mail ein"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Name"
msgstr "Bitte Namen eingeben"

#: wizard/assets/build/wizard-app.js:4
msgid "Entered email address is not a valid email"
msgstr "Die eingegebene E-Mail-Adresse ist keine gültige E-Mail"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Email ID"
msgstr "Bitte E-Mail-ID eingeben"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome"
msgstr "Willkommen"

#: wizard/assets/build/wizard-app.js:1
msgid "Page Builder"
msgstr "Seiten-Builder"

#: wizard/assets/build/wizard-app.js:1
msgid "Required Plugins"
msgstr "Erforderliche Plugins"

#: wizard/assets/build/wizard-app.js:1
msgid "Done"
msgstr "Fertig"

#: wizard/assets/build/wizard-app.js:1
msgid "Exit setup wizard"
msgstr "Setup-Assistenten beenden"

#: wizard/assets/build/wizard-app.js:1
msgid "Redirecting.."
msgstr "Weiterleitung.."

#: wizard/assets/build/wizard-app.js:1
msgid "Skip"
msgstr "Überspringen"

#: wizard/assets/build/wizard-app.js:1
#: wizard/assets/build/wizard-app.js:5
msgid "Finish Store Setup"
msgstr "Einrichtung des Geschäfts abschließen"

#: wizard/assets/build/wizard-app.js:1
msgid "Select Color"
msgstr "Farbe auswählen"

#: wizard/assets/build/wizard-app.js:5
msgid "Recommended"
msgstr "Empfohlen"

#: wizard/assets/build/wizard-app.js:5
msgid "Upload a Logo"
msgstr "Laden Sie ein Logo hoch"

#: wizard/assets/build/wizard-app.js:5
msgid "Change a Logo"
msgstr "Ein Logo ändern"

#: wizard/assets/build/wizard-app.js:5
msgid "Remove logo"
msgstr "Logo entfernen"

#: wizard/assets/build/wizard-app.js:5
msgid "Suggested Dimensions: 180x60 pixels"
msgstr "Vorgeschlagene Abmessungen: 180x60 Pixel"

#: wizard/assets/build/wizard-app.js:5
msgid "Oops!!! No templates found"
msgstr "Hoppla!!! Keine Vorlagen gefunden"

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Seems like no templates are available for chosen page editor. Don't worry, you can always import the store checkout "
"template from the CartFlows setting menu."
msgstr ""
"Es scheint, dass keine Vorlagen für den ausgewählten Seiteneditor verfügbar sind. Keine Sorge, Sie können die "
"Store-Checkout-Vorlage jederzeit aus dem CartFlows-Einstellungsmenü importieren."

#: wizard/assets/build/wizard-app.js:5
msgid "Skip to Next"
msgstr "Zum Nächsten springen"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 4 of 6"
msgstr "Schritt 4 von 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Awesome"
msgstr "Großartig"

#: wizard/assets/build/wizard-app.js:5
msgid "Now let's setup your new store checkout."
msgstr "Richten wir jetzt die Kasse Ihres neuen Geschäfts ein."

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Choose one of the store checkout designs below. After setup you can change the text and color or even choose an "
"entirely new store checkout design."
msgstr ""
"Wählen Sie eines der unten stehenden Kassen-Designs aus. Nach der Einrichtung können Sie den Text und die Farbe ändern "
"oder sogar ein völlig neues Kassen-Design wählen."

#: wizard/assets/build/wizard-app.js:5
msgid "Import & Continue"
msgstr "Importieren & Fortfahren"

#: wizard/assets/build/wizard-app.js:5
msgid "Processing.."
msgstr "Verarbeitung.."

#: wizard/assets/build/wizard-app.js:5
msgid "Importing Failed.."
msgstr "Importieren fehlgeschlagen.."

#: wizard/assets/build/wizard-app.js:5
msgid "Selected Template:"
msgstr "Ausgewählte Vorlage:"

#: wizard/assets/build/wizard-app.js:5
msgid "Change Primary Color"
msgstr "Primärfarbe ändern"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 6 of 6"
msgstr "Schritt 6 von 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Congratulations, You Did It!"
msgstr "Herzlichen Glückwunsch, du hast es geschafft!"

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows is set up on your website! Please watch the short video below for your next steps."
msgstr ""
"CartFlows ist auf Ihrer Website eingerichtet! Bitte sehen Sie sich das kurze Video unten an, um die nächsten Schritte "
"zu erfahren."

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows Extended Walkthrough Tutorial"
msgstr "CartFlows Erweiterte Schritt-für-Schritt-Anleitung"

#: wizard/assets/build/wizard-app.js:5
msgid "Finishing the Setup"
msgstr "Abschluss der Einrichtung"

#: admin-core/inc/admin-menu.php:1052
msgid "A simple yet powerful way to add content restriction to your website."
msgstr "Eine einfache, aber leistungsstarke Möglichkeit, Inhaltsbeschränkungen zu Ihrer Website hinzuzufügen."

#: classes/class-cartflows-loader.php:368
msgid "Quick Feedback"
msgstr "Schnelles Feedback"

#: classes/class-cartflows-loader.php:370
msgid "If you have a moment, please share why you are deactivating CartFlows:"
msgstr "Wenn Sie einen Moment Zeit haben, teilen Sie uns bitte mit, warum Sie CartFlows deaktivieren:"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unlock Premium Features with CartFlows PRO!"
msgstr "Schalten Sie Premium-Funktionen mit CartFlows PRO frei!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Get the tools you need to create powerful sales funnels, increase conversions, and grow your business with ease."
msgstr ""
"Holen Sie sich die Werkzeuge, die Sie benötigen, um leistungsstarke Verkaufstrichter zu erstellen, die Konversionen zu "
"steigern und Ihr Geschäft mühelos auszubauen."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Select a Settings Tab"
msgstr "Wählen Sie einen Einstellungen-Tab"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Free vs Pro"
msgstr "Frei vs Pro"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Increase Your Revenue with Smart Order Bumps"
msgstr "Steigern Sie Ihren Umsatz mit intelligenten Bestell-Ergänzungen"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Boost sales with CartFlows’ Order Bump! Offer personalized add-ons at checkout to increase revenue effortlessly. Quick "
"to set up, no coding needed!"
msgstr ""
"Steigern Sie den Umsatz mit dem Order Bump von CartFlows! Bieten Sie personalisierte Zusatzangebote beim Checkout an, "
"um den Umsatz mühelos zu steigern. Schnell einzurichten, keine Programmierkenntnisse erforderlich!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show the Right Offer to the Right People – Automatically!"
msgstr "Zeigen Sie das richtige Angebot den richtigen Personen – automatisch!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Personalize deals based on location, cart details, and more. Upgrade to CartFlows PRO and unlock this smart feature "
"today!"
msgstr ""
"Personalisieren Sie Angebote basierend auf Standort, Warenkorbdetails und mehr. Upgraden Sie auf CartFlows PRO und "
"schalten Sie dieses intelligente Feature noch heute frei!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Boost Sales Instantly with Auto-Applied Coupons!"
msgstr "Steigern Sie den Umsatz sofort mit automatisch angewendeten Gutscheinen!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"No codes, no hassle—discounts apply instantly at checkout. Upgrade to CartFlows PRO and start converting more customers "
"today!"
msgstr ""
"Keine Codes, kein Aufwand – Rabatte werden sofort an der Kasse angewendet. Upgrade auf CartFlows PRO und beginnen Sie "
"noch heute, mehr Kunden zu gewinnen!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Give Your Customers More Choices, Boost Your Sales"
msgstr "Geben Sie Ihren Kunden mehr Auswahlmöglichkeiten, steigern Sie Ihren Umsatz"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"Make buying easier with flexible product options. Let customers make the right choices from your checkout. Upgrade to "
"CartFlows PRO and start customizing today!"
msgstr ""
"Machen Sie den Kauf einfacher mit flexiblen Produktoptionen. Lassen Sie Kunden die richtigen Entscheidungen an der "
"Kasse treffen. Upgraden Sie zu CartFlows PRO und beginnen Sie noch heute mit der Anpassung!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Optin Product"
msgstr "Optin-Produkt"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Select free & virtual product only. Once you select a product, it will be displayed here."
msgstr "Wählen Sie nur kostenlose und virtuelle Produkte aus. Sobald Sie ein Produkt ausgewählt haben, wird es hier angezeigt."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Search for a Free & Virtual Product"
msgstr "Suche nach einem kostenlosen und virtuellen Produkt"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Please select a free & virtual product only."
msgstr "Bitte wählen Sie nur ein kostenloses und virtuelles Produkt aus."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:80
msgid "Free"
msgstr "Kostenlos"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "A/B Testing"
msgstr "A/B-Tests"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid ""
"Optimize your sales with A/B testing in CartFlows! Experiment with product pricing, page layouts, messaging, and "
"design. Create variants, analyze results, and discover new ways to boost revenue."
msgstr ""
"Optimieren Sie Ihre Verkäufe mit A/B-Tests in CartFlows! Experimentieren Sie mit Produktpreisen, Seitenlayouts, "
"Nachrichten und Design. Erstellen Sie Varianten, analysieren Sie Ergebnisse und entdecken Sie neue Wege zur "
"Umsatzsteigerung."

#: admin-core/assets/build/settings-app.js:23
msgid "Finish Setup"
msgstr "Einrichtung abschließen"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup CartFlows"
msgstr "CartFlows einrichten"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup Store Checkout"
msgstr "Einrichtung des Kassenbereichs im Geschäft"

#: admin-core/assets/build/settings-app.js:23
msgid "Create"
msgstr "Erstellen"

#: admin-core/assets/build/settings-app.js:23
msgid "Build Your Funnel"
msgstr "Erstellen Sie Ihren Trichter"

#: admin-core/assets/build/settings-app.js:23
msgid "Start From Scratch"
msgstr "Von vorne anfangen"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Library"
msgstr "Zur Bibliothek gehen"

#: admin-core/assets/build/settings-app.js:23
msgid "Offer add-ons with Order Bump."
msgstr "Bieten Sie Add-ons mit Order Bump an."

#: admin-core/assets/build/settings-app.js:23
msgid "Increase Revenue with Upsells."
msgstr "Steigern Sie den Umsatz mit Upsells."

#: admin-core/assets/build/settings-app.js:23
msgid "Almost There! Let's Go Live."
msgstr "Fast geschafft! Lass uns live gehen."

#: admin-core/assets/build/settings-app.js:24
#. translators: %d is the number of completed steps.
msgid "%d out of 5 completed"
msgstr "%d von 5 abgeschlossen"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade to PRO"
msgstr "Upgrade auf PRO"

#: admin-core/assets/build/settings-app.js:24
msgid "Completed"
msgstr "Abgeschlossen"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade your plan anytime and get more detailed analytics data."
msgstr "Aktualisieren Sie Ihren Plan jederzeit und erhalten Sie detailliertere Analysedaten."

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views is a Premium Feature"
msgstr "Die Gesamtseitenaufrufe sind eine Premium-Funktion"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue is a Premium Feature"
msgstr "Angebotsumsatz ist eine Premium-Funktion"

#: admin-core/assets/build/settings-app.js:24
msgid "Custom Filter:"
msgstr "Benutzerdefinierter Filter:"

#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Features"
msgstr "Kassenfunktionen"

#: admin-core/assets/build/settings-app.js:80
msgid "Modern Checkout Styles"
msgstr "Moderne Checkout-Stile"

#: admin-core/assets/build/settings-app.js:80
msgid "Optimized replacement for the standard WooCommerce checkout page designed for higher conversion"
msgstr "Optimierte Ersatzlösung für die standardmäßige WooCommerce-Kassenseite, die für höhere Konversionen entwickelt wurde"

#: admin-core/assets/build/settings-app.js:80
msgid "Custom Checkout Fields"
msgstr "Benutzerdefinierte Checkout-Felder"

#: admin-core/assets/build/settings-app.js:80
msgid "Have complete control over the field editor to manage the fields as required"
msgstr "Haben Sie die vollständige Kontrolle über den Feldeeditor, um die Felder nach Bedarf zu verwalten"

#: admin-core/assets/build/settings-app.js:80
msgid "One-Click Upsells / Downsells"
msgstr "One-Click Upsells / Downsells"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic One-Click Upsells"
msgstr "Dynamische One-Click-Upsells"

#: admin-core/assets/build/settings-app.js:80
msgid "Use cart contents or customer data to display relevant upsells for maximum conversion"
msgstr "Verwenden Sie Warenkorbinhalte oder Kundendaten, um relevante Upsells für maximale Konversion anzuzeigen"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Upsell Templates"
msgstr "Dynamische Upsell-Vorlagen"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional templates to help you sell more even if you’re not a designer"
msgstr "Professionelle Vorlagen, die Ihnen helfen, mehr zu verkaufen, auch wenn Sie kein Designer sind"

#: admin-core/assets/build/settings-app.js:80
msgid "Order Bump Features"
msgstr "Bestellaufschlag-Funktionen"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Order Bumps"
msgstr "Dynamische Bestell-Ergänzungen"

#: admin-core/assets/build/settings-app.js:80
msgid "Smart order bumps using customer data to display most relevant products or offers"
msgstr "Intelligente Bestell-Ergänzungen, die Kundendaten nutzen, um die relevantesten Produkte oder Angebote anzuzeigen"

#: admin-core/assets/build/settings-app.js:80
msgid "Advanced Funnel Features"
msgstr "Erweiterte Trichterfunktionen"

#: admin-core/assets/build/settings-app.js:80
msgid "A / B Split Testing"
msgstr "A/B-Split-Test"

#: admin-core/assets/build/settings-app.js:80
msgid "Increase conversions and sales with CartFlows A/B Testing by running simple tests"
msgstr "Steigern Sie die Konversionen und Verkäufe mit CartFlows A/B-Tests, indem Sie einfache Tests durchführen"

#: admin-core/assets/build/settings-app.js:80
msgid "Analyze transactions and user behavior to refine conversions and make more profit"
msgstr "Analysiere Transaktionen und das Nutzerverhalten, um Konversionen zu verfeinern und mehr Gewinn zu erzielen"

#: admin-core/assets/build/settings-app.js:80
msgid "Cloud-based automation tools that intelligently links your websites, stores, plugins and apps"
msgstr "Cloud-basierte Automatisierungstools, die intelligent Ihre Websites, Shops, Plugins und Apps verknüpfen"

#: admin-core/assets/build/settings-app.js:80
msgid "SkillJet Academy Access"
msgstr "SkillJet Academy Zugang"

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows offers full training to help you make more profit with SkillJet academy"
msgstr "CartFlows bietet eine umfassende Schulung an, um Ihnen zu helfen, mit der SkillJet-Akademie mehr Gewinn zu erzielen"

#: admin-core/assets/build/settings-app.js:80
msgid "Others Benefits"
msgstr "Weitere Vorteile"

#: admin-core/assets/build/settings-app.js:80
msgid "Premium Support"
msgstr "Premium-Support"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional Support, Professional Support Team or Dedicated Support Team"
msgstr "Professioneller Support, Professionelles Support-Team oder Dediziertes Support-Team"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community"
msgstr "Erstaunliche Benutzer-Community"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community is already a great message unless you’re looking for a different meaning"
msgstr ""
"Erstaunliche Benutzer-Community ist bereits eine großartige Nachricht, es sei denn, Sie suchen nach einer anderen "
"Bedeutung"

#: admin-core/assets/build/settings-app.js:80
msgid "Great Documentation & Video Tutorials"
msgstr "Großartige Dokumentation & Video-Tutorials"

#: admin-core/assets/build/settings-app.js:80
msgid "Comprehensive Documentation and Video Tutorials or Comprehensive Documentation and Video Guides"
msgstr "Umfassende Dokumentation und Video-Tutorials oder umfassende Dokumentation und Videoanleitungen"

#: admin-core/assets/build/settings-app.js:80
msgid "Free Plugins"
msgstr "Kostenlose Plugins"

#: admin-core/assets/build/settings-app.js:80
msgid "Variation Swatches"
msgstr "Variationsmuster"

#: admin-core/assets/build/settings-app.js:80
msgid "Give customers choice by including relevant product variations including size, color and more"
msgstr "Geben Sie den Kunden die Wahl, indem Sie relevante Produktvariationen wie Größe, Farbe und mehr einbeziehen"

#: admin-core/assets/build/settings-app.js:80
msgid "Stripe Payment Gateway"
msgstr "Stripe-Zahlungsgateway"

#: admin-core/assets/build/settings-app.js:80
msgid "Accepting multiple payment methods gives customers choice and can significantly increase conversion"
msgstr "Die Akzeptanz mehrerer Zahlungsmethoden gibt den Kunden Wahlmöglichkeiten und kann die Konversion erheblich steigern"

#: admin-core/assets/build/settings-app.js:80
msgid "Features"
msgstr "Funktionen"

#: admin-core/assets/build/settings-app.js:80
msgid "See all CartFlows Pro features"
msgstr "Alle CartFlows Pro-Funktionen anzeigen"

#: admin-core/assets/build/settings-app.js:80
msgid "Sell More with CartFlows Pro"
msgstr "Verkaufen Sie mehr mit CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Get access to powerful features for painless WordPress designing, without the high costs. With all the time you will "
"save, it’s a product that pays for itself!"
msgstr ""
"Erhalten Sie Zugang zu leistungsstarken Funktionen für ein müheloses WordPress-Design, ohne hohe Kosten. Mit all der "
"Zeit, die Sie sparen werden, ist es ein Produkt, das sich von selbst bezahlt macht!"

#: wizard/assets/build/wizard-app.js:1
msgid "Please complete the previous step before proceeding."
msgstr "Bitte schließen Sie den vorherigen Schritt ab, bevor Sie fortfahren."

#: cartflows.php
#. Author of the plugin
msgid "Brainstorm Force"
msgstr "Brainstorm Force"

#: cartflows.php
#. Author URI of the plugin
msgid "https://www.brainstormforce.com"
msgstr "https://www.brainstormforce.com"

#: admin-core/ajax/importer.php:1017
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$s article%3$s."
msgstr ""
"Anforderungszeitüberschreitungsfehler. Bitte überprüfen Sie, ob die Firewall oder ein Sicherheits-Plugin die "
"ausgehenden HTTP/HTTPS-Anfragen an templates.cartflows.com blockiert. %1$sUm dieses Problem zu beheben, lesen Sie bitte "
"diesen %2$s Artikel%3$s."

#: admin-core/inc/admin-menu.php:288
#: admin-core/inc/admin-menu.php:290
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Modern Cart"
msgstr "Moderner Warenkorb"

#: admin-core/inc/admin-menu.php:1038
msgid "OttoKit"
msgstr "OttoKit"

#: admin-core/inc/admin-menu.php:1039
msgid ""
"OttoKit helps people automate their work by integrating multiple apps and plugins, allowing them to share data and "
"perform tasks automatically."
msgstr ""
"OttoKit hilft Menschen, ihre Arbeit zu automatisieren, indem es mehrere Apps und Plugins integriert, sodass sie Daten "
"teilen und Aufgaben automatisch ausführen können."

#: admin-core/inc/admin-menu.php:1088
msgid "Modern Cart for WooCommerce"
msgstr "Moderner Warenkorb für WooCommerce"

#: admin-core/inc/admin-menu.php:1089
msgid ""
"Modern Cart for WooCommerce that helps every shop owner improve their user experience, increase conversions & maximize "
"profits."
msgstr ""
"Moderner Warenkorb für WooCommerce, der jedem Shop-Besitzer hilft, die Benutzererfahrung zu verbessern, die "
"Konversionen zu steigern und die Gewinne zu maximieren."

#: admin-core/inc/flow-meta.php:59
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Enable Instant Layout"
msgstr "Sofortlayout aktivieren"

#: admin-core/inc/flow-meta.php:64
#. translators: %1$s: Break line, %2$s: link html Start, %3$s: Link html end.
msgid ""
"This layout will replace the default page template for the Checkout, Upsell/Downsell and Thank You steps. You can "
"customize the design %1$sin the Checkout, Upsell/Downsell and Thank You step's settings, under the design tab. %2$sRead "
"More.%3$s"
msgstr ""
"Dieses Layout wird die Standardseitenvorlage für die Schritte Checkout, Upsell/Downsell und Danke ersetzen. Sie können "
"das Design %1$sin den Einstellungen der Schritte Checkout, Upsell/Downsell und Danke unter dem Design-Tab anpassen. "
"%2$sMehr erfahren.%3$s"

#: admin-core/inc/flow-meta.php:87
msgid "Custom Logo"
msgstr "Benutzerdefiniertes Logo"

#: admin-core/inc/flow-meta.php:92
msgid "If you've added a custom logo, it will show up here. If not, a default logo from the theme will be used instead."
msgstr ""
"Wenn Sie ein benutzerdefiniertes Logo hinzugefügt haben, wird es hier angezeigt. Wenn nicht, wird stattdessen ein "
"Standardlogo aus dem Thema verwendet."

#: admin-core/inc/flow-meta.php:103
msgid "Minimum image size should be 130 x 40 in pixes for ideal display."
msgstr "Die Mindestbildgröße sollte 130 x 40 Pixel für eine ideale Anzeige betragen."

#: admin-core/inc/flow-meta.php:143
msgid "Header Color"
msgstr "Header-Farbe"

#: admin-core/inc/flow-meta.php:249
msgid ""
"The Test Mode automatically adds sample products to your funnel if you haven't selected any. This helps you preview and "
"test the checkout experience easily."
msgstr ""
"Der Testmodus fügt automatisch Beispielprodukte zu Ihrem Funnel hinzu, wenn Sie keine ausgewählt haben. Dies hilft "
"Ihnen, die Checkout-Erfahrung einfach vorzuschauen und zu testen."

#: admin-core/inc/flow-meta.php:250
msgid ""
"The Test Mode automatically adds sample products to your store checkout funnel if you haven't selected any. This helps "
"you preview and test the experience easily on all steps except the Checkout page."
msgstr ""
"Der Testmodus fügt automatisch Beispielprodukte zu Ihrem Store-Checkout-Trichter hinzu, wenn Sie keine ausgewählt "
"haben. Dies hilft Ihnen, die Erfahrung in allen Schritten außer der Checkout-Seite einfach vorzuschauen und zu testen."

#: admin-core/inc/flow-meta.php:256
msgid "Disallow Indexing"
msgstr "Indizierung verbieten"

#: admin-core/inc/flow-meta.php:257
msgid "Changing this will replace the default global setting. To go back to the global setting, just select Default."
msgstr ""
"Wenn Sie dies ändern, wird die standardmäßige globale Einstellung ersetzt. Um zur globalen Einstellung zurückzukehren, "
"wählen Sie einfach Standard aus."

#: admin-core/inc/flow-meta.php:280
msgid "Any code you add here will work across all the pages in this funnel."
msgstr "Jeder Code, den Sie hier hinzufügen, wird auf allen Seiten in diesem Funnel funktionieren."

#: admin-core/inc/global-settings.php:50
msgid "Allow full access to all settings to customize everything."
msgstr "Vollen Zugriff auf alle Einstellungen erlauben, um alles anzupassen."

#: admin-core/inc/global-settings.php:55
msgid "Allow limited access to create, edit, delete, or import flows and steps."
msgstr "Erlaube eingeschränkten Zugriff zum Erstellen, Bearbeiten, Löschen oder Importieren von Flows und Schritten."

#: admin-core/inc/global-settings.php:349
#: admin-core/inc/global-settings.php:539
msgid "This event will trigger when someone subscribes or signs up on the opt-in page."
msgstr "Dieses Ereignis wird ausgelöst, wenn sich jemand auf der Opt-in-Seite anmeldet oder registriert."

#: admin-core/inc/global-settings.php:930
msgid "This option is only available for products that are part of a subscription."
msgstr "Diese Option ist nur für Produkte verfügbar, die Teil eines Abonnements sind."

#: admin-core/inc/global-settings.php:1415
msgid "Usage Tracking"
msgstr "Nutzungsverfolgung"

#: admin-core/inc/global-settings.php:1417
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Allow CartFlows Inc products to track non-sensitive usage tracking data. %1$1s Learn More%2$2s."
msgstr "Erlauben Sie CartFlows Inc-Produkten, nicht sensible Nutzungsdaten zu verfolgen. %1$1s Mehr erfahren%2$2s."

#: classes/class-cartflows-admin-notices.php:183
msgid "Hi there! You recently used CartFlows to build a sales funnel &mdash; Thanks a ton!"
msgstr "Hallo! Du hast kürzlich CartFlows verwendet, um einen Verkaufstrichter zu erstellen &mdash; Vielen Dank!"

#: classes/class-cartflows-admin-notices.php:184
msgid ""
"It would be awesome if you give us a 5-star review and share your experience on WordPress. Your reviews pump us up and "
"also help other WordPress users make a better decision when choosing CartFlows!"
msgstr ""
"Es wäre großartig, wenn Sie uns eine 5-Sterne-Bewertung geben und Ihre Erfahrungen auf WordPress teilen. Ihre "
"Bewertungen motivieren uns und helfen auch anderen WordPress-Nutzern, eine bessere Entscheidung bei der Auswahl von "
"CartFlows zu treffen!"

#: classes/class-cartflows-admin-notices.php:186
msgid "Ok, you deserve it"
msgstr "Ok, du hast es verdient"

#: classes/class-cartflows-admin-notices.php:188
msgid "Nope, maybe later"
msgstr "Nö, vielleicht später"

#: classes/class-cartflows-admin-notices.php:189
msgid "I already did"
msgstr "Ich habe es bereits getan"

#: classes/class-cartflows-flow-frontend.php:90
msgid ""
"Test mode is currently enabled to help you preview your funnel. You can turn it off anytime from the funnel's settings "
"in the admin dashboard."
msgstr ""
"Der Testmodus ist derzeit aktiviert, um Ihnen eine Vorschau Ihres Funnels zu ermöglichen. Sie können ihn jederzeit in "
"den Einstellungen des Funnels im Admin-Dashboard deaktivieren."

#: classes/class-cartflows-flow-frontend.php:91
msgid "Click here to disable it."
msgstr "Klicken Sie hier, um es zu deaktivieren."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:119
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:131
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:162
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:145
#: modules/gutenberg/build/blocks.js:7
msgid "Legacy"
msgstr "Vermächtnis"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:120
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:132
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:163
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:149
#: modules/gutenberg/build/blocks.js:7
msgid "Modern"
msgstr "Modern"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:132
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:146
#: modules/gutenberg/build/blocks.js:7
msgid "The Thank You Text is only applicable for the old layout."
msgstr "Der Dankestext gilt nur für das alte Layout."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:322
msgid "Pick a background color for the left side of your Checkout page."
msgstr "Wählen Sie eine Hintergrundfarbe für die linke Seite Ihrer Checkout-Seite aus."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:339
msgid "Pick a background color for the right side of your Checkout page."
msgstr "Wählen Sie eine Hintergrundfarbe für die rechte Seite Ihrer Checkout-Seite aus."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:841
msgid "Change the background color of the payment description box to match your style."
msgstr "Ändern Sie die Hintergrundfarbe des Zahlungsbeschreibungsfelds, um Ihrem Stil zu entsprechen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1022
msgid "This is the name (slug) of the current step. Changing it will update the URL for this step, so be cautious!"
msgstr ""
"Dies ist der Name (Slug) des aktuellen Schritts. Eine Änderung wird die URL für diesen Schritt aktualisieren, also "
"seien Sie vorsichtig!"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1030
#: modules/landing/classes/class-cartflows-landing-meta-data.php:123
#: modules/optin/classes/class-cartflows-optin-meta-data.php:577
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:405
msgid "Add your own custom code here. If you're adding CSS, make sure to wrap it inside &lt;style&gt; tags."
msgstr ""
"Fügen Sie hier Ihren eigenen benutzerdefinierten Code hinzu. Wenn Sie CSS hinzufügen, stellen Sie sicher, dass Sie es "
"in &lt;style&gt;-Tags einfügen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1044
msgid "Turn this ON to show your product images in the order review section."
msgstr "Schalten Sie dies EIN, um Ihre Produktbilder im Abschnitt Bestellübersicht anzuzeigen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1052
msgid "Users can easily remove products from the checkout page if they decide not to purchase them."
msgstr "Benutzer können Produkte auf der Checkout-Seite leicht entfernen, wenn sie sich entscheiden, diese nicht zu kaufen."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1216
msgid "Turn this on to show a custom message when no shipping options are available at checkout."
msgstr ""
"Aktivieren Sie diese Option, um eine benutzerdefinierte Nachricht anzuzeigen, wenn beim Checkout keine Versandoptionen "
"verfügbar sind."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1244
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:449
msgid "Choose this option to adjust where the order summary appears on mobile devices."
msgstr "Wählen Sie diese Option, um anzupassen, wo die Bestellübersicht auf mobilen Geräten angezeigt wird."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1297
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1305
msgid "This heading will only appear when you use the Modern Checkout style."
msgstr "Diese Überschrift wird nur angezeigt, wenn Sie den modernen Checkout-Stil verwenden."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1313
msgid "This message will appear next to the field name to show an error if something goes wrong."
msgstr "Diese Nachricht wird neben dem Feldnamen angezeigt, um einen Fehler zu zeigen, falls etwas schiefgeht."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1346
msgid ""
"Customizes the text on the 'Place Order' button during checkout, allowing you to make it more relevant to your "
"customers."
msgstr ""
"Passt den Text auf der Schaltfläche „Bestellung aufgeben“ während des Checkouts an, sodass er für Ihre Kunden "
"relevanter wird."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1354
msgid ""
"Enabling this will add a lock icon to the 'Place Order' button on the checkout page, indicating secure payment "
"processing."
msgstr ""
"Wenn Sie dies aktivieren, wird dem Button „Bestellung aufgeben“ auf der Kassenseite ein Schloss-Symbol hinzugefügt, das "
"eine sichere Zahlungsabwicklung anzeigt."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1363
msgid "This will display the total amount in the cart when you click the 'Place Order' button."
msgstr "Dies zeigt den Gesamtbetrag im Warenkorb an, wenn Sie auf die Schaltfläche 'Bestellung aufgeben' klicken."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:408
msgid "This heading will appear only when the Instant Layout option is used."
msgstr "Diese Überschrift wird nur angezeigt, wenn die Option \"Sofortlayout\" verwendet wird."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:139
msgid "Thank You Skin"
msgstr "Danke Haut"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:501
msgid "After submitting, users will be sent to this URL instead of the usual thank you page."
msgstr "Nach dem Absenden werden die Benutzer zu dieser URL weitergeleitet, anstatt zur üblichen Dankeseite."

#: modules/thankyou/templates/instant-thankyou-order-details.php:35
#. Translators: Order ID.
msgid "Order #%s"
msgstr "Bestellung Nr. %s"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:139
msgid "Type to search a funnel..."
msgstr "Tippen Sie, um einen Trichter zu suchen..."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Disabled"
msgstr "Deaktiviert"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "upgrading to PRO"
msgstr "Upgrade auf PRO"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "activating CartFlows Pro"
msgstr "CartFlows Pro aktivieren"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You're using"
msgstr "Sie verwenden"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "CartFlows Free"
msgstr "CartFlows Kostenlos"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "To unlock more features, consider"
msgstr "Um mehr Funktionen freizuschalten, ziehen Sie in Betracht"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Activate CartFlows Pro"
msgstr "CartFlows Pro aktivieren"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Activated!"
msgstr "Aktiviert!"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid ""
"You can't edit this step directly because Instant Layout is turned on in the funnel settings. To make design changes, "
"go to the Design tab inside this step's settings."
msgstr ""
"Sie können diesen Schritt nicht direkt bearbeiten, da das Instant Layout in den Trichtereinstellungen aktiviert ist. Um "
"Designänderungen vorzunehmen, gehen Sie zum Design-Tab in den Einstellungen dieses Schritts."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#. translators: %s: The current step type.
msgid "Use this setting to customize the style of the Instant %s Layout."
msgstr "Verwenden Sie diese Einstellung, um den Stil des Instant-%s-Layouts anzupassen."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Turn this on to set up rules that decide when visitors should be redirected to a special offer or the next step."
msgstr ""
"Schalten Sie dies ein, um Regeln festzulegen, die entscheiden, wann Besucher zu einem Sonderangebot oder dem nächsten "
"Schritt weitergeleitet werden sollen."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Your email address can't be edited when using the Modern Checkout Style."
msgstr "Ihre E-Mail-Adresse kann nicht bearbeitet werden, wenn Sie den modernen Checkout-Stil verwenden."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "The Company field won't be visible if you're using the Instant Layout Style."
msgstr "Das Feld \"Firma\" wird nicht sichtbar sein, wenn Sie den Sofort-Layout-Stil verwenden."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from all your funnels combined."
msgstr "Dies zeigt den Gesamtbetrag des Geldes, das mit all Ihren Funnels zusammen verdient wurde."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of orders placed through your CartFlows checkout pages."
msgstr "Dies zeigt die Gesamtzahl der Bestellungen, die über Ihre CartFlows-Checkout-Seiten aufgegeben wurden."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of times people visited any step in your funnel."
msgstr "Dies zeigt die Gesamtzahl der Besuche von Personen in einem beliebigen Schritt Ihres Trichters."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from your Upsell and Downsell offers."
msgstr "Dies zeigt den Gesamtbetrag des Geldes, das mit Ihren Upsell- und Downsell-Angeboten verdient wurde."

#: admin-core/assets/build/settings-app.js:35
msgid "Set up a Store Checkout in just one click:"
msgstr "Richten Sie einen Store-Checkout mit nur einem Klick ein:"

#: admin-core/assets/build/settings-app.js:35
msgid "Thank You Page"
msgstr "Dankeschön-Seite"

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Use ready-made templates from the CartFlows Library, our custom widget, or shortcodes on each page to set this up "
"easily—no coding needed!"
msgstr ""
"Verwenden Sie vorgefertigte Vorlagen aus der CartFlows-Bibliothek, unser benutzerdefiniertes Widget oder Shortcodes auf "
"jeder Seite, um dies einfach einzurichten – kein Programmieren erforderlich!"

#: admin-core/assets/build/settings-app.js:80
msgid "Install OttoKit for Free"
msgstr "Installieren Sie OttoKit kostenlos"

#: admin-core/assets/build/settings-app.js:80
msgid "Visit OttoKit Website"
msgstr "Besuchen Sie die OttoKit-Website"

#: admin-core/assets/build/settings-app.js:80
msgid "Here are a few simple examples of what OttoKit can do on your WooCommerce store:"
msgstr "Hier sind einige einfache Beispiele dafür, was OttoKit in Ihrem WooCommerce-Shop tun kann:"

#: admin-core/assets/build/settings-app.js:80
msgid "Join Thousands of Entrepreneurs Already Using OttoKit."
msgstr "Schließen Sie sich Tausenden von Unternehmern an, die bereits OttoKit nutzen."

#: admin-core/assets/build/settings-app.js:80
msgid "Bonus ($200 Value)"
msgstr "Bonus (Wert von 200 $)"

#: admin-core/assets/build/settings-app.js:80
msgid "Access to OttoKit Pro Plan"
msgstr "Zugang zum OttoKit Pro-Plan"

#: admin-core/assets/build/settings-app.js:80
msgid "Plus - Annual"
msgstr "Plus - Jährlich"

#: admin-core/assets/build/settings-app.js:80
msgid "/ year"
msgstr "/ Jahr"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Annual"
msgstr "Pro - Jährlich"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (One Time Pay)"
msgstr "Pro - Lebenslang (Einmalzahlung)"

#: admin-core/assets/build/settings-app.js:80
msgid "for Lifetime"
msgstr "für das ganze Leben"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (11 x Split Pay)"
msgstr "Pro - Lebenslang (11 x Ratenzahlung)"

#: admin-core/assets/build/settings-app.js:80
msgid "x 11 Months"
msgstr "x 11 Monate"

#: admin-core/assets/build/settings-app.js:80
msgid "Explore the key differences between Plus and Pro to find the perfect fit for your needs."
msgstr "Erkunden Sie die wichtigsten Unterschiede zwischen Plus und Pro, um die perfekte Lösung für Ihre Bedürfnisse zu finden."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Free vs Pro Image"
msgstr "CartFlows Free vs Pro Bild"

#: admin-core/assets/build/settings-app.js:80
msgid "Unlock Pro Features"
msgstr "Pro-Funktionen freischalten"

#: admin-core/assets/build/settings-app.js:80
msgid "Generate More Sales With CartFlows Pro!"
msgstr "Erzielen Sie mehr Verkäufe mit CartFlows Pro!"

#: admin-core/assets/build/settings-app.js:80
msgid "And More…"
msgstr "Und mehr…"

#: admin-core/assets/build/settings-app.js:80
msgid "Buy Now"
msgstr "Jetzt kaufen"

#: admin-core/assets/build/settings-app.js:80
msgid "View plans"
msgstr "Pläne anzeigen"

#: admin-core/assets/build/settings-app.js:80
msgid "Get Modern Cart Now"
msgstr "Jetzt Modern Cart holen"

#: admin-core/assets/build/settings-app.js:80
msgid "Moderncart"
msgstr "Moderncart"

#: admin-core/assets/build/settings-app.js:81
#. translators: %s: line break
msgid "Your Cart Can Do More — Let’s Make It %sa Sales Machine!"
msgstr "Ihr Warenkorb kann mehr — Lassen Sie ihn zu %seiner Verkaufsmaschine werden!"

#: admin-core/assets/build/settings-app.js:81
msgid ""
"Transform your default WooCommerce cart into a high-converting, fast, and user-friendly shopping experience — designed "
"to keep customers engaged and ready to buy."
msgstr ""
"Verwandeln Sie Ihren standardmäßigen WooCommerce-Warenkorb in ein hochkonvertierendes, schnelles und "
"benutzerfreundliches Einkaufserlebnis – entwickelt, um Kunden zu binden und zum Kauf zu motivieren."

#: admin-core/assets/build/settings-app.js:81
msgid "Visit Modern Cart"
msgstr "Besuchen Sie Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Why Store Owners ❤️ Modern Cart"
msgstr "Warum Ladenbesitzer ❤️ Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Trusted by Top Brands to Boost Conversions Instantly"
msgstr "Von Top-Marken vertraut, um sofort die Konversionen zu steigern"

#: admin-core/assets/build/settings-app.js:81
msgid "Brand logo"
msgstr "Markenlogo"

#: admin-core/assets/build/settings-app.js:81
msgid "Stop Losing Sales at the Cart — Fix It in Minutes!"
msgstr "Verlieren Sie keine Verkäufe mehr im Warenkorb – beheben Sie es in Minuten!"

#: admin-core/assets/build/settings-app.js:81
msgid "Modern Cart is your instant upgrade for more sales, bigger orders, and smoother checkouts."
msgstr "Modern Cart ist Ihr sofortiges Upgrade für mehr Verkäufe, größere Bestellungen und reibungslosere Kassen."

#: wizard/assets/build/wizard-app.js:1
msgid "Learn more about usage tracking"
msgstr "Erfahren Sie mehr über die Nutzungsverfolgung"

#: wizard/assets/build/wizard-app.js:3
msgid "I agree to share anonymous usage data to help improve CartFlows."
msgstr "Ich stimme zu, anonyme Nutzungsdaten zu teilen, um CartFlows zu verbessern."

#: wizard/assets/build/wizard-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag close
msgid ""
"We never collect personal info, Only anonymized data like PHP version, admin language, and feature usage. To learn what "
"we collect and why, see this %1$sdocument%2$s."
msgstr ""
"Wir sammeln niemals persönliche Informationen, sondern nur anonymisierte Daten wie PHP-Version, Administrationssprache "
"und Funktionsnutzung. Um zu erfahren, was wir sammeln und warum, sehen Sie sich dieses %1$sDokument%2$s an."

#: wizard/assets/build/wizard-app.js:5
#. translators: %1$s: Anchor tag one start, %2$s: Anchor tag one close, %3$s: Anchor tag two start, %4$s: Anchor tag two close.
msgid "By continuing you agree to our %1$sTerms%2$s and %3$sPrivacy Policy%4$s."
msgstr "Indem Sie fortfahren, stimmen Sie unseren %1$sNutzungsbedingungen%2$s und unserer %3$sDatenschutzrichtlinie%4$s zu."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:266
msgctxt "Width."
msgid "Auto"
msgstr "Auto"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping %d"
msgstr "Versand %d"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping"
msgstr "Versand"

#: modules/flow/classes/class-cartflows-flow-post-type.php:103
msgctxt "flow general name"
msgid "Flows"
msgstr "Flüsse"

#: modules/flow/classes/class-cartflows-flow-post-type.php:104
msgctxt "flow singular name"
msgid "Flow"
msgstr "Fluss"

#: modules/flow/classes/class-cartflows-step-post-type.php:170
msgctxt "flow step general name"
msgid "Steps"
msgstr "Schritte"

#: modules/flow/classes/class-cartflows-step-post-type.php:171
msgctxt "flow step singular name"
msgid "Step"
msgstr "Schritt"
