<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="128" height="128" fill="#D1D5DB"/>
<g filter="url(#filter0_d_727_2371)">
<rect x="8" y="8" width="112" height="112" rx="6" fill="#F9FAFB"/>
</g>
<circle cx="17" cy="16" r="3" fill="#D1D5DB"/>
<path d="M14 29.4C14 27.1598 14 26.0397 14.436 25.184C14.8195 24.4314 15.4314 23.8195 16.184 23.436C17.0397 23 18.1598 23 20.4 23H107.6C109.84 23 110.96 23 111.816 23.436C112.569 23.8195 113.181 24.4314 113.564 25.184C114 26.0397 114 27.1598 114 29.4V54.6C114 56.8402 114 57.9603 113.564 58.816C113.181 59.5686 112.569 60.1805 111.816 60.564C110.96 61 109.84 61 107.6 61H20.4C18.1598 61 17.0397 61 16.184 60.564C15.4314 60.1805 14.8195 59.5686 14.436 58.816C14 57.9603 14 56.8402 14 54.6V29.4Z" fill="#E5E7EB"/>
<circle cx="91" cy="42" r="15" fill="white"/>
<path d="M90.0291 30.9327C90.2794 29.9189 91.7206 29.9189 91.9709 30.9327L93.8251 38.4437C93.9141 38.8043 94.1957 39.0859 94.5563 39.1749L102.067 41.0291C103.081 41.2794 103.081 42.7206 102.067 42.9709L94.5563 44.8251C94.1957 44.9141 93.9141 45.1957 93.8251 45.5563L91.9709 53.0673C91.7206 54.0811 90.2794 54.0811 90.0291 53.0673L88.1749 45.5563C88.0859 45.1957 87.8043 44.9141 87.4437 44.8251L79.9327 42.9709C78.9189 42.7206 78.9189 41.2794 79.9327 41.0291L87.4437 39.1749C87.8043 39.0859 88.0859 38.8043 88.1749 38.4437L90.0291 30.9327Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 36C20 35.4477 20.4477 35 21 35H53C53.5523 35 54 35.4477 54 36C54 36.5523 53.5523 37 53 37H21C20.4477 37 20 36.5523 20 36Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 41C20 40.4477 20.4477 40 21 40H45C45.5523 40 46 40.4477 46 41C46 41.5523 45.5523 42 45 42H21C20.4477 42 20 41.5523 20 41Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 48C20 46.8954 21.066 46 22.381 46H34.619C35.934 46 37 46.8954 37 48C37 49.1046 35.934 50 34.619 50H22.381C21.066 50 20 49.1046 20 48Z" fill="#9CA3AF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M97 16C97 14.8954 98.066 14 99.381 14H111.619C112.934 14 114 14.8954 114 16C114 17.1046 112.934 18 111.619 18H99.381C98.066 18 97 17.1046 97 16Z" fill="#9CA3AF"/>
<path d="M22 79.4C22 77.1598 22 76.0397 22.436 75.184C22.8195 74.4314 23.4314 73.8195 24.184 73.436C25.0397 73 26.1598 73 28.4 73H54.6C56.8402 73 57.9603 73 58.816 73.436C59.5686 73.8195 60.1805 74.4314 60.564 75.184C61 76.0397 61 77.1598 61 79.4V97.6C61 99.8402 61 100.96 60.564 101.816C60.1805 102.569 59.5686 103.181 58.816 103.564C57.9603 104 56.8402 104 54.6 104H28.4C26.1598 104 25.0397 104 24.184 103.564C23.4314 103.181 22.8195 102.569 22.436 101.816C22 100.96 22 99.8402 22 97.6V79.4Z" fill="#E5E7EB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 88C70 87.4477 70.4477 87 71 87H103C103.552 87 104 87.4477 104 88C104 88.5523 103.552 89 103 89H71C70.4477 89 70 88.5523 70 88Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 83C70 82.4477 70.4477 82 71 82H103C103.552 82 104 82.4477 104 83C104 83.5523 103.552 84 103 84H71C70.4477 84 70 83.5523 70 83Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 77C70 76.4477 70.4477 76 71 76H87C87.5523 76 88 76.4477 88 77C88 77.5523 87.5523 78 87 78H71C70.4477 78 70 77.5523 70 77Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 93C70 92.4477 70.4477 92 71 92H95C95.5523 92 96 92.4477 96 93C96 93.5523 95.5523 94 95 94H71C70.4477 94 70 93.5523 70 93Z" fill="#D1D5DB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70 100C70 98.8954 71.066 98 72.381 98H84.619C85.934 98 87 98.8954 87 100C87 101.105 85.934 102 84.619 102H72.381C71.066 102 70 101.105 70 100Z" fill="#9CA3AF"/>
<defs>
<filter id="filter0_d_727_2371" x="6" y="8" width="116" height="118" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="erode" in="SourceAlpha" result="effect1_dropShadow_727_2371"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_727_2371"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_727_2371" result="shape"/>
</filter>
</defs>
</svg>
