<?php
/**
 * Optin post meta
 *
 * @package cartflows
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Meta Boxes setup
 */
class Cartflows_Pro_Optin_Default_Meta {

	/**
	 * Instance
	 *
	 * @var Cartflows_Pro_Optin_Default_Meta $instance Class object
	 */
	private static $instance;

	/**
	 * Initiator
	 */
	public static function get_instance() {
		if ( ! isset( self::$instance ) ) {
			self::$instance = new self();
		}

		return self::$instance;
	}

	/**
	 * Constructor
	 */
	public function __construct() {

	}
}

/**
 * Kicking this off by calling 'get_instance()' method
 */
Cartflows_Pro_Optin_Default_Meta::get_instance();
