@import "../../styles/variables";

.uagb-spacing-control {
	position: relative;
	display: inline-block;

	.components-base-control__label {
		margin-bottom: 5px;
	}

	.components-base-control__help {
		margin-top: -8px;
	}

	input[type="number"].uagb-spacing-control__number,
	.uagb-spacing-control__link {
		border-left: 1px solid;
		border-right: 1px solid;
		border-color: $spectra-color-border;
		color: $spectra-color-body;
		border-radius: 0;
		box-shadow: none !important;
		height: 30px;
		margin: 0 -1px 0 0;
		padding-right: 1px;
		width: 25%;
	}

	input[type="number"].uagb-spacing-control__number:focus {
		z-index: 1;
		outline-offset: -1px;
		outline: 1px solid $spectra-color-icon-disabled;
	}

	.uagb-spacing-control__link {
		border: 1px solid $spectra-color-border-hover;
		background-color: $spectra-color-border-hover;
		color: $spectra-color-plain-background;
		justify-content: center;
		display: flex;
		align-items: center;
		padding: 2px 0;
		font-size: 12px;
		line-height: 28px;
		cursor: pointer;
		transition: none;

		&.uagb-spacing-control-disconnected {
			border-color: $spectra-color-border;
			background-color: $spectra-color-plain-background;
			color: $spectra-color-border-hover;
		}
	}

	.uagb-spacing-control__mobile-controls button[type="button"] > span {
		cursor: pointer;
		width: 15px;
		height: 15px;
		font-size: 15px;
	}

	.uagb-spacing-control__inputs > :first-child.uagb-spacing-control__number {
		-webkit-border-radius: $spectra-control-border-radius 0 0 $spectra-control-border-radius;
		-moz-border-radius: $spectra-control-border-radius 0 0 $spectra-control-border-radius;
		border-radius: $spectra-control-border-radius 0 0 $spectra-control-border-radius;
	}

	.uagb-spacing-control__inputs > :last-child {
		-webkit-border-radius: 0 $spectra-control-border-radius $spectra-control-border-radius 0;
		-moz-border-radius: 0 $spectra-control-border-radius $spectra-control-border-radius 0;
		border-radius: 0 $spectra-control-border-radius $spectra-control-border-radius 0;
	}

	.uagb-control__actions .components-tab-panel__tabs {
		padding-right: 4px;
		margin-right: 0;
	}

	.uagb-spacing-control__inputs {
		display: flex;
	}

	.uagb-spacing-control__mobile-controls-item {
		border: 0;
		display: none;
		font-size: 10px;
		height: 30px;
		justify-content: center;
		left: 50px;
		line-height: 12px;
		padding: 0;
		position: absolute;
		text-align: center;
		text-shadow: none;
		top: -5px;
		width: 26px;
		z-index: 1;
		box-shadow: unset;
		border-radius: unset;
		color: unset;

		&.is-active {
			display: block;
		}

		&:focus:enabled {
			box-shadow: inset 0 0 0 1px #fff;
		}

		&:focus:enabled svg {
			fill: #fff;
		}

		svg {
			position: relative;
		}

		&.uagb-spacing-control__mobile-controls-item--spacing.components-button.is-secondary:active:not(:disabled),
		&.uagb-spacing-control__mobile-controls-item--spacing.components-button.is-tertiary:active:not(:disabled) {
			background: #fff;
			color: $spectra-color-body;
		}

		&.components-button {

			&.is-secondary:hover:not(:disabled),
			&.is-tertiary:hover:not(:disabled) {
				color: unset;
				background: unset;
				box-shadow: unset;
			}
		}
	}

	.uagb-spacing-control__mobile-controls-item--default svg {
		top: 3px;
	}

	.uagb-spacing-control__mobile-controls-item--desktop svg {
		top: 3px;
	}

	.uagb-spacing-control__mobile-controls-item--tablet svg {
		top: 3px;
	}

	.uagb-spacing-control__mobile-controls-item--mobile svg {
		top: 3px;
	}

	.uagb-spacing-control__input-labels {
		display: flex;
		margin-bottom: -3px;

		span {
			text-align: center;
			width: 20%;
		}

		.uagb-spacing-control__link-label {
			width: 10%;
		}
	}

	.uagb-control__label {
		margin-bottom: 0;
		width: 50%;
	}

	.uagb-spacing-control__number-label {
		color: $spectra-color-icon-disabled;
		display: block;
		font-size: 10px;
		margin-top: 5px;
	}
}

/*.components-panel .block-editor-block-inspector .uagb-inspector-tab .uagb-spacing-control.components-base-control:last-child{
	margin-bottom: 15px;
}*/
