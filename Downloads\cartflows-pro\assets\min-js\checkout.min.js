!function(p){function t(){p(".wcf-qty-row").on("click",function(e){if(!p(e.target).is(".wcf-multiple-sel, .wcf-single-sel, .wcf-qty, .wcf-qty-selection, .wcf-variable-item-popup-text, .wcf-qty-selection-btn, .wcf-qty-increment-icon, .wcf-qty-decrement-icon")){const t=p(this).find(".wcf-single-sel"),c=p(this).find(".wcf-multiple-sel");0<t.length?t.trigger("click"):0<c.length&&c.trigger("click")}}),p(document).on("change",".wcf-single-sel",function(){const e=p(this),t=e.closest(".wcf-qty-options"),c=e.closest(".wcf-qty-row"),o=c.data("options"),i=c.find(".wcf-qty input");let a=parseInt(i.val());var r=p("._wcf_checkout_id").val(),r=((a<=0||isNaN(a))&&(i.val(1),a=1),o.input_quantity=a,o.checkout_id=r,p("form.checkout").serialize());w(),t.addClass("wcf-loading"),p.ajax({url:cartflows.ajax_url,data:{action:"wcf_single_selection",option:o,post_data:r,security:cartflows.wcf_single_selection_nonce},dataType:"json",type:"POST",success(e){e.hasOwnProperty("cartflows_data")&&(c.find(".wcf-display-quantity").html(e.cartflows_data.display_quantity),c.find(".wcf-display-price").html(e.cartflows_data.display_price),c.find(".wcf-display-discount-value").html(e.cartflows_data.display_discount_value),c.find(".wcf-display-discount-percent").html(e.cartflows_data.display_discount_percent),c.find(".wcf_subscription_price").html(e.cartflows_data.subscription_price),c.find(".wcf_subscription_fee").html(e.cartflows_data.sign_up_fee)),_(e),t.removeClass("wcf-loading"),o.cart_item_key=e.cart_item_key,c.attr("data-options",JSON.stringify(o)),h()},error(){p(".woocommerce-checkout-review-order-table").unblock()}})}),p(document).on("change",".wcf-multiple-sel",function(){const e=p(".wcf-multiple-sel:checked"),t=p(this),c=t.closest(".wcf-qty-row"),o=c.find(".wcf-qty input");let i=parseInt(o.val());if((i<=0||isNaN(i))&&(o.val(1),i=1),0===e.length)return t.prop("checked",!0),void t.prop("disabled",!0);1===e.length?e.prop("disabled",!0):e.removeAttr("disabled");const a=c.data("options");var r=p("._wcf_checkout_id").val(),r=(a.checkout_id=r,a.input_quantity=i,a.checked="no",p("form.checkout").serialize());t.is(":checked")&&(a.checked="yes"),w(),p(".wcf-qty-options").addClass("wcf-loading"),p.ajax({url:cartflows.ajax_url,data:{action:"wcf_multiple_selection",option:a,post_data:r,security:cartflows.wcf_multiple_selection_nonce},dataType:"json",type:"POST",success(e){e.hasOwnProperty("cartflows_data")&&(c.find(".wcf-display-quantity").html(e.cartflows_data.display_quantity),c.find(".wcf-display-price").html(e.cartflows_data.display_price),c.find(".wcf-display-discount-value").html(e.cartflows_data.display_discount_value),c.find(".wcf-display-discount-percent").html(e.cartflows_data.display_discount_percent),c.find(".wcf_subscription_price").html(e.cartflows_data.subscription_price),c.find(".wcf_subscription_fee").html(e.cartflows_data.sign_up_fee)),_(e),p(".wcf-qty-options").removeClass("wcf-loading"),a.cart_item_key=e.cart_item_key,c.attr("data-options",JSON.stringify(a)),h()},error(){p(".woocommerce-checkout-review-order-table").unblock()}})}),p(document).on("change",".wcf-var-sel",function(){const e=p(this),t=e.closest(".wcf-qty-row"),c=t.find(".wcf-qty input"),o=t.data("options");var i=parseInt(c.val()),a=p("._wcf_checkout_id").val();o.checkout_id=a,o.input_quantity=i,w(),p(".wcf-qty-options").addClass("wcf-loading"),p.ajax({url:cartflows.ajax_url,data:{action:"wcf_variation_selection",option:o,security:cartflows.wcf_variation_selection_nonce},dataType:"json",type:"POST",success(e){e.hasOwnProperty("cartflows_data")&&(t.find(".wcf-display-quantity").html(e.cartflows_data.display_quantity),t.find(".wcf-display-price").html(e.cartflows_data.display_price),t.find(".wcf-display-discount-value").html(e.cartflows_data.display_discount_value),t.find(".wcf-display-discount-percent").html(e.cartflows_data.display_discount_percent),t.find(".wcf_subscription_price").html(e.cartflows_data.subscription_price),t.find(".wcf_subscription_fee").html(e.cartflows_data.sign_up_fee)),_(e),p(".wcf-qty-options").removeClass("wcf-loading"),h()},error(){p(".woocommerce-checkout-review-order-table").unblock()}})}),p(document).on("change",".wcf-qty-selection",function(){const t=p(this),c=t.closest(".wcf-qty-row"),e=c.find(".wcf-item-selector");if(0<e.length){const i=e.find("input");if(0<i.length&&!i.is(":checked"))return}var o=Boolean(t.data("sale-limit"));if(!o){let e=parseInt(t.val());(e<=0||isNaN(e))&&(t.val(1),e=1);o=parseInt(t.attr("max"));if(o&&e>o)t.val(o);else{const a=c.data("options"),r=p("._wcf_checkout_id").val();a.input_quantity=e,a.checkout_id=r;o=p("form.checkout").serialize();"undefined"!=typeof data&&(a.cart_item_key=data.cart_item_key),w(),p(".wcf-qty-options").addClass("wcf-loading"),p.ajax({url:cartflows.ajax_url,data:{action:"wcf_quantity_update",option:a,post_data:o,security:cartflows.wcf_quantity_update_nonce},dataType:"json",type:"POST",success(e){e.hasOwnProperty("cartflows_data")&&(c.find(".wcf-display-quantity").html(e.cartflows_data.display_quantity),c.find(".wcf-display-price").html(e.cartflows_data.display_price),c.find(".wcf-display-discount-value").html(e.cartflows_data.display_discount_value),c.find(".wcf-display-discount-percent").html(e.cartflows_data.display_discount_percent),c.find(".wcf_subscription_price").html(e.cartflows_data.subscription_price),c.find(".wcf_subscription_fee").html(e.cartflows_data.sign_up_fee)),_(e),p(".wcf-qty-options").removeClass("wcf-loading"),h()},error(){p(".woocommerce-checkout-review-order-table").unblock()}})}}}),p("form.woocommerce-checkout").on("checkout_place_order",function(e){var t=p(".wcf-select-variation-attribute.wcf-invalid-variation");if(0<t.length)return e.preventDefault(),m(t),!1}),p(".wcf-select-variation-attribute").on("click",function(e){e.preventDefault();const t=p(this).closest(".wcf-qty-row");t.find(".wcf-item-choose-options a").trigger("click")});{const e=p(".wcf-item-choose-options a"),u=p(".wcf-quick-view-wrapper"),o=(u.appendTo(document.body),u.find(".wcf-quick-view-bg")),a=u.find("#wcf-quick-view-modal"),r=a.find("#wcf-quick-view-content"),c=a.find("#wcf-quick-view-close"),i=a.find(".wcf-content-main-wrapper"),s=(e.off("click").on("click",function(e){e.preventDefault();const t=p(this),c=t.closest(".wcf-item");c.find(".wcf-item-selector input").is(":checked")||c.find(".wcf-item-selector input").trigger("click");e=t.data("product");t.addClass("wcf-variation-popup-open"),a.hasClass("loading")||a.addClass("loading"),o.hasClass("wcf-quick-view-bg-ready")||o.addClass("wcf-quick-view-bg-ready"),p(document).trigger("wcf_quick_view_loading"),s(t,e)}),function(e,t){a.css("opacity",0),p.ajax({url:cartflows.ajax_url,data:{action:"wcf_woo_quick_view",product_id:t},dataType:"html",type:"POST",success(e){r.html(e),n()}})}),n=function(){const e=r.find(".variations_form");if(e.trigger("check_variations"),e.trigger("reset_image"),!a.hasClass("open")){a.removeClass("loading").addClass("open");var t=l();const o=p("html");o.css("margin-right",t),o.addClass("wcf-quick-view-is-open")}0<e.length&&"function"==typeof e.wc_variation_form&&(e.wc_variation_form(),e.find("select").trigger("change"));const c=a.find(".wcf-qv-image-slider");1<c.find("li").length?c.flexslider({animation:"slide",start(){setTimeout(function(){d(!0)},300)}}):setTimeout(function(){d(!0)},300),p(document).on("woocommerce_gallery_reset_slide_position",function(){c.flexslider(0)}),p(document).trigger("wcf_quick_view_loader_stop")},t=function(){i.on("click",function(e){this===e.target&&t()}),p(document).on("keyup",function(e){27===e.keyCode&&t()}),c.on("click",function(e){e.preventDefault(),t()});const t=function(){o.removeClass("wcf-quick-view-bg-ready"),a.removeClass("open").removeClass("loading"),p("html").removeClass("wcf-quick-view-is-open"),p("html").css("margin-right",""),e.removeClass("wcf-variation-popup-open"),setTimeout(function(){r.html("")},600)}},d=function(e){const t=r,c=t.find(".product .wcf-qv-image-slider").first().height(),o=t.find(".product .summary.entry-summary"),i=o.css("content");void 0!==i&&544===i.replace(/[^0-9]/g,"")&&0!==c&&null!==c?o.css("height",c):o.css("height",""),!0===e&&a.css("opacity",1)},l=function(){const e=p('<div style="width:50px;height:50px;overflow:hidden;position:absolute;top:-200px;left:-200px;"><div style="height:100px;"></div>');p("body").append(e);var t=p("div",e).innerWidth(),c=(e.css("overflow-y","scroll"),p("div",e).innerWidth());return p(e).remove(),t-c},f=(t(),window.addEventListener("resize",function(){d()}),function(){u.off("click","#wcf-quick-view-content .single_add_to_cart_button").off("wcf_added_to_cart").on("click","#wcf-quick-view-content .single_add_to_cart_button",this.onAddToCart).on("wcf_added_to_cart",this.updateButton)});f.prototype.onAddToCart=function(e){e.preventDefault();const t=p(this).closest("form");if(!t[0].checkValidity())return t[0].reportValidity(),!1;const o=p(this),c=t.find('input[name="product_id"]').val()||"",i=t.find('input[name="variation_id"]').val()||"",a=p(".wcf-variation-popup-open"),r=a.closest(".wcf-qty-row"),s=r.find(".wcf-qty-selection"),n=s.val()||1,d=r.data("options"),l=p("._wcf_checkout_id").val(),f=r.find(".wcf-item-wrap");d.input_quantity=n,d.checkout_id=l,o.is(".single_add_to_cart_button")&&(o.removeClass("added"),o.addClass("loading"),""!==i&&jQuery.ajax({url:cartflows.ajax_url,type:"POST",data:{action:"wcf_add_cart_single_product",form_data:t.serialize(),product_id:c,variation_id:i,quantity:n,option:d,security:cartflows.wcf_quick_view_add_cart_nonce},dataType:"json",success(e){if(e.hasOwnProperty("cartflows_data")&&"yes"===e.cartflows_data.added_to_cart){var e=e.cartflows_data,t=(a.closest(".wcf-item").find(".wcf-display-attributes").html(e.display_attr),a.closest(".wcf-item").find(".wcf-item-image").html(e.variation_image),a.attr("data-variation",e.variation_id),d.variation_id=e.variation_id,d.original_price=e.original_price,d.discounted_price=e.discounted_price,d.subscription_price=e.subscription_price,d.sign_up_fee=e.signup_fee,r.attr("data-options",JSON.stringify(d)),r.find(".wcf-item-selector"));if(0<t.length){const c=r.data("options");c.variation_id=e.variation_id,r.attr("data-options",JSON.stringify(c))}r.find(".wcf-display-quantity").html(e.display_quantity),r.find(".wcf-display-price").html(e.display_price),r.find(".wcf-display-discount-value").html(e.display_discount_value),r.find(".wcf-display-discount-percent").html(e.display_discount_percent),f.find(".wcf_subscription_price").html(e.display_subscription_price),f.find(".wcf_subscription_period").html(e.display_subscription_details),f.find(".wcf_subscription_fee").html(e.display_signup_fee),f.find(".wcf_subscription_free_trial").html(e.trial_period_string)}p(document.body).trigger("wc_fragment_refresh"),u.trigger("wcf_added_to_cart",[o])}}))},f.prototype.updateButton=function(){p("body").trigger("update_checkout"),c.trigger("click")},new f}}function o(){c(),p(".wcf-embed-checkout-form-two-step .woocommerce").addClass("step-one"),p(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps a").on("click",function(e){e.preventDefault(),c()}),p(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-nav-btns a").on("click",function(e){{var t;e.preventDefault(),a()&&(p(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div.wcf-current").removeClass("wcf-current"),"#customer_details"===(t=p(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-nav-btns a").attr("href"))?(m(p(".wcf-embed-checkout-form-nav")),p(".wcf-embed-checkout-form-two-step .woocommerce").removeClass("step-two"),p(".wcf-embed-checkout-form-two-step .woocommerce").addClass("step-one"),p(".wcf-embed-checkout-form-two-step").find(".step-one").addClass("wcf-current")):"#wcf-order-wrap"===t&&(m(p(".wcf-embed-checkout-form-nav")),p(".wcf-embed-checkout-form-two-step .woocommerce").removeClass("step-one"),p(".wcf-embed-checkout-form-two-step .woocommerce").addClass("step-two"),p(".wcf-embed-checkout-form-two-step").find(".step-two").addClass("wcf-current")))}})}function c(){p(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps a").on("click",function(e){if(e.preventDefault(),a()){const t=p(this),c=t.closest(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div");p(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div.wcf-current").removeClass("wcf-current"),c.addClass("wcf-current");e=t.closest(".wcf-embed-checkout-form-two-step .wcf-embed-checkout-form-steps div a").attr("href");"#customer_details"===e?(p(".wcf-embed-checkout-form-two-step .woocommerce").removeClass("step-two"),p(".wcf-embed-checkout-form-two-step .woocommerce").addClass("step-one")):"#wcf-order-wrap"===e&&(p(".wcf-embed-checkout-form-two-step .woocommerce").removeClass("step-one"),p(".wcf-embed-checkout-form-two-step .woocommerce").addClass("step-two"))}})}function i(){let u=!1;p(document).on("click",".wcf-ob-qty-selection-btn",function(e){if(e.preventDefault(),!0===u)return!1;u=!0;const t=p(this),c=t.parents(".wcf-ob-qty-selection-wrap").find(".wcf-order-bump-quantity-updater"),o=c.attr("min");let i="";var a=parseInt(c.val(),10);i=p(e.target).hasClass("wcf-ob-qty-increment")?a+1:a<=o?o:a-1,c.val(i);{e=t,a=i;const r=e.closest(".wcf-bump-order-wrap"),s=r.find("#wcf-bump-order-cb"),n=s.attr("data-ob_data"),d=s.data("ob_data"),l=d.product_id,f={_wcf_product_id:l,_bump_offer_data:n||"",_bump_offer_qty:0<a?a:"",security:cartflows.wcf_update_order_bump_qty_nonce,action:"wcf_update_order_bump_qty"};if(!1!==s.is(":checked"))f._wcf_bump_product_action="add_bump_product",r.block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),w(),p.ajax({url:cartflows.ajax_url,data:f,dataType:"json",type:"POST",success(e){h(),_(e)},error(){p(".woocommerce-checkout-review-order-table").unblock()}})}u=!1,p(".wcf-order-bump-quantity-updater").trigger("change")})}const u={vars:{originalTitle:document.title,interval:null},On(e,t){const c=this;c.vars.interval=setInterval(function(){document.title=c.vars.originalTitle===document.title?e:c.vars.originalTitle},t||1e3)},Off(){clearInterval(this.vars.interval),document.title=this.vars.originalTitle}},m=function(e){e.length&&(event.preventDefault(),p("html, body").stop().animate({scrollTop:e.offset().top-50},100))},w=function(){p(".woocommerce-checkout-review-order-table, .wcf-product-option-wrap, .wcf-bump-order-wrap").block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},_=function(e){!cartflows.wcf_refresh_checkout&&jQuery(".wc_payment_methods").length?e.hasOwnProperty("cart_total")&&(e.hasOwnProperty("fragments")&&p.each(e.fragments,function(e,t){if("#place_order"===e){const i=p(".wc_payment_methods input[name='payment_method']:checked");var c,o=0<p("#cppw-paypal-button-container .paypal-buttons #smart-menu").length;0<i.length&&(p(e).replaceWith(t),"ppcp"!==(c=i.val())&&"ppcp-gateway"!==c||p(e).addClass("ppcp-hidden"),"cppw_paypal"===c&&o&&p(e).css("display","none"),i.trigger("click"))}else p(e).replaceWith(t)}),0<parseFloat(e.cart_total)?(p("body").trigger("cartflows_update_checkout"),e.force_update&&p("body").trigger("update_checkout")):p("body").trigger("update_checkout"),p(".woocommerce-checkout-review-order-table, .wcf-product-option-wrap, .wcf-bump-order-wrap").unblock()):(p("body").trigger("update_checkout"),p(".woocommerce-checkout-review-order-table, .wcf-product-option-wrap, .wcf-bump-order-wrap").unblock())},h=function(){p(document.body).trigger("updated_cart_totals")},a=function(){var e=p(".wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-billing-fields, .wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-account-fields").find('input[type="text"], input[type="tel"], input[type="email"], input[type="password"]'),t=p(".wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-billing-fields, .wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-account-fields").find('input[type="checkbox"]'),c=p(".wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-billing-fields").find(".select2"),o=p(".wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-shipping-fields").find('input[type="text"], input[type="tel"], input[type="email"], input[type="password"]'),i=p(".wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-shipping-fields .woocommerce-shipping-fields__field-wrapper").find('input[type="checkbox"]'),a=p(".wcf-embed-checkout-form-two-step form.woocommerce-checkout .woocommerce-shipping-fields").find(".select2"),r=p(".wcf-embed-checkout-form-two-step form.woocommerce-checkout").find('h3#ship-to-different-address input[type="checkbox"]:checked').val();let s=!0,n="";return Array.from(e).forEach(function(e){const t=e.type,c=e.name,o=e.closest(".form-row"),i=p.trim(e.value);let a=o.classList.contains("validate-required");var r;"account_password"!==c&&"account_username"!==c||(r=document.getElementById("createaccount"),a=!r||!!p(r).is(":checked")),a&&""===i?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):("email"===t&&!1===/^([a-zA-Z0-9_\+\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,14})$/.test(i)&&(e.classList.add("field-required"),s=!1,""===n&&(n=e)),e.classList.remove("field-required"))}),Array.from(t).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required");let o=!1;p(e).is(":checked")&&(o=!0),c&&!1===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")}),Array.from(c).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required"),o=p.trim(t.querySelector(".select2-selection__rendered[title]"));name=t.querySelector("select").name,c&&""===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")}),"1"===r&&(Array.from(o).forEach(function(e){const t=e.type,c=e.closest(".form-row"),o=c.classList.contains("validate-required"),i=p.trim(e.value);o&&""===i?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):("email"===t&&!1===/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(i)&&(e.classList.add("field-required"),s=!1,""===n&&(n=e)),e.classList.remove("field-required"))}),Array.from(a).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required"),o=p.trim(t.querySelector(".select2-selection__rendered[title]"));name=t.querySelector("select").name,c&&""===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")}),Array.from(i).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required");let o=!1;p(e).is(":checked")&&(o=!0),c&&!1===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")})),""!==n&&n.focus(),s};p(function(){if(p(document).on("cartflows_remove_product",function(e,t){jQuery('.wcf-multiple-sel[value="'+t+'"]').prop("checked",!1),jQuery('.wcf-single-sel[value="'+t+'"]').prop("checked",!1)}),"yes"===cartflows_animate_tab_fields.enabled&&(p(window).blur(function(){u.On(cartflows_animate_tab_fields.title)}),p(window).on("focus",function(){u.Off()})),"yes"===cartflows.allow_autocomplete_zipcode){let i;p(document.body).on("textInput input change keypress paste","#billing_postcode, #shipping_postcode",function(){const e=p(this),t=e.attr("id").split("_")[0],c=p("#"+t+"_country").val();if(""!==c){const o=e.val().trim();""!==o&&(clearTimeout(i),i=setTimeout(function(){-1===["GB","CA"].indexOf(c)&&l(t,c,o)},800))}});const l=function(t,e,c){p.ajax({url:"https://api.zippopotam.us/"+e+"/"+c,cache:!0,dataType:"json",type:"GET",success(e){p.each(e.places,function(){return p("#"+t+"_city").val(this["place name"]).trigger("change"),p('[name="'+t+'_state"]:visible').val(this["state abbreviation"]).trigger("change"),!1})},error(){}})}}t();{const e=p(".wcf-multiple-sel:checked");1===e.length&&(e.prop("checked",!0),e.prop("disabled",!0))}{let f=!1;p(document).on("change",".wcf-bump-order-cb",function(){if(!0===f)return!1;f=!0;const t=p(this),c=t.data("ob_data").product_id;var e=p("[name=_wcf_checkout_id]").val();const o=t.attr("data-ob_data"),i=t.data("ob_data").ob_id;var a=t.closest(".wcf-bump-order-content").find(".wcf-order-bump-quantity-updater").val();const r=t.closest(".wcf-bump-order-field-wrap").find(".wcf-bump-order-cb-button"),s=t.closest(".wcf-bump-order-wrap");let n="";const d={post_data:p("form.checkout").serialize(),security:cartflows.wcf_bump_order_process_nonce,_wcf_checkout_id:e,_wcf_product_id:c,_bump_offer_data:o||"",_bump_offer_qty:0<a?a:"",action:"wcf_bump_order_process"};let l=p("[name=_wcf_bump_products]").val();return l=""===l?{}:JSON.parse(l),n=t.is(":checked")?(d._wcf_bump_product_action="add_bump_product",!0):!(d._wcf_bump_product_action="remove_bump_product"),s.block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),p.ajax({url:cartflows.ajax_url,data:d,dataType:"json",type:"POST",success(e){_(e),0<r.length&&(n?(r.toggleClass("wcf-bump-add-to-cart wcf-bump-remove-from-cart"),r.text(t.data("remove"))):(r.toggleClass("wcf-bump-remove-from-cart wcf-bump-add-to-cart"),r.text(t.data("add")))),e.cart_item_key?(t.is(":checked")?l[i]={id:c,price:e.cartflows_data.total_product_price}:delete l[i],p("[name=_wcf_bump_products]").val(JSON.stringify(l))):t.is(":checked")&&p("[name=wcf-bump-order-cb-"+i+"]").removeAttr("checked"),h()},error(){p(".woocommerce-checkout-review-order-table").unblock()}}),f=!1})}p(document).on("click",".wcf-bump-order-cb-button",function(){const e=p(this),t=e.closest(".wcf-bump-order-action"),c=t.find('input[type="checkbox"]');e.hasClass("wcf-bump-add-to-cart")?(c.attr("checked",!0),e.text(e.data("adding"))):(c.attr("checked",!1),e.text(e.data("removing"))),t.find(".wcf-bump-order-cb").trigger("change")}),0<p(".wcf-embed-checkout-form-two-step").length&&o(),p(document).on("wcf_cart_data_restored",function(e,t){"yes"===cartflows.is_product_options&&t&&p(".wcf-qty-row").each(function(){const e=p(this).data("options");e&&e.unique_id in t&&(e.cart_item_key=t[e.unique_id],p(this).attr("data-options",JSON.stringify(e)))})}),p(".wcf-qty-selection-btn").click(function(e){e.preventDefault();const t=p(this).parents(".wcf-qty").find(".wcf-qty-selection"),c=Boolean(t.data("sale-limit"));if(c)return!1;var o=parseInt(t.val(),10),i=parseInt(t.attr("min")),a=parseInt(t.attr("max"));p(e.target).hasClass("wcf-qty-increment")?(t.val(o+1),a&&t.val()>=a?p(e.target).addClass("max-quantity-reached"):p(e.target).removeClass("max-quantity-reached")):(p(".wcf-qty-increment").removeClass("max-quantity-reached"),t.val(o<=i?i:o-1)),p(".wcf-qty-selection").trigger("change")}),i();{let o="billing",i="billing";function c(){const e=p(".wcf-multistep-nav-next-btn"),t=p(".wcf-multistep-nav-back-btn");"shipping"===o&&p(".wcf-embed-checkout-form").removeClass("wcf-shipping"),"payment"===o&&p(".wcf-embed-checkout-form").removeClass("wcf-payment"),p(".wcf-embed-checkout-form").addClass("wcf-billing"),cartflows.is_hide_shipping_tab?e.attr("data-target","payment"):e.attr("data-target","shipping"),t.attr("data-target",""),p(".wcf-checkout-breadcrumb.information-step a").addClass("wcf-current-step"),e.text(cartflows.multistep_buttons_strings.billing)}function a(){const e=p(".wcf-multistep-nav-next-btn"),t=p(".wcf-multistep-nav-back-btn");"billing"===o&&p(".wcf-embed-checkout-form").removeClass("wcf-billing"),"payment"===o&&p(".wcf-embed-checkout-form").removeClass("wcf-payment"),p(".wcf-embed-checkout-form").addClass("wcf-shipping"),e.attr("data-target","payment"),t.attr("data-target","billing"),p(".wcf-checkout-breadcrumb.shipping-step a").addClass("wcf-current-step"),s(),e.text(cartflows.multistep_buttons_strings.shipping)}function r(){const e=p(".wcf-multistep-nav-back-btn");"shipping"===o&&p(".wcf-embed-checkout-form").removeClass("wcf-shipping"),"billing"===o&&p(".wcf-embed-checkout-form").removeClass("wcf-billing"),cartflows.is_hide_shipping_tab?e.attr("data-target","billing"):e.attr("data-target","shipping"),p(".wcf-embed-checkout-form").addClass("wcf-payment"),p(".wcf-checkout-breadcrumb.payment-step a").addClass("wcf-current-step"),s()}function s(){p(".wcf-review-detail-content.contact-details").text(p(".wcf-embed-checkout-form form.woocommerce-checkout").find('input[type="email"]').val())}function n(e="wcf-embed-checkout-form",t=1e3){p("html, body").animate({scrollTop:p("#"+e).offset().top},t)}function d(){p(".wcf-current-step").length&&p(".wcf-current-step").removeClass("wcf-current-step")}p(document).on("click",".wcf-multistep-nav-next-btn",function(e){e.preventDefault();const t=p(".wcf-multistep-nav-next-btn"),c=t.attr("data-target");i!==c&&f()&&(d(),o=i,"shipping"===(i=c)?a():"payment"===c&&r(),n())}),p(document).on("click",".wcf-multistep-nav-back-btn",function(e){e.preventDefault();const t=p(".wcf-multistep-nav-back-btn");previous_step_element_target=t.attr("data-target"),i!==previous_step_element_target&&f()&&(d(),o=i,""===(i=previous_step_element_target)?t.css("visibility","hidden"):"shipping"===previous_step_element_target?a():"billing"===previous_step_element_target&&c(),n())}),p(document).on("click",".wcf-checkout-breadcrumb a",function(e){e.preventDefault();e=p(e.target).attr("data-tab");i!==e&&f()&&(o=i,i=e,d(),"billing"===e?c():"shipping"===e?a():"payment"===e&&r(),n())}),p(document).on("click",".wcf-step-link",function(e){e.preventDefault();e=p(e.target).attr("data-target");i!==e&&f()&&(o=i,i=e,d(),"billing"===e?c():"shipping"===e&&a())});const f=function(){let s=!0,n="";var e,t,c;return"billing"===i&&(e=p(".wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-billing-fields-custom, .wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-billing-fields, .wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-account-fields").find('input[type="text"], input[type="tel"], input[type="email"], input[type="password"]'),t=p(".wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-billing-fields, .wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-account-fields").find('input[type="checkbox"]'),c=p(".wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-billing-fields").find(".select2"),Array.from(e).forEach(function(e){const t=e.type,c=e.name,o=e.closest(".form-row"),i=p.trim(e.value);let a=o.classList.contains("validate-required");var r;"billing_password"===c&&(a=!(!p(".wcf-email-validation-block").hasClass("success")||cartflows?.multistep_buttons_strings?.is_guest_checkout)),"account_password"!==c&&"account_username"!==c||(r=document.getElementById("createaccount"),a=!r||!!p(r).is(":checked")),a&&""===i?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):("email"===t&&!1===/^([a-zA-Z0-9_\+\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,14})$/.test(i)&&(e.classList.add("field-required"),s=!1,""===n&&(n=e)),e.classList.remove("field-required"))}),Array.from(t).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required");let o=!1;p(e).is(":checked")&&(o=!0),c&&!1===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")}),Array.from(c).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required"),o=p.trim(t.querySelector(".select2-selection__rendered[title]"));name=t.querySelector("select").name,c&&""===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")})),"shipping"===i&&(e=p(".wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-shipping-fields").find('input[type="text"], input[type="tel"], input[type="email"], input[type="password"]'),t=p(".wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-shipping-fields .woocommerce-shipping-fields__field-wrapper").find('input[type="checkbox"]'),c=p(".wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout .woocommerce-shipping-fields").find(".select2"),"1"===p(".wcf-embed-checkout-form-modern-checkout.wcf-modern-skin-multistep form.woocommerce-checkout").find('h3#ship-to-different-address input[type="checkbox"]:checked').val()&&(Array.from(e).forEach(function(e){const t=e.type,c=e.closest(".form-row"),o=c.classList.contains("validate-required"),i=p.trim(e.value);o&&""===i?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):("email"===t&&!1===/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(i)&&(e.classList.add("field-required"),s=!1,""===n&&(n=e)),e.classList.remove("field-required"))}),Array.from(c).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required"),o=p.trim(t.querySelector(".select2-selection__rendered[title]"));name=t.querySelector("select").name,c&&""===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")}),Array.from(t).forEach(function(e){const t=e.closest(".form-row"),c=t.classList.contains("validate-required");let o=!1;p(e).is(":checked")&&(o=!0),c&&!1===o?(e.classList.add("field-required"),s=!1,""===n&&(n=e)):e.classList.remove("field-required")}))),""!==n&&n.focus(),s};return}})}(jQuery);