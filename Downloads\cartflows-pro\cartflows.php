<?php
/**
 * Plugin Name: CartFlows
 * Plugin URI: https://cartflows.com/
 * Description: Create beautiful checkout pages & sales flows for WooCommerce.
 * Version: 2.1.14
 * Author: Brainstorm Force
 * Author URI: https://www.brainstormforce.com
 * Text Domain: cartflows
 * WC requires at least: 3.0
 * WC tested up to: 9.8.5
 * Elementor tested up to: 3.28.4
 * Requires Plugins: woocommerce
 * 
 * @package CartFlows
 */

/**
 * Set constants.
 */
define( 'CARTFLOWS_FILE', __FILE__ );

/**
 * Loader
 */
require_once 'classes/class-cartflows-loader.php';
