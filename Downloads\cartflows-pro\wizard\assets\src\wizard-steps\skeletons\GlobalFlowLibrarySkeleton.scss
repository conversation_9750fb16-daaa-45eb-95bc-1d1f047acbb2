.wcf-flow-importer__list.is-placeholder {
	.wcf-item {
		padding-left: 0;
		padding-right: 0;
		margin-right: 0;
		margin-left: 0;
	}

	.wcf-item__heading-wrap {
		padding: 8px;
	}

	.wcf-skeleton--wave {
		overflow: hidden;
		position: relative;

		&::after {
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			content: "";
			position: absolute;
			animation: wcf-skeleton-keyframes-wave 1.6s linear 0.5s infinite;
			transform: translateX( -100% );
			background: linear-gradient( 90deg, transparent, rgba( 0, 0, 0, 0.04 ), transparent );
		}
	}

	.wcf-skeleton {
		display: block;
		background-color: rgba( 0, 0, 0, 0.11 );
	}
}

@keyframes wcf-skeleton-keyframes-wave {
	0% {
		transform: translateX( -100% );
	}

	60% {
		transform: translateX( 100% );
	}

	100% {
		transform: translateX( 100% );
	}
}
