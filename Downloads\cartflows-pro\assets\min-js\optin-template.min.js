(o=>{function t(){function c(e,a,l,t){""===e||"select"===t&&" "===e?a.hasClass("validate-required")&&l.addClass("field-required"):(l.removeClass("field-required"),a.find(".wcf-field-required-error").remove())}var e=(a=o("form.woocommerce-checkout #customer_details")).find("input, textarea"),a=a.find("select");e.on("blur",function(){var e,a=o(this),l=a.attr("type"),t=a.closest("p.form-row"),s=a.val();c(s,t,a,l),"number"===l&&(l=a.attr("min"),e=a.attr("max"),s=Number(s),t=t,a=a,l=Number(l),e=Number(e),""===s||s<l||e<s?(a.addClass("field-required"),a.after('<span class="wcf-field-required-error">'+cartflows.field_validation_msgs.number_field+l+" & "+e+"</span>")):(a.removeClass("field-required"),t.find(".wcf-field-required-error").remove()))}),a.on("blur",function(){var e=o(this).closest("p.form-row"),a=e.find(".select2-container--default"),l=e.find("select").val();c(l,e,a,"select")})}function c(){if("yes"===cartflows.allow_persistence&&!1!==(()=>{var e="test";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch(e){return!1}})()){var a="form.woocommerce-checkout #customer_details";let e={set(){let e=[];var a=o("form.woocommerce-checkout #customer_details");localStorage.removeItem("cartflows_checkout_form"),a.find("input[type=text], select, input[type=email], input[type=tel]").each(function(){e.push({name:this.name,value:this.value})}),cartflows_checkout_form=JSON.stringify(e),localStorage.setItem("cartflows_checkout_form",cartflows_checkout_form)},get(){if(null!==localStorage.getItem("cartflows_checkout_form")){checkout_data=JSON.parse(localStorage.getItem("cartflows_checkout_form"));for(let e=0;e<checkout_data.length;e++)o("form.woocommerce-checkout [name="+checkout_data[e].name+"]").hasClass("select2-hidden-accessible")?o("form.woocommerce-checkout [name="+checkout_data[e].name+"]").selectWoo("val",[checkout_data[e].value]):o("form.woocommerce-checkout [name="+checkout_data[e].name+"]").val(checkout_data[e].value)}}};e.get(),o(a+" input, "+a+" select").on("change",function(){e.set()})}}o(function(){function s(e,a,l){(""!==e||" "!==e&&"select"===a)&&l.addClass("wcf-anim-label"),"checkbox"===a&&l.removeClass("wcf-anim-label"),"hidden"===a&&(l.removeClass("wcf-anim-label"),l.addClass("wcf-anim-label-fix"))}var e,a,l;c(),t(),o(".wcf-field-floating-labels").length<1||(e=o(".wcf-field-floating-labels"),a=e.find("input"),l=e.find(".select2"),e=e.find("textarea"),a.on("focus",function(){var e=o(this),a=e.closest(".form-row");has_class=a.hasClass("wcf-anim-label"),""===(field_value=e.val())&&a.addClass("wcf-anim-label")}),a.on("focusout",function(){var e=o(this),a=e.closest(".form-row");has_class=a.hasClass("wcf-anim-label"),""===(field_value=e.val())?a.removeClass("wcf-anim-label"):a.addClass("wcf-anim-label")}),l.on("click",function(){var e=o(this),a=e.closest(".form-row");has_class=a.hasClass("wcf-anim-label"),""===(field_value=e.find(".select2-selection__rendered").text())&&a.addClass("wcf-anim-label")}),l.on("focusout",function(){var e=o(this),a=e.closest(".form-row");has_class=a.hasClass("wcf-anim-label"),""===(field_value=e.find(".select2-selection__rendered").text())?a.removeClass("wcf-anim-label"):a.addClass("wcf-anim-label")}),e.on("click",function(){var e=o(this),a=e.closest(".form-row");has_class=a.hasClass("wcf-anim-label"),""===(field_value=e.val())&&a.addClass("wcf-anim-label")}),e.on("focusout",function(){var e=o(this),a=e.closest(".form-row");has_class=a.hasClass("wcf-anim-label"),""===(field_value=e.val())?a.removeClass("wcf-anim-label"):a.addClass("wcf-anim-label")}),a=o(".wcf-field-floating-labels"),l=a.find("input"),o(l).each(function(){var e=o(this),a=e.attr("type"),l=e.closest(".form-row"),t=e.val();e.attr("placeholder",""),s(t,a,l)}),l=a.find("select"),o(l).each(function(){var e=o(this),a=e.closest(".form-row");s(e.val(),"select",a)}))})})(jQuery);