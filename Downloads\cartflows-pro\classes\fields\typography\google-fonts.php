<?php
// phpcs:ignoreFile
/**
 * Google fonts array file.
 *
 * @package     CartFlows
 * <AUTHOR> Inc
 * @copyright   Copyright (c) 2021, CartFlows Inc
 * @link        https://cartflows.com/
 */

/**
 * Returns google fonts array
 *
 * @since 1.6.10
 */
return array(
	array(
		'ABeeZee' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Abel' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Abhaya Libre' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'Abril Fatface' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Aclonica' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Acme' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Actor' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Adamina' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Advent Pro' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Aguafina Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Akaya Kanadaka' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Akaya Telivigala' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Akronim' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Aladin' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Alata' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Alatsi' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Aldrich' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Alef' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Alegreya' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Alegreya SC' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Alegreya Sans' => array(
			'variants' => array( '100', '100italic', '300', '300italic', '400', 'italic', '500', '500italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Alegreya Sans SC' => array(
			'variants' => array( '100', '100italic', '300', '300italic', '400', 'italic', '500', '500italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Aleo' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Alex Brush' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Alfa Slab One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Alice' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Alike' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Alike Angular' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Allan' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Allerta' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Allerta Stencil' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Allura' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Almarai' => array(
			'variants' => array( '300', '400', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Almendra' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Almendra Display' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Almendra SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Amarante' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Amaranth' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Amatic SC' => array(
			'variants' => array( '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Amethysta' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Amiko' => array(
			'variants' => array( '400', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Amiri' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Amita' => array(
			'variants' => array( '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Anaheim' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Andada' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Andika' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Andika New Basic' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Angkor' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Annie Use Your Telescope' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Anonymous Pro' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Antic' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Antic Didone' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Antic Slab' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Anton' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Arapey' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Arbutus' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Arbutus Slab' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Architects Daughter' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Archivo' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Archivo Black' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Archivo Narrow' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Aref Ruqaa' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Arima Madurai' => array(
			'variants' => array( '100', '200', '300', '400', '500', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Arimo' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Arizonia' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Armata' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Arsenal' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Artifika' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Arvo' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Arya' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Asap' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Asap Condensed' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Asar' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Asset' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Assistant' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Astloch' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Asul' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Athiti' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Atma' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Atomic Age' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Aubrey' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Audiowide' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Autour One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Average' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Average Sans' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Averia Gruesa Libre' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Averia Libre' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'Averia Sans Libre' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'Averia Serif Libre' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'B612' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'B612 Mono' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Bad Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Bahiana' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bahianita' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bai Jamjuree' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ballet' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Baloo 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Bhai 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Bhaina 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Chettan 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Da 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Paaji 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Tamma 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Tammudu 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Baloo Thambi 2' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Balsamiq Sans' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'Balthazar' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Bangers' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Barlow' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Barlow Condensed' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Barlow Semi Condensed' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Barriecito' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Barrio' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Basic' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Baskervville' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Battambang' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Baumans' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bayon' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Be Vietnam' => array(
			'variants' => array( '100', '100italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Bebas Neue' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Belgrano' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Bellefair' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Belleza' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Bellota' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'Bellota Text' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'BenchNine' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Benne' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Bentham' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Berkshire Swash' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Beth Ellen' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Bevan' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Big Shoulders Display' => array(
			'variants' => array( '100', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Big Shoulders Inline Display' => array(
			'variants' => array( '100', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Big Shoulders Inline Text' => array(
			'variants' => array( '100', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Big Shoulders Stencil Display' => array(
			'variants' => array( '100', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Big Shoulders Stencil Text' => array(
			'variants' => array( '100', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Big Shoulders Text' => array(
			'variants' => array( '100', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Bigelow Rules' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bigshot One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bilbo' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Bilbo Swash Caps' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'BioRhyme' => array(
			'variants' => array( '200', '300', '400', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'BioRhyme Expanded' => array(
			'variants' => array( '200', '300', '400', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'Biryani' => array(
			'variants' => array( '200', '300', '400', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Bitter' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Black And White Picture' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Black Han Sans' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Black Ops One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Blinker' => array(
			'variants' => array( '100', '200', '300', '400', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Bodoni Moda' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Bokor' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bonbon' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Boogaloo' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bowlby One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bowlby One SC' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Brawler' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Bree Serif' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Brygada 1918' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Bubblegum Sans' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bubbler One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Buda' => array(
			'variants' => array( '300' ),
			'category' => 'display',
		),
	),
	array(
		'Buenard' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Bungee' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bungee Hairline' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bungee Inline' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bungee Outline' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Bungee Shade' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Butcherman' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Butterfly Kids' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Cabin' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cabin Condensed' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cabin Sketch' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Caesar Dressing' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Cagliostro' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cairo' => array(
			'variants' => array( '200', '300', '400', '600', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Caladea' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Calistoga' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Calligraffitti' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Cambay' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cambo' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Candal' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cantarell' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cantata One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Cantora One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Capriola' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cardo' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Carme' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Carrois Gothic' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Carrois Gothic SC' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Carter One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Castoro' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Catamaran' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Caudex' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Caveat' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Caveat Brush' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Cedarville Cursive' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Ceviche One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Chakra Petch' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Changa' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Changa One' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'display',
		),
	),
	array(
		'Chango' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Charm' => array(
			'variants' => array( '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Charmonman' => array(
			'variants' => array( '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Chathura' => array(
			'variants' => array( '100', '300', '400', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Chau Philomene One' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Chela One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Chelsea Market' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Chenla' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Cherry Cream Soda' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Cherry Swash' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Chewy' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Chicle' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Chilanka' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Chivo' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Chonburi' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Cinzel' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Cinzel Decorative' => array(
			'variants' => array( '400', '700', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Clicker Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Coda' => array(
			'variants' => array( '400', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Coda Caption' => array(
			'variants' => array( '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Codystar' => array(
			'variants' => array( '300', '400' ),
			'category' => 'display',
		),
	),
	array(
		'Coiny' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Combo' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Comfortaa' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Comic Neue' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Coming Soon' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Commissioner' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Concert One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Condiment' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Content' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Contrail One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Convergence' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cookie' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Copse' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Corben' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Cormorant' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Cormorant Garamond' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Cormorant Infant' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Cormorant SC' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Cormorant Unicase' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Cormorant Upright' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Courgette' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Courier Prime' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Cousine' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Coustard' => array(
			'variants' => array( '400', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Covered By Your Grace' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Crafty Girls' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Creepster' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Crete Round' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Crimson Pro' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800', '900', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Crimson Text' => array(
			'variants' => array( '400', 'italic', '600', '600italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Croissant One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Crushed' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Cuprum' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Cute Font' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Cutive' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Cutive Mono' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'DM Mono' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '500', '500italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'DM Sans' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'DM Serif Display' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'DM Serif Text' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Damion' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Dancing Script' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Dangrek' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Darker Grotesque' => array(
			'variants' => array( '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'David Libre' => array(
			'variants' => array( '400', '500', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Dawning of a New Day' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Days One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Dekko' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Dela Gothic One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Delius' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Delius Swash Caps' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Delius Unicase' => array(
			'variants' => array( '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Della Respira' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Denk One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Devonshire' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Dhurjati' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Didact Gothic' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Diplomata' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Diplomata SC' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Do Hyeon' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Dokdo' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Domine' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Donegal One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Doppio One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Dorsa' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Dosis' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'DotGothic16' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Dr Sugiyama' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Duru Sans' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Dynalight' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'EB Garamond' => array(
			'variants' => array( '400', '500', '600', '700', '800', 'italic', '500italic', '600italic', '700italic', '800italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Eagle Lake' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'East Sea Dokdo' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Eater' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Economica' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Eczar' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'El Messiri' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Electrolize' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Elsie' => array(
			'variants' => array( '400', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Elsie Swash Caps' => array(
			'variants' => array( '400', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Emblema One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Emilys Candy' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Encode Sans' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Encode Sans Condensed' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Encode Sans Expanded' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Encode Sans Semi Condensed' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Encode Sans Semi Expanded' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Engagement' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Englebert' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Enriqueta' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Epilogue' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Erica One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Esteban' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Euphoria Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Ewert' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Exo' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Exo 2' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Expletus Sans' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'Fahkwang' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Fanwood Text' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Farro' => array(
			'variants' => array( '300', '400', '500', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Farsan' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fascinate' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fascinate Inline' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Faster One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fasthand' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Fauna One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Faustina' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Federant' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Federo' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Felipa' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Fenix' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Finger Paint' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fira Code' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'monospace',
		),
	),
	array(
		'Fira Mono' => array(
			'variants' => array( '400', '500', '700' ),
			'category' => 'monospace',
		),
	),
	array(
		'Fira Sans' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Fira Sans Condensed' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Fira Sans Extra Condensed' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Fjalla One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Fjord One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Flamenco' => array(
			'variants' => array( '300', '400' ),
			'category' => 'display',
		),
	),
	array(
		'Flavors' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fondamento' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Fontdiner Swanky' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Forum' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Francois One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Frank Ruhl Libre' => array(
			'variants' => array( '300', '400', '500', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Fraunces' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Freckle Face' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fredericka the Great' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fredoka One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Freehand' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fresca' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Frijole' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fruktur' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Fugaz One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'GFS Didot' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'GFS Neohellenic' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Gabriela' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Gaegu' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Gafata' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Galada' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Galdeano' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Galindo' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Gamja Flower' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Gayathri' => array(
			'variants' => array( '100', '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Gelasio' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Gentium Basic' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Gentium Book Basic' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Geo' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Geostar' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Geostar Fill' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Germania One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Gidugu' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Gilda Display' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Girassol' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Give You Glory' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Glass Antiqua' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Glegoo' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Gloria Hallelujah' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Goblin One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Gochi Hand' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Goldman' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Gorditas' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Gothic A1' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Gotu' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Goudy Bookletter 1911' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Graduate' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Grand Hotel' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Grandstander' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'display',
		),
	),
	array(
		'Gravitas One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Great Vibes' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Grenze' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Grenze Gotisch' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Griffy' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Gruppo' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Gudea' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Gugi' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Gupter' => array(
			'variants' => array( '400', '500', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Gurajada' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Habibi' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Hachi Maru Pop' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Halant' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Hammersmith One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Hanalei' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Hanalei Fill' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Handlee' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Hanuman' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Happy Monkey' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Harmattan' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Headland One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Heebo' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Henny Penny' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Hepta Slab' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Herr Von Muellerhoff' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Hi Melody' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Hind' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Hind Guntur' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Hind Madurai' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Hind Siliguri' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Hind Vadodara' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Holtwood One SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Homemade Apple' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Homenaje' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'IBM Plex Mono' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'IBM Plex Sans' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'IBM Plex Sans Condensed' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'IBM Plex Serif' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell DW Pica' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell DW Pica SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell Double Pica' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell Double Pica SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell English' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell English SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell French Canon' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell French Canon SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell Great Primer' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'IM Fell Great Primer SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Ibarra Real Nova' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Iceberg' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Iceland' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Imbue' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Imprima' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Inconsolata' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'monospace',
		),
	),
	array(
		'Inder' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Indie Flower' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Inika' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Inknut Antiqua' => array(
			'variants' => array( '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Inria Sans' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Inria Serif' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Inter' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Irish Grover' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Istok Web' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Italiana' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Italianno' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Itim' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Jacques Francois' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Jacques Francois Shadow' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Jaldi' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'JetBrains Mono' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Jim Nightshade' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Jockey One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Jolly Lodger' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Jomhuria' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Jomolhari' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Josefin Sans' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Josefin Slab' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Jost' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Joti One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Jua' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Judson' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Julee' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Julius Sans One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Junge' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Jura' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Just Another Hand' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Just Me Again Down Here' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'K2D' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kadwa' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Kalam' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Kameron' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Kanit' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kantumruy' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Karla' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Karma' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Katibeh' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kaushan Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Kavivanar' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Kavoon' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kdam Thmor' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Keania One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kelly Slab' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kenia' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Khand' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Khmer' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Khula' => array(
			'variants' => array( '300', '400', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kirang Haerang' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kite One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kiwi Maru' => array(
			'variants' => array( '300', '400', '500' ),
			'category' => 'serif',
		),
	),
	array(
		'Knewave' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'KoHo' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kodchasan' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kosugi' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kosugi Maru' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kotta One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Koulen' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kranky' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kreon' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Kristi' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Krona One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Krub' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kufam' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'display',
		),
	),
	array(
		'Kulim Park' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kumar One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kumar One Outline' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Kumbh Sans' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Kurale' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'La Belle Aurore' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Lacquer' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Laila' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lakki Reddy' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Lalezar' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Lancelot' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Langar' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Lateef' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Lato' => array(
			'variants' => array( '100', '100italic', '300', '300italic', '400', 'italic', '700', '700italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'League Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Leckerli One' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Ledger' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Lekton' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lemon' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Lemonada' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Lexend' => array(
			'variants' => array( '100', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lexend Deca' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lexend Exa' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lexend Giga' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lexend Mega' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lexend Peta' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lexend Tera' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lexend Zetta' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Libre Barcode 128' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Libre Barcode 128 Text' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Libre Barcode 39' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Libre Barcode 39 Extended' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Libre Barcode 39 Extended Text' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Libre Barcode 39 Text' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Libre Barcode EAN13 Text' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Libre Baskerville' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Libre Caslon Display' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Libre Caslon Text' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Libre Franklin' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Life Savers' => array(
			'variants' => array( '400', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Lilita One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Lily Script One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Limelight' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Linden Hill' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Literata' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800', '900', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Liu Jian Mao Cao' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Livvic' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Lobster' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Lobster Two' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'Londrina Outline' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Londrina Shadow' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Londrina Sketch' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Londrina Solid' => array(
			'variants' => array( '100', '300', '400', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Long Cang' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Lora' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Love Ya Like A Sister' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Loved by the King' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Lovers Quarrel' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Luckiest Guy' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Lusitana' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Lustria' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'M PLUS 1p' => array(
			'variants' => array( '100', '300', '400', '500', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'M PLUS Rounded 1c' => array(
			'variants' => array( '100', '300', '400', '500', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ma Shan Zheng' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Macondo' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Macondo Swash Caps' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Mada' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Magra' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Maiden Orange' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Maitree' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Major Mono Display' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'Mako' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mali' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Mallanna' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mandali' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Manjari' => array(
			'variants' => array( '100', '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Manrope' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mansalva' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Manuale' => array(
			'variants' => array( '400', '500', '600', '700', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Marcellus' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Marcellus SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Marck Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Margarine' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Markazi Text' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Marko One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Marmelad' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Martel' => array(
			'variants' => array( '200', '300', '400', '600', '700', '800', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Martel Sans' => array(
			'variants' => array( '200', '300', '400', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Marvel' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mate' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Mate SC' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Maven Pro' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'McLaren' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Meddon' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'MedievalSharp' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Medula One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Meera Inimai' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Megrim' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Meie Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Merienda' => array(
			'variants' => array( '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Merienda One' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Merriweather' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic', '900', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Merriweather Sans' => array(
			'variants' => array( '300', '400', '500', '600', '700', '800', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Metal' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Metal Mania' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Metamorphous' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Metrophobic' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Michroma' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Milonga' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Miltonian' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Miltonian Tattoo' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Mina' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Miniver' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Miriam Libre' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mirza' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Miss Fajardose' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Mitr' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Modak' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Modern Antiqua' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Mogra' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Molengo' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Molle' => array(
			'variants' => array( 'italic' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Monda' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Monofett' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Monoton' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Monsieur La Doulaise' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Montaga' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Montez' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Montserrat' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Montserrat Alternates' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Montserrat Subrayada' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Moul' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Moulpali' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Mountains of Christmas' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Mouse Memoirs' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mr Bedfort' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Mr Dafoe' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Mr De Haviland' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Mrs Saint Delafield' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Mrs Sheppards' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Mukta' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mukta Mahee' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mukta Malar' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mukta Vaani' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Mulish' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800', '900', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'MuseoModerno' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Mystery Quest' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'NTR' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Nanum Brush Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Nanum Gothic' => array(
			'variants' => array( '400', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Nanum Gothic Coding' => array(
			'variants' => array( '400', '700' ),
			'category' => 'monospace',
		),
	),
	array(
		'Nanum Myeongjo' => array(
			'variants' => array( '400', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'Nanum Pen Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Nerko One' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Neucha' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Neuton' => array(
			'variants' => array( '200', '300', '400', 'italic', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'New Rocker' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'New Tegomin' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'News Cycle' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Newsreader' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Niconne' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Niramit' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Nixie One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Nobile' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Nokora' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Norican' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Nosifer' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Notable' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Nothing You Could Do' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Noticia Text' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Noto Sans' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Noto Sans HK' => array(
			'variants' => array( '100', '300', '400', '500', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Noto Sans JP' => array(
			'variants' => array( '100', '300', '400', '500', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Noto Sans KR' => array(
			'variants' => array( '100', '300', '400', '500', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Noto Sans SC' => array(
			'variants' => array( '100', '300', '400', '500', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Noto Sans TC' => array(
			'variants' => array( '100', '300', '400', '500', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Noto Serif' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Noto Serif JP' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Noto Serif KR' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Noto Serif SC' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Noto Serif TC' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Nova Cut' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Nova Flat' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Nova Mono' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'Nova Oval' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Nova Round' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Nova Script' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Nova Slim' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Nova Square' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Numans' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Nunito' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Nunito Sans' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Odibee Sans' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Odor Mean Chey' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Offside' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Oi' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Old Standard TT' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Oldenburg' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Oleo Script' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Oleo Script Swash Caps' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Open Sans' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic', '800', '800italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Open Sans Condensed' => array(
			'variants' => array( '300', '300italic', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Oranienbaum' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Orbitron' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Oregano' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'display',
		),
	),
	array(
		'Orienta' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Original Surfer' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Oswald' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Over the Rainbow' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Overlock' => array(
			'variants' => array( '400', 'italic', '700', '700italic', '900', '900italic' ),
			'category' => 'display',
		),
	),
	array(
		'Overlock SC' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Overpass' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Overpass Mono' => array(
			'variants' => array( '300', '400', '600', '700' ),
			'category' => 'monospace',
		),
	),
	array(
		'Ovo' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Oxanium' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Oxygen' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Oxygen Mono' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'PT Mono' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'PT Sans' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'PT Sans Caption' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'PT Sans Narrow' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'PT Serif' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'PT Serif Caption' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Pacifico' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Padauk' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Palanquin' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Palanquin Dark' => array(
			'variants' => array( '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Pangolin' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Paprika' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Parisienne' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Passero One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Passion One' => array(
			'variants' => array( '400', '700', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Pathway Gothic One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Patrick Hand' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Patrick Hand SC' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Pattaya' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Patua One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Pavanam' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Paytone One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Peddana' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Peralta' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Permanent Marker' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Petit Formal Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Petrona' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Philosopher' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Piazzolla' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Piedra' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Pinyon Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Pirata One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Plaster' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Play' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Playball' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Playfair Display' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Playfair Display SC' => array(
			'variants' => array( '400', 'italic', '700', '700italic', '900', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Podkova' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'Poiret One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Poller One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Poly' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Pompiere' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Pontano Sans' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Poor Story' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Poppins' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Port Lligat Sans' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Port Lligat Slab' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Potta One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Pragati Narrow' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Prata' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Preahvihear' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Press Start 2P' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Pridi' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Princess Sofia' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Prociono' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Prompt' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Prosto One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Proza Libre' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Public Sans' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Puritan' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Purple Purse' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Quando' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Quantico' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Quattrocento' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Quattrocento Sans' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Questrial' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Quicksand' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Quintessential' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Qwigley' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Racing Sans One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Radley' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Rajdhani' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rakkas' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Raleway' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Raleway Dots' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Ramabhadra' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ramaraja' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Rambla' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rammetto One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Ranchers' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Rancho' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Ranga' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Rasa' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Rationale' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ravi Prakash' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Recursive' => array(
			'variants' => array( '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Red Hat Display' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '700', '700italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Red Hat Text' => array(
			'variants' => array( '400', 'italic', '500', '500italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Red Rose' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Redressed' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Reem Kufi' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Reenie Beanie' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Reggae One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Revalia' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Rhodium Libre' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Ribeye' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Ribeye Marrow' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Righteous' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Risque' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Roboto' => array(
			'variants' => array( '100', '100italic', '300', '300italic', '400', 'italic', '500', '500italic', '700', '700italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Roboto Condensed' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Roboto Mono' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Roboto Slab' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Rochester' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Rock Salt' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'RocknRoll One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rokkitt' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Romanesco' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Ropa Sans' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rosario' => array(
			'variants' => array( '300', '400', '500', '600', '700', '300italic', 'italic', '500italic', '600italic', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rosarivo' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Rouge Script' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Rowdies' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Rozha One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Rubik' => array(
			'variants' => array( '300', '400', '500', '600', '700', '800', '900', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rubik Mono One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ruda' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rufina' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Ruge Boogie' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Ruluko' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Rum Raisin' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ruslan Display' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Russo One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ruthie' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Rye' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sacramento' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Sahitya' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Sail' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Saira' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Saira Condensed' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Saira Extra Condensed' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Saira Semi Condensed' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Saira Stencil One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Salsa' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sanchez' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Sancreek' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sansita' => array(
			'variants' => array( '400', 'italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sansita Swashed' => array(
			'variants' => array( '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'display',
		),
	),
	array(
		'Sarabun' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sarala' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sarina' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sarpanch' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Satisfy' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Sawarabi Gothic' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sawarabi Mincho' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Scada' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Scheherazade' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Schoolbell' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Scope One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Seaweed Script' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Secular One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sedgwick Ave' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Sedgwick Ave Display' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Sen' => array(
			'variants' => array( '400', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sevillana' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Seymour One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Shadows Into Light' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Shadows Into Light Two' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Shanti' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Share' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'display',
		),
	),
	array(
		'Share Tech' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Share Tech Mono' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'Shippori Mincho' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'Shippori Mincho B1' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'Shojumaru' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Short Stack' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Shrikhand' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Siemreap' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sigmar One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Signika' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Signika Negative' => array(
			'variants' => array( '300', '400', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Simonetta' => array(
			'variants' => array( '400', 'italic', '900', '900italic' ),
			'category' => 'display',
		),
	),
	array(
		'Single Day' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sintony' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sirin Stencil' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Six Caps' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Skranji' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Slabo 13px' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Slabo 27px' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Slackey' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Smokum' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Smythe' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sniglet' => array(
			'variants' => array( '400', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Snippet' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Snowburst One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sofadi One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sofia' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Solway' => array(
			'variants' => array( '300', '400', '500', '700', '800' ),
			'category' => 'serif',
		),
	),
	array(
		'Song Myung' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Sonsie One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sora' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sorts Mill Goudy' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Source Code Pro' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '900', '900italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Source Sans Pro' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Source Serif Pro' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic', '900', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Space Grotesk' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Space Mono' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Spartan' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Special Elite' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Spectral' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Spectral SC' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Spicy Rice' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Spinnaker' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Spirax' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Squada One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sree Krushnadevaraya' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Sriracha' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Srisakdi' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Staatliches' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Stalemate' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Stalinist One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Stardos Stencil' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Stick' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Stint Ultra Condensed' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Stint Ultra Expanded' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Stoke' => array(
			'variants' => array( '300', '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Strait' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Stylish' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sue Ellen Francisco' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Suez One' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Sulphur Point' => array(
			'variants' => array( '300', '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sumana' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Sunflower' => array(
			'variants' => array( '300', '500', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Sunshiney' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Supermercado One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Sura' => array(
			'variants' => array( '400', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Suranna' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Suravaram' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Suwannaphum' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Swanky and Moo Moo' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Syncopate' => array(
			'variants' => array( '400', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Syne' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Syne Mono' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'Syne Tactile' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Tajawal' => array(
			'variants' => array( '200', '300', '400', '500', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Tangerine' => array(
			'variants' => array( '400', '700' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Taprom' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Tauri' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Taviraj' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Teko' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Telex' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Tenali Ramakrishna' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Tenor Sans' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Text Me One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Texturina' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Thasadith' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'The Girl Next Door' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Tienne' => array(
			'variants' => array( '400', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Tillana' => array(
			'variants' => array( '400', '500', '600', '700', '800' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Timmana' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Tinos' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Titan One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Titillium Web' => array(
			'variants' => array( '200', '200italic', '300', '300italic', '400', 'italic', '600', '600italic', '700', '700italic', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Tomorrow' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Trade Winds' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Train One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Trirong' => array(
			'variants' => array( '100', '100italic', '200', '200italic', '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic', '800', '800italic', '900', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Trispace' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Trocchi' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Trochut' => array(
			'variants' => array( '400', 'italic', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Truculenta' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Trykker' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Tulpen One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Turret Road' => array(
			'variants' => array( '200', '300', '400', '500', '700', '800' ),
			'category' => 'display',
		),
	),
	array(
		'Ubuntu' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '500', '500italic', '700', '700italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ubuntu Condensed' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Ubuntu Mono' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Ultra' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Uncial Antiqua' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Underdog' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Unica One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'UnifrakturCook' => array(
			'variants' => array( '700' ),
			'category' => 'display',
		),
	),
	array(
		'UnifrakturMaguntia' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Unkempt' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
	array(
		'Unlock' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Unna' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'VT323' => array(
			'variants' => array( '400' ),
			'category' => 'monospace',
		),
	),
	array(
		'Vampiro One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Varela' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Varela Round' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Varta' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Vast Shadow' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Vesper Libre' => array(
			'variants' => array( '400', '500', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Viaoda Libre' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Vibes' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Vibur' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Vidaloka' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Viga' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Voces' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Volkhov' => array(
			'variants' => array( '400', 'italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Vollkorn' => array(
			'variants' => array( '400', '500', '600', '700', '800', '900', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Vollkorn SC' => array(
			'variants' => array( '400', '600', '700', '900' ),
			'category' => 'serif',
		),
	),
	array(
		'Voltaire' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Waiting for the Sunrise' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Wallpoet' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Walter Turncoat' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Warnes' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Wellfleet' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Wendy One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Wire One' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Work Sans' => array(
			'variants' => array( '100', '200', '300', '400', '500', '600', '700', '800', '900', '100italic', '200italic', '300italic', 'italic', '500italic', '600italic', '700italic', '800italic', '900italic' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Xanh Mono' => array(
			'variants' => array( '400', 'italic' ),
			'category' => 'monospace',
		),
	),
	array(
		'Yanone Kaffeesatz' => array(
			'variants' => array( '200', '300', '400', '500', '600', '700' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Yantramanav' => array(
			'variants' => array( '100', '300', '400', '500', '700', '900' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'Yatra One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Yellowtail' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Yeon Sung' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Yeseva One' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'Yesteryear' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Yrsa' => array(
			'variants' => array( '300', '400', '500', '600', '700' ),
			'category' => 'serif',
		),
	),
	array(
		'Yusei Magic' => array(
			'variants' => array( '400' ),
			'category' => 'sans-serif',
		),
	),
	array(
		'ZCOOL KuaiLe' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'ZCOOL QingKe HuangYou' => array(
			'variants' => array( '400' ),
			'category' => 'display',
		),
	),
	array(
		'ZCOOL XiaoWei' => array(
			'variants' => array( '400' ),
			'category' => 'serif',
		),
	),
	array(
		'Zeyada' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Zhi Mang Xing' => array(
			'variants' => array( '400' ),
			'category' => 'handwriting',
		),
	),
	array(
		'Zilla Slab' => array(
			'variants' => array( '300', '300italic', '400', 'italic', '500', '500italic', '600', '600italic', '700', '700italic' ),
			'category' => 'serif',
		),
	),
	array(
		'Zilla Slab Highlight' => array(
			'variants' => array( '400', '700' ),
			'category' => 'display',
		),
	),
);
