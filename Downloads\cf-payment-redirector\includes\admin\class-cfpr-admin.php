<?php
/**
 * Admin
 *
 * @package CF_Payment_Redirector
 */

// Sair se acessado diretamente.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe Admin.
 */
class CFPR_Admin {

    /**
     * Variável de instância.
     *
     * @var CFPR_Admin
     */
    private static $instance;

    /**
     * Slug do menu.
     *
     * @var string
     */
    private $menu_slug = 'cf-payment-redirector';

    /**
     * Inicializador.
     */
    public static function get_instance() {
        if ( ! isset( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Construtor.
     */
    public function __construct() {
        add_action( 'admin_menu', array( $this, 'setup_menu' ) );
        add_action( 'admin_init', array( $this, 'register_settings' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'admin_scripts' ) );
        
        // AJAX para obter passos de um funil
        add_action( 'wp_ajax_cfpr_get_flow_steps', array( $this, 'ajax_get_flow_steps' ) );
        
        // AJAX para limpar logs
        add_action( 'wp_ajax_cfpr_clear_logs', array( $this, 'ajax_clear_logs' ) );
        
        // Debug do menu - remover em produção
        add_action( 'admin_notices', array( $this, 'debug_menu_slugs' ) );
    }

    /**
     * Configurar menu admin.
     */
    public function setup_menu() {
        // Usando o plugin base como slug principal
        add_submenu_page(
            'cartflows', // Slug principal do CartFlows
            __( 'Redirecionamento de Pagamento', 'cf-payment-redirector' ),
            __( 'Redirecionamento', 'cf-payment-redirector' ),
            'manage_options',
            $this->menu_slug,
            array( $this, 'render_admin_page' )
        );
        
        // Solução alternativa caso o slug principal seja diferente
        add_action('admin_menu', function() {
            global $submenu;
            
            // Verificar se existe o menu CartFlows no painel
            foreach ($submenu as $slug => $items) {
                // Caso seja encontrado o menu CartFlows com outro slug
                if (strpos($slug, 'cartflows') !== false || strpos($slug, 'wcf') !== false) {
                    if ($slug !== 'cartflows') {
                        // Adicionar o menu usando o slug correto encontrado
                        add_submenu_page(
                            $slug,
                            __( 'Redirecionamento de Pagamento', 'cf-payment-redirector' ),
                            __( 'Redirecionamento', 'cf-payment-redirector' ),
                            'manage_options',
                            $this->menu_slug,
                            array( $this, 'render_admin_page' )
                        );
                        break;
                    }
                }
            }
        }, 99); // Prioridade alta para garantir que o menu CartFlows já está registrado
        
        // Adicionar menu independente como opção alternativa
        add_menu_page(
            __( 'Redirecionamento CartFlows', 'cf-payment-redirector' ),
            __( 'Redir. CartFlows', 'cf-payment-redirector' ),
            'manage_options',
            'cfpr-settings',
            array( $this, 'render_admin_page' ),
            'dashicons-randomize',
            59 // Posicionar logo abaixo do WooCommerce
        );
    }

    /**
     * Registrar configurações.
     */
    public function register_settings() {
        register_setting(
            'cfpr_settings',
            'cfpr_redirect_rules',
            array(
                'sanitize_callback' => array( $this, 'sanitize_settings' ),
            )
        );
    }

    /**
     * Sanitizar configurações.
     *
     * @param array $input Input do formulário.
     * @return array Dados sanitizados.
     */
    public function sanitize_settings( $input ) {
        $sanitized_input = array();
        
        if ( ! is_array( $input ) ) {
            return $sanitized_input;
        }
        
        foreach ( $input as $flow_id => $payment_rules ) {
            $flow_id = absint( $flow_id );
            
            if ( empty( $flow_id ) ) {
                continue;
            }
            
            $sanitized_input[ $flow_id ] = array();
            
            foreach ( $payment_rules as $payment_method => $step_id ) {
                $payment_method = sanitize_text_field( $payment_method );
                $step_id = absint( $step_id );
                
                if ( ! empty( $payment_method ) ) {
                    $sanitized_input[ $flow_id ][ $payment_method ] = $step_id;
                }
            }
        }
        
        return $sanitized_input;
    }

    /**
     * Enfileirar scripts admin.
     *
     * @param string $hook Hook atual.
     */
    public function admin_scripts( $hook ) {
        if ( 'cartflows_page_' . $this->menu_slug !== $hook && 'toplevel_page_cfpr-settings' !== $hook ) {
            return;
        }

        // Enfileirar fonte Inter do CartFlows
        wp_enqueue_style( 'cartflows-admin-font', 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap', array(), CFPR_VER );

        // Enfileirar estilos comuns do CartFlows
        if ( defined( 'CARTFLOWS_ADMIN_CORE_URL' ) ) {
            wp_enqueue_style( 'cartflows-admin-common', CARTFLOWS_ADMIN_CORE_URL . 'assets/css/common.css', array(), CFPR_VER );
            wp_style_add_data( 'cartflows-admin-common', 'rtl', 'replace' );
        }

        // Enfileirar jQuery e scripts necessários
        wp_enqueue_script( 'jquery' );
        wp_enqueue_script( 'jquery-ui-tabs' );

        // Enfileirar nosso script admin
        wp_enqueue_script(
            'cfpr-admin-js',
            CFPR_URL . 'assets/js/admin.js',
            array( 'jquery' ),
            CFPR_VER,
            true
        );

        // Enviar dados para o script
        wp_localize_script(
            'cfpr-admin-js',
            'cfpr_admin',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'cfpr_admin_nonce' ),
            )
        );

        // Enfileirar nossos estilos admin (após os do CartFlows)
        wp_enqueue_style(
            'cfpr-admin-css',
            CFPR_URL . 'assets/css/admin.css',
            array( 'cartflows-admin-common' ),
            CFPR_VER
        );
    }

    /**
     * Renderizar página admin.
     */
    public function render_admin_page() {
        ?>
        <div class="wcf-menu-page-wrapper">
            <div id="wcf-menu-page">
                <div class="wcf-menu-page-content wcf-clear">
                    <?php $this->render_header(); ?>
                    <div class="cfpr-admin-content">
                        <div class="cfpr-admin-description">
                            <p><?php echo esc_html__( 'Configure o redirecionamento automático com base no método de pagamento utilizado no checkout.', 'cf-payment-redirector' ); ?></p>
                        </div>

                        <div class="cfpr-admin-settings">
                            <form method="post" action="options.php">
                                <?php settings_fields( 'cfpr_settings' ); ?>
                                <?php $this->render_settings_form(); ?>
                                <?php submit_button( __( 'Salvar Configurações', 'cf-payment-redirector' ) ); ?>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Renderizar cabeçalho no estilo CartFlows.
     */
    private function render_header() {
        $header_wrapper_class = array( 'wcf-menu-page-header' );
        ?>
        <div class="wcf-menu-page-header <?php echo esc_attr( implode( ' ', $header_wrapper_class ) ); ?>">
            <div class="wcf-container wcf-flex">
                <div class="wcf-title">
                    <span class="screen-reader-text"><?php echo esc_html__( 'Redirecionamento de Pagamento CartFlows', 'cf-payment-redirector' ); ?></span>
                    <img class="wcf-logo" src="<?php echo esc_url_raw( CARTFLOWS_URL . 'assets/images/cartflows-logo.svg' ); ?>" alt="" />
                    <span class="cfpr-page-title"><?php echo esc_html__( 'Redirecionamento de Pagamento', 'cf-payment-redirector' ); ?></span>
                </div>
                <div class="wcf-top-links">
                    <?php
                        esc_attr_e( 'Configure Redirecionamentos por Método de Pagamento', 'cf-payment-redirector' );
                    ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Renderizar formulário de configurações.
     */
    private function render_settings_form() {
        $integration = CFPR_Integration::get_instance();
        $flows = $integration->get_flows();
        $payment_gateways = $integration->get_available_payment_gateways();
        $redirect_rules = get_option( 'cfpr_redirect_rules', array() );
        
        if ( empty( $flows ) ) {
            echo '<div class="notice notice-warning"><p>' . esc_html__( 'Nenhum funil CartFlows encontrado.', 'cf-payment-redirector' ) . '</p></div>';
            return;
        }
        
        if ( empty( $payment_gateways ) ) {
            echo '<div class="notice notice-warning"><p>' . esc_html__( 'Nenhum método de pagamento WooCommerce encontrado.', 'cf-payment-redirector' ) . '</p></div>';
            return;
        }
        
        // Abas para cada funil
        echo '<div id="cfpr-flow-tabs">';
        
        // Cabeçalhos de abas
        echo '<ul class="cfpr-tabs-nav">';
        foreach ( $flows as $flow_id => $flow_title ) {
            echo '<li><a href="#cfpr-flow-' . esc_attr( $flow_id ) . '">' . esc_html( $flow_title ) . '</a></li>';
        }
        echo '</ul>';
        
        // Conteúdo das abas
        foreach ( $flows as $flow_id => $flow_title ) {
            echo '<div id="cfpr-flow-' . esc_attr( $flow_id ) . '" class="cfpr-tab-content">';
            
            echo '<h3>' . esc_html__( 'Configurar Redirecionamentos para: ', 'cf-payment-redirector' ) . esc_html( $flow_title ) . '</h3>';
            
            echo '<table class="form-table cfpr-settings-table">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>' . esc_html__( 'Método de Pagamento', 'cf-payment-redirector' ) . '</th>';
            echo '<th>' . esc_html__( 'Redirecionar para', 'cf-payment-redirector' ) . '</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            // Para cada método de pagamento
            foreach ( $payment_gateways as $gateway_id => $gateway_title ) {
                echo '<tr>';
                echo '<td>' . esc_html( $gateway_title ) . '</td>';
                echo '<td>';
                
                // Valor atual da configuração
                $current_value = isset( $redirect_rules[ $flow_id ][ $gateway_id ] ) ? $redirect_rules[ $flow_id ][ $gateway_id ] : '';
                
                // Campo de select para escolher o passo
                echo '<select name="cfpr_redirect_rules[' . esc_attr( $flow_id ) . '][' . esc_attr( $gateway_id ) . ']" class="cfpr-step-select" data-flow-id="' . esc_attr( $flow_id ) . '">';
                echo '<option value="">' . esc_html__( '-- Nenhum --', 'cf-payment-redirector' ) . '</option>';
                
                // Obter passos deste funil
                $steps = $integration->get_flow_steps( $flow_id );
                
                if ( ! empty( $steps ) ) {
                    foreach ( $steps as $step_id => $step_title ) {
                        echo '<option value="' . esc_attr( $step_id ) . '" ' . selected( $current_value, $step_id, false ) . '>' . esc_html( $step_title ) . '</option>';
                    }
                }
                
                echo '</select>';
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
            
            echo '</div>'; // Tab content
        }
        
        echo '</div>'; // Flow tabs
    }

    /**
     * AJAX para obter passos de um funil.
     */
    public function ajax_get_flow_steps() {
        // Verificar nonce
        check_ajax_referer( 'cfpr_admin_nonce', 'nonce' );
        
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permissão negada.', 'cf-payment-redirector' ) ) );
        }
        
        $flow_id = isset( $_POST['flow_id'] ) ? absint( $_POST['flow_id'] ) : 0;
        
        if ( empty( $flow_id ) ) {
            wp_send_json_error( array( 'message' => __( 'ID do funil inválido.', 'cf-payment-redirector' ) ) );
        }
        
        $integration = CFPR_Integration::get_instance();
        $steps = $integration->get_flow_steps( $flow_id );
        
        wp_send_json_success( array( 'steps' => $steps ) );
    }

    /**
     * Handler AJAX para limpar logs de depuração
     */
    public function ajax_clear_logs() {
        // Verificar nonce
        check_ajax_referer( 'cfpr_clear_logs', 'nonce' );
        
        // Verificar permissões
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => 'Permissão negada' ) );
        }
        
        // Limpar logs
        update_option( 'cfpr_debug_logs', array() );
        
        wp_send_json_success( array( 'message' => 'Logs limpos com sucesso' ) );
    }

    /**
     * Debug para verificar os slugs dos menus.
     */
    public function debug_menu_slugs() {
        // Somente mostrar para administradores
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Verificar se estamos na página de plugins ou no dashboard
        $screen = get_current_screen();
        if (!in_array($screen->id, array('dashboard', 'plugins'))) {
            return;
        }
        
        global $menu, $submenu;
        
        // Encontrar o menu do CartFlows
        $cartflows_slug = '';
        $menu_info = '';
        
        foreach ($menu as $item) {
            if (isset($item[0]) && (strpos($item[0], 'CartFlows') !== false || strpos($item[2], 'cartflows') !== false)) {
                $cartflows_slug = $item[2];
                $menu_info .= "Menu CartFlows encontrado com slug: " . esc_html($item[2]) . "<br>";
            }
        }
        
        if (!empty($cartflows_slug)) {
            $menu_info .= "Submenus: <br>";
            if (isset($submenu[$cartflows_slug])) {
                foreach ($submenu[$cartflows_slug] as $submenu_item) {
                    $menu_info .= "- " . esc_html($submenu_item[0]) . " (slug: " . esc_html($submenu_item[2]) . ")<br>";
                }
            } else {
                $menu_info .= "Nenhum submenu encontrado para este slug.<br>";
            }
        } else {
            $menu_info = "Menu CartFlows não encontrado.";
        }
        
        // Mostrar informações
        echo '<div class="notice notice-info is-dismissible"><p><strong>CartFlows Payment Redirector - Debug:</strong><br>' . $menu_info . '</p></div>';
    }
} 