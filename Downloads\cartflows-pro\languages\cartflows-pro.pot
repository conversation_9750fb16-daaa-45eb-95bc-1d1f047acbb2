# Copyright (C) 2024 CartFlows Inc
# This file is distributed under the same license as the CartFlows Pro plugin.
msgid ""
msgstr ""
"Project-Id-Version: CartFlows Pro 2.1.2\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/cartflows-pro\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-12-09T14:46:58+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.5.0\n"
"X-Domain: cartflows-pro\n"

#. Plugin Name of the plugin
msgid "CartFlows Pro"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://cartflows.com/"
msgstr ""

#. Description of the plugin
msgid "eCommerce on steroid with Order Bump, One Click Upsells and much more!"
msgstr ""

#. Author of the plugin
msgid "CartFlows Inc"
msgstr ""

#: admin-core/ajax/ajax-errors.php:58
msgid "Sorry, you are not allowed to do this operation."
msgstr ""

#: admin-core/ajax/ajax-errors.php:59
#: modules/gateways/class-cartflows-pro-gateway-woocommerce-payments.php:141
msgid "Nonce validation failed"
msgstr ""

#: admin-core/ajax/ajax-errors.php:60
msgid "Sorry, something went wrong."
msgstr ""

#: admin-core/ajax/flow-analytics.php:154
msgid "Invalid Flow ID has been provided."
msgstr ""

#: admin-core/ajax/form-fields.php:74
#: admin-core/ajax/form-fields.php:193
#: admin-core/ajax/multiple-order-bump.php:74
#: admin-core/ajax/multiple-order-bump.php:311
#: admin-core/ajax/multiple-order-bump.php:547
#: admin-core/ajax/multiple-order-bump.php:619
#: admin-core/ajax/multiple-order-bump.php:689
#: admin-core/ajax/multiple-order-bump.php:753
#: admin-core/ajax/multiple-order-bump.php:831
#: admin-core/ajax/multiple-order-bump.php:898
msgid "No post data found!"
msgstr ""

#: admin-core/ajax/form-fields.php:164
msgid "Custom field prepared."
msgstr ""

#: admin-core/ajax/form-fields.php:172
msgid "Name field is empty!"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:88
msgid "Order Bump data not saved."
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:135
msgid "Order bump settings saved successfully!"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:335
msgid "Order Bump Data Retrieved"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:342
msgid "No data."
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:561
msgid "Cant update order bump status!"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:593
msgid "Order Bump Status Updated Succesfully"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:633
msgid "Cant delete order bump!"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:664
msgid "Order Bump Deleted Succesfully"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:726
msgid "Order Bump Added Succesfully"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:733
msgid "Cant add order bump!"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:767
msgid "Can't clone the order bump! Order Bump ID or Page ID not found."
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:796
msgid " Clone"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:808
msgid "Order Bump Cloned Succesfully"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:871
msgid "Order Bump Title Updated Succesfully"
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:879
msgid "Order Bump Title Not Updated."
msgstr ""

#: admin-core/ajax/multiple-order-bump.php:916
msgid "Order Bumps not sorted."
msgstr ""

#. translators: %s step id
#: admin-core/ajax/multiple-order-bump.php:943
msgid "Order Bump sorted for step - %s"
msgstr ""

#: admin-core/api/ob-product-data.php:67
msgid "Step ID."
msgstr ""

#: admin-core/api/ob-product-data.php:97
msgid "Sorry, WooCommerce need to be installed & activated to call API."
msgstr ""

#: admin-core/api/ob-product-data.php:142
msgid "Sorry, you cannot list resources."
msgstr ""

#: admin-core/inc/admin-hooks.php:226
msgid "YES : "
msgstr ""

#: admin-core/inc/admin-hooks.php:228
msgid "YES : Step not Found"
msgstr ""

#: admin-core/inc/admin-hooks.php:233
msgid "No : "
msgstr ""

#: admin-core/inc/admin-hooks.php:235
msgid "No : Step not Found"
msgstr ""

#: admin-core/inc/global-settings-pro.php:79
#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:97
#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:161
msgid "Nonce verification failed."
msgstr ""

#: admin-core/inc/global-settings-pro.php:194
msgid "Enable Pre Checkout Offers"
msgstr ""

#: admin-core/inc/global-settings-pro.php:195
msgid "If enable, it will add the Pre Checkout Offer settings in checkout step settings."
msgstr ""

#: admin-core/inc/global-settings-pro.php:207
msgid "Enable PayPal Reference Transactions."
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: admin-core/inc/global-settings-pro.php:209
msgid "This option will work with %1$1s PayPal Standard%2$2s & %3$3s PayPal Checkout%4$4s Gateways only. To know more about PayPal reference transactions %5$5s click here. %6$6s"
msgstr ""

#: admin-core/inc/global-settings-pro.php:222
msgid "Create a new child order (Recommended)"
msgstr ""

#: admin-core/inc/global-settings-pro.php:223
msgid "This option create a new order for all accepted upsell/downsell offers. Main order will be parent order for them."
msgstr ""

#: admin-core/inc/global-settings-pro.php:227
msgid "Add to main order"
msgstr ""

#: admin-core/inc/global-settings-pro.php:228
msgid "This option will merge all accepted upsell/downsell offers into main order."
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: admin-core/inc/global-settings-pro.php:235
msgid "For more information about the offer settings please %1$1s Click here. %2$2s"
msgstr ""

#: admin-core/inc/global-settings-pro.php:248
msgid "Enter License Key"
msgstr ""

#: admin-core/inc/global-settings-pro.php:249
msgid "Enter your license key"
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: admin-core/inc/global-settings-pro.php:255
msgid "If you don't have License key, you can get it from %1$shere%2$s"
msgstr ""

#: admin-core/inc/global-settings-pro.php:269
msgid "A/B test Pemalink"
msgstr ""

#: admin-core/inc/global-settings-pro.php:275
msgid "Override Permalink for A/B test"
msgstr ""

#: admin-core/inc/global-settings-pro.php:276
msgid "If enable, it will use same permalink for all variants."
msgstr ""

#: admin-core/views/license-log.php:27
msgid "License debug log"
msgstr ""

#: admin-core/views/license-log.php:34
msgid "License Arguments:"
msgstr ""

#: admin-core/views/license-log.php:46
msgid "License Call:"
msgstr ""

#: admin-core/views/license-log.php:53
msgid "License API Response:"
msgstr ""

#: admin/views/html-refund-offer.php:86
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:138
msgid "Product Name"
msgstr ""

#: admin/views/html-refund-offer.php:87
msgid "Offer Type"
msgstr ""

#: admin/views/html-refund-offer.php:88
#: modules/checkout/templates/your-product/product-table-titles.php:23
msgid "Quantity"
msgstr ""

#: admin/views/html-refund-offer.php:89
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:173
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:397
#: modules/checkout/templates/checkout/multistep-review-order.php:115
#: modules/thankyou/template/child-order-details.php:59
msgid "Total"
msgstr ""

#: admin/views/html-refund-offer.php:90
msgid "Action"
msgstr ""

#. translators: %1$s: item total, %2$s: tax, %3$s: product total
#: admin/views/html-refund-offer.php:136
msgid "<div class=\"amount_distribution\"><span class=\"\">Item Prices: %1$s</span><br/><span class=\"\">Tax & Other: %2$s</span><br><span class=\"\">Total: %3$s</span></div>"
msgstr ""

#: admin/views/html-refund-offer.php:155
msgid "Refund"
msgstr ""

#: admin/views/html-refund-offer.php:158
msgid "Refunded"
msgstr ""

#: admin/views/html-refund-offer.php:176
msgid "Use WooCommerce's refund feature to refund the main order. You can refund upsell/downsell offers from this section. Always refund upsell/downsell offers first & then refund the main order."
msgstr ""

#: admin/views/html-refund-offer.php:181
msgid "Refunds are not available for any offer(s) against this order."
msgstr ""

#: classes/class-cartflows-pro-admin-helper.php:48
msgid "Upsell ( Woo )"
msgstr ""

#: classes/class-cartflows-pro-admin-helper.php:49
msgid "Downsell ( Woo )"
msgstr ""

#: classes/class-cartflows-pro-admin-helper.php:50
msgid "Thankyou ( Woo )"
msgstr ""

#. translators: %1$s: HTML, %2$s: HTML
#: classes/class-cartflows-pro-admin.php:202
msgid "%1$sYou are installing the lower plan of the CartFlows compared to what currently you have, it will disable some pro features that you might be using. %2$s"
msgstr ""

#. translators: %1$s: HTML, %2$s: HTML
#: classes/class-cartflows-pro-admin.php:208
msgid "%1$sYou are installing the lower plan of the CartFlows compared to what currently you have, it will disable some plus features that you might be using. %2$s"
msgstr ""

#: classes/class-cartflows-pro-admin.php:230
msgid "CartFlows is migrating the old order bump to the new one in the background. The migration process may take a little while, so please be patient."
msgstr ""

#: classes/class-cartflows-pro-admin.php:231
#: classes/class-cartflows-pro-admin.php:440
msgid "View Progress >>"
msgstr ""

#: classes/class-cartflows-pro-admin.php:285
msgid "Hurray! CartFlows Multiple Order Bump migration process is completed successfully."
msgstr ""

#: classes/class-cartflows-pro-admin.php:316
#: classes/class-cartflows-pro-admin.php:470
msgid "Cannot schedule a migration. Action scheduler function not found."
msgstr ""

#: classes/class-cartflows-pro-admin.php:326
msgid "Order Bump 1st migration action scheduled successfully."
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: classes/class-cartflows-pro-admin.php:356
msgid "CartFlows is migrating the old order bump to the new one in the background. The migration process may take a little while, so please be patient. %1$1s View Progress >> %2$2s"
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: classes/class-cartflows-pro-admin.php:358
msgid "CartFlows is migrating the pre checkout offer styles in the background. The migration process may take a little while, so please be patient. %1$1s View Progress >> %2$2s"
msgstr ""

#: classes/class-cartflows-pro-admin.php:376
msgid "We have introduced the multiple order bump feature. If your checkout page has the order bump feature enabled, you need to migrate an old order bump settings to the new multiple order bump settings."
msgstr ""

#: classes/class-cartflows-pro-admin.php:378
msgid "Migrate Order Bump"
msgstr ""

#: classes/class-cartflows-pro-admin.php:397
msgid "We have moved the pre-checkout offer styling option from the page builder settings and added it to the checkout offer tab. If your checkout page has the pre-checkout feature enabled, then you need to migrate those settings."
msgstr ""

#: classes/class-cartflows-pro-admin.php:399
msgid "Migrate pre-checkout offer styles"
msgstr ""

#: classes/class-cartflows-pro-admin.php:417
msgid "Hurray! CartFlows pre checkout offer styles migration process is completed successfully."
msgstr ""

#: classes/class-cartflows-pro-admin.php:439
msgid "CartFlows is migrating the pre checkout offer styles in the background. The migration process may take a little while, so please be patient."
msgstr ""

#: classes/class-cartflows-pro-admin.php:480
msgid "1st migration action scheduled successfully."
msgstr ""

#: classes/class-cartflows-pro-admin.php:603
msgid "Upsell Offer"
msgstr ""

#: classes/class-cartflows-pro-admin.php:605
msgid "Downsell Offer"
msgstr ""

#: classes/class-cartflows-pro-default-meta.php:81
msgid "Nonce validation failed."
msgstr ""

#: classes/class-cartflows-pro-default-meta.php:258
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:247
msgid "Processing Order..."
msgstr ""

#: classes/class-cartflows-pro-default-meta.php:262
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:254
msgid "Product Added Successfully."
msgstr ""

#: classes/class-cartflows-pro-default-meta.php:266
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:261
msgid "Oooops! Your Payment Failed."
msgstr ""

#: classes/class-cartflows-pro-default-meta.php:270
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:268
msgid "Please wait while we process your payment..."
msgstr ""

#: classes/class-cartflows-pro-flow-frontend.php:69
msgid "Loading..."
msgstr ""

#: classes/class-cartflows-pro-flow-frontend.php:73
msgid "Please wait while we redirect you..."
msgstr ""

#: classes/class-cartflows-pro-frontend.php:283
msgid "Your session is expired"
msgstr ""

#. translators: %1$s: payment gateway names, %2$s: link start, %3$s: link end
#: classes/class-cartflows-pro-gateways.php:349
msgid "CartFlows Upsell/Downsell offer does not support the %1$s payment gateway. Please find the supported payment gateways %2$shere%3$s."
msgstr ""

#: classes/class-cartflows-pro-helper.php:192
#: classes/class-cartflows-pro-licence.php:533
msgid "No error found."
msgstr ""

#: classes/class-cartflows-pro-helper.php:202
msgid "API call to create a purchase failed."
msgstr ""

#: classes/class-cartflows-pro-licence.php:139
#: classes/class-cartflows-pro-licence.php:334
#: classes/class-cartflows-pro-licence.php:417
#: classes/class-cartflows-pro-refund.php:63
msgid "Permission denied!"
msgstr ""

#. translators: %1$s Software Title, %2$s Plugin, %3$s Anchor opening tag, %4$s Anchor closing tag, %5$s Software Title.
#: classes/class-cartflows-pro-licence.php:175
msgid "The <strong>%1$s</strong> License Key has not been activated, so the %2$s is inactive! %3$sClick here%4$s to activate <strong>%5$s</strong>."
msgstr ""

#. translators: %1$s,%2$s: Find API key article link
#: classes/class-cartflows-pro-licence.php:203
msgid "If you don't have License key, you can get it from %1$s here%2$s."
msgstr ""

#: classes/class-cartflows-pro-licence.php:213
#: classes/class-cartflows-pro-licence.php:278
msgid "Activate License"
msgstr ""

#: classes/class-cartflows-pro-licence.php:223
#: classes/class-cartflows-pro-licence.php:253
msgid "License Key"
msgstr ""

#: classes/class-cartflows-pro-licence.php:225
msgid "Enter your License Key"
msgstr ""

#: classes/class-cartflows-pro-licence.php:245
msgid "Activate"
msgstr ""

#: classes/class-cartflows-pro-licence.php:261
msgid "Deactivate"
msgstr ""

#: classes/class-cartflows-pro-licence.php:278
#: classes/class-cartflows-pro-licence.php:280
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:118
msgid "Settings"
msgstr ""

#: classes/class-cartflows-pro-licence.php:280
msgid "Deactivate License"
msgstr ""

#: classes/class-cartflows-pro-licence.php:345
#: classes/class-cartflows-pro-licence.php:428
msgid "Oops! Security nonce is invalid."
msgstr ""

#. translators: %1$s: HTML, %2$s: HTML
#: classes/class-cartflows-pro-licence.php:544
msgid "For more information, please check this %1$sarticle%2$s."
msgstr ""

#. translators: %1$s: HTML, %2$s: HTML, %3$s: HTML
#: classes/class-cartflows-pro-licence.php:557
msgid "Sorry for the inconvenience, but your website seems to be having trouble connecting to our licensing system. %1$s Please open a technical %2$ssupport ticket%3$s and share the server's outgoing IP address."
msgstr ""

#: classes/class-cartflows-pro-license-helper.php:42
msgid "Provide a license key to process license request!"
msgstr ""

#: classes/class-cartflows-pro-license-helper.php:46
msgid "Provide a license request!"
msgstr ""

#: classes/class-cartflows-pro-license-helper.php:52
msgid "Invalid license request!"
msgstr ""

#. translators: %s: html tags
#: classes/class-cartflows-pro-loader.php:522
msgid "The %1$s CartFlows Pro %2$s plugin requires %1$s CartFlows %2$s plugin to be activated."
msgstr ""

#: classes/class-cartflows-pro-loader.php:523
msgid "Activate CartFlows Now"
msgstr ""

#. translators: %s: html tags
#: classes/class-cartflows-pro-loader.php:531
msgid "The %1$s CartFlows Pro %2$s plugin requires %1$s CartFlows %2$s plugin to be installed."
msgstr ""

#: classes/class-cartflows-pro-loader.php:532
msgid "Install CartFlows Now"
msgstr ""

#. translators: %%1$s: Plugin name, %2$s: required cartflows version
#: classes/class-cartflows-pro-loader.php:555
msgid "You are using an older version of CartFlows. To keep things running smoothly with %1$s, please update CartFlows version %2$s or greater."
msgstr ""

#: classes/class-cartflows-pro-loader.php:556
msgid "Update CartFlows Now"
msgstr ""

#: classes/class-cartflows-pro-refund.php:71
msgid "Unexpected error occoured"
msgstr ""

#: classes/class-cartflows-pro-refund.php:100
msgid "Refund unsuccessful"
msgstr ""

#: classes/class-cartflows-pro-refund.php:155
msgid "Refund Unsuccessful"
msgstr ""

#: classes/class-cartflows-pro-refund.php:163
msgid "Refund Successful"
msgstr ""

#: classes/class-cartflows-pro-refund.php:193
msgid "CartFlows Refund Offers"
msgstr ""

#: classes/class-cartflows-pro-utils.php:231
msgid "Place order"
msgstr ""

#. translators: %s license_key
#: classes/class-cartflows-pro-wp-cli.php:52
msgid "Invalid License Key : %s"
msgstr ""

#. translators: %s license_key
#: classes/class-cartflows-pro-wp-cli.php:58
msgid "License Activated : %s"
msgstr ""

#. translators: %s license_key
#: classes/class-cartflows-pro-wp-cli.php:65
msgid "License Deactivated : %s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:233
msgid "Step not deleted for flow - %s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:250
msgid "Step deleted for flow - %s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:278
msgid "Can't archive this step - %1$s. Funnel - %2$s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:291
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:651
msgid "Step - %1$s archived. Funnel - %2$s"
msgstr ""

#. translators: %s step id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:318
msgid "Can't create a variation for this step - %s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:378
msgid "Variation created for step - %s"
msgstr ""

#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:406
msgid "Can't start a split test"
msgstr ""

#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:417
msgid "Stop Split Test"
msgstr ""

#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:431
msgid "Start Split Test"
msgstr ""

#. translators: %s step id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:476
msgid "Can't update the winner for this step - %s"
msgstr ""

#. translators: %s step id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:572
msgid "Winner updated for this step - %s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:603
msgid "Can't clone this step - %1$s. Funnel - %2$s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:683
msgid "Can't restore this variation - %1$s. Funnel - %2$s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:744
msgid "Variation - %1$s restored. Funnel  - %2$s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:773
msgid "Can't delete this variation - %1$s. Funnel - %2$s"
msgstr ""

#. translators: %s flow id
#: modules/ab-test/classes/class-cartflows-pro-ab-test-meta.php:819
msgid "Variation - %1$s deleted. Funnel  - %2$s"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:215
msgid "Select Position"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:218
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1121
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:321
msgid "Before Checkout"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:219
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1125
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2540
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:322
msgid "After Customer Details"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:220
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2544
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:323
msgid "Before Order Review"
msgstr ""

#. translators: %1$1s, %2$2s Link to meta
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:225
msgid "Please enable \"Product-Options\" from %1$1shere%2$2s to edit options."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:232
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:331
msgid "Skin"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:235
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2556
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:335
msgid "Classic"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:236
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2560
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:336
msgid "Cards"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:241
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2566
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:344
msgid "Show Product Images"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:244
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:512
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:79
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:348
msgid "Yes"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:245
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:513
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:80
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:349
msgid "No"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:250
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:357
msgid "Section Title Text"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:251
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:67
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2524
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:359
msgid "Your Products"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:258
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2516
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:308
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:404
msgid "Product Options"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:262
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:429
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:150
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:85
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:92
#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:75
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2420
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:414
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:517
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:364
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:183
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:190
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:190
msgid "Text Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:276
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:460
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2325
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:425
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:506
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:377
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:220
msgid "Background Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:291
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:106
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:437
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:528
msgid "Border Style"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:293
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:400
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:108
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:196
msgid "The type of border to use. Double borders must have a width of at least 3px to render properly."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:295
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:402
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:268
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:110
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:198
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1393
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1504
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1660
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2485
msgid "None"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:296
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:403
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:111
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:199
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1389
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1500
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1656
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2489
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:443
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:534
msgid "Solid"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:297
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:404
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:112
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:200
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1381
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1492
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1648
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:446
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:537
msgid "Dashed"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:298
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:405
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:113
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:201
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1385
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1496
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1652
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:445
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:536
msgid "Dotted"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:299
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:406
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:114
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:202
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:444
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:535
msgid "Double"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:324
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:430
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:138
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:226
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:458
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:548
msgid "Border Width"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:340
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:445
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:153
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:241
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:471
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:560
msgid "Border Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:354
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:458
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:166
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:254
msgid "Border Radius"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:370
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2590
msgid "Highlight Product Background Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:384
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2585
msgid "Highlight Product Text Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:398
msgid "Highlight Product Border Style"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:473
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2600
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:584
msgid "Highlight Flag Text Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:487
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2605
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:595
msgid "Highlight Flag Background Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:509
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:98
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2400
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:107
msgid "Enable Checkout Note"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:523
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:106
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2405
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:119
msgid "Note Text"
msgstr ""

#. translators: abbreviation for units
#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:524
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:255
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:121
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:122
#: modules/gutenberg/dist/blocks/cartflows-pro-gb-checkout-form-extend/class-cartflows-pro-checkout-form.php:339
#: modules/gutenberg/build/blocks.js:3
msgid "Get Your FREE copy of CartFlows in just few steps."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:529
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:120
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2449
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:142
msgid "Step One Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:530
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:122
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:270
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:341
#: modules/checkout/templates/checkout/multistep-review-order.php:83
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:143
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:145
#: modules/gutenberg/dist/blocks/cartflows-pro-gb-checkout-form-extend/class-cartflows-pro-checkout-form.php:346
#: modules/gutenberg/build/blocks.js:3
msgid "Shipping"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:535
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:127
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2455
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:152
msgid "Step One Sub Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:536
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:129
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:274
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:153
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:155
#: modules/gutenberg/dist/blocks/cartflows-pro-gb-checkout-form-extend/class-cartflows-pro-checkout-form.php:350
#: modules/gutenberg/build/blocks.js:3
msgid "Where to ship it?"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:541
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:134
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2461
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:162
msgid "Step Two Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:542
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:136
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:279
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:353
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:362
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:582
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:163
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:165
#: modules/gutenberg/dist/blocks/cartflows-pro-gb-checkout-form-extend/class-cartflows-pro-checkout-form.php:354
#: modules/gutenberg/build/blocks.js:3
msgid "Payment"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:547
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:141
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2467
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:172
msgid "Step Two Sub Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:548
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:143
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:283
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:173
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:175
#: modules/gutenberg/dist/blocks/cartflows-pro-gb-checkout-form-extend/class-cartflows-pro-checkout-form.php:358
#: modules/gutenberg/build/blocks.js:3
msgid "Of your order"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:553
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:155
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2496
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:191
msgid "Offer Button Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:554
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:157
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:287
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:192
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:195
#: modules/gutenberg/dist/blocks/cartflows-pro-gb-checkout-form-extend/class-cartflows-pro-checkout-form.php:362
#: modules/gutenberg/build/blocks.js:3
msgid "For Special Offer Click Here"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:559
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:162
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2502
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:202
msgid "Offer Button Sub Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:560
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:164
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:291
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:203
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:206
#: modules/gutenberg/dist/blocks/cartflows-pro-gb-checkout-form-extend/class-cartflows-pro-checkout-form.php:366
#: modules/gutenberg/build/blocks.js:3
msgid "Yes! I want this offer!"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:568
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:74
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:79
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:97
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:227
msgid "Two Step"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:572
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:169
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:239
msgid "Note Text Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:586
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:183
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:255
msgid "Note Background Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-checkout-form-extend/cartflows-pro-bb-checkout-form-extend.php:600
#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:204
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:274
msgid "Note Typography"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:31
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:69
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:105
msgid "Offer Yes/No Button"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:32
msgid "Offer Yes/No Button."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:33
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:34
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:33
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:34
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:33
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:34
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:34
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:35
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:33
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:34
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:32
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:33
#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:33
#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:34
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:33
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:34
msgid "Cartflows Modules"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:137
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:86
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:155
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:297
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:138
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:244
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:348
msgid "General"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:144
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:154
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:93
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:103
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:162
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:177
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:145
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:160
#: modules/gutenberg/build/blocks.js:11
msgid "Offer Action"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:147
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:96
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:108
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:166
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:149
#: modules/gutenberg/build/blocks.js:11
msgid "Accept Offer"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:148
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:97
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:114
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:167
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:150
#: modules/gutenberg/build/blocks.js:11
msgid "Reject Offer"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:153
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1881
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:175
msgid "Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:163
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:184
msgid "Sub Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:172
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:112
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:192
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:167
msgid "Icon"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:180
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:120
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:200
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:175
msgid "Icon Position"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:183
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:123
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:204
msgid "Before Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:184
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:124
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:205
msgid "After Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:185
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:206
msgid "Before Title & Sub Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:186
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:207
msgid "After Title & Sub Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:202
msgid "Vertical Alignment"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:205
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1958
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:224
msgid "Top"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:206
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:228
msgid "Middle"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:214
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:132
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:245
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:191
msgid "Icon Spacing"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:225
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:228
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:143
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:146
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:69
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:72
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:70
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:69
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:72
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:67
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:70
#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:68
#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:71
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:69
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:72
msgid "Style"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:232
msgid "Type"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:236
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1377
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1488
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1644
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:442
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:533
msgid "Default"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:237
msgid "Flat"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:238
msgid "Gradient"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:239
msgid "Transparent"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:240
msgid "3D"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:250
msgid "Border Size"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:265
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:281
msgid "Hover Styles"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:269
msgid "Fade Background"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:270
msgid "Fill Background From Top"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:271
msgid "Fill Background From Bottom"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:272
msgid "Fill Background From Left"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:273
msgid "Fill Background From Right"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:274
msgid "Fill Background Vertical"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:275
msgid "Fill Background Diagonal"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:276
msgid "Fill Background Horizontal"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:284
msgid "Move Down"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:285
msgid "Move Up"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:286
msgid "Move Left"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:287
msgid "Move Right"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:288
msgid "Animate Top"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:289
msgid "Animate Bottom"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:295
msgid "Structure"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:299
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1100
msgid "Width"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:302
msgctxt "Width."
msgid "Auto"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:303
msgid "Full Width"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:304
msgid "Custom"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:320
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:77
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:76
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:74
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:76
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-price.php:71
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-quantity.php:71
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-title.php:72
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-variation.php:72
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:305
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:252
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:155
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:142
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:143
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:144
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:143
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:143
msgid "Alignment"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:323
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:333
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:80
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:79
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:77
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:79
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:313
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:490
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:260
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:163
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:150
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:151
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:152
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:151
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:151
msgid "Center"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:324
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:334
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:81
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:80
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:78
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:80
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1954
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:309
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:486
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:256
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:159
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:146
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:147
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:148
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:147
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:147
msgid "Left"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:325
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:335
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:82
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:81
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:79
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:81
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1962
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:317
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:494
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:264
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:167
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:154
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:155
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:156
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:155
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:155
msgid "Right"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:330
msgid "Mobile Alignment"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:340
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:343
msgid "Padding"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:355
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:482
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-image.php:90
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:397
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:232
msgid "Border"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:369
msgid "Border Hover Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:380
msgid "Custom Width"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:389
msgid "Custom Height"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:398
msgid "Padding Top/Bottom"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:407
msgid "Padding Left/Right"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:416
msgid "Round Corners"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:425
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1143
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2230
msgid "Colors"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:443
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:166
msgid "Text Hover Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:468
msgid "Background Hover Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:479
msgid "Apply Hover Color To"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:483
msgid "Background"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:493
#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:508
#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:182
#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:99
#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:107
#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:131
#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:89
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:134
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-price.php:83
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-title.php:84
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:196
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:172
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:187
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:172
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:210
msgid "Typography"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:496
msgid "Button Settings"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-button/cartflows-pro-bb-offer-action-button.php:500
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:265
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:212
msgid "Icon Size"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:31
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:65
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:52
msgid "Offer Yes/No Link"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:32
msgid "Offer Yes/No Link."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-action-link/cartflows-pro-bb-offer-action-link.php:102
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:158
msgid "Link Text"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:31
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-description.php:101
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:65
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:135
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:264
msgid "Offer Product Description"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:32
msgid "Offer Product Description."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-description/cartflows-pro-bb-offer-product-description.php:76
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-description.php:73
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:143
msgid "Short Description"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:32
#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:73
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-image.php:151
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:63
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:134
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:489
msgid "Offer Product Image"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:33
msgid "Offer Product Image."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:92
msgid "Image Bottom Spacing"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:182
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-image.php:156
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:219
msgid "Thumbnails"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:186
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-image.php:114
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:227
msgid "Spacing between Thumbnails"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-image/cartflows-pro-bb-offer-product-image.php:194
msgid "Thumbnails Border Style"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:31
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-price.php:104
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:65
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:135
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:322
msgid "Offer Product Price"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-price/cartflows-pro-bb-offer-product-price.php:32
msgid "Offer Product Price."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:30
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-quantity.php:146
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:66
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:136
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:374
msgid "Offer Product Quantity"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:31
msgid "Offer Product Quantity."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:89
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:91
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:167
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:166
msgid "Width(%)"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:103
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:105
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:196
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:185
msgid "Label Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-quantity/cartflows-pro-bb-offer-product-quantity.php:117
#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:120
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:208
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:197
msgid "Input Text Color"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:31
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-title.php:105
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:65
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:135
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:210
msgid "Offer Product Title"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-title/cartflows-pro-bb-offer-product-title.php:32
msgid "Offer Product Title."
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:31
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-variation.php:134
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:65
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:135
#: modules/gutenberg/classes/class-cartflows-pro-block-config.php:437
msgid "Offer Product Variation"
msgstr ""

#: modules/beaver-builder/cartflows-pro-bb-offer-product-variation/cartflows-pro-bb-offer-product-variation.php:32
msgid "Offer Product Variation."
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:66
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2157
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:310
msgid "Product Title"
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:67
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:73
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:79
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:85
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:91
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:97
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:103
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:109
#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:115
msgid "CartFlows"
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:72
#: modules/bricks/elements/class-cartflows-pro-bricks-upsell-layout.php:164
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:331
msgid "Product Price"
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:78
#: modules/bricks/elements/class-cartflows-pro-bricks-upsell-layout.php:129
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2173
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:317
msgid "Product Description"
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:84
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:324
msgid "Product Short Description"
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:90
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1929
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:339
msgid "Product Image"
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:96
#: modules/bricks/elements/class-cartflows-pro-bricks-upsell-layout.php:178
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:302
msgid "Product Quantity"
msgstr ""

#: modules/bricks/class-cartflows-pro-bricks-dynamic-data.php:102
msgid "Product Variation"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:108
msgid "Get Your FREE copy of CartFlows in just few steps"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:113
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:133
msgid "Steps"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-checkout-form-extended.php:148
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:182
msgid "Offer Button"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-description.php:49
msgid "Product Description "
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-description.php:79
msgid "Styling"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-image.php:56
msgid "Product Image "
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-image.php:78
msgid "Image Spacing"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-image.php:126
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:243
msgid "Thumbnail Border"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-price.php:49
msgid "Product Price "
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-quantity.php:49
msgid "Product Quantity "
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-quantity.php:83
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-variation.php:84
msgid "Max-Width"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-quantity.php:98
msgid "Quantity Field Border"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-quantity.php:110
#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-variation.php:99
msgid "Label Typography"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-quantity.php:124
msgid "Text Typography"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-title.php:49
msgid "Product Title "
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-variation.php:49
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:294
msgid "Product Variation "
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-offer-product-variation.php:113
msgid "Input Text Typography"
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-upsell-layout.php:53
msgid "Offer Steps Layout "
msgstr ""

#: modules/bricks/elements/class-cartflows-pro-bricks-upsell-layout.php:472
#: modules/bricks/elements/class-cartflows-pro-bricks-upsell-layout.php:482
msgid "Column"
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:85
#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:149
msgid "No post data found."
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:349
msgid "Product not added in the cart."
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:368
#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:378
#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:390
msgid "Oops, can't add the product to the cart"
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:398
#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:436
msgid "Product is Out of Stock"
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:430
msgid "Product Added Successfully"
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:569
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:130
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2127
msgid "{first_name}, Wait! Your Order Is Almost Complete..."
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:574
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:142
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2195
msgid "Yes, Add to My Order!"
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:579
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:147
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2211
msgid "No, thanks!"
msgstr ""

#: modules/checkout/classes/class-cartflows-pre-checkout-offer-product.php:599
#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:126
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2176
msgid "Write a few words about this awesome product and tell shoppers why they must get it. You may highlight this as \"one time offer\" and make it irresistible."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:117
msgid "___Don't miss out the offer___"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:134
msgid "We have a special one time offer just for you."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:364
msgid "no title"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:424
msgid "Yes, I will take it!"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:432
msgid "Here is Great Offer!"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:436
msgid "One Time Offer"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-default-meta.php:440
msgid "Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aut, quod hic expedita consectetur vitae nulla sint adipisci cupiditate at."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-markup.php:1060
#: modules/optin/classes/class-cartflows-pro-optin-markup.php:141
msgid "Billing Custom Fields"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-markup.php:1098
msgid "Shipping Custom Fields"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-markup.php:1143
msgid "Processing..."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:82
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:122
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:133
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:297
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:337
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:347
msgid "matches any of"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:86
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:137
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:301
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:351
msgid "matches all of"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:90
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:126
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:141
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:305
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:341
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:355
msgid "matches none of"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:96
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:311
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:369
msgid "is equal to"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:100
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:315
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:373
msgid "is not equal to"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:104
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:319
msgid "is greater than"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:108
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:323
msgid "is less than"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:112
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:327
msgid "is greater or equal to"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:116
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:331
msgid "is less or equal to"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:145
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:359
msgid "exist"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:149
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:363
msgid "not exist"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:157
msgid "Cart"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:161
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:385
msgid "Product(s)"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:165
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:389
msgid "Product category(s)"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:169
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:393
msgid "Product tag(s)"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:177
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:401
msgid "Coupon(s)"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:181
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:405
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:256
msgid "Shipping method"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:188
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:416
msgid "Geography"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:192
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:420
msgid "Shipping country"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:196
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:424
msgid "Billing country"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:209
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:446
msgid "Search for products.."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:220
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:457
msgid "Search for products cat.."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:231
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:468
msgid "Search for products tags.."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:249
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:486
msgid "Search for coupons.."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:259
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:496
msgid "Search for shipping methods.."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:270
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:281
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:507
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:518
msgid "Search for country.."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:381
msgid "Order"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:409
msgid "Payment Method"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:430
msgid "Custom Fields"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:434
msgid "Order Custom Field"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:529
msgid "Search for payment method.."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:542
msgid "Enter custom field meta key"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:546
msgid "Expected custom field meta value"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:565
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2345
msgid "Enable Browser Tab Animation"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:571
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2125
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2352
msgid "Title Text"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:867
msgid "Select Coupon"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:868
msgid "Search for a coupon"
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:875
msgid "For more information about the CartFlows coupon please %1$1s Click here.%2$2s"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:882
msgid "Enable Product Options"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:889
msgid "Enable Conditions"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:894
msgid "Restrict user to purchase all products"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:898
msgid "Let user select one product from all options"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:902
msgid "Let user select multiple products from all options"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:930
msgid "Enable Variations"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:951
msgid "Show variations inline"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:955
msgid "Show variations in popup"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:987
msgid "Enable Quantity"
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1012
msgid "For more information about the product option settings %1$1s Click here. %2$2s"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1023
msgid "Replace First Product"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1027
msgid "It will replace the first selected product (from checkout products) with the order bump product. %1$1sLearn More »%2$2s"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1040
msgid "Show Pre-Checked"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1042
msgid "It will pre-check this order bump and add the assigned product to the cart on the checkout page."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1051
msgid "Enable Quantity Field"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1053
msgid "It will display the quantity field in the order bump description."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1068
msgid "Layout"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1072
msgid "Order Bump Skin"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1077
msgid "Style 1"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1081
msgid "Style 2"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1085
msgid "Style 3"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1089
msgid "Style 4"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1093
msgid "Style 5"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1105
msgid "50%"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1109
msgid "100%"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1116
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:317
msgid "Position"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1129
msgid "After Order"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1133
msgid "After Payment"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1147
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2280
msgid "Description Text Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1152
msgid "Title Text Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1166
msgid "Label Text Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1196
msgid "Label Background Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1226
#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2295
msgid "Button Text Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1256
msgid "Button Text Hover Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1286
msgid "Button Background Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1316
msgid "Button Background Hover Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1346
msgid "Highlight Text Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1360
msgid "Box Background Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1368
msgid "Borders"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1372
msgid "Label Border Style"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1415
msgid "Label Border Width"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1438
msgid "Label Border Radius"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1461
msgid "label Border Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1483
msgid "Button Border Style"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1537
msgid "Button Border Width"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1571
msgid "Button Border Radius"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1606
msgid "Button Border Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1639
msgid "Box Border Style"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1668
msgid "Box Border Width"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1677
msgid "Box Border Radius"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1687
msgid "Box Border Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1696
msgid "Shadows"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1700
msgid "Horizontal"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1709
msgid "Vertical"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1718
msgid "Blur"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1727
msgid "Spread"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1736
msgid "Box Shadow Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1747
msgid "Enable Arrow "
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1777
msgid "Enable Animation "
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1810
msgid "Action Element"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1824
msgid "Element"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1829
msgid "Checkbox"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1833
msgid "Button"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1849
msgid "Checkbox Label"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1897
msgid "Highlight Text"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1912
msgid "Description"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1917
msgid "Use {{product_name}}, {{product_desc}}, {{product_price}} & {{quantity}} to fetch respective product details."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1923
msgid "Enable Image Options"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1933
msgid "By default, product image will be shown. If product image is not set then placeholder image will be used as product image."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1943
msgid "Minimum image size should be 300 X 300 in pixes for ideal display."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1949
msgid "Image Position"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1979
msgid "Image Width"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1984
msgid "Keep value empty for 100% width"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:1999
msgid "Show Image on Tab and Mobile"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2022
msgid "Select Product"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2026
msgid "Type to search for a product"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2039
msgid "Discount Type"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2045
msgid "Original"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2049
msgid "Discount Percentage"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2053
msgid "Discount Price"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2069
msgid "Discount Value"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2089
msgid "Original Price"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2091
msgid "This is the unit price of product"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2106
msgid "Sell Price"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2108
msgid "This is the unit discounted price of product"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2142
msgid "Sub-title Text"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2159
msgid "Enter to override default product title."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2193
msgid "Order Button Text"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2209
msgid "Skip Button Text"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2235
msgid "NavBar Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2250
msgid "Title Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2265
msgid "Subtitle Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2310
msgid "Overlay Background Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2382
msgid "Two-Step Design"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2398
msgid "Enable Note Text"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2434
msgid "Note Box Background Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2473
msgid "Step Section Width"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2479
msgid "Step Border"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2522
msgid "Section Title"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2530
msgid "Section Position"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2536
msgid "Before Checkout Section"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2550
msgid "Skins"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2568
msgid "It will add images on checkout page."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2574
msgid "Product Text Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2579
msgid "Product Background Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-meta-data.php:2595
msgid "Highlight Box Border Color"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-checkout-rules.php:120
msgid "No gateway found"
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-order-bump-product.php:152
msgid "Sorry, your session has expired."
msgstr ""

#: modules/checkout/classes/class-cartflows-pro-product-options.php:1147
msgid "Choose a Variation"
msgstr ""

#. translators: %s: Attribute name.
#: modules/checkout/classes/class-cartflows-pro-product-options.php:1308
msgid "Invalid value posted for %s"
msgstr ""

#. translators: %s: Attribute name.
#: modules/checkout/classes/class-cartflows-pro-product-options.php:1317
msgid "%s is a required field"
msgid_plural "%s are required fields"
msgstr[0] ""
msgstr[1] ""

#: modules/checkout/classes/class-cartflows-pro-product-options.php:1442
#: modules/checkout/templates/your-product/item-title.php:60
msgid " every "
msgstr ""

#. translators:  %1$s %2$s : trial length trial period
#: modules/checkout/classes/class-cartflows-pro-product-options.php:1784
msgid " with %1$s %2$s free trial "
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:76
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:414
#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:358
msgid "Continue to shipping"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:78
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:417
msgid "Continue to order notes"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:82
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:89
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:423
#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:359
msgid "Continue to payment"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:295
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:660
msgid "Contact"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:299
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:664
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:674
msgid "Change"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:344
msgid "Order notes"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:352
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:360
msgid "Information"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:387
msgid "« Back to shipping"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:390
msgid "« Back to order notes"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:394
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:428
msgid "« Back to information"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:464
msgid "Customer information"
msgstr ""

#. translators: %1$s: Link HTML start, %2$s Link HTML End
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:466
msgid "Already have an account? %1$1s Log in%2$2s"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:479
msgid "Email Address"
msgstr ""

#. translators: %s: asterisk mark
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:482
msgid "Email Address %s"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:497
msgid "Password"
msgstr ""

#. translators: %s: asterisk mark
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:499
msgid "Password %s"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:505
msgid "Login"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:506
msgid "Lost your password?"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:511
msgid "Login is optional, you can continue with your order below."
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:523
msgid "Create an account?"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:538
msgid "Account username"
msgstr ""

#. translators: %s: asterisk mark
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:540
msgid "Account username %s"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:552
msgid "Create account password"
msgstr ""

#. translators: %s: asterisk mark
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:554
msgid "Create account password %s"
msgstr ""

#. translators: %1$s: username, %2$s emailid
#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:563
msgid " Welcome Back %1$s ( %2$s )"
msgstr ""

#: modules/checkout/classes/layout/class-cartflows-pro-multistep-checkout.php:670
msgid "Address"
msgstr ""

#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:46
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:51
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:60
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:64
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:52
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:56
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:110
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:114
msgid "Add"
msgstr ""

#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:46
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:49
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:60
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:62
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:52
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:54
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:110
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:112
msgid "Remove"
msgstr ""

#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:49
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:62
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:54
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:112
msgid "Removing"
msgstr ""

#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:51
#: modules/checkout/templates/bump-order/wcf-bump-order-style-4.php:64
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:56
#: modules/checkout/templates/bump-order/wcf-bump-order-style-5.php:114
msgid "Adding"
msgstr ""

#: modules/checkout/templates/checkout/multistep-review-order.php:23
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:111
msgid "Products"
msgstr ""

#: modules/checkout/templates/checkout/multistep-review-order.php:24
#: modules/checkout/templates/checkout/multistep-review-order.php:56
msgid "Subtotal"
msgstr ""

#: modules/checkout/templates/pre-checkout-offer/pre-checkout-offer.php:19
msgid "Order Submitted"
msgstr ""

#: modules/checkout/templates/pre-checkout-offer/pre-checkout-offer.php:27
msgid "Special Offer"
msgstr ""

#: modules/checkout/templates/pre-checkout-offer/pre-checkout-offer.php:35
msgid "Order Receipt"
msgstr ""

#: modules/checkout/templates/quick-view/quick-view-product-image.php:54
msgid "Placeholder"
msgstr ""

#: modules/checkout/templates/your-product/item-quantity.php:23
msgid "This product is set to purchase only 1 item per order."
msgstr ""

#: modules/checkout/templates/your-product/item-title.php:38
msgid "Select"
msgstr ""

#: modules/checkout/templates/your-product/item-title.php:64
msgid " and a "
msgstr ""

#: modules/checkout/templates/your-product/item-title.php:66
msgid " sign-up fee "
msgstr ""

#: modules/checkout/templates/your-product/product-table-titles.php:20
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:149
#: modules/thankyou/template/child-order-details.php:58
msgid "Product"
msgstr ""

#: modules/checkout/templates/your-product/product-table-titles.php:26
msgid "Price"
msgstr ""

#: modules/downsell/classes/class-cartflows-downsell-markup.php:82
#: modules/downsell/classes/class-cartflows-downsell-markup.php:137
#: modules/offer/classes/class-cartflows-pro-base-offer-markup.php:222
#: modules/upsell/classes/class-cartflows-pro-upsell-markup.php:87
#: modules/upsell/classes/class-cartflows-pro-upsell-markup.php:144
msgid "Order does not exist"
msgstr ""

#: modules/downsell/classes/class-cartflows-downsell-markup.php:90
msgid "Downsell Payment Failed"
msgstr ""

#: modules/downsell/classes/class-cartflows-downsell-markup.php:129
#: modules/upsell/classes/class-cartflows-pro-upsell-markup.php:136
msgid "Current Step Not Found"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:109
msgid "YES"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:110
msgid "NO"
msgstr ""

#. translators: %1$1s, %2$2s Link to meta
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:370
msgid "Please enable \"Product Options\" from %1$1smeta settings%2$2s to edit options."
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:483
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:572
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:405
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:243
msgid "Rounded Corners"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:497
msgid "Highlight Product"
msgstr ""

#. translators: %1$1s, %2$2s Link to meta
#: modules/elementor/widgets/class-cartflows-pro-el-checkout-form-extend.php:609
msgid "Please enable \"Product Options\" from %1$1smeta settings%2$2s to apply styles."
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:218
msgid "Icon Vertical Alignment"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:321
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:268
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:171
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:159
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:159
msgid "Justify"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:333
msgid "Button Size"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:357
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:292
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:183
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:183
msgid "Normal"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:419
#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:313
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:213
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:213
msgid "Hover"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:426
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:220
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:220
msgid "Hover Text Color"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:439
msgid "Hover Background Color"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:474
msgid "Content"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:482
msgid "Text Alignment"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:513
msgid "Title Typography"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:525
msgid "Sub Title Typography"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-button.php:539
msgid "Title and Sub Title Spacing"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:179
msgid "Before Link"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:180
msgid "After Link"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:281
msgid "Link Typography"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:299
msgid "Link Color"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-action-link.php:320
msgid "Hover Link Color"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:145
msgid "Show"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:146
msgid "Hide"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-description.php:206
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:203
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-quantity.php:256
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:203
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-variation.php:222
msgid "Text Shadow"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:168
msgid "Image bottom Spacing"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:184
msgid "Image Border"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:195
msgid "Image Rounded Corners"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-image.php:254
msgid "Thumbnail Rounded Corners"
msgstr ""

#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-price.php:233
#: modules/elementor/widgets/class-cartflows-pro-el-offer-product-title.php:233
msgid "Hover Text Shadow"
msgstr ""

#. translators: %1s Release payment
#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:368
msgid "%1$s - Release Payment for Order %2$s"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:372
msgid "Payment token missing/invalid."
msgstr ""

#. translators: %1s payment released
#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:395
msgid "%1$s %2$s Release Payment Approved: %3$s ending in %4$s (expires %5$s)"
msgstr ""

#. translators: %1s transaction id
#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:402
msgid "(Transaction ID %s)"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:419
msgid "Authorization only transaction"
msgstr ""

#. translators: %1s payment failed message
#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:436
msgid "Pre-Order Release Payment Failed: %s"
msgstr ""

#. translators: %1s error message
#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:690
msgid "Authorize.net CIM Transaction Failed (%s)"
msgstr ""

#. translators: %1s site name
#: modules/gateways/class-cartflows-pro-gateway-authorize-net.php:700
msgid "Authorize.Net CIM Transaction Failed (%s)"
msgstr ""

#. translators: %1s site name
#. translators: %1$s: site name, %2$s: order id, %3$s: step id
#: modules/gateways/class-cartflows-pro-gateway-cpsw-stripe-element.php:248
#: modules/gateways/class-cartflows-pro-gateway-cpsw-stripe.php:266
#: modules/gateways/class-cartflows-pro-gateway-square-old.php:264
#: modules/gateways/class-cartflows-pro-gateway-square.php:327
msgid "%1$s - Order %2$s_%3$s - One Click Payment"
msgstr ""

#. translators: %1s order number
#: modules/gateways/class-cartflows-pro-gateway-cpsw-stripe-element.php:250
#: modules/gateways/class-cartflows-pro-gateway-cpsw-stripe.php:268
msgid "Order %1$s_%2$s OTO"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-mollie-credit-card.php:192
#: modules/gateways/class-cartflows-pro-gateway-mollie-ideal.php:193
msgid "0 value product"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-mollie-credit-card.php:243
#: modules/gateways/class-cartflows-pro-gateway-mollie-ideal.php:244
msgid "Customer id not found. Payment failed"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-mollie-credit-card.php:331
msgid "Mollie credit card payment processed."
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-mollie-ideal.php:350
msgid "Mollie ideal payment processed."
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-paypal-express.php:303
#: modules/gateways/class-cartflows-pro-gateway-paypal-standard.php:499
msgid "Nonce verification failed"
msgstr ""

#. translators: placeholder is blogname.
#: modules/gateways/class-cartflows-pro-gateway-paypal-express.php:433
#: modules/gateways/class-cartflows-pro-gateway-paypal-standard.php:627
msgctxt "data sent to paypal"
msgid "Orders with %s"
msgstr ""

#. translators: blog name.
#: modules/gateways/class-cartflows-pro-gateway-paypal-express.php:560
msgctxt "data sent to PayPal"
msgid "Orders with %s"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-paypal-express.php:689
#: modules/gateways/class-cartflows-pro-gateway-paypal-standard.php:1189
msgid "Unable to find order for PayPal billing agreement."
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-paypal-express.php:744
#: modules/gateways/class-cartflows-pro-gateway-paypal-standard.php:1244
msgid "An error occurred, please try again or try an alternate form of payment."
msgstr ""

#. translators: exception message.
#: modules/gateways/class-cartflows-pro-gateway-paypal-express.php:1064
msgid "PayPal Exp Transaction Failed (%s)"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-paypal-payments.php:221
msgid "Cannot make the Payment for Zero value product"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-paypal-payments.php:294
#: modules/gateways/class-cartflows-pro-gateway-paypal-payments.php:384
msgid "PayPal order is not created"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-paypal-payments.php:314
msgid "Order created successfully"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-paypal-payments.php:403
msgid "Order Captured successfully"
msgstr ""

#. translators: %s order id.
#: modules/gateways/class-cartflows-pro-gateway-paypal-payments.php:435
msgid "One Time Offer - %s"
msgstr ""

#. translators: %1s site name
#: modules/gateways/class-cartflows-pro-gateway-stripe.php:562
msgid "%1$s - Order %2$s - One Time offer"
msgstr ""

#. translators: %1s order number
#: modules/gateways/class-cartflows-pro-gateway-stripe.php:565
#: modules/gateways/class-cartflows-pro-gateway-woocommerce-payments.php:529
msgid "Order %1$s-OTO"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-stripe.php:582
msgid "customer_name"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-stripe.php:583
msgid "customer_email"
msgstr ""

#: modules/gateways/class-cartflows-pro-gateway-woocommerce-payments.php:240
msgid "No payment. No gateway found"
msgstr ""

#: modules/gateways/class-cartflows-pro-paypal-gateway-helper.php:159
msgid "Total Discount"
msgstr ""

#. translators: placeholder is blogname.
#: modules/gateways/class-cartflows-pro-paypal-gateway-helper.php:251
msgid "%s - Order"
msgstr ""

#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:107
#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:125
#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:149
#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:167
#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:185
#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:203
msgid "Permission denied."
msgstr ""

#: modules/gutenberg/classes/class-cartflows-pro-init-blocks.php:402
msgid "Cartflows Pro"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-markup.php:257
msgid "Seems like this order is been already purchased."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-markup.php:285
msgid "Oooops! Product is out of stock."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-markup.php:307
msgid "Oooops! Product's price is not correct."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-markup.php:431
msgid "Redirecting..."
msgstr ""

#. translators: %1$1s, %2$2s Link to meta
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:161
msgid "Do you want to cancel the main order on the purchase of upsell/downsell offer?<br>Please set the \"Create a new child order\" option in the %1$1sOffer Global Settings%2$2s to use the cancel primary order option."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:169
msgid "Replace Main Order"
msgstr ""

#. translators: %1$1s, %2$2s Link to meta
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:173
msgid "Note: If \"Replace Main Order\" option is enabled then on the purchase of upsell/downsell offer it will charge the difference of main order total and this product. %1$1sLearn More >>%2$2s"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:177
msgid "If this option is enabled, it will cancel the main order on the purchase of upsell/downsell offer."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:187
msgid "Skip Offer"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:188
msgid "Exclude the offer if the buyer has previously purchased the selected product."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:239
msgid "Offer Popup Strings"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:245
msgid "Offer Processing"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:252
msgid "Offer Success"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:259
msgid "Offer Failure"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:266
msgid "Offer Success Note"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:274
msgid "Shortcodes"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:280
msgid "Accept Offer Link"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:287
msgid "Decline Offer Link"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:296
msgid "Add this shortcode to your offer page for variation selection. If product is variable, it will show variations."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:304
msgid "Add this shortcode to your offer page for quantity selection."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:333
msgid "This shortcode will show the products single quantity price."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:355
msgid "Step Slug"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:363
msgid "Conditional Redirection"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:369
msgid "Offer - Yes Next Step"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:371
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:387
msgid "Upsell &lpar;Woo&rpar;"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:372
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:388
msgid "Downsell &lpar;Woo&rpar;"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:373
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:389
msgid "Thankyou &lpar;Woo&rpar;"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:385
msgid "Offer - No Next Step"
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:401
msgid "For more information about the conditional redirection please %1$1sClick here.%2$2s"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:407
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:413
msgid "Custom Script"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:420
msgid "Offer Success Script"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:423
msgid "Add your custom script which you need to run when the offer is accepted."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:428
msgid "Offer Rejected Script"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:431
msgid "Add your custom script which you need to run when the offer is rejected."
msgstr ""

#. translators: %1$1s: link html start, %2$12: link html end
#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:433
msgid "Use {{order_id}}, {{product_id}} & {{quantity}} and more shortcodes to fetch offer details. %1$1sClick here.%2$2s to know more."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-meta-data.php:445
msgid "Step Note"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-shortcodes.php:198
msgid "Shipping: "
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-shortcodes.php:200
msgid " via Flat rate"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-base-offer-shortcodes.php:309
msgid "Awaiting product image"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:75
msgid "CartFlows Order Auto Cancelled"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:83
msgid "CartFlows Upsell"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:85
msgid "CartFlows Downsell"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:110
msgid "Amount Charged: "
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:111
msgid "This order has charged the difference, and the same amount will be considered while refunding this order. You need to refund the rest of the amount from the parent order."
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:120
msgid "CartFlows Parent Order"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:132
msgid "CartFlows Upsell/Downsell Orders"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:141
msgid "Upsell"
msgstr ""

#: modules/offer/classes/class-cartflows-pro-offer-order-meta.php:143
msgid "Downsell"
msgstr ""

#: modules/orders/class-cartflows-pro-orders.php:78
msgctxt "Order status"
msgid "Main Order Accepted (CF)"
msgstr ""

#. translators: %s: Single count value
#: modules/orders/class-cartflows-pro-orders.php:100
msgid "Main Order Accepted <span class=\"count\">(%s)</span>"
msgid_plural "Main Order Accepted <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: %s step type
#: modules/orders/class-cartflows-pro-orders.php:512
msgid "Order has been cancelled as the user has upgraded to the CartFlows %s order."
msgstr ""

#: modules/thankyou/template/child-order-details.php:37
msgid "Order number:"
msgstr ""

#: modules/thankyou/template/child-order-details.php:42
msgid "Total:"
msgstr ""

#: modules/thankyou/template/child-order-details.php:49
msgid "Order details"
msgstr ""

#: modules/thankyou/template/child-order-details.php:100
msgid "Note:"
msgstr ""

#: modules/tracking/class-cartflows-pro-analytics-tracking.php:111
msgid "Page is opened in a preview mode."
msgstr ""

#: modules/tracking/class-cartflows-pro-analytics-tracking.php:123
msgid "Page is opened directly without placing an order."
msgstr ""

#: modules/upsell/classes/class-cartflows-pro-upsell-markup.php:95
msgid "Upsell Payment Failed"
msgstr ""

#. translators: abbreviation for units
#: modules/gutenberg/build/blocks.js:11
msgid "Before Link Text"
msgstr ""

#: modules/gutenberg/build/blocks.js:11
msgid "After Link Text"
msgstr ""

#: modules/gutenberg/build/blocks.js:11
msgid "Add text…"
msgstr ""
