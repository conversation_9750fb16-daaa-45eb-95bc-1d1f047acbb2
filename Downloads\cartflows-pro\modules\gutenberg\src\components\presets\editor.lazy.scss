.uagb-presets-radio-image-wrap {
	display: grid;
	grid-column-gap: 16px;
	grid-template-columns: repeat(2, 1fr);
	grid-row-gap: 14px;

	.uag-presets-radio-input {
		display: none;
	}

	.uag-presets-radio-input-label {
		position: relative;
		display: inline-block;
	}

	svg {
		width: 100%;
		height: auto;
		border-radius: 3px;
		fill: #898e95;
		border: 1px solid #dadada;
		color: #898e95;
		background-color: #f0f0f1;
		transition: background 0.08s ease, box-shadow 0.08s ease;
	}

	.uag-presets-radio-input:checked + .uag-presets-radio-input-label svg {
		background-color: #007cba;
		border: 1px solid #00608f;
	}

	.uag-presets-radio-image-clickable {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		height: 100%;
	}
}

.uagb-inspector-tab .components-panel__body .uag-presets-label {
	line-height: 2.4;
}

.uagb-presets-label-reset-wrap {
	display: flex;
	justify-content: space-between;
}

.uagb-presets-main-wrap {

	&.border-none svg {
		border: none;
	}
}
