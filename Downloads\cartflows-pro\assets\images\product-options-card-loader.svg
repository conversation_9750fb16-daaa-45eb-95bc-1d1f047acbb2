<svg role="img" aria-labelledby="loading-aria" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="0" y="0" width="100%" height="100%" clip-path="url(#clip-path)" style='fill: url("#fill");'></rect>
  <defs>
    <clipPath xmlns="http://www.w3.org/2000/svg" id="clip-path">
        <rect x="0" y="0" rx="0" ry="0" width="NaN" height="NaN"/> 
        <rect x="85" y="67" rx="3" ry="3" width="65%" height="10"/> 
        <rect x="85" y="86" rx="3" ry="3" width="50%" height="10"/> 
        <rect x="10" y="44" rx="3" ry="3" width="57" height="57"/> 
        <rect x="85" y="47" rx="3" ry="3" width="90%" height="10"/> 
        <rect x="10" y="10" rx="3" ry="3" width="20" height="20"/> 
        <rect x="49" y="13" rx="3" ry="3" width="94%" height="15"/>
    </clipPath>
    <linearGradient id="fill">
      <stop offset="0.599964" stop-color="#dddddd" stop-opacity="1" >
        <animate attributeName="offset" values="-2; -2; 1"
          keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite"></animate>
      </stop>
      <stop offset="1.59996" stop-color="#b0b0b0" stop-opacity="1" >
        <animate attributeName="offset" values="-1; -1; 2"
          keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite"></animate>
      </stop>
      <stop offset="2.59996" stop-color="#dddddd" stop-opacity="1" >
        <animate attributeName="offset" values="0; 0; 3"
          keyTimes="0; 0.25; 1" dur="2s" repeatCount="indefinite"></animate>
      </stop>
    </linearGradient>
  </defs>
</svg>