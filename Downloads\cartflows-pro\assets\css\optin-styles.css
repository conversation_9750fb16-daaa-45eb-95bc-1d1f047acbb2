.wcf-clear::before,
.wcf-clear::after {
	content: " ";
	display: table;
}

.wcf-clear::after {
	clear: both;
}

/* Select2 */

.wcf-optin-form .woocommerce .woocommerce-billing-fields .form-row .select2-hidden-accessible {
	height: 0 !important;
	min-height: 0 !important;
	width: 0 !important;
	margin: 0 !important;
}

.wcf-optin-form .select2-container--default .select2-selection--single .select2-selection__rendered {
	line-height: 26px;
	min-height: 26px;
	overflow: visible;
	padding-left: 0;
}

/**
* ************************
* Optimize Checkout fields
* ************************
*/

.wcf-optin-form .wcf-hide-field {
	-js-display: flex !important;
	display: flex !important;
	align-items: center;
	font-size: 13px;
}

.wcf-optin-form .wcf-hide-field label,
.wcf-optin-form .wcf-hide-field span {
	display: none !important;
}

.wcf-optin-form .woocommerce .woocommerce-billing-fields .form-row.wcf-hide-field.mt20 {
	margin-top: 0;
}
