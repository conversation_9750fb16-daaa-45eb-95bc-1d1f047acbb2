msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: gpt-po v1.1.1\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-02-04T15:33:29+00:00\n"
"PO-Revision-Date: 2025-02-04T15:33:29+00:00\n"
"Language: \n"

#: cartflows.php
#: classes/class-cartflows-admin-notices.php:217
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:62
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:71
#. Plugin Name of the plugin
msgid "CartFlows"
msgstr "CartFlows"

#: cartflows.php
#. Plugin URI of the plugin
msgid "https://cartflows.com/"
msgstr "https://cartflows.com/"

#: cartflows.php
#. Description of the plugin
msgid "Create beautiful checkout pages & sales flows for WooCommerce."
msgstr "Crea bellissime pagine di pagamento e flussi di vendita per WooCommerce."

#: admin-core/ajax/ab-steps.php:89
#. translators: %s step id
msgid "Can't create a variation for this step - %s, Invalid Step ID."
msgstr "Impossibile creare una variazione per questo passaggio - %s, ID del passaggio non valido."

#: admin-core/ajax/ab-steps.php:105
#. translators: %s flow id
msgid "Step successfully hidden - %s"
msgstr "Passaggio nascosto con successo - %s"

#: admin-core/ajax/ab-steps.php:140
#. translators: %s step id
msgid "Can't delete a variation for this step - %s, Invalid Step Id or Funnel Id."
msgstr "Impossibile eliminare una variazione per questo passaggio - %s, ID passaggio o ID funnel non valido."

#: admin-core/ajax/ab-steps.php:188
#. translators: %s flow id
msgid "Step deleted - %s"
msgstr "Passo eliminato - %s"

#: admin-core/ajax/ab-steps.php:223
#. translators: %s step id
msgid "Can't create a variation for this step - %s"
msgstr "Impossibile creare una variazione per questo passaggio - %s"

#: admin-core/ajax/ab-steps.php:279
#. translators: %s step id
msgid "A/B test settings updated for this step - %s"
msgstr "Impostazioni del test A/B aggiornate per questo passaggio - %s"

#: admin-core/ajax/ajax-errors.php:59
#: wizard/ajax/ajax-errors.php:59
msgid "Sorry, you are not allowed to do this operation."
msgstr "Spiacente, non ti è permesso eseguire questa operazione."

#: admin-core/ajax/ajax-errors.php:60
#: admin-core/ajax/common-settings.php:217
#: admin-core/ajax/common-settings.php:279
#: admin-core/ajax/common-settings.php:385
#: admin-core/ajax/common-settings.php:418
#: admin-core/inc/meta-ops.php:32
#: modules/checkout/classes/class-cartflows-checkout-ajax.php:110
#: wizard/ajax/ajax-errors.php:60
msgid "Nonce validation failed"
msgstr "Convalida del nonce fallita"

#: admin-core/ajax/ajax-errors.php:61
#: wizard/ajax/ajax-errors.php:61
msgid "Sorry, something went wrong."
msgstr "Mi dispiace, qualcosa è andato storto."

#: admin-core/ajax/ajax-errors.php:62
msgid "Required parameter is missing from the posted data."
msgstr "Manca un parametro richiesto nei dati inviati."

#: admin-core/ajax/common-settings.php:85
msgid "Successfully deleted the dynamic CSS keys!"
msgstr "Chiavi CSS dinamiche eliminate con successo!"

#: admin-core/ajax/common-settings.php:105
msgid "No post data found!"
msgstr "Nessun dato post trovato!"

#: admin-core/ajax/common-settings.php:152
msgid "Successfully saved data!"
msgstr "Dati salvati con successo!"

#: admin-core/ajax/debugger.php:82
#: admin-core/ajax/debugger.php:133
#: admin-core/ajax/debugger.php:157
msgid "You don't have permission to perform this action."
msgstr "Non hai il permesso di eseguire questa azione."

#: admin-core/ajax/debugger.php:91
msgid "Sync Success."
msgstr "Sincronizzazione riuscita."

#: admin-core/ajax/debugger.php:105
#: admin-core/inc/log-status.php:79
msgid "You don't have permission to view this page."
msgstr "Non hai il permesso di visualizzare questa pagina."

#: admin-core/ajax/debugger.php:139
#: admin-core/inc/log-status.php:175
msgid "Filename is empty. Please refresh the page and retry."
msgstr "Il nome del file è vuoto. Si prega di aggiornare la pagina e riprovare."

#: admin-core/ajax/debugger.php:174
#: admin-core/inc/log-status.php:210
msgid "Invalid file."
msgstr "File non valido."

#: admin-core/ajax/debugger.php:181
msgid "Export logs successfully"
msgstr "Esporta i registri con successo"

#: admin-core/ajax/flows.php:97
msgid "No Funnel IDs has been supplied to export!"
msgstr "Non sono stati forniti ID Funnel da esportare!"

#: admin-core/ajax/flows.php:110
#: admin-core/ajax/importer.php:109
#: admin-core/ajax/importer.php:212
msgid "Funnel exported successfully"
msgstr "Esportazione del funnel avvenuta con successo"

#: admin-core/ajax/flows.php:142
msgid "Can't update the flow data"
msgstr "Impossibile aggiornare i dati del flusso"

#: admin-core/ajax/flows.php:159
#: admin-core/ajax/steps.php:415
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "(no title)"
msgstr "(nessun titolo)"

#: admin-core/ajax/flows.php:184
msgid "Successfully saved the flow data!"
msgstr "Dati del flusso salvati con successo!"

#: admin-core/ajax/flows.php:246
msgid "Successfully deleted the Funnels!"
msgstr "Funnel eliminati con successo!"

#: admin-core/ajax/flows.php:274
#: admin-core/ajax/flows.php:352
#: admin-core/ajax/flows.php:988
msgid "No Funnel IDs has been supplied to delete!"
msgstr "Non sono stati forniti ID del funnel da eliminare!"

#: admin-core/ajax/flows.php:323
#: admin-core/ajax/flows.php:391
msgid "Successfully trashed the Funnels!"
msgstr "Imbuti eliminati con successo!"

#: admin-core/ajax/flows.php:422
msgid "Invalid Funnel ID has been supplied to update title."
msgstr "È stato fornito un ID Funnel non valido per aggiornare il titolo."

#: admin-core/ajax/flows.php:427
msgid "Can't update the flow title"
msgstr "Impossibile aggiornare il titolo del flusso"

#: admin-core/ajax/flows.php:443
#. translators: %s flow id
msgid "Funnel title updated - %s"
msgstr "Titolo del funnel aggiornato - %s"

#: admin-core/ajax/flows.php:468
msgid "Invalid Funnel ID has been supplied to clone!"
msgstr "È stato fornito un ID Funnel non valido per la clonazione!"

#: admin-core/ajax/flows.php:502
msgid "Invalid Funnel ID has been supplied to duplicate!"
msgstr "È stato fornito un ID funnel non valido da duplicare!"

#: admin-core/ajax/flows.php:679
msgid "Successfully cloned the Funnel!"
msgstr "Funnel clonato con successo!"

#: admin-core/ajax/flows.php:708
msgid "Invalid Funnel ID has been supplied to restore!"
msgstr "È stato fornito un ID imbuto non valido per il ripristino!"

#: admin-core/ajax/flows.php:748
msgid "Successfully restored the Funnel!"
msgstr "Ripristino del Funnel riuscito!"

#: admin-core/ajax/flows.php:775
msgid "Invalid Funnel ID has been supplied to trash!"
msgstr "È stato fornito un ID imbuto non valido per il cestino!"

#: admin-core/ajax/flows.php:814
msgid "Successfully trashed the Funnel!"
msgstr "Imbuto eliminato con successo!"

#: admin-core/ajax/flows.php:841
msgid "Invalid Funnel ID has been supplied to delete!"
msgstr "È stato fornito un ID funnel non valido per l'eliminazione!"

#: admin-core/ajax/flows.php:882
msgid "Successfully deleted the Funnel!"
msgstr "Funnel eliminato con successo!"

#: admin-core/ajax/flows.php:909
msgid "Invalid Funnel IDs has been supplied to update status!"
msgstr "Sono stati forniti ID di funnel non validi per aggiornare lo stato!"

#: admin-core/ajax/flows.php:958
#: admin-core/ajax/flows.php:1017
msgid "Successfully updated the Funnel status!"
msgstr "Stato del Funnel aggiornato con successo!"

#: admin-core/ajax/flows.php:1057
msgid "Invalid flow ID has been provided."
msgstr "È stato fornito un ID di flusso non valido."

#: admin-core/ajax/flows.php:1073
#. translators: %s flow id
msgid "Steps not sorted for flow - %s"
msgstr "Passaggi non ordinati per il flusso - %s"

#: admin-core/ajax/flows.php:1113
#. translators: %s flow id
msgid "Steps sorted for flow - %s"
msgstr "Passaggi ordinati per flusso - %s"

#: admin-core/ajax/flows.php:1146
msgid "No Funnel ID is been supplied"
msgstr "Nessun ID del funnel è stato fornito"

#: admin-core/ajax/flows.php:1159
#. translators: %s flow id
msgid "Notice Dismissed"
msgstr "Avviso respinto"

#: admin-core/ajax/importer.php:116
msgid "No Funnels to export"
msgstr "Nessun funnel da esportare"

#: admin-core/ajax/importer.php:205
msgid "Invalid flow ID."
msgstr "ID flusso non valido."

#: admin-core/ajax/importer.php:392
msgid "Invalid Funnel Id has been provided."
msgstr "È stato fornito un ID funnel non valido."

#: admin-core/ajax/importer.php:407
#. translators: %s: step ID
msgid "Invalid step id %1$s."
msgstr "ID del passaggio non valido %1$s."

#: admin-core/ajax/importer.php:414
msgid "Successfully created the step!"
msgstr "Passaggio creato con successo!"

#: admin-core/ajax/importer.php:516
msgid "Theme Activated"
msgstr "Tema attivato"

#: admin-core/ajax/importer.php:575
#: admin-core/ajax/importer.php:590
#: modules/flow/classes/class-cartflows-step-post-type.php:262
#: wizard/ajax/wizard.php:717
msgid "Checkout"
msgstr "Pagamento"

#: admin-core/ajax/importer.php:579
#: admin-core/ajax/importer.php:594
#: admin-core/ajax/importer.php:606
#: modules/flow/classes/class-cartflows-step-post-type.php:269
#: wizard/ajax/wizard.php:721
#: wizard/ajax/wizard.php:732
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Thank You"
msgstr "Grazie"

#: admin-core/ajax/importer.php:586
msgid "Sales Landing"
msgstr "Pagina di atterraggio delle vendite"

#: admin-core/ajax/importer.php:602
#: modules/flow/classes/class-cartflows-step-post-type.php:248
#: wizard/ajax/wizard.php:728
msgid "Landing"
msgstr "Atterraggio"

#: admin-core/ajax/importer.php:661
#: wizard/ajax/wizard.php:805
msgid "Successfully created the Funnel!"
msgstr "Funnel creato con successo!"

#: admin-core/ajax/importer.php:719
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "CartFlows Pro richiesto! %1$sAggiorna a CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:721
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$supgrade to CartFlows Pro%2$s."
msgstr "Per importare il flusso premium %1$saggiorna a CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:724
#: wizard/ajax/wizard.php:544
#. translators: %1$s: link html start, %2$s: link html end
msgid "Activate the CartFlows Pro to import the flow! %1$sActivate CartFlows Pro%2$s"
msgstr "Attiva CartFlows Pro per importare il flusso! %1$sAttiva CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:726
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate Cartflows Pro%2$s and validate the license key."
msgstr "Per importare il flusso premium %1$sattiva Cartflows Pro%2$s e convalida la chiave di licenza."

#: admin-core/ajax/importer.php:729
#. translators: %1$s: link html start, %2$s: link html end
msgid "Invalid License Key! %1$sActivate CartFlows Pro%2$s"
msgstr "Chiave di licenza non valida! %1$sAttiva CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:731
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import the premium flow %1$sactivate CartFlows Pro%2$s."
msgstr "Per importare il flusso premium %1$sattiva CartFlows Pro%2$s."

#: admin-core/ajax/importer.php:744
#: admin-core/ajax/importer.php:1056
msgid "Funnel data not found."
msgstr "Dati del funnel non trovati."

#: admin-core/ajax/importer.php:791
msgid "Steps not found."
msgstr "Passi non trovati."

#: admin-core/ajax/importer.php:824
#: wizard/ajax/wizard.php:642
msgid "Successfully imported the Flow!"
msgstr "Flusso importato con successo!"

#: admin-core/ajax/importer.php:873
msgid "Step data ID not found for import."
msgstr "ID dati del passaggio non trovato per l'importazione."

#: admin-core/ajax/importer.php:885
msgid "Funnel ID not found in the request."
msgstr "ID del funnel non trovato nella richiesta."

#: admin-core/inc/admin-helper.php:1127
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$1sTo resolve this issue, please check this %2$2sarticle%3$3s."
msgstr ""
"Errore di timeout della richiesta. Si prega di verificare se il firewall o qualsiasi plugin di sicurezza sta bloccando "
"le richieste HTTP/HTTPS in uscita verso templates.cartflows.com o meno. %1$1sPer risolvere questo problema, si prega di "
"controllare questo %2$2sarticolo%3$3s."

#: admin-core/ajax/importer.php:915
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sUpgrade to CartFlows Pro.%2$s"
msgstr "%1$sAggiorna a CartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:916
msgid "To import the premium step, please upgrade to CartFlows Pro"
msgstr "Per importare il passaggio premium, aggiorna a CartFlows Pro"

#: admin-core/ajax/importer.php:919
#: admin-core/ajax/importer.php:1041
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro%2$s"
msgstr "%1$sAttiva CartFlows Pro%2$s"

#: admin-core/ajax/importer.php:920
msgid "To import the premium step activate Cartflows Pro and validate the license key."
msgstr "Per importare il passaggio premium, attiva Cartflows Pro e convalida la chiave di licenza."

#: admin-core/ajax/importer.php:923
#. translators: %1$s: link html start, %2$s: link html end
msgid "%1$sActivate CartFlows Pro License %2$s"
msgstr "%1$sAttiva la licenza di CartFlows Pro %2$s"

#: admin-core/ajax/importer.php:924
msgid "To import the premium step activate the CartFlows Pro."
msgstr "Per importare il passaggio premium, attiva CartFlows Pro."

#: admin-core/ajax/importer.php:959
#: admin-core/ajax/importer.php:1080
msgid "Step data not found."
msgstr "Dati dei passi non trovati."

#: admin-core/ajax/importer.php:967
#: admin-core/ajax/importer.php:1088
msgid "Successfully imported the Step!"
msgstr "Importazione del passaggio riuscita!"

#: admin-core/ajax/importer.php:1038
#. translators: %1$s: link html start, %2$s: link html end
msgid "Upgrade to %1$sCartFlows Pro.%2$s"
msgstr "Aggiorna a %1$sCartFlows Pro.%2$s"

#: admin-core/ajax/importer.php:1044
#. translators: %1$s: link html start, %2$s: link html end
msgid "CartFlows Pro license is not active. Activate %1$sCartFlows Pro License %2$s"
msgstr "La licenza CartFlows Pro non è attiva. Attiva %1$sLicenza CartFlows Pro %2$s"

#: admin-core/ajax/importer.php:1112
#: admin-core/ajax/importer.php:1198
#. translators: %s: step ID
msgid "Invalid step id %1$s or post id %2$s."
msgstr "ID del passaggio %1$s o ID del post %2$s non valido."

#: admin-core/ajax/importer.php:1175
#: admin-core/inc/admin-menu.php:1194
#: admin-core/inc/store-checkout.php:110
msgid "Nonce verification failed."
msgstr "Verifica del nonce fallita."

#: admin-core/ajax/importer.php:1454
#: wizard/ajax/wizard.php:388
msgid "Successful!"
msgstr "Riuscito!"

#: admin-core/ajax/meta-data.php:143
#. Translators: %d stock amount
msgid "Stock: %d"
msgstr "Scorte: %d"

#: admin-core/ajax/meta-data.php:271
msgid "On backorder"
msgstr "In arretrato"

#: admin-core/ajax/meta-data.php:274
msgid "In stock"
msgstr "Disponibile"

#: admin-core/ajax/meta-data.php:277
msgid "Out of stock"
msgstr "Esaurito"

#: admin-core/ajax/setup-page.php:84
msgid "Setup page dismissed successfully."
msgstr "Pagina di configurazione chiusa con successo."

#: admin-core/ajax/steps.php:91
msgid "Can't update the step title"
msgstr "Impossibile aggiornare il titolo del passaggio"

#: admin-core/ajax/steps.php:112
#. translators: %s flow id
msgid "Step title updated - %s"
msgstr "Titolo del passaggio aggiornato - %s"

#: admin-core/ajax/steps.php:148
#. translators: %s flow id
msgid "Can't clone this step - %1$s. Flow - %2$s"
msgstr "Impossibile clonare questo passaggio - %1$s. Flusso - %2$s"

#: admin-core/ajax/steps.php:267
#. translators: %s flow id
msgid "Step - %1$s cloned. Flow - %2$s"
msgstr "Passo - %1$s clonato. Flusso - %2$s"

#: admin-core/ajax/steps.php:315
#. translators: %s flow id
msgid "Step not deleted for flow - %s"
msgstr "Passaggio non eliminato per il flusso - %s"

#: admin-core/ajax/steps.php:358
#. translators: %s flow id
msgid "Step deleted for flow - %s"
msgstr "Passaggio eliminato per il flusso - %s"

#: admin-core/ajax/steps.php:367
#. translators: %s flow id
msgid "This step can not be deleted."
msgstr "Questo passaggio non può essere eliminato."

#: admin-core/ajax/steps.php:400
#. translators: %s flow id
msgid "Invalid Step Id has been provided."
msgstr "È stato fornito un ID passaggio non valido."

#: admin-core/ajax/steps.php:451
#. translators: %s flow id
msgid "Data saved successfully for step id %s"
msgstr "Dati salvati con successo per l'ID del passaggio %s"

#: admin-core/api/common-settings.php:129
#: admin-core/api/flow-data.php:139
#: admin-core/api/flows.php:287
#: admin-core/api/home-page.php:172
#: admin-core/api/product/product-data.php:121
#: admin-core/api/step-data.php:144
msgid "Sorry, you cannot list resources."
msgstr "Spiacente, non puoi elencare le risorse."

#: admin-core/api/flow-data.php:70
msgid "Flow ID."
msgstr "ID del flusso."

#: admin-core/api/flows.php:194
#: modules/flow/classes/class-cartflows-flow-post-type.php:229
#: admin-core/assets/build/settings-app.js:80
msgid "View"
msgstr "Visualizza"

#: admin-core/api/flows.php:202
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Edit"
msgstr "Modifica"

#: admin-core/api/flows.php:210
#: admin-core/inc/admin-helper.php:763
#: admin-core/inc/admin-helper.php:845
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate"
msgstr "Duplicato"

#: admin-core/api/flows.php:217
#: admin-core/assets/build/settings-app.js:32
msgid "Export"
msgstr "Esporta"

#: admin-core/api/flows.php:224
#: admin-core/inc/admin-helper.php:780
#: admin-core/inc/admin-helper.php:854
#: admin-core/inc/admin-helper.php:922
#: admin-core/inc/admin-helper.php:942
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Delete"
msgstr "Elimina"

#: admin-core/api/product/product-data.php:68
#: admin-core/api/step-data.php:69
msgid "Step ID."
msgstr "ID del passaggio."

#: admin-core/inc/admin-helper.php:580
#: admin-core/inc/flow-meta.php:262
#: classes/class-cartflows-helper.php:1412
#: classes/class-cartflows-helper.php:1429
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:167
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:194
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:96
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1211
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:103
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:442
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:228
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:143
#: modules/optin/classes/class-cartflows-optin-meta-data.php:305
#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:50
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Default"
msgstr "Predefinito"

#: admin-core/inc/admin-helper.php:597
msgid "System Fonts"
msgstr "Font di sistema"

#: admin-core/inc/admin-helper.php:615
msgid "Google Fonts"
msgstr "Google Fonts"

#: admin-core/inc/admin-helper.php:772
msgid "A/B Test"
msgstr "Test A/B"

#: admin-core/inc/admin-helper.php:797
msgid "Automation"
msgstr "Automazione"

#: admin-core/inc/admin-helper.php:800
msgid "(Connect)"
msgstr "(Connetti)"

#: admin-core/inc/admin-helper.php:862
msgid "Archive"
msgstr "Archivio"

#: admin-core/inc/admin-helper.php:869
msgid "Declare as Winner"
msgstr "Dichiara come vincitore"

#: admin-core/inc/admin-helper.php:913
msgid "Deleted variation can't be restored."
msgstr "La variazione eliminata non può essere ripristinata."

#: admin-core/inc/admin-helper.php:914
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:188
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:200
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:212
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:224
msgid "Hide"
msgstr "Nascondi"

#: admin-core/inc/admin-helper.php:934
#: admin-core/assets/build/settings-app.js:32
msgid "Restore"
msgstr "Ripristina"

#: admin-core/inc/admin-helper.php:1115
msgid "Ooops! Something went wrong. Please open a support ticket from the website."
msgstr "Ooops! Qualcosa è andato storto. Si prega di aprire un ticket di supporto dal sito web."

#: admin-core/inc/admin-helper.php:1116
msgid "No error found."
msgstr "Nessun errore trovato."

#: admin-core/inc/admin-helper.php:1141
#. translators: %1$s: HTML, %2$s: HTML, %3$s: HTML
msgid ""
"Sorry for the inconvenience, but your website seems to be having trouble connecting to our server. %1$s Please open a "
"technical %2$ssupport ticket%3$s and share the server's outgoing IP address."
msgstr ""
"Ci scusiamo per l'inconveniente, ma il tuo sito web sembra avere problemi a connettersi al nostro server. %1$s Per "
"favore, apri un ticket di %2$sassistenza tecnica%3$s e condividi l'indirizzo IP in uscita del server."

#: admin-core/inc/admin-helper.php:1143
msgid "Server's outgoing IP address: "
msgstr "Indirizzo IP in uscita del server:"

#: admin-core/inc/admin-menu.php:123
#: admin-core/inc/admin-menu.php:174
#: classes/class-cartflows-flow-frontend.php:70
#: admin-core/assets/build/settings-app.js:32
msgid "Edit Funnel"
msgstr "Modifica Funnel"

#: admin-core/inc/admin-menu.php:210
msgid "Go to Funnel Editing"
msgstr "Vai alla modifica del funnel"

#: admin-core/inc/admin-menu.php:258
#: admin-core/inc/admin-menu.php:259
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:25
msgid "Funnels"
msgstr "Imbuti"

#: admin-core/inc/admin-menu.php:269
#: admin-core/inc/admin-menu.php:270
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: wizard/assets/build/wizard-app.js:1
msgid "Store Checkout"
msgstr "Cassa del negozio"

#: admin-core/inc/admin-menu.php:278
#: admin-core/inc/admin-menu.php:280
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Automations"
msgstr "Automazioni"

#: admin-core/inc/admin-menu.php:280
#: admin-core/inc/admin-menu.php:290
msgid "New"
msgstr "Nuovo"

#: admin-core/inc/admin-menu.php:298
#: admin-core/inc/admin-menu.php:299
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Add-ons"
msgstr "Componenti aggiuntivi"

#: admin-core/inc/admin-menu.php:308
#: admin-core/inc/admin-menu.php:309
msgid "Setup"
msgstr "Impostazione"

#: admin-core/inc/admin-menu.php:328
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Dashboard"
msgstr "Cruscotto"

#: admin-core/inc/admin-menu.php:541
msgid "Thin 100"
msgstr "Sottile 100"

#: admin-core/inc/admin-menu.php:542
msgid "Extra-Light 200"
msgstr "Extra-Light 200"

#: admin-core/inc/admin-menu.php:543
msgid "Light 300"
msgstr "Luce 300"

#: admin-core/inc/admin-menu.php:544
msgid "Normal 400"
msgstr "Normale 400"

#: admin-core/inc/admin-menu.php:545
msgid "Medium 500"
msgstr "Medio 500"

#: admin-core/inc/admin-menu.php:546
msgid "Semi-Bold 600"
msgstr "Semi-Grassetto 600"

#: admin-core/inc/admin-menu.php:547
msgid "Bold 700"
msgstr "Grassetto 700"

#: admin-core/inc/admin-menu.php:548
msgid "Extra-Bold 800"
msgstr "Extra-Bold 800"

#: admin-core/inc/admin-menu.php:549
msgid "Ultra-Bold 900"
msgstr "Ultra-Bold 900"

#: admin-core/inc/admin-menu.php:634
#. Translators: %1$s is the required page builder title, %2$s is the opening anchor tag to plugins.php, %3$s is the closing anchor tag, %4$s is the plugin title.
msgid "The default page builder is set to %1$s. Please %2$sinstall & activate%3$s the %4$s to start editing the steps."
msgstr ""
"Il generatore di pagine predefinito è impostato su %1$s. Si prega di %2$sinstallare e attivare%3$s il %4$s per iniziare "
"a modificare i passaggi."

#: admin-core/inc/admin-menu.php:1002
msgid "Stripe Payments For WooCommerce"
msgstr "Pagamenti Stripe per WooCommerce"

#: admin-core/inc/admin-menu.php:1003
msgid "Accept credit card payments in your store with Stripe for WooCommerce."
msgstr "Accetta pagamenti con carta di credito nel tuo negozio con Stripe per WooCommerce."

#: admin-core/inc/admin-menu.php:1014
msgid "PayPal Payments For WooCommerce"
msgstr "Pagamenti PayPal per WooCommerce"

#: admin-core/inc/admin-menu.php:1015
msgid "Accept payments in your store with PayPal for WooCommerce."
msgstr "Accetta pagamenti nel tuo negozio con PayPal per WooCommerce."

#: admin-core/inc/admin-menu.php:1026
msgid "WooCommerce"
msgstr "WooCommerce"

#: admin-core/inc/admin-menu.php:1027
msgid "WooCommerce is a customizable, open-source ecommerce platform built on WordPress."
msgstr "WooCommerce è una piattaforma ecommerce personalizzabile e open-source costruita su WordPress."

#: admin-core/inc/admin-menu.php:1051
msgid "SureMembers"
msgstr "SureMembers"

#: admin-core/inc/admin-menu.php:1065
msgid "Transform your WordPress form-building experience with stunning designs, ai integration, and no-code flexibility."
msgstr ""
"Trasforma la tua esperienza di creazione di moduli WordPress con design straordinari, integrazione AI e flessibilità "
"senza codice."

#: admin-core/inc/admin-menu.php:1064
msgid "SureForms"
msgstr "SureForms"

#: admin-core/inc/admin-menu.php:1076
#: wizard/assets/build/wizard-app.js:1
msgid "Spectra"
msgstr "Spettri"

#: admin-core/inc/admin-menu.php:1077
msgid "Power-up the Gutenberg editor with advanced and powerful blocks."
msgstr "Potenzia l'editor Gutenberg con blocchi avanzati e potenti."

#: admin-core/inc/admin-menu.php:1108
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Cart Abandonment"
msgstr "Abbandono del carrello"

#: admin-core/inc/admin-menu.php:1109
msgid "Recover abandonded carts with ease in less than 10 minutes."
msgstr "Recupera i carrelli abbandonati con facilità in meno di 10 minuti."

#: admin-core/inc/admin-menu.php:1120
msgid "Variation Swatches for WooCommerce"
msgstr "Varianti di Colore per WooCommerce"

#: admin-core/inc/admin-menu.php:1121
msgid "Convert dropdown boxes into highly engaging variation swatches."
msgstr "Converti i menu a tendina in varianti altamente coinvolgenti."

#: admin-core/inc/admin-menu.php:1137
msgid "Astra"
msgstr "Astra"

#: admin-core/inc/admin-menu.php:1138
msgid ""
"Astra is fast, fully customizable & beautiful WordPress theme suitable for blog, personal portfolio, business website "
"and WooCommerce storefront."
msgstr ""
"Astra è un tema WordPress veloce, completamente personalizzabile e bello, adatto per blog, portfolio personale, sito "
"web aziendale e vetrina WooCommerce."

#: admin-core/inc/admin-menu.php:1148
msgid "Spectra One"
msgstr "Spettri Uno"

#: admin-core/inc/admin-menu.php:1149
msgid ""
"Spectra One is a beautiful and modern WordPress theme built with the Full Site Editing (FSE) feature. It's a versatile "
"theme that can be used for blogs, portfolios, businesses, and more."
msgstr ""
"Spectra One è un tema WordPress bello e moderno costruito con la funzionalità di Full Site Editing (FSE). È un tema "
"versatile che può essere utilizzato per blog, portfolio, aziende e altro ancora."

#: admin-core/inc/flow-meta.php:54
msgid "Instant Layout "
msgstr "Layout istantaneo"

#: admin-core/inc/flow-meta.php:73
msgid "Logo"
msgstr "Logo"

#: admin-core/inc/flow-meta.php:108
msgid "Width (In px)"
msgstr "Larghezza (In px)"

#: admin-core/inc/flow-meta.php:125
msgid "Height (In px)"
msgstr "Altezza (In px)"

#: admin-core/inc/flow-meta.php:159
msgid "Global Styling"
msgstr "Stile Globale"

#: admin-core/inc/flow-meta.php:164
msgid "Enable Global Styling"
msgstr "Abilita lo stile globale"

#: admin-core/inc/flow-meta.php:172
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:211
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:119
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:119
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:291
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:297
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:168
#: modules/optin/classes/class-cartflows-optin-meta-data.php:277
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:125
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Primary Color"
msgstr "Colore primario"

#: admin-core/inc/flow-meta.php:187
msgid "Secondary Color"
msgstr "Colore secondario"

#: admin-core/inc/flow-meta.php:202
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:219
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:247
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:441
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:674
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:861
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:397
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:222
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:250
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:278
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:325
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:390
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:456
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:522
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1052
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:172
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:251
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:303
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:329
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:592
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:690
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:763
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:361
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:350
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:410
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:334
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:383
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:413
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:478
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:541
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:572
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:633
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:663
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:722
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:752
#: modules/optin/classes/class-cartflows-optin-meta-data.php:500
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Color"
msgstr "Colore del testo"

#: admin-core/inc/flow-meta.php:217
msgid "Heading/Accent Color"
msgstr "Colore dell'intestazione/accento"

#: admin-core/inc/flow-meta.php:233
msgid "General "
msgstr "Generale"

#: admin-core/inc/flow-meta.php:239
#: admin-core/inc/global-settings.php:149
msgid "Funnel Slug"
msgstr "Slug dell'imbuto"

#: admin-core/inc/flow-meta.php:245
msgid "Enable Test Mode"
msgstr "Abilita la modalità di test"

#: admin-core/inc/flow-meta.php:266
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:139
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:148
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:157
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:166
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "Yes"
msgstr "Sì"

#: admin-core/inc/flow-meta.php:270
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:140
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:149
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:158
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:167
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:43
#: admin-core/assets/build/settings-app.js:44
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
msgid "No"
msgstr "No"

#: admin-core/inc/flow-meta.php:277
msgid "Funnel Custom Script"
msgstr "Script personalizzato del funnel"

#: admin-core/inc/global-settings.php:45
msgid "No Access"
msgstr "Nessun accesso"

#: admin-core/inc/global-settings.php:49
msgid "Full Access"
msgstr "Accesso completo"

#: admin-core/inc/global-settings.php:54
msgid "Limited Access"
msgstr "Accesso limitato"

#: admin-core/inc/global-settings.php:71
msgid "Show Ready Templates for"
msgstr "Mostra modelli pronti per"

#: admin-core/inc/global-settings.php:73
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"Please choose your preferred page builder from the list so you will only see templates that are made using that page "
"builder. %1$sLearn More >>%2$s"
msgstr ""
"Si prega di scegliere il proprio page builder preferito dall'elenco in modo da visualizzare solo i modelli realizzati "
"con quel page builder. %1$sScopri di più >>%2$s"

#: admin-core/inc/global-settings.php:77
msgid "Block Editor"
msgstr "Editor di blocchi"

#: admin-core/inc/global-settings.php:82
msgid "Elementor"
msgstr "Elementor"

#: admin-core/inc/global-settings.php:87
msgid "Bricks"
msgstr "Mattoni"

#: admin-core/inc/global-settings.php:92
msgid "Beaver"
msgstr "Castoro"

#: admin-core/inc/global-settings.php:97
msgid "Other"
msgstr "Altro"

#: admin-core/inc/global-settings.php:110
msgid "Override Store Checkout"
msgstr "Sostituisci il checkout del negozio"

#: admin-core/inc/global-settings.php:112
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the Store Checkout settings please %1$sClick here%2$s."
msgstr "Per ulteriori informazioni sulle impostazioni del pagamento in negozio, si prega di %1$sCliccare qui%2$s."

#: admin-core/inc/global-settings.php:120
msgid "Disallow search engine from indexing funnels."
msgstr "Impedisci ai motori di ricerca di indicizzare i funnel."

#: admin-core/inc/global-settings.php:122
msgid "Prevent search engines from including funnels in their search results."
msgstr "Impedisci ai motori di ricerca di includere i funnel nei loro risultati di ricerca."

#: admin-core/inc/global-settings.php:139
msgid "Default Permalinks"
msgstr "Permalink predefiniti"

#: admin-core/inc/global-settings.php:140
msgid "Default WordPress Permalink"
msgstr "Permalink predefinito di WordPress"

#: admin-core/inc/global-settings.php:144
msgid "Funnel and Step Slug"
msgstr "Funnel e Step Slug"

#: admin-core/inc/global-settings.php:154
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1019
#: modules/landing/classes/class-cartflows-landing-meta-data.php:113
#: modules/optin/classes/class-cartflows-optin-meta-data.php:568
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:396
msgid "Step Slug"
msgstr "Slug del passaggio"

#: admin-core/inc/global-settings.php:164
msgid "Post Type Permalink Base"
msgstr "Base permalink del tipo di post"

#: admin-core/inc/global-settings.php:168
msgid "Step Base"
msgstr "Base del passo"

#: admin-core/inc/global-settings.php:174
msgid "Funnel Base"
msgstr "Base dell'imbuto"

#: admin-core/inc/global-settings.php:185
#: admin-core/inc/global-settings.php:376
#: admin-core/inc/global-settings.php:582
#: admin-core/inc/global-settings.php:956
msgid "Enable For CartFlows Pages"
msgstr "Abilita per le pagine di CartFlows"

#: admin-core/inc/global-settings.php:203
#: admin-core/inc/global-settings.php:394
#: admin-core/inc/global-settings.php:600
#: admin-core/inc/global-settings.php:788
#: admin-core/inc/global-settings.php:974
#: admin-core/inc/global-settings.php:1180
msgid "Enable for the whole site"
msgstr "Abilita per l'intero sito"

#: admin-core/inc/global-settings.php:205
msgid "If checked, page view and view content event will also be triggered for other pages/posts of site."
msgstr ""
"Se selezionato, l'evento di visualizzazione della pagina e di visualizzazione del contenuto verrà attivato anche per "
"altre pagine/post del sito."

#: admin-core/inc/global-settings.php:231
msgid "Enter Facebook pixel ID"
msgstr "Inserisci l'ID del pixel di Facebook"

#: admin-core/inc/global-settings.php:258
msgid "Facebook Pixel Events"
msgstr "Eventi Pixel di Facebook"

#: admin-core/inc/global-settings.php:272
#: admin-core/inc/global-settings.php:701
#: admin-core/inc/global-settings.php:888
#: admin-core/inc/global-settings.php:1093
msgid "View Content"
msgstr "Visualizza contenuto"

#: admin-core/inc/global-settings.php:288
msgid "Initiate Checkout"
msgstr "Inizia il checkout"

#: admin-core/inc/global-settings.php:304
#: admin-core/inc/global-settings.php:495
#: admin-core/inc/global-settings.php:716
#: admin-core/inc/global-settings.php:1108
#: admin-core/inc/global-settings.php:1309
msgid "Add Payment Info"
msgstr "Aggiungi informazioni di pagamento"

#: admin-core/inc/global-settings.php:321
msgid "Purchase Complete"
msgstr "Acquisto completato"

#: admin-core/inc/global-settings.php:337
#: admin-core/inc/global-settings.php:527
#: admin-core/inc/global-settings.php:748
#: admin-core/inc/global-settings.php:934
#: admin-core/inc/global-settings.php:1140
#: admin-core/inc/global-settings.php:1357
msgid "Optin Lead"
msgstr "Lead di adesione"

#: admin-core/inc/global-settings.php:358
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Facebook Pixel not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Il Pixel di Facebook non funziona correttamente? %1$1s Clicca qui %2$2s per saperne di più."

#: admin-core/inc/global-settings.php:396
msgid "If checked, page view event will also be triggered for other pages/posts of site."
msgstr "Se selezionato, l'evento di visualizzazione della pagina verrà attivato anche per altre pagine/post del sito."

#: admin-core/inc/global-settings.php:422
msgid "Enter Google Analytics ID"
msgstr "Inserisci l'ID di Google Analytics"

#: admin-core/inc/global-settings.php:426
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s google analytics account %2$2s to find your ID. e.g. G-XXXXX or UA-XXXXX-X"
msgstr "Accedi al tuo %1$1s account di Google Analytics %2$2s per trovare il tuo ID. ad es. G-XXXXX o UA-XXXXX-X"

#: admin-core/inc/global-settings.php:451
msgid "Google Analytics Events"
msgstr "Eventi di Google Analytics"

#: admin-core/inc/global-settings.php:464
#: admin-core/inc/global-settings.php:670
#: admin-core/inc/global-settings.php:858
#: admin-core/inc/global-settings.php:1062
#: admin-core/inc/global-settings.php:1279
msgid "Begin Checkout"
msgstr "Inizia il checkout"

#: admin-core/inc/global-settings.php:480
#: admin-core/inc/global-settings.php:686
#: admin-core/inc/global-settings.php:873
#: admin-core/inc/global-settings.php:1078
#: admin-core/inc/global-settings.php:1294
msgid "Add To Cart"
msgstr "Aggiungi al carrello"

#: admin-core/inc/global-settings.php:511
#: admin-core/inc/global-settings.php:732
#: admin-core/inc/global-settings.php:903
#: admin-core/inc/global-settings.php:1124
#: admin-core/inc/global-settings.php:1325
msgid "Purchase"
msgstr "Acquisto"

#: admin-core/inc/global-settings.php:548
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Google Analytics not working correctly? %1$1s Click here %2$2s to know more."
msgstr "Google Analytics non funziona correttamente? %1$1s Clicca qui %2$2s per saperne di più."

#: admin-core/inc/global-settings.php:566
msgid "Enter Google Map API key"
msgstr "Inserisci la chiave API di Google Maps"

#: admin-core/inc/global-settings.php:573
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Check this %1$1s article %2$2s to setup and find an API key."
msgstr "Controlla questo %1$1s articolo %2$2s per configurare e trovare una chiave API."

#: admin-core/inc/global-settings.php:602
#: admin-core/inc/global-settings.php:790
#: admin-core/inc/global-settings.php:976
msgid "If checked, PageView event will also be triggered for other pages/posts of site."
msgstr "Se selezionato, l'evento PageView verrà attivato anche per altre pagine/post del sito."

#: admin-core/inc/global-settings.php:628
msgid "Enter TikTok ID"
msgstr "Inserisci l'ID TikTok"

#: admin-core/inc/global-settings.php:632
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s TikTok business account %2$2s to find your ID."
msgstr "Accedi al tuo %1$1s account aziendale TikTok %2$2s per trovare il tuo ID."

#: admin-core/inc/global-settings.php:657
msgid "TikTok Events"
msgstr "Eventi TikTok"

#: admin-core/inc/global-settings.php:760
#: admin-core/inc/global-settings.php:946
#: admin-core/inc/global-settings.php:1152
#: admin-core/inc/global-settings.php:1369
msgid "Optin Lead event will be triggered for optin page."
msgstr "L'evento Optin Lead verrà attivato per la pagina di optin."

#: admin-core/inc/global-settings.php:770
#: admin-core/inc/global-settings.php:1162
msgid "Enable for CartFlows pages"
msgstr "Abilita per le pagine di CartFlows"

#: admin-core/inc/global-settings.php:816
msgid "Enter Snapchat pixel ID"
msgstr "Inserisci l'ID del pixel di Snapchat"

#: admin-core/inc/global-settings.php:820
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Snapchat business account %2$2s to find your ID."
msgstr "Accedi al tuo %1$1s account aziendale di Snapchat %2$2s per trovare il tuo ID."

#: admin-core/inc/global-settings.php:845
msgid "Snapchat Events"
msgstr "Eventi di Snapchat"

#: admin-core/inc/global-settings.php:918
#: wizard/assets/build/wizard-app.js:1
msgid "Subscribe"
msgstr "Iscriviti"

#: admin-core/inc/global-settings.php:1002
msgid "Enter Google Ads Conversion ID"
msgstr "Inserisci l'ID di conversione di Google Ads"

#: admin-core/inc/global-settings.php:1006
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion ID."
msgstr "Accedi al tuo %1$1s account Google Ads %2$2s per trovare il tuo ID di conversione."

#: admin-core/inc/global-settings.php:1019
msgid "Enter Google Ads Conversion Label"
msgstr "Inserisci l'etichetta di conversione di Google Ads"

#: admin-core/inc/global-settings.php:1023
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Google Ads account %2$2s to find your conversion label."
msgstr "Accedi al tuo %1$1s account Google Ads %2$2s per trovare la tua etichetta di conversione."

#: admin-core/inc/global-settings.php:1049
msgid "Google Ads Events"
msgstr "Eventi di Google Ads"

#: admin-core/inc/global-settings.php:1182
msgid "If checked, PageVisit event will also be triggered for other pages/posts of site."
msgstr "Se selezionato, l'evento PageVisit verrà attivato anche per altre pagine/post del sito."

#: admin-core/inc/global-settings.php:1208
msgid "Enter Pinterest Tag ID"
msgstr "Inserisci l'ID del Tag Pinterest"

#: admin-core/inc/global-settings.php:1212
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Log into your %1$1s Pinterest business account %2$2s to find your ID."
msgstr "Accedi al tuo %1$1s account aziendale di Pinterest %2$2s per trovare il tuo ID."

#: admin-core/inc/global-settings.php:1237
msgid "Enable Pinterest tag tracking consent notice"
msgstr "Abilita l'avviso di consenso per il tracciamento del tag Pinterest"

#: admin-core/inc/global-settings.php:1250
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"This setting enables a consent notice for Pinterest Tag tracking on your website. For more information check "
"%1$1sPinterest documentation%2$2s."
msgstr ""
"Questa impostazione abilita un avviso di consenso per il tracciamento del Pinterest Tag sul tuo sito web. Per ulteriori "
"informazioni, consulta la %1$1sdocumentazione di Pinterest%2$2s."

#: admin-core/inc/global-settings.php:1266
msgid "Pinterest Events"
msgstr "Eventi Pinterest"

#: admin-core/inc/global-settings.php:1340
msgid "Signup"
msgstr "Iscriviti"

#: admin-core/inc/global-settings.php:1352
msgid "Signup event will be triggered for optin page."
msgstr "L'evento di iscrizione sarà attivato per la pagina di opt-in."

#: admin-core/inc/global-settings.php:1382
msgid "Store Revenue Report Emails"
msgstr "Email del rapporto sulle entrate del negozio"

#: admin-core/inc/global-settings.php:1387
msgid "Enable Store Report Email."
msgstr "Abilita l'email del rapporto del negozio."

#: admin-core/inc/global-settings.php:1390
#. translators: %1$1s: link html start, %2$12: link html end
msgid "If enabled, you will receive the weekly report emails of your store for the revenue stats generated by CartFlows."
msgstr ""
"Se abilitato, riceverai le email del rapporto settimanale del tuo negozio per le statistiche sui ricavi generate da "
"CartFlows."

#: admin-core/inc/global-settings.php:1397
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:577
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:579
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:174
msgid "Email Address"
msgstr "Indirizzo email"

#: admin-core/inc/global-settings.php:1398
msgid "Email address to receive the weekly sales report emails. For multiple emails, add each email address per line."
msgstr ""
"Indirizzo email per ricevere le email del rapporto settimanale sulle vendite. Per più email, aggiungi ogni indirizzo "
"email per riga."

#: admin-core/inc/global-settings.php:1425
msgid "Delete plugin data on plugin deletion"
msgstr "Elimina i dati del plugin alla cancellazione del plugin"

#: admin-core/inc/global-settings.php:1430
msgid "Are you sure? Do you want to delete plugin data while deleting the plugin? Type \"DELETE\" to confirm!"
msgstr "Sei sicuro? Vuoi eliminare i dati del plugin mentre elimini il plugin? Digita \"DELETE\" per confermare!"

#: admin-core/inc/global-settings.php:1433
#. translators: %1$1s: link html start, %2$12: link html end
msgid ""
"This option will delete all the CartFlows options data on plugin deletion. If you enable this and deletes the plugin, "
"you can't restore your saved data. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Questa opzione eliminerà tutti i dati delle opzioni di CartFlows alla cancellazione del plugin. Se abiliti questa "
"opzione e cancelli il plugin, non potrai ripristinare i tuoi dati salvati. Per saperne di più, %1$1s Clicca qui %2$2s."

#: admin-core/inc/log-status.php:108
msgid "Log deleted successfully!"
msgstr "Log eliminato con successo!"

#: admin-core/inc/log-status.php:171
#: admin-core/inc/log-status.php:195
msgid "Nonce verification failed. Please refresh the page and retry."
msgstr "Verifica del nonce fallita. Si prega di aggiornare la pagina e riprovare."

#: admin-core/inc/store-checkout.php:63
msgid "Checkout (Store)"
msgstr "Cassa (Negozio)"

#: admin-core/inc/store-checkout.php:67
msgid "Thank You (Store)"
msgstr "Grazie (Negozio)"

#: admin-core/views/404-error.php:36
msgid "404 ERROR"
msgstr "ERRORE 404"

#: admin-core/views/404-error.php:37
msgid "Page Not Found."
msgstr "Pagina non trovata."

#: admin-core/views/404-error.php:38
msgid "Sorry, we couldn’t find the page you’re looking for."
msgstr "Spiacenti, non siamo riusciti a trovare la pagina che stai cercando."

#: admin-core/views/404-error.php:39
msgid "Go back home"
msgstr "Torna a casa"

#: admin-core/views/header.php:22
msgid "Generate More Leads & More Sales"
msgstr "Genera più contatti e più vendite"

#: classes/class-cartflows-admin-notices.php:88
#. translators: %1$s Software Title, %2$s Plugin, %3$s Anchor opening tag, %4$s Anchor closing tag, %5$s Software Title.
msgid ""
"%1$sCartFlows:%2$s We just introduced an awesome new feature, weekly store revenue reports via email. Now you can see "
"how many revenue we are generating for your store each week, without having to log into your website. You can set the "
"email address for these email from %3$shere.%4$s"
msgstr ""
"%1$sCartFlows:%2$s Abbiamo appena introdotto una nuova fantastica funzionalità, i rapporti settimanali delle entrate "
"del negozio via email. Ora puoi vedere quante entrate stiamo generando per il tuo negozio ogni settimana, senza dover "
"accedere al tuo sito web. Puoi impostare l'indirizzo email per queste email da %3$squi.%4$s"

#: classes/class-cartflows-admin-notices.php:218
msgid "How likely are you to recommend #pluginname to your friends or colleagues?"
msgstr "Quanto è probabile che tu consigli #pluginname ai tuoi amici o colleghi?"

#: classes/class-cartflows-admin-notices.php:221
msgid ""
"Could you please do us a favor and give us a 5-star rating on WordPress? It would help others choose CartFlows with "
"confidence. Thank you!"
msgstr ""
"Potresti per favore farci un favore e darci una valutazione di 5 stelle su WordPress? Aiuterebbe gli altri a scegliere "
"CartFlows con fiducia. Grazie!"

#: classes/class-cartflows-admin-notices.php:225
msgid "Thank you for your feedback"
msgstr "Grazie per il tuo feedback"

#: classes/class-cartflows-admin-notices.php:226
msgid "We value your input. How can we improve your experience?"
msgstr "Apprezziamo il tuo contributo. Come possiamo migliorare la tua esperienza?"

#: classes/class-cartflows-admin-notices.php:249
#. translators: %1$s: HTML, %2$s: HTML
msgid ""
"Heads up! The Gutenberg plugin is not recommended on production sites as it may contain non-final features that cause "
"compatibility issues with CartFlows and other plugins. %1$s Please deactivate the Gutenberg plugin %2$s to ensure the "
"proper functioning of your website."
msgstr ""
"Attenzione! Il plugin Gutenberg non è raccomandato sui siti di produzione poiché potrebbe contenere funzionalità non "
"definitive che causano problemi di compatibilità con CartFlows e altri plugin. %1$s Si prega di disattivare il plugin "
"Gutenberg %2$s per garantire il corretto funzionamento del tuo sito web."

#: classes/class-cartflows-admin.php:122
#: wizard/views/wizard-base.php:19
msgid "CartFlows Setup"
msgstr "Impostazione di CartFlows"

#: classes/class-cartflows-admin.php:167
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "Step"
msgstr "Passo"

#: classes/class-cartflows-admin.php:167
msgid "of"
msgstr "di"

#: classes/class-cartflows-admin.php:173
msgid "You're almost there! Once you complete CartFlows setup you can start receiving orders from flows."
msgstr "Sei quasi arrivato! Una volta completata la configurazione di CartFlows, puoi iniziare a ricevere ordini dai flussi."

#: classes/class-cartflows-admin.php:175
#: admin-core/assets/build/settings-app.js:34
msgid "Complete Setup"
msgstr "Completa l'installazione"

#: classes/class-cartflows-admin.php:233
msgid "Docs"
msgstr "Documenti"

#: classes/class-cartflows-admin.php:246
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:162
#: modules/landing/classes/class-cartflows-landing-meta-data.php:57
#: modules/optin/classes/class-cartflows-optin-meta-data.php:188
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:58
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Settings"
msgstr "Impostazioni"

#: classes/class-cartflows-admin.php:362
msgid "You do not have permission to access this page."
msgstr "Non hai il permesso di accedere a questa pagina."

#: classes/class-cartflows-admin.php:363
#: classes/class-cartflows-admin.php:394
#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:11
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback to Previous Version"
msgstr "Ripristina alla versione precedente"

#: classes/class-cartflows-admin.php:376
msgid "Error occurred, The version selected is invalid. Try selecting different version."
msgstr "Si è verificato un errore, la versione selezionata non è valida. Prova a selezionare una versione diversa."

#: classes/class-cartflows-default-meta.php:163
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1880
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1889
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1223
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:501
#: modules/checkout/templates/checkout/shipping-methods.php:69
msgid ""
"There are no shipping options available. Please ensure that your address has been entered correctly, or contact us if "
"you need any help."
msgstr ""
"Non ci sono opzioni di spedizione disponibili. Assicurati che il tuo indirizzo sia stato inserito correttamente o "
"contattaci se hai bisogno di aiuto."

#: classes/class-cartflows-default-meta.php:176
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1345
msgid "Place Order"
msgstr "Effettua ordine"

#: classes/class-cartflows-default-meta.php:367
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1320
msgid "is required"
msgstr "è richiesto"

#: classes/class-cartflows-default-meta.php:629
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:126
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:143
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:174
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:484
#: modules/thankyou/templates/instant-thankyou.php:76
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank you. Your order has been received."
msgstr "Grazie. Il tuo ordine è stato ricevuto."

#: classes/class-cartflows-default-meta.php:820
#: modules/optin/classes/class-cartflows-optin-meta-data.php:593
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit"
msgstr "Invia"

#: classes/class-cartflows-flow-frontend.php:284
msgid "Edit Design"
msgstr "Modifica design"

#: classes/class-cartflows-functions.php:595
#. translators: %1$s page builder name "string"
msgid ""
"We have introduced %1$1s widgets for CartFlows shortcodes. Now, you can add/change/update design settings directly from "
"the page builder as well."
msgstr ""
"Abbiamo introdotto i widget %1$1s per gli shortcode di CartFlows. Ora, puoi aggiungere/modificare/aggiornare le "
"impostazioni di design direttamente anche dal page builder."

#: classes/class-cartflows-functions.php:596
msgid "Learn More »"
msgstr "Scopri di più »"

#: classes/class-cartflows-helper.php:568
msgid "First name"
msgstr "Nome"

#: classes/class-cartflows-helper.php:577
msgid "Last name"
msgstr "Cognome"

#: classes/class-cartflows-helper.php:586
#: wizard/assets/build/wizard-app.js:3
msgid "Email address"
msgstr "Indirizzo email"

#: classes/class-cartflows-helper.php:1370
msgid "Enable Field"
msgstr "Abilita campo"

#: classes/class-cartflows-helper.php:1376
msgid "Field Width"
msgstr "Larghezza del campo"

#: classes/class-cartflows-helper.php:1382
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "33%"
msgstr "33%"

#: classes/class-cartflows-helper.php:1386
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "50%"
msgstr "50%"

#: classes/class-cartflows-helper.php:1390
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "100%"
msgstr "100%"

#: classes/class-cartflows-helper.php:1397
msgid "Field Label"
msgstr "Etichetta del campo"

#: classes/class-cartflows-helper.php:1402
#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
msgid "Field ID"
msgstr "ID del campo"

#: classes/class-cartflows-helper.php:1406
msgid "Copy this field id to use in Order Custom Field rule of dynamic offers."
msgstr "Copia questo ID di campo per utilizzarlo nella regola del Campo Personalizzato dell'Ordine delle offerte dinamiche."

#: classes/class-cartflows-helper.php:1418
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Checked"
msgstr "Controllato"

#: classes/class-cartflows-helper.php:1422
msgid "Un-Checked"
msgstr "Non selezionato"

#: classes/class-cartflows-helper.php:1439
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:411
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Options"
msgstr "Opzioni"

#: classes/class-cartflows-helper.php:1463
msgid "Min Date"
msgstr "Data minima"

#: classes/class-cartflows-helper.php:1470
msgid "Max Date"
msgstr "Data massima"

#: classes/class-cartflows-helper.php:1482
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Placeholder"
msgstr "Segnaposto"

#: classes/class-cartflows-helper.php:1491
msgid "Min Number"
msgstr "Numero Min"

#: classes/class-cartflows-helper.php:1497
msgid "Max Number"
msgstr "Numero massimo"

#: classes/class-cartflows-helper.php:1506
msgid "Show In Email"
msgstr "Mostra nell'email"

#: classes/class-cartflows-helper.php:1513
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Required"
msgstr "Richiesto"

#: classes/class-cartflows-helper.php:1521
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Collapsible"
msgstr "Pieghevole"

#: classes/class-cartflows-helper.php:1571
msgid "CartFlows Primary Color"
msgstr "Colore primario di CartFlows"

#: classes/class-cartflows-helper.php:1572
msgid "CartFlows Secondary Color"
msgstr "Colore secondario di CartFlows"

#: classes/class-cartflows-helper.php:1573
msgid "CartFlows Text Color"
msgstr "Colore del testo di CartFlows"

#: classes/class-cartflows-helper.php:1574
msgid "CartFlows Heading/Accent Color"
msgstr "Colore intestazione/accento di CartFlows"

#: classes/class-cartflows-loader.php:292
#. translators: %s: html tags
msgid ""
"The new version of  %1$s%3$s%2$s is released. Please download the latest zip to install the new updates. Click here to "
"%4$sdownload%5$s."
msgstr ""
"La nuova versione di %1$s%3$s%2$s è stata rilasciata. Si prega di scaricare l'ultimo zip per installare i nuovi "
"aggiornamenti. Clicca qui per %4$sscaricare%5$s."

#: classes/class-cartflows-loader.php:309
#. translators: %s: html tags
msgid "You are using an older version of %1$s%3$s%2$s. Please update %1$s%3$s%2$s plugin to version %1$s%4$s%2$s or higher."
msgstr ""
"Stai utilizzando una versione precedente di %1$s%3$s%2$s. Aggiorna il plugin %1$s%3$s%2$s alla versione %1$s%4$s%2$s o "
"successiva."

#: classes/class-cartflows-loader.php:612
#. translators: %s: html tags
msgid "This %1$sCartFlows%2$s page requires %1$sWooCommerce%2$s plugin installed & activated."
msgstr "Questa pagina %1$sCartFlows%2$s richiede il plugin %1$sWooCommerce%2$s installato e attivato."

#: classes/class-cartflows-loader.php:622
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
msgid "Activate WooCommerce"
msgstr "Attiva WooCommerce"

#: classes/class-cartflows-loader.php:629
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Install WooCommerce"
msgstr "Installa WooCommerce"

#: classes/class-cartflows-rollback.php:167
msgid "CartFlows <p>Rollback to Previous Version</p>"
msgstr "CartFlows <p>Ripristina alla versione precedente</p>"

#: classes/class-cartflows-tracking.php:1396
msgid "We use Pinterest tags to improve your experience. Do you consent to our use of Pinterest tags?"
msgstr "Utilizziamo i tag di Pinterest per migliorare la tua esperienza. Acconsenti all'uso dei tag di Pinterest?"

#: classes/class-cartflows-tracking.php:1397
msgid "Accept"
msgstr "Accetta"

#: classes/class-cartflows-tracking.php:1398
msgid "Decline"
msgstr "Rifiuta"

#: classes/class-cartflows-tracking.php:1403
msgid "Pinterest Consent"
msgstr "Consenso di Pinterest"

#: classes/importer/batch-process/class-cartflows-batch-process.php:482
msgid ""
"ERROR! Cron schedules are disabled by setting constant DISABLE_WP_CRON to true.<br/>To start the import process please "
"enable the cron by setting the constant to false. E.g. define( 'DISABLE_WP_CRON', false );"
msgstr ""
"ERRORE! Le pianificazioni Cron sono disabilitate impostando la costante DISABLE_WP_CRON su true.<br/>Per avviare il "
"processo di importazione, abilita il cron impostando la costante su false. Ad esempio, define( 'DISABLE_WP_CRON', false "
");"

#: classes/importer/batch-process/class-cartflows-batch-process.php:486
msgid ""
"ERROR! Cron schedules are disabled by setting constant ALTERNATE_WP_CRON to true.<br/>To start the import process "
"please enable the cron by setting the constant to false. E.g. define( 'ALTERNATE_WP_CRON', false );"
msgstr ""
"ERRORE! Le pianificazioni Cron sono disabilitate impostando la costante ALTERNATE_WP_CRON su true.<br/>Per avviare il "
"processo di importazione, abilita il cron impostando la costante su false. Ad esempio, define( 'ALTERNATE_WP_CRON', "
"false );"

#: classes/importer/batch-process/class-cartflows-batch-process.php:522
#. translators: 1: The HTTP response code.
msgid "Unexpected HTTP response code: %s"
msgstr "Codice di risposta HTTP inatteso: %s"

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:46
msgid "(✕) Empty content."
msgstr "(✕) Contenuto vuoto."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:51
msgid "(✕) Invalid content."
msgstr "(✕) Contenuto non valido."

#: classes/importer/batch-process/class-cartflows-importer-elementor.php:62
msgid "Invalid content. Expected an array."
msgstr "Contenuto non valido. Era previsto un array."

#: classes/importer/batch-process/helpers/class-wp-background-process-cartflows-sync-library.php:69
msgid "All processes are complete"
msgstr "Tutti i processi sono completi"

#: classes/importer/batch-process/helpers/class-wp-background-process.php:440
#. Translators: %d: interval
msgid "Every %d Minutes"
msgstr "Ogni %d minuti"

#: classes/importer/class-cartflows-api.php:428
msgid "Request successfully processed!"
msgstr "Richiesta elaborata con successo!"

#: classes/logger/class-cartflows-log-handler-file.php:355
#: classes/logger/class-cartflows-log-handler-file.php:375
msgid "This method should not be called before plugins_loaded."
msgstr "Questo metodo non dovrebbe essere chiamato prima di plugins_loaded."

#: classes/logger/class-cartflows-wc-logger.php:58
#. translators: 1: class name 2: Cartflows_Log_Handler_Interface
msgid "The provided handler %1$s does not implement %2$s."
msgstr "Il gestore fornito %1$s non implementa %2$s."

#: classes/logger/class-cartflows-wc-logger.php:136
#. translators: 1: Cartflows_WC_Logger::log 2: level
msgid "%1$s was called with an invalid level \"%2$s\"."
msgstr "%1$s è stato chiamato con un livello non valido \"%2$s\"."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:85
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:354
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:546
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:220
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:249
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:202
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:348
#: modules/gutenberg/dist/blocks.build.js:1
msgid "None"
msgstr "Nessuno"

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:112
#. translators: 1: anchor start, 2: anchor close
msgid ""
"Non-enrolled students will redirect to the selected CartFlows template. If you have not created any Flow already, add "
"new Flow from %1$shere%2$s."
msgstr ""
"Gli studenti non iscritti verranno reindirizzati al modello CartFlows selezionato. Se non hai ancora creato alcun "
"Flusso, aggiungi un nuovo Flusso da %1$ qui%2$s."

#: compatibilities/plugins/class-cartflows-learndash-compatibility.php:118
msgid "Select CartFlows Template for this Course"
msgstr "Seleziona il modello CartFlows per questo corso"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:44
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:150
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:373
#: modules/gutenberg/build/blocks-placeholder.js:9
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Checkout Form"
msgstr "Modulo di pagamento"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:34
msgid "Checkout Form."
msgstr "Modulo di pagamento."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:35
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:36
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:34
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:35
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:36
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:34
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:35
msgid "Cartflows Modules"
msgstr "Moduli Cartflows"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:137
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:146
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:59
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:68
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:251
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:198
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:207
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Checkout"
msgstr "Checkout moderno"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:138
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:60
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:69
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:255
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:199
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:208
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern One Column"
msgstr "Moderno a una colonna"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:139
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:149
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:61
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:70
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:263
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:200
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:209
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "One Column"
msgstr "Una colonna"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:140
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:150
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:62
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:71
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:267
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:210
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Column"
msgstr "Due colonne"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:141
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:64
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:203
msgid "MultiStep Checkout ( PRO )"
msgstr "Checkout MultiStep ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:142
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:63
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:202
msgid "Two Step ( PRO )"
msgstr "Due passi ( PRO )"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:148
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:73
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:212
#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout"
msgstr "Checkout a più fasi"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:151
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:271
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:211
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step"
msgstr "Due passi"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:168
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1212
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:446
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:229
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Modern Labels"
msgstr "Etichette moderne"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:132
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:109
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:68
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:82
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1012
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:157
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:294
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:160
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:152
#: modules/landing/classes/class-cartflows-landing-meta-data.php:106
#: modules/optin/classes/class-cartflows-optin-meta-data.php:561
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:389
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "General"
msgstr "Generale"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:190
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:253
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Layout"
msgstr "Seleziona layout"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:192
#. translators: %s: link
msgid "The PRO layout options are available in the CartFlows Pro. %1$s  Upgrade Now! %2$s"
msgstr "Le opzioni di layout PRO sono disponibili in CartFlows Pro. %1$s  Aggiorna ora! %2$s"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:204
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:277
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:183
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:186
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:112
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:141
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:390
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:164
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:414
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:207
#: modules/optin/classes/class-cartflows-optin-meta-data.php:298
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Style"
msgstr "Stile"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:207
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:115
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1226
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:70
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:289
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Global"
msgstr "Globale"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:233
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:262
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:341
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:529
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:458
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:473
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:127
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:189
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:334
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:236
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:264
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:310
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:346
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:399
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:689
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:228
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:347
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Typography"
msgstr "Tipografia"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:243
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:218
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1234
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:78
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:362
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:301
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:523
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:616
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:705
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:168
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading"
msgstr "Intestazione"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:273
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:137
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1230
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:72
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:421
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:406
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:199
#: modules/optin/classes/class-cartflows-optin-meta-data.php:292
msgid "Input Fields"
msgstr "Campi di input"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:287
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:147
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:460
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:913
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1114
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:443
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:878
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:226
#: modules/optin/classes/class-cartflows-optin-meta-data.php:375
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label Color"
msgstr "Colore dell'etichetta"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:301
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:161
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:477
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:188
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:572
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:454
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:237
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Background Color"
msgstr "Colore di sfondo del campo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:320
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:175
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:503
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:201
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:465
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:248
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Text / Placeholder Color"
msgstr "Colore del testo di input / segnaposto"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:350
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:542
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:198
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:344
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:533
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:476
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:259
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Style"
msgstr "Stile del bordo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:352
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:544
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:200
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:346
msgid "The type of border to use. Double borders must have a width of at least 3px to render properly."
msgstr ""
"Il tipo di bordo da utilizzare. I bordi doppi devono avere una larghezza di almeno 3px per essere visualizzati "
"correttamente."

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:355
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:547
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:203
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:349
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:539
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:482
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:265
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Solid"
msgstr "Solido"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:356
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:548
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:204
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:350
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:542
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:485
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:268
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dashed"
msgstr "Tratteggiato"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:357
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:549
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:205
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:351
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:541
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:484
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:267
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Dotted"
msgstr "Punteggiato"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:358
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:550
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:206
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:352
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:540
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:483
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:266
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Double"
msgstr "Doppio"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:377
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:582
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:230
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:377
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:575
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:500
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:278
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width"
msgstr "Larghezza del bordo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:398
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:605
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:840
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:245
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:393
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:610
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1024
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:517
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:940
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:290
#: modules/optin/classes/class-cartflows-optin-meta-data.php:396
#: modules/optin/classes/class-cartflows-optin-meta-data.php:528
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Color"
msgstr "Colore del bordo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:416
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:646
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:752
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:258
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:421
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius"
msgstr "Raggio del bordo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:437
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:274
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:556
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:287
#: modules/gutenberg/build/blocks.js:11
msgid "Buttons"
msgstr "Pulsanti"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:465
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:411
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:292
#: modules/optin/classes/class-cartflows-optin-meta-data.php:507
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Hover Color"
msgstr "Colore al passaggio del mouse sul testo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:485
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:824
#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:874
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:425
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:306
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:293
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:339
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:404
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:470
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:536
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:743
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1002
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1070
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:264
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:329
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:240
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:263
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:360
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:411
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:459
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:505
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:607
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:724
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:927
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:989
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:374
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:363
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:433
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:437
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:585
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:676
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:765
#: modules/optin/classes/class-cartflows-optin-meta-data.php:389
#: modules/optin/classes/class-cartflows-optin-meta-data.php:514
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Color"
msgstr "Colore di sfondo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:509
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:433
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:320
#: modules/optin/classes/class-cartflows-optin-meta-data.php:521
#: modules/gutenberg/build/blocks.js:11
msgid "Background Hover Color"
msgstr "Colore di sfondo al passaggio del mouse"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:626
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:331
#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:407
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:316
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:703
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:456
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:421
#: modules/optin/classes/class-cartflows-optin-meta-data.php:535
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Hover Color"
msgstr "Colore al passaggio del mouse sul bordo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:670
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1250
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:756
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Payment Section"
msgstr "Sezione Pagamenti"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:688
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1131
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:776
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Description Color"
msgstr "Descrizione Colore"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:702
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:802
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Information Background Color"
msgstr "Colore di sfondo delle informazioni"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:710
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1143
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:829
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:789
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:256
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Background Color"
msgstr "Colore di sfondo della sezione"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:724
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1161
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:814
msgid "Section Padding"
msgstr "Padding della sezione"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:738
#: modules/gutenberg/build/blocks.js:11
msgid "Margin"
msgstr "Margine"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:768
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1246
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:861
msgid "Field Validation & Error Messages"
msgstr "Convalida dei campi e messaggi di errore"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:772
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:602
msgid "Field Label Color"
msgstr "Colore dell'etichetta del campo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:788
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:936
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:587
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:890
msgid "Field Border Color"
msgstr "Colore del bordo del campo"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:808
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:980
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:913
msgid "Error Message Color"
msgstr "Colore del messaggio di errore"

#: modules/beaver-builder/cartflows-bb-checkout-form/cartflows-bb-checkout-form.php:857
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1242
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:962
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Review"
msgstr "Revisione dell'ordine"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:32
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:70
#: modules/gutenberg/classes/class-cartflows-block-config.php:54
#: modules/gutenberg/build/blocks-placeholder.js:10
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button"
msgstr "Pulsante Passo Successivo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:33
msgid "A simple next step button."
msgstr "Un semplice pulsante per il passo successivo."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:139
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:192
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text"
msgstr "Testo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:140
#: modules/bricks/class-cartflows-bricks-dynamic-data.php:61
msgid "Next Step"
msgstr "Prossimo passo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:149
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:187
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon"
msgstr "Icona"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:160
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:195
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Position"
msgstr "Posizione dell'icona"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:163
msgid "Before Text"
msgstr "Testo precedente"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:164
msgid "After Text"
msgstr "Dopo il testo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:172
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:240
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Spacing"
msgstr "Spaziatura delle icone"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:190
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Type"
msgstr "Digita"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:195
msgid "Flat"
msgstr "Piatto"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:196
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Gradient"
msgstr "Gradiente"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:197
msgid "Transparent"
msgstr "Trasparente"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:198
msgid "3D"
msgstr "3D"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:208
msgid "Border Size"
msgstr "Dimensione del bordo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:217
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:233
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:246
msgid "Hover Styles"
msgstr "Stili al passaggio del mouse"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:221
msgid "Fade Background"
msgstr "Sfuma sfondo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:222
msgid "Fill Background From Top"
msgstr "Riempi lo sfondo dall'alto"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:223
msgid "Fill Background From Bottom"
msgstr "Riempi sfondo dal basso"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:224
msgid "Fill Background From Left"
msgstr "Riempi sfondo da sinistra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:225
msgid "Fill Background From Right"
msgstr "Riempi sfondo da destra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:226
msgid "Fill Background Vertical"
msgstr "Riempi sfondo verticale"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:227
msgid "Fill Background Diagonal"
msgstr "Riempi sfondo diagonale"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:228
msgid "Fill Background Horizontal"
msgstr "Riempi sfondo orizzontale"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:236
msgid "Move Down"
msgstr "Sposta giù"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:237
msgid "Move Up"
msgstr "Sposta su"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:238
msgid "Move Left"
msgstr "Sposta a sinistra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:239
msgid "Move Right"
msgstr "Sposta a destra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:240
msgid "Animate Top"
msgstr "Anima in alto"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:241
msgid "Animate Bottom"
msgstr "Animare dal basso"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:250
msgid "Appear Icon From Right"
msgstr "Appare icona da destra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:251
msgid "Appear Icon From Left"
msgstr "Apparire icona da sinistra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:252
msgid "Appear Icon From Top"
msgstr "Appare l'icona dall'alto"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:253
msgid "Appear Icon From Bottom"
msgstr "Apparire icona dal basso"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:259
msgid "Structure"
msgstr "Struttura"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:263
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Width"
msgstr "Larghezza"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:267
#: modules/gutenberg/build/blocks.js:11
msgid "Full Width"
msgstr "Larghezza completa"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:268
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:509
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:680
#: modules/optin/classes/class-cartflows-optin-meta-data.php:355
#: modules/optin/classes/class-cartflows-optin-meta-data.php:459
msgid "Custom"
msgstr "Personalizzato"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:284
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:302
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:309
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Alignment"
msgstr "Allineamento"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:287
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:297
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:310
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:507
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:490
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center"
msgstr "Centro"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:288
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:298
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:306
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:503
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:313
#: modules/optin/classes/class-cartflows-optin-meta-data.php:486
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Left"
msgstr "Sinistra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:289
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:299
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:314
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:511
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:321
#: modules/optin/classes/class-cartflows-optin-meta-data.php:494
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Right"
msgstr "Destra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:294
msgid "Mobile Alignment"
msgstr "Allineamento mobile"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:304
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:340
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Padding"
msgstr "Riempimento"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:318
#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:447
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:792
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:215
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:277
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:626
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:395
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:373
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border"
msgstr "Confine"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:342
msgid "Custom Width"
msgstr "Larghezza personalizzata"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:351
msgid "Custom Height"
msgstr "Altezza personalizzata"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:360
msgid "Padding Top/Bottom"
msgstr "Imbottitura Superiore/Inferiore"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:369
msgid "Padding Left/Right"
msgstr "Padding Sinistra/Destra"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:378
msgid "Round Corners"
msgstr "Angoli arrotondati"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:393
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Colors"
msgstr "Colori"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:444
msgid "Apply Hover Color To"
msgstr "Applica colore al passaggio del mouse a"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:448
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:322
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
msgid "Background"
msgstr "Sfondo"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:461
msgid "Button Settings"
msgstr "Impostazioni pulsante"

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:465
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:260
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Size"
msgstr "Dimensione icona"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:33
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:46
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:66
#: modules/gutenberg/classes/class-cartflows-block-config.php:578
#: modules/optin/classes/class-cartflows-optin-meta-data.php:181
#: modules/optin/classes/class-cartflows-optin-meta-data.php:260
#: modules/gutenberg/build/blocks-placeholder.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Optin Form"
msgstr "Modulo di adesione"

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:34
msgid "Optin Form."
msgstr "Modulo di adesione."

#: modules/beaver-builder/cartflows-bb-optin-form/cartflows-bb-optin-form.php:97
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:104
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:144
#: modules/optin/classes/class-cartflows-optin-meta-data.php:309
msgid "Floating Labels"
msgstr "Etichette fluttuanti"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:32
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:46
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:65
#: modules/gutenberg/classes/class-cartflows-block-config.php:157
msgid "Order Details Form"
msgstr "Modulo Dettagli Ordine"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:33
msgid "Order Details Form."
msgstr "Modulo Dettagli Ordine."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:125
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:141
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:172
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Thank You Text"
msgstr "Testo di ringraziamento"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:136
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:321
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:86
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:150
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:458
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Overview"
msgstr "Panoramica dell'ordine"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:145
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:428
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:94
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:157
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:605
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:99
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Details"
msgstr "Dettagli dell'ordine"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:154
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:164
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:209
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Billing Address"
msgstr "Indirizzo di fatturazione"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:163
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:171
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:221
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Shipping Address"
msgstr "Indirizzo di spedizione"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:178
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:74
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:245
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing"
msgstr "Spaziatura"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:182
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:185
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:253
msgid "Heading Bottom Spacing"
msgstr "Spaziatura inferiore dell'intestazione"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:197
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:197
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:269
msgid "Spacing Between Sections"
msgstr "Spaziatura tra le sezioni"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:375
msgid "Sections Heading"
msgstr "Intestazioni delle sezioni"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:274
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:404
msgid "Sections Content"
msgstr "Contenuto delle sezioni"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:353
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:418
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:484
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:550
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:398
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:446
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:492
msgid "Text Typography"
msgstr "Tipografia del testo"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:363
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:90
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:515
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Downloads"
msgstr "Download"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:367
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:432
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:498
#: modules/gutenberg/build/blocks.js:11
msgid "Heading Color"
msgstr "Colore dell'intestazione"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:381
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:447
#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:513
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:381
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:432
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:479
msgid "Heading Typography"
msgstr "Tipografia dell'intestazione"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:494
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:98
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:697
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Customer Details"
msgstr "Dettagli del cliente"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:116
#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:117
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:128
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:246
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:159
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
#. translators: abbreviation for units
msgid "Layout"
msgstr "Layout"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:134
#. translators: %s is the URL for upgrading
msgid "This feature is available in the CartFlows higher plan. <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">%2$s</a>."
msgstr ""
"Questa funzione è disponibile nel piano superiore di CartFlows. <a href=\"%1$s\" target=\"_blank\" "
"rel=\"noopener\">%2$s</a>."

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:136
msgid "Upgrade Now!"
msgstr "Aggiorna ora!"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:154
msgid " Global Text Typography"
msgstr "Tipografia del testo globale"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:252
msgid " Global Primary Color"
msgstr "Colore primario globale"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:538
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:481
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:264
msgid "Inherit"
msgstr "Ereditare"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:645
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:528
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:643
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:403
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:301
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:381
msgid "Rounded Corners"
msgstr "Angoli arrotondati"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:841
#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:290
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Box Shadow"
msgstr "Ombra del riquadro"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:906
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:869
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Field Validation"
msgstr "Convalida del campo"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:973
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:904
msgid "Error Messages"
msgstr "Messaggi di errore"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1174
#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:827
msgid "Section Margin"
msgstr "Margine della sezione"

#: modules/bricks/elements/class-cartflows-bricks-checkout-form.php:1238
msgid "Buttons (Normal)"
msgstr "Pulsanti (Normale)"

#: modules/bricks/elements/class-cartflows-bricks-optin-form.php:76
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button"
msgstr "Pulsante"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:276
msgid "Sections Heading Typography"
msgstr "Tipografia dell'intestazione delle sezioni"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:288
msgid "Sections Text Typography"
msgstr "Sezioni Testo Tipografia"

#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:304
msgid "Sections Background Color"
msgstr "Colore di sfondo delle sezioni"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:145
msgid "Sorry there was a problem removing this coupon."
msgstr "Spiacente, si è verificato un problema durante la rimozione di questo coupon."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:148
msgid "Coupon has been removed."
msgstr "Il coupon è stato rimosso."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:166
msgid "Sorry there was a problem removing "
msgstr "Spiacente, si è verificato un problema durante la rimozione"

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:169
msgid " has been removed."
msgstr "è stato rimosso."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email Exist."
msgstr "Email esistente."

#: modules/checkout/classes/class-cartflows-checkout-ajax.php:203
msgid "Email not exist"
msgstr "Email non esiste"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:126
#. Translators: %1$s & %2$s is replaced with Field Name
msgid "%1$s Add %2$s"
msgstr "%1$s Aggiungi %2$s"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:137
#. Translators: %s is replaced with Field Icon
msgid "%s Have a coupon?"
msgstr "%s Hai un coupon?"

#: modules/checkout/classes/class-cartflows-checkout-fields.php:146
#. Translators: %s is replaced with Field Icon
msgid "%s Add Order Notes"
msgstr "%s Aggiungi note all'ordine"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:474
#: modules/optin/classes/class-cartflows-optin-markup.php:228
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:232
msgid "WooCommerce functions do not exist. If you are in an IFrame, please reload it."
msgstr "Le funzioni di WooCommerce non esistono. Se sei in un IFrame, ricaricalo."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:475
#: modules/optin/classes/class-cartflows-optin-markup.php:229
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:233
msgid "Click Here to Reload"
msgstr "Fai clic qui per ricaricare"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:503
msgid "Checkout ID not found"
msgstr "ID di pagamento non trovato"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:506
#. translators: %1$1s, %2$2s Link to article
msgid ""
"It seems that this is not the CartFlows Checkout page where you have added this shortcode. Please refer to this "
"%1$1sarticle%2$2s to know more."
msgstr ""
"Sembra che questa non sia la pagina di Checkout di CartFlows dove hai aggiunto questo shortcode. Si prega di fare "
"riferimento a questo %1$1sarticolo%2$2s per saperne di più."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:576
#: modules/checkout/templates/embed/checkout-template-simple.php:48
#: modules/checkout/templates/wcf-template.php:40
#: modules/optin/templates/optin-template-simple.php:29
msgid "Your cart is currently empty."
msgstr "Il tuo carrello è attualmente vuoto."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:616
#. translators: %1$1s, %2$2s Link to meta
msgid "No product is selected. Please select products from the %1$1scheckout meta settings%2$2s to continue."
msgstr "Nessun prodotto è selezionato. Seleziona i prodotti dalle %1$1simpostazioni meta del checkout%2$2s per continuare."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:724
msgid "Variations Not set"
msgstr "Variazioni Non impostate"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:735
msgid "This product can't be purchased"
msgstr "Questo prodotto non può essere acquistato"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1467
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1511
#: modules/checkout/templates/checkout/collapsed-order-summary.php:46
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:82
msgid "Coupon Code"
msgstr "Codice Coupon"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1468
#: modules/checkout/classes/class-cartflows-checkout-markup.php:1520
#: modules/checkout/templates/checkout/collapsed-order-summary.php:51
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:87
msgid "Apply"
msgstr "Applica"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1544
msgid "Entered email address is not a valid email."
msgstr "L'indirizzo email inserito non è un'email valida."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1545
msgid "This email is already registered. Please enter the password to continue."
msgstr "Questa email è già registrata. Inserisci la password per continuare."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1548
msgid "Value must be between "
msgstr "Il valore deve essere compreso tra"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1752
msgid "Show Order Summary"
msgstr "Mostra riepilogo ordine"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1753
msgid "Hide Order Summary"
msgstr "Nascondi riepilogo ordine"

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1876
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:498
#: modules/checkout/templates/checkout/shipping-methods.php:66
msgid "Enter your address to view shipping options."
msgstr "Inserisci il tuo indirizzo per visualizzare le opzioni di spedizione."

#: modules/checkout/classes/class-cartflows-checkout-markup.php:1885
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:405
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:407
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:796
#: modules/checkout/templates/checkout/instant-checkout-review-order.php:110
#: modules/thankyou/templates/instant-thankyou-order-details.php:107
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping"
msgstr "Spedizione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:138
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:943
#: modules/optin/classes/class-cartflows-optin-meta-data.php:174
msgid "Products"
msgstr "Prodotti"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:144
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:80
msgid "Order Bumps"
msgstr "Incrementi d'ordine"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:156
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Offers"
msgstr "Offerte Dinamiche"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:172
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:982
msgid "Checkout Offer"
msgstr "Offerta di Checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:205
msgid "Two Step (Available in higher plan) "
msgstr "Due passaggi (Disponibile nel piano superiore)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:206
msgid "Multistep Checkout (Available in higher plan) "
msgstr "Checkout multistep (Disponibile nel piano superiore)"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:213
#: modules/landing/classes/class-cartflows-landing-meta-data.php:91
#: modules/optin/classes/class-cartflows-optin-meta-data.php:253
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:92
msgid "Shortcode"
msgstr "Codice breve"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:230
msgid "CartFlows Checkout"
msgstr "Checkout di CartFlows"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:232
msgid "Add this shortcode to your checkout page"
msgstr "Aggiungi questo shortcode alla tua pagina di pagamento"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:239
msgid "Checkout Design"
msgstr "Design del checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:245
msgid "Checkout Skin"
msgstr "Verifica la pelle"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:259
#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout"
msgstr "Pagamento in più fasi"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:296
#: modules/optin/classes/class-cartflows-optin-meta-data.php:284
#: modules/optin/classes/class-cartflows-optin-meta-data.php:317
#: modules/optin/classes/class-cartflows-optin-meta-data.php:420
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:181
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:205
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:306
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Family"
msgstr "Famiglia di caratteri"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:304
msgid "Instant Checkout"
msgstr "Pagamento immediato"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:319
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:328
msgid "Left Column Background Color"
msgstr "Colore di sfondo della colonna sinistra"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:336
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:344
msgid "Right Column Background Color"
msgstr "Colore di sfondo della colonna destra"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:355
msgid "Checkout Texts & Buttons"
msgstr "Testi e pulsanti di pagamento"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:362
msgid "Enable Advance Options"
msgstr "Abilita opzioni avanzate"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:370
msgid "Heading Font"
msgstr "Carattere dell'intestazione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:384
#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text Color"
msgstr "Colore del testo dell'intestazione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:400
msgid "Heading Font Family"
msgstr "Famiglia di caratteri dell'intestazione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:436
msgid "Input Field Style"
msgstr "Stile del campo di input"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:463
msgid "Input Field Font Family"
msgstr "Famiglia di caratteri del campo di input"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:483
msgid "Field Size"
msgstr "Dimensione del campo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:489
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:660
#: modules/optin/classes/class-cartflows-optin-meta-data.php:335
#: modules/optin/classes/class-cartflows-optin-meta-data.php:439
msgid "Extra Small"
msgstr "Extra Small"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:493
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:664
#: modules/optin/classes/class-cartflows-optin-meta-data.php:339
#: modules/optin/classes/class-cartflows-optin-meta-data.php:443
msgid "Small"
msgstr "Piccolo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:497
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:668
#: modules/optin/classes/class-cartflows-optin-meta-data.php:343
#: modules/optin/classes/class-cartflows-optin-meta-data.php:447
msgid "Medium"
msgstr "Medio"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:501
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:672
#: modules/optin/classes/class-cartflows-optin-meta-data.php:347
#: modules/optin/classes/class-cartflows-optin-meta-data.php:451
msgid "Large"
msgstr "Grande"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:505
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:676
#: modules/optin/classes/class-cartflows-optin-meta-data.php:351
#: modules/optin/classes/class-cartflows-optin-meta-data.php:455
msgid "Extra Large"
msgstr "Extra Large"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:525
msgid "Field Top-Bottom Spacing"
msgstr "Spaziatura campo dall'alto verso il basso"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:541
msgid "Field Left-Right Spacing"
msgstr "Spaziatura Campo Sinistra-Destra"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:557
msgid "Field Text / Placeholder Color"
msgstr "Colore del testo del campo / segnaposto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:618
msgid "Button Fields"
msgstr "Campi Pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:634
msgid "Button Font Family"
msgstr "Famiglia di caratteri del pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:654
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:330
msgid "Button Size"
msgstr "Dimensione del pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:696
msgid "Button Top-Bottom Spacing"
msgstr "Spaziatura Pulsante Alto-Basso"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:717
msgid "Button Left-Right Spacing"
msgstr "Spaziatura Sinistra-Destra del Pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:738
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:294
msgid "Button Text Color"
msgstr "Colore del testo del pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:753
msgid "Button Text Hover Color"
msgstr "Colore al passaggio del mouse sul testo del pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:768
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:300
msgid "Button Background Color"
msgstr "Colore di sfondo del pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:783
msgid "Button Background Hover Color"
msgstr "Colore di sfondo al passaggio del mouse sul pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:798
msgid "Button Border Color"
msgstr "Colore del bordo del pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:813
msgid "Button Border Hover Color"
msgstr "Colore del bordo del pulsante al passaggio del mouse"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:861
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:865
msgid "Enable Product Options"
msgstr "Abilita opzioni prodotto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:870
msgid "Enable Conditions"
msgstr "Abilita condizioni"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:874
msgid "Restrict user to purchase all products"
msgstr "Limitare l'utente all'acquisto di tutti i prodotti"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:878
msgid "Let user select one product from all options"
msgstr "Consenti all'utente di selezionare un prodotto tra tutte le opzioni"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:882
msgid "Let user select multiple products from all options"
msgstr "Consenti all'utente di selezionare più prodotti da tutte le opzioni"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:888
msgid "Enable Variations"
msgstr "Abilita variazioni"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:897
msgid "Show variations inline"
msgstr "Mostra variazioni in linea"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:901
msgid "Show variations in popup"
msgstr "Mostra le variazioni nel popup"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:907
msgid "Enable Quantity"
msgstr "Abilita Quantità"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:920
msgid "Select Coupon"
msgstr "Seleziona coupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:921
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a coupon"
msgstr "Cerca un coupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:925
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows coupon please %1$1s Click here.%2$2s"
msgstr "Per ulteriori informazioni sul coupon CartFlows, si prega di %1$1s fare clic qui.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:951
msgid "Select Product"
msgstr "Seleziona prodotto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:952
msgid "Search for a product..."
msgstr "Cerca un prodotto..."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:962
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the checkout product settings please %1$1s Click here.%2$2s"
msgstr "Per ulteriori informazioni sulle impostazioni del prodotto di checkout, si prega di %1$1s fare clic qui.%2$2s"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:968
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Auto Apply Coupon"
msgstr "Applica automaticamente il coupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:975
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Product Options"
msgstr "Opzioni del prodotto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1026
#: modules/flow/classes/class-cartflows-step-meta-base.php:80
#: modules/landing/classes/class-cartflows-landing-meta-data.php:119
#: modules/optin/classes/class-cartflows-optin-meta-data.php:574
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:402
msgid "Custom Script"
msgstr "Script personalizzato"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1035
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:475
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Advanced"
msgstr "Avanzato"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1041
msgid "Display product images"
msgstr "Mostra le immagini dei prodotti"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1049
msgid "Enable cart editing on checkout"
msgstr "Abilita la modifica del carrello al checkout"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1064
#: modules/landing/classes/class-cartflows-landing-meta-data.php:134
#: modules/optin/classes/class-cartflows-optin-meta-data.php:634
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:523
msgid "Step Note"
msgstr "Nota del passaggio"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1148
msgid "Form Settings"
msgstr "Impostazioni del modulo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1153
msgid "Enable Coupon Field"
msgstr "Abilita campo coupon"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1159
msgid "Collapsible Coupon Field"
msgstr "Campo coupon comprimibile"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1175
msgid "Enable Additional Field"
msgstr "Abilita campo aggiuntivo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1181
msgid "Collapsible Additional Field"
msgstr "Campo aggiuntivo comprimibile"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1197
msgid "Enable Ship To Different Address"
msgstr "Abilita Spedizione a Indirizzo Diverso"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1204
msgid "Enable Google Address Autocomplete"
msgstr "Abilita il completamento automatico degli indirizzi di Google"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1207
#. translators: %1$s: link html start, %2$s: link html end
msgid "Before enabling this option, make sure that you have added API key in Google Address Autocomplete Settings."
msgstr ""
"Prima di abilitare questa opzione, assicurati di aver aggiunto la chiave API nelle Impostazioni di completamento "
"automatico degli indirizzi di Google."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1212
msgid "Enable Custom Shipping Message"
msgstr "Abilita messaggio di spedizione personalizzato"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1220
msgid "Shipping Message"
msgstr "Messaggio di spedizione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1224
msgid "This message will be displayed when no shipping method is available."
msgstr "Questo messaggio verrà visualizzato quando non è disponibile alcun metodo di spedizione."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1241
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:446
msgid "Order Summary Position"
msgstr "Posizione riepilogo ordine"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1248
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:219
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:453
msgid "Top"
msgstr "In alto"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1252
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:457
msgid "Bottom"
msgstr "Inferiore"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1266
msgid "Form Headings"
msgstr "Intestazioni del modulo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1273
msgid "Billing Details"
msgstr "Dettagli di fatturazione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1275
msgid "Billing details"
msgstr "Dettagli di fatturazione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1280
msgid "Shipping Details"
msgstr "Dettagli di spedizione"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1282
msgid "Ship to a different address?"
msgstr "Spedire a un indirizzo diverso?"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1287
#: modules/thankyou/templates/instant-thankyou-your-product.php:23
msgid "Your Order"
msgstr "Il tuo ordine"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1289
msgid "Your order"
msgstr "Il tuo ordine"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1294
msgid "Customer Information"
msgstr "Informazioni del cliente"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1296
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:159
msgid "Customer information"
msgstr "Informazioni sul cliente"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1302
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1304
#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:823
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:276
#: modules/thankyou/templates/instant-thankyou-order-details.php:127
msgid "Payment"
msgstr "Pagamento"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1310
msgid "Enable Field validation error message"
msgstr "Abilita il messaggio di errore di convalida del campo"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1318
msgid "Validation error message"
msgstr "Messaggio di errore di convalida"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1336
msgid "Place Order Button"
msgstr "Pulsante Ordina"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1342
#: modules/optin/classes/class-cartflows-optin-meta-data.php:590
#: modules/gutenberg/build/blocks.js:11
msgid "Button Text"
msgstr "Testo del pulsante"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1351
msgid "Enable Lock Icon"
msgstr "Abilita icona di blocco"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1360
msgid "Enable Price Display"
msgstr "Abilita visualizzazione prezzi"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:260
msgid "Home"
msgstr "Casa"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:496
#: modules/checkout/templates/checkout/shipping-methods.php:64
msgid "Shipping costs are calculated during checkout."
msgstr "I costi di spedizione vengono calcolati durante il checkout."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:515
#: modules/checkout/templates/checkout/shipping-methods.php:72
#. Translators: $s shipping destination.
msgid "No shipping options were found for %s."
msgstr "Non sono state trovate opzioni di spedizione per %s."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:519
#: modules/checkout/templates/checkout/shipping-methods.php:73
#. Translators: $s shipping destination.
msgid "Enter a different address"
msgstr "Inserisci un indirizzo diverso"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:562
#: modules/thankyou/templates/instant-thankyou-order-details.php:67
msgid "Contact"
msgstr "Contatto"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:564
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "%1$1s Log in%2$2s"
msgstr "%1$1s Accedi%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:594
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:192
msgid "Password"
msgstr "Password"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:596
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:194
#. translators: %s: asterisk mark
msgid "Password %s"
msgstr "Password %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:602
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:200
msgid "Login"
msgstr "Accesso"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:603
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:201
msgid "Lost your password?"
msgstr "Hai perso la password?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:608
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:206
msgid "Login is optional, you can continue with your order below."
msgstr "Il login è facoltativo, puoi continuare con il tuo ordine qui sotto."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:620
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:218
msgid "Create an account?"
msgstr "Creare un account?"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:635
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:233
msgid "Account username"
msgstr "Nome utente dell'account"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:637
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:235
#. translators: %s: asterisk mark
msgid "Account username %s"
msgstr "Nome utente dell'account %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:649
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:247
msgid "Create account password"
msgstr "Crea la password dell'account"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:651
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:249
#. translators: %s: asterisk mark
msgid "Create account password %s"
msgstr "Crea password account %s"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:660
#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:258
#. translators: %1$s: username, %2$s emailid
msgid " Welcome Back %1$s ( %2$s )"
msgstr "Bentornato %1$s ( %2$s )"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:915
msgid "Looks like you haven't added any items to cart yet — start shopping to fill it up!"
msgstr "Sembra che tu non abbia ancora aggiunto articoli al carrello — inizia a fare acquisti per riempirlo!"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:927
msgid "Your Cart is Currently Empty."
msgstr "Il tuo carrello è attualmente vuoto."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:929
msgid "Start Shopping"
msgstr "Inizia a fare acquisti"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:161
#. translators: %1$s: Link HTML start, %2$s Link HTML End
msgid "Already have an account? %1$1s Log in%2$2s"
msgstr "Hai già un account? %1$1s Accedi%2$2s"

#: modules/checkout/classes/layouts/class-cartflows-modern-checkout.php:177
#. translators: %s: asterisk mark
msgid "Email Address %s"
msgstr "Indirizzo email %s"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:69
msgid "Coupon code applied successfully."
msgstr "Codice coupon applicato con successo."

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:76
msgid "Have a coupon?"
msgstr "Hai un coupon?"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:95
#: modules/checkout/templates/checkout/order-review-table.php:17
#: modules/checkout/templates/checkout/order-review-table.php:43
#: modules/thankyou/templates/instant-thankyou-your-product.php:118
msgid "Subtotal"
msgstr "Subtotale"

#: modules/checkout/templates/checkout/instant-checkout-review-order.php:147
#: modules/checkout/templates/checkout/order-review-table.php:79
#: modules/thankyou/templates/instant-thankyou-your-product.php:148
msgid "Total"
msgstr "Totale"

#: modules/checkout/templates/checkout/order-review-table.php:16
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Product"
msgstr "Prodotto"

#: modules/checkout/templates/checkout/shipping-methods.php:53
#. Translators: $s shipping destination.
msgid "Shipping to %s."
msgstr "Spedizione a %s."

#: modules/checkout/templates/checkout/shipping-methods.php:54
#. Translators: $s shipping destination.
msgid "Change address"
msgstr "Cambia indirizzo"

#: modules/checkout/templates/checkout/shipping-methods.php:56
msgid "Shipping options will be updated during checkout."
msgstr "Le opzioni di spedizione verranno aggiornate durante il checkout."

#: modules/checkout/templates/wcf-template.php:51
msgid "Copyright &copy;"
msgstr "Copyright &copy;"

#: modules/checkout/templates/wcf-template.php:56
msgid "All Rights Reserved"
msgstr "Tutti i diritti riservati"

#: modules/elementor/class-cartflows-el-widgets-loader.php:177
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:588
msgid "Cartflows"
msgstr "Cartflows"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:267
#. translators: %s admin link
msgid ""
"This feature is available in the CartFlows higher plan. <a href=\"%s\" target=\"_blank\" rel=\"noopener\">Upgrade "
"Now!</a>."
msgstr ""
"Questa funzione è disponibile nel piano superiore di CartFlows. <a href=\"%s\" target=\"_blank\" "
"rel=\"noopener\">Aggiorna ora!</a>."

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:370
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:175
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color"
msgstr "Colore"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:585
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:354
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:343
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Normal"
msgstr "Normale"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:683
#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:417
#: modules/elementor/widgets/class-cartflows-el-optin-form.php:403
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover"
msgstr "Sospensione"

#: modules/elementor/widgets/class-cartflows-el-checkout-form.php:839
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Rounded Corners"
msgstr "Sezione Angoli Arrotondati"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:164
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title"
msgstr "Titolo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:166
msgid "BUY NOW"
msgstr "ACQUISTA ORA"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:176
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sub Title"
msgstr "Sottotitolo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:199
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title"
msgstr "Prima del titolo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:200
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title"
msgstr "Dopo il titolo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:201
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Before Title & Sub Title"
msgstr "Prima del Titolo e del Sottotitolo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:202
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "After Title & Sub Title"
msgstr "Dopo Titolo & Sottotitolo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:213
msgid "Icon Vertical Alignment"
msgstr "Allineamento verticale dell'icona"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:223
msgid "Middle"
msgstr "Mezzo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:318
msgid "Justify"
msgstr "Giustificare"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:424
msgid "Hover Text Color"
msgstr "Colore del testo al passaggio del mouse"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:437
msgid "Hover Background Color"
msgstr "Colore di sfondo al passaggio del mouse"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:467
msgid "Hover Animation"
msgstr "Animazione al passaggio del mouse"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:491
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:554
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:645
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:734
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Content"
msgstr "Contenuto"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:499
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Alignment"
msgstr "Allineamento del testo"

#: modules/elementor/widgets/class-cartflows-el-next-step-button.php:556
msgid "Title and Sub Title Spacing"
msgstr "Spaziatura del Titolo e del Sottotitolo"

#: modules/elementor/widgets/class-cartflows-el-optin-form.php:324
#: modules/optin/classes/class-cartflows-optin-meta-data.php:406
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button"
msgstr "Pulsante di invio"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:187
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:199
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:211
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:223
msgid "Show"
msgstr "Mostra"

#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:367
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Sections"
msgstr "Sezioni"

#: modules/email-report/class-cartflows-admin-report-emails.php:104
msgid "There"
msgstr "Lì"

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "You have successfully unsubscribed from our weekly emails list."
msgstr "Ti sei cancellato con successo dalla nostra lista di email settimanali."

#: modules/email-report/class-cartflows-admin-report-emails.php:142
msgid "Unsubscribed"
msgstr "Annullato l'abbonamento"

#: modules/email-report/class-cartflows-admin-report-emails.php:174
msgid "Here’s how your store performed last week!"
msgstr "Ecco come ha performato il tuo negozio la scorsa settimana!"

#: modules/email-report/templates/email-body.php:17
msgid "CartFlows Weekly Report"
msgstr "Rapporto settimanale di CartFlows"

#: modules/email-report/templates/email-cf-pro-block.php:26
msgid "CartFlows Pro can help you to increase conversion and maximize profits."
msgstr "CartFlows Pro può aiutarti ad aumentare le conversioni e massimizzare i profitti."

#: modules/email-report/templates/email-cf-pro-block.php:43
msgid ""
"Want to earn 30% more store revenue on autopilot? CartFlows order bumps and upsells help you do just that. Try "
"CartFlows Pro risk-free for 30 days!"
msgstr ""
"Vuoi guadagnare il 30% in più di entrate del negozio in automatico? Gli order bumps e gli upsell di CartFlows ti "
"aiutano a fare proprio questo. Prova CartFlows Pro senza rischi per 30 giorni!"

#: modules/email-report/templates/email-cf-pro-block.php:62
msgid "GET CARTFLOWS NOW"
msgstr "OTTIENI CARTFLOWS ORA"

#: modules/email-report/templates/email-content-section.php:27
#. translators: %s user name
msgid "Hey %s!"
msgstr "Ehi %s!"

#: modules/email-report/templates/email-content-section.php:42
#. translators: %1$s: store name, %2$s: total revenue.  %3$s: total revenue
msgid ""
"%1$s has earned a total %2$s in revenue last week by using CartFlows to power your store! And in the last month, it "
"earned %3$s"
msgstr ""
"%1$s ha guadagnato un totale di %2$s in entrate la scorsa settimana utilizzando CartFlows per potenziare il tuo "
"negozio! E nell'ultimo mese, ha guadagnato %3$s"

#: modules/email-report/templates/email-content-section.php:79
msgid "(Last 7 days)"
msgstr "(Ultimi 7 giorni)"

#: modules/email-report/templates/email-content-section.php:93
msgid "(Last 30 days)"
msgstr "(Ultimi 30 giorni)"

#: modules/email-report/templates/email-footer.php:63
#. translators: %1$s - link to a site;
msgid "This email was auto-generated and sent from %1$s."
msgstr "Questa email è stata generata automaticamente e inviata da %1$s."

#: modules/email-report/templates/email-footer.php:70
msgid "Unsubscribe"
msgstr "Annulla iscrizione"

#: modules/email-report/templates/email-header.php:27
msgid "Your weekly summary from CartFlows."
msgstr "Il tuo riepilogo settimanale da CartFlows."

#: modules/email-report/templates/email-other-product-block.php:26
msgid "Would you like to try our other products that help WooCommerce stores sell more?"
msgstr "Ti piacerebbe provare i nostri altri prodotti che aiutano i negozi WooCommerce a vendere di più?"

#: modules/email-report/templates/email-other-product-block.php:41
msgid "TRY OUR OTHER PRODUCTS"
msgstr "PROVA I NOSTRI ALTRI PRODOTTI"

#: modules/email-report/templates/email-stat-content.php:26
msgid "Full Overview"
msgstr "Panoramica completa"

#: modules/email-report/templates/email-stat-content.php:82
#: modules/email-report/templates/email-stat-content.php:220
msgid "Order Placed"
msgstr "Ordine effettuato"

#: modules/email-report/templates/email-stat-content.php:112
#: modules/email-report/templates/email-stat-content.php:247
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total Visits"
msgstr "Visite totali"

#: modules/email-report/templates/email-stat-content.php:142
#: modules/email-report/templates/email-stat-content.php:274
msgid "Order Bumps Revenue"
msgstr "Entrate da Incrementi d'Ordine"

#: modules/email-report/templates/email-stat-content.php:172
#: modules/email-report/templates/email-stat-content.php:304
msgid "Offers Revenue"
msgstr "Entrate dalle offerte"

#: modules/email-report/templates/email-stat-content.php:250
#: modules/email-report/templates/email-stat-content.php:282
#: modules/email-report/templates/email-stat-content.php:307
msgid "CartFlows Pro"
msgstr "CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Flow: "
msgstr "Flusso:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:73
msgid "Name: "
msgstr "Nome:"

#: modules/flow/classes/class-cartflows-flow-post-type.php:105
msgid "Search Flows"
msgstr "Cerca Flussi"

#: modules/flow/classes/class-cartflows-flow-post-type.php:106
msgid "All Flows"
msgstr "Tutti i flussi"

#: modules/flow/classes/class-cartflows-flow-post-type.php:107
msgid "Edit Flow"
msgstr "Modifica flusso"

#: modules/flow/classes/class-cartflows-flow-post-type.php:108
msgid "View Flow"
msgstr "Visualizza flusso"

#: modules/flow/classes/class-cartflows-flow-post-type.php:109
#: modules/flow/classes/class-cartflows-flow-post-type.php:111
#: modules/flow/classes/class-cartflows-step-post-type.php:176
#: modules/flow/classes/class-cartflows-step-post-type.php:178
#: admin-core/assets/build/settings-app.js:25
msgid "Add New"
msgstr "Aggiungi nuovo"

#: modules/flow/classes/class-cartflows-flow-post-type.php:110
msgid "Update Flow"
msgstr "Aggiorna flusso"

#: modules/flow/classes/class-cartflows-flow-post-type.php:112
msgid "New Flow Name"
msgstr "Nuovo Nome Flusso"

#: modules/flow/classes/class-cartflows-flow-post-type.php:194
#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Upgrade to CartFlows Pro"
msgstr "Aggiorna a CartFlows Pro"

#: modules/flow/classes/class-cartflows-flow-post-type.php:213
msgid "Slug"
msgstr "Slug"

#: modules/flow/classes/class-cartflows-flow-post-type.php:332
#: modules/flow/classes/class-cartflows-flow-post-type.php:338
#: modules/flow/classes/class-cartflows-step-post-type.php:410
#: modules/flow/classes/class-cartflows-step-post-type.php:416
#. translators: %s: singular custom post type name
msgid "%s updated."
msgstr "%s aggiornato."

#: modules/flow/classes/class-cartflows-flow-post-type.php:334
#: modules/flow/classes/class-cartflows-step-post-type.php:412
#. translators: %s: singular custom post type name
msgid "Custom %s updated."
msgstr "%s personalizzato aggiornato."

#: modules/flow/classes/class-cartflows-flow-post-type.php:336
#: modules/flow/classes/class-cartflows-step-post-type.php:414
#. translators: %s: singular custom post type name
msgid "Custom %s deleted."
msgstr "%s personalizzato eliminato."

#: modules/flow/classes/class-cartflows-flow-post-type.php:340
#: modules/flow/classes/class-cartflows-step-post-type.php:418
#. translators: %1$s: singular custom post type name ,%2$s: date and time of the revision
msgid "%1$s restored to revision from %2$s"
msgstr "%1$s ripristinato alla revisione del %2$s"

#: modules/flow/classes/class-cartflows-flow-post-type.php:342
#: modules/flow/classes/class-cartflows-step-post-type.php:420
#. translators: %s: singular custom post type name
msgid "%s published."
msgstr "%s pubblicato."

#: modules/flow/classes/class-cartflows-flow-post-type.php:344
#: modules/flow/classes/class-cartflows-step-post-type.php:422
#. translators: %s: singular custom post type name
msgid "%s saved."
msgstr "%s salvato."

#: modules/flow/classes/class-cartflows-flow-post-type.php:346
#: modules/flow/classes/class-cartflows-step-post-type.php:424
#. translators: %s: singular custom post type name
msgid "%s submitted."
msgstr "%s inviato."

#: modules/flow/classes/class-cartflows-flow-post-type.php:348
#: modules/flow/classes/class-cartflows-step-post-type.php:426
#. translators: %s: singular custom post type name
msgid "%s scheduled for."
msgstr "%s programmato per."

#: modules/flow/classes/class-cartflows-flow-post-type.php:350
#: modules/flow/classes/class-cartflows-step-post-type.php:428
#. translators: %s: singular custom post type name
msgid "%s draft updated."
msgstr "Bozza %s aggiornata."

#: modules/flow/classes/class-cartflows-step-meta-base.php:58
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:118
msgid "Design"
msgstr "Design"

#: modules/flow/classes/class-cartflows-step-meta-base.php:82
msgid "Custom script lets you add your own custom script on front end of this flow page."
msgstr ""
"Lo script personalizzato ti consente di aggiungere il tuo script personalizzato nella parte frontale di questa pagina "
"di flusso."

#: modules/flow/classes/class-cartflows-step-post-type.php:172
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Search Steps"
msgstr "Passaggi di ricerca"

#: modules/flow/classes/class-cartflows-step-post-type.php:173
msgid "All Steps"
msgstr "Tutti i passaggi"

#: modules/flow/classes/class-cartflows-step-post-type.php:174
msgid "Edit Step"
msgstr "Modifica passaggio"

#: modules/flow/classes/class-cartflows-step-post-type.php:175
msgid "View Step"
msgstr "Visualizza passaggio"

#: modules/flow/classes/class-cartflows-step-post-type.php:177
msgid "Update Step"
msgstr "Aggiorna passaggio"

#: modules/flow/classes/class-cartflows-step-post-type.php:179
msgid "New Step Name"
msgstr "Nuovo nome del passaggio"

#: modules/flow/classes/class-cartflows-step-post-type.php:220
msgid "Step Type"
msgstr "Tipo di passaggio"

#: modules/flow/classes/class-cartflows-step-post-type.php:230
msgid "Step Flow"
msgstr "Flusso dei passaggi"

#: modules/flow/classes/class-cartflows-step-post-type.php:255
msgid "Optin"
msgstr "Iscrizione"

#: modules/flow/classes/class-cartflows-step-post-type.php:276
msgid "Upsell"
msgstr "Vendita aggiuntiva"

#: modules/flow/classes/class-cartflows-step-post-type.php:283
msgid "Downsell"
msgstr "Offerta ridotta"

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:146
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:201
#: modules/gutenberg/classes/class-cartflows-init-blocks.php:311
#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:86
msgid "Permission denied."
msgstr "Permesso negato."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:225
msgid "No product is selected. Please select products from the checkout meta settings to continue."
msgstr "Nessun prodotto è selezionato. Si prega di selezionare i prodotti dalle impostazioni meta del checkout per continuare."

#: modules/gutenberg/classes/class-cartflows-init-blocks.php:328
#: modules/optin/classes/class-cartflows-optin-markup.php:321
msgid "No product is selected. Please select a Simple, Virtual and Free product from the meta settings."
msgstr ""
"Nessun prodotto è selezionato. Si prega di selezionare un prodotto Semplice, Virtuale e Gratuito dalle impostazioni "
"meta."

#: modules/landing/classes/class-cartflows-landing-meta-data.php:98
msgid "Next Step Link"
msgstr "Link al passo successivo"

#: modules/optin/classes/class-cartflows-optin-markup.php:261
msgid "Please place shortcode on Optin step-type only."
msgstr "Si prega di inserire il shortcode solo nel tipo di passaggio Optin."

#: modules/optin/classes/class-cartflows-optin-markup.php:338
msgid "Please update the selected product's price to zero (0)."
msgstr "Si prega di aggiornare il prezzo del prodotto selezionato a zero (0)."

#: modules/optin/classes/class-cartflows-optin-markup.php:347
#: modules/optin/classes/class-cartflows-optin-markup.php:351
msgid "Please select a Simple, Virtual and Free product."
msgstr "Si prega di selezionare un prodotto Semplice, Virtuale e Gratuito."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:76
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enable Custom Field Editor"
msgstr "Abilita l'editor di campi personalizzati"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Search for a Product"
msgstr "Cerca un prodotto"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:229
#. translators: %1$1s: link html start, %2$12: link html end
msgid "For more information about the CartFlows Optin step please %1$sClick here.%2$s"
msgstr "Per ulteriori informazioni sul passaggio Optin di CartFlows, si prega di %1$sfare clic qui.%2$s"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:262
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:101
msgid "Add this shortcode to your optin page"
msgstr "Aggiungi questo shortcode alla tua pagina di iscrizione"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:270
msgid "Global Settings"
msgstr "Impostazioni globali"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:328
#: modules/optin/classes/class-cartflows-optin-meta-data.php:432
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size"
msgstr "Dimensione"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:361
#: modules/optin/classes/class-cartflows-optin-meta-data.php:465
msgid "Top Bottom Spacing"
msgstr "Spaziatura superiore e inferiore"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:368
#: modules/optin/classes/class-cartflows-optin-meta-data.php:472
msgid "Left Right Spacing"
msgstr "Spaziatura Sinistra Destra"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:382
msgid "Text / Placeholder Color"
msgstr "Colore del testo / segnaposto"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:412
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Size"
msgstr "Dimensione del carattere"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:479
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Position"
msgstr "Posizione"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:584
msgid "Optin Settings"
msgstr "Impostazioni di adesione"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:598
msgid "Pass Fields as URL Parameters"
msgstr "Passa i campi come parametri URL"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:601
msgid "You can pass specific fields from the form to next step as URL query parameters."
msgstr "Puoi passare campi specifici dal modulo al passaggio successivo come parametri di query URL."

#: modules/optin/classes/class-cartflows-optin-meta-data.php:606
msgid "Enter form field"
msgstr "Inserisci il campo del modulo"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:609
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:492
msgid "Enter comma seprated field name. E.g. first_name, last_name"
msgstr "Inserisci il nome del campo separato da virgole. Ad esempio, first_name, last_name"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:610
msgid "Fields to pass, separated by commas"
msgstr "Campi da passare, separati da virgole"

#: modules/optin/classes/class-cartflows-optin-meta-data.php:612
#. translators: %s: link
msgid "You can pass field value as a URL parameter to the next step. %1$sLearn More >>%2$s"
msgstr "Puoi passare il valore del campo come parametro URL al passaggio successivo. %1$sScopri di più >>%2$s"

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:180
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:183
msgid "We can't seem to find an order for you."
msgstr "Sembra che non riusciamo a trovare un ordine per te."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:272
#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:662
msgid "No completed or processing order found to show the order details form demo."
msgstr "Nessun ordine completato o in elaborazione trovato per mostrare il modulo dei dettagli dell'ordine demo."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:280
msgid "Order not found. You cannot access this page directly."
msgstr "Ordine non trovato. Non puoi accedere direttamente a questa pagina."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:673
msgid "Order Details Not Found."
msgstr "Dettagli dell'ordine non trovati."

#: modules/thankyou/classes/class-cartflows-thankyou-markup.php:675
msgid "Return to Shopping"
msgstr "Torna a fare acquisti"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:212
msgid "Font Size (In px)"
msgstr "Dimensione del carattere (in px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:221
msgid "Advanced Options"
msgstr "Opzioni avanzate"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:227
msgid "Enable Advanced Options"
msgstr "Abilita opzioni avanzate"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:234
msgid "Container Width (In px)"
msgstr "Larghezza del contenitore (in px)"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:331
msgid "Background color of left side column for Instant Thank You Layout."
msgstr "Colore di sfondo della colonna sinistra per il layout di ringraziamento istantaneo."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:347
msgid "Background color of right side column for Instant Thank You Layout."
msgstr "Colore di sfondo della colonna sul lato destro per il layout di Ringraziamento Istantaneo."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:417
msgid "Enable Order Overview"
msgstr "Abilita panoramica ordini"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:424
msgid "Enable Order Details"
msgstr "Abilita Dettagli Ordine"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:431
msgid "Enable Billing Details"
msgstr "Abilita Dettagli di Fatturazione"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:438
msgid "Enable Shipping Details"
msgstr "Abilita dettagli di spedizione"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:481
msgid "Thank You Page Text"
msgstr "Testo della pagina di ringraziamento"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:489
msgid "Redirect After Purchase"
msgstr "Reindirizzamento dopo l'acquisto"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:497
msgid "Redirect Link"
msgstr "Reindirizza Link"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:500
msgid "https://"
msgstr "https://"

#: modules/thankyou/templates/instant-thankyou-order-details.php:41
#. Translators: First name.
msgid "Thank you, %s!"
msgstr "Grazie, %s!"

#: modules/thankyou/templates/instant-thankyou-order-details.php:58
msgid "Order Updates"
msgstr "Aggiornamenti sugli ordini"

#: modules/thankyou/templates/instant-thankyou-order-details.php:59
msgid "You will receive order and shipping updates via email."
msgstr "Riceverai aggiornamenti sull'ordine e sulla spedizione via email."

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
msgid "Address"
msgstr "Indirizzo"

#: modules/thankyou/templates/instant-thankyou-order-details.php:78
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Billing"
msgstr "Fatturazione"

#: modules/thankyou/templates/instant-thankyou-order-details.php:155
msgid "Continue Shopping"
msgstr "Continua a fare acquisti"

#: modules/thankyou/templates/instant-thankyou.php:37
msgid ""
"Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please "
"attempt your purchase again."
msgstr ""
"Purtroppo il tuo ordine non può essere elaborato poiché la banca/il commerciante di origine ha rifiutato la tua "
"transazione. Ti preghiamo di tentare nuovamente l'acquisto."

#: modules/thankyou/templates/instant-thankyou.php:41
msgid "Pay"
msgstr "Paga"

#: modules/thankyou/templates/instant-thankyou.php:43
msgid "My account"
msgstr "Il mio account"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:137
msgid "Select the Flow"
msgstr "Seleziona il flusso"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:147
msgid "Add to Cart text"
msgstr "Testo Aggiungi al Carrello"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:149
msgid "Add to cart"
msgstr "Aggiungi al carrello"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:154
#. translators: %1$s,%2$s HTML content
msgid ""
"If you want to start the flow from the product page, select the appropriate flow & button text field if required. Refer "
"%1$sthis article%2$s for more information."
msgstr ""
"Se desideri avviare il flusso dalla pagina del prodotto, seleziona il flusso appropriato e il campo di testo del "
"pulsante, se necessario. Consulta %1$squesto articolo%2$s per ulteriori informazioni."

#: wizard/ajax/wizard.php:207
msgid "Please enter your email ID."
msgstr "Per favore inserisci il tuo ID email."

#: wizard/ajax/wizard.php:262
msgid "Oops! Something went wrong. Please refresh the page and try again."
msgstr "Ops! Qualcosa è andato storto. Si prega di aggiornare la pagina e riprovare."

#: wizard/ajax/wizard.php:363
msgid "Please select any of the page builder to display the ready templates."
msgstr "Si prega di selezionare uno dei costruttori di pagine per visualizzare i modelli pronti."

#: wizard/ajax/wizard.php:502
msgid "No flow ID found. Please select atleast one flow to import."
msgstr "Nessun ID di flusso trovato. Seleziona almeno un flusso da importare."

#: admin-core/ajax/importer.php:893
#: wizard/ajax/wizard.php:516
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$sarticle%3$s."
msgstr ""
"Errore di timeout della richiesta. Si prega di verificare se il firewall o qualsiasi plugin di sicurezza sta bloccando "
"le richieste HTTP/HTTPS in uscita verso templates.cartflows.com o meno. %1$sPer risolvere questo problema, si prega di "
"controllare questo %2$sarticolo%3$s."

#: wizard/ajax/wizard.php:539
#. translators: %1$s: link html start, %2$s: link html end
msgid "To import this template, CartFlows Pro Required! %1$sUpgrade to CartFlows Pro%2$s"
msgstr "Per importare questo modello, è necessario CartFlows Pro! %1$sAggiorna a CartFlows Pro%2$s"

#: wizard/ajax/wizard.php:540
#: wizard/ajax/wizard.php:542
msgid "CartFlows Pro Required"
msgstr "È richiesto CartFlows Pro"

#: wizard/ajax/wizard.php:546
msgid "Invalid License Key"
msgstr "Chiave di licenza non valida"

#: wizard/ajax/wizard.php:548
#. translators: %1$s: link html start, %2$s: link html end
msgid "No valid license key found! %1$sActivate license%2$s"
msgstr "Nessuna chiave di licenza valida trovata! %1$sAttiva licenza%2$s"

#: wizard/inc/wizard-core.php:174
msgid "Thanks for installing and using CartFlows!"
msgstr "Grazie per aver installato e utilizzato CartFlows!"

#: wizard/inc/wizard-core.php:175
msgid "It is easy to use the CartFlows. Please use the setup wizard to quick start setup."
msgstr ""
"È facile usare CartFlows. Si prega di utilizzare la procedura guidata di configurazione per avviare rapidamente "
"l'installazione."

#: wizard/inc/wizard-core.php:177
msgid "Start Wizard"
msgstr "Avvia procedura guidata"

#: wizard/inc/wizard-core.php:178
msgid "Skip Setup"
msgstr "Salta configurazione"

#: wizard/inc/wizard-core.php:394
msgid "Oops!! Unexpected error occoured"
msgstr "Ops!! Si è verificato un errore imprevisto"

#: wizard/inc/wizard-core.php:395
msgid "Import template API call failed. Please reload the page and try again!"
msgstr "La chiamata API del modello di importazione non è riuscita. Ricarica la pagina e riprova!"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Settings Saved"
msgstr "Impostazioni salvate"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:53
msgid "Saving…"
msgstr "Salvataggio in corso…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:42
msgid "Save Settings"
msgstr "Salva impostazioni"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Regular Price of the product"
msgstr "Prezzo normale del prodotto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Price after discount."
msgstr "Prezzo dopo lo sconto."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Product Name"
msgstr "Nome del prodotto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{product_name}} and {{quantity}} to dynamically fetch respective product details."
msgstr "Usa {{product_name}} e {{quantity}} per recuperare dinamicamente i dettagli del rispettivo prodotto."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Subtext"
msgstr "Sottotesto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Use {{quantity}}, {{discount_value}}, {{discount_percent}} to dynamically fetch respective product details."
msgstr ""
"Usa {{quantity}}, {{discount_value}}, {{discount_percent}} per recuperare dinamicamente i rispettivi dettagli del "
"prodotto."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Enable Highlight"
msgstr "Abilita evidenziazione"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Highlight Text"
msgstr "Testo evidenziato"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Create New Product"
msgstr "Crea nuovo prodotto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add"
msgstr "Aggiungi"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Once you have selected products, they will be displayed here."
msgstr "Una volta selezionati i prodotti, verranno visualizzati qui."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Items"
msgstr "Articoli"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Quantity"
msgstr "Quantità"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
msgid "Discount"
msgstr "Sconto"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Adding…"
msgstr "Aggiungendo…"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Please search and select at-lease one product to add."
msgstr "Si prega di cercare e selezionare almeno un prodotto da aggiungere."

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
#: wizard/assets/build/wizard-app.js:1
msgid "Reset"
msgstr "Reimposta"

#: admin-core/assets/build/editor-app.js:1
msgid "Image Preview"
msgstr "Anteprima immagine"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "Upload a file"
msgstr "Carica un file"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:24
msgid "or drag and drop"
msgstr "o trascina e rilascia"

#: admin-core/assets/build/editor-app.js:1
msgid "PNG, JPG, GIF up to 10MB"
msgstr "PNG, JPG, GIF fino a 10MB"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/settings-app.js:1
msgid "Select Dates"
msgstr "Seleziona date"

#: admin-core/assets/build/editor-app.js:4
#: admin-core/assets/build/settings-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Pro %2$s to use %3$s feature."
msgstr "Si prega di eseguire l'upgrade a %1$s CartFlows Pro %2$s per utilizzare la funzione %3$s."

#: admin-core/assets/build/editor-app.js:7
#: admin-core/assets/build/settings-app.js:7
#. translators: %1$s: anchor tag start, %2$s: anchor tag end, %3$s: feature name.
msgid "Please upgrade to the %1$s CartFlows Higher Plan %2$s to use %3$s feature."
msgstr "Si prega di eseguire l'upgrade al %1$s piano superiore di CartFlows %2$s per utilizzare la funzione %3$s."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
#. translators: %s is replaced with feature name
msgid "Please upgrade to the CartFlows Higher Plan to use the %s feature."
msgstr "Si prega di eseguire l'upgrade al piano superiore di CartFlows per utilizzare la funzione %s."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Role"
msgstr "Ruolo"

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Access"
msgstr "Accesso"

#: admin-core/assets/build/editor-app.js:11
#: admin-core/assets/build/settings-app.js:11
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the user role management please %1$sClick here.%2$s"
msgstr "Per ulteriori informazioni sulla gestione dei ruoli utente, si prega di %1$sfare clic qui.%2$s"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
#. translators: %1$s is the selected product of CartFlows, %2$s is the selected version of CartFlows.
msgid "Are you sure you want to rollback to %1$s v%2$s?"
msgstr "Sei sicuro di voler tornare a %1$s v%2$s?"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid "Rollback"
msgstr "Ripristina"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:14
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Cancel"
msgstr "Annulla"

#: admin-core/assets/build/editor-app.js:14
#: admin-core/assets/build/settings-app.js:14
msgid ""
"Experiencing an issue with the current version of CartFlows? Roll back to a previous version to help troubleshoot the "
"problem."
msgstr ""
"Stai riscontrando un problema con la versione attuale di CartFlows? Torna a una versione precedente per aiutare a "
"risolvere il problema."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Now"
msgstr "Rigenera ora"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:19
msgid "Reset Permalinks Settings"
msgstr "Reimposta le impostazioni dei permalink"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate License"
msgstr "Attiva licenza"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Deactivate License"
msgstr "Disattiva licenza"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Please enter a valid license key!"
msgstr "Per favore, inserisci un codice di licenza valido!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
#: wizard/assets/build/wizard-app.js:4
msgid "Processing"
msgstr "Elaborazione"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unknown error occurred while activating the license."
msgstr "Si è verificato un errore sconosciuto durante l'attivazione della licenza."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Facebook Pixel"
msgstr "Pixel di Facebook"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Analytics Pixel"
msgstr "Pixel di Google Analytics"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Ads Pixel"
msgstr "Pixel di Google Ads"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Tiktok Pixel"
msgstr "Pixel di Tiktok"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Pinterest Tag"
msgstr "Tag di Pinterest"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Snapchat Pixel"
msgstr "Snapchat Pixel"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Google Auto Address"
msgstr "Indirizzo automatico di Google"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerate Inline CSS"
msgstr "Rigenera CSS inline"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid " Regenerating…."
msgstr "Rigenerazione in corso…."

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Regenerated"
msgstr "Rigenerato"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
#. translators: %1$1s: link html start, %2$2s: link html end
msgid ""
"If you are using the CartFlows Shortcode and using the Design Settings, then this option will regenerate the steps "
"inline CSS. To learn more, %1$1s Click here %2$2s."
msgstr ""
"Se stai utilizzando lo Shortcode di CartFlows e le Impostazioni di Design, questa opzione rigenererà i passaggi del CSS "
"inline. Per saperne di più, %1$1s fai clic qui %2$2s."

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Updating"
msgstr "Aggiornamento"

#: admin-core/assets/build/editor-app.js:19
#: admin-core/assets/build/settings-app.js:19
msgid "Permalinks reset successfully"
msgstr "Permalink reimpostati con successo"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#. translators: %1$s: link html start, %2$s: link html end
msgid "For more information about the CartFlows Permalink settings please %1$sClick here.%2$s"
msgstr "Per ulteriori informazioni sulle impostazioni del Permalink di CartFlows, si prega di %1$sfare clic qui.%2$s"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Oops! You don't have access to this page."
msgstr "Ops! Non hai accesso a questa pagina."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You don't have permission to access this page. Please reach out to your admin for help."
msgstr "Non hai il permesso di accedere a questa pagina. Contatta il tuo amministratore per assistenza."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Back to Dashboard"
msgstr "Torna alla Dashboard"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Integrations"
msgstr "Integrazioni"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "User Role Manager"
msgstr "Gestore dei Ruoli Utente"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Permalink"
msgstr "Permalink"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Version Control"
msgstr "Controllo delle versioni"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Order"
msgstr "Ordine"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "License"
msgstr "Licenza"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Sync Knowledge Base"
msgstr "Sincronizza la base di conoscenza"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Knowledge Base"
msgstr "Base di conoscenza"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Search knowledge base"
msgstr "Cerca nella base di conoscenza"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "No Docs Founds"
msgstr "Nessun documento trovato"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Please try syncing the docs library"
msgstr "Per favore, prova a sincronizzare la libreria di documenti"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Syncing…"
msgstr "Sincronizzazione in corso…"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Synced. Reloading.."
msgstr "Sincronizzato. Ricaricamento in corso.."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Need Help?"
msgstr "Hai bisogno di aiuto?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "We aim to answer all priority support requests within 2-3 hours."
msgstr "Miriamo a rispondere a tutte le richieste di supporto prioritario entro 2-3 ore."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Get Support"
msgstr "Ottieni supporto"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "All Documentation"
msgstr "Tutta la documentazione"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse documentation, reference material, and tutorials for CartFlows."
msgstr "Esplora la documentazione, il materiale di riferimento e i tutorial per CartFlows."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "View documentation"
msgstr "Visualizza documentazione"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Videos"
msgstr "Video"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Browse tutorial videos on our YouTube channel."
msgstr "Sfoglia i video tutorial sul nostro canale YouTube."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Youtube Channel"
msgstr "Canale Youtube"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Support"
msgstr "Supporto"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "What's New?"
msgstr "Novità?"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Unlicensed"
msgstr "Senza licenza"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Licensed"
msgstr "Concesso in licenza"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Open Global Settings"
msgstr "Apri Impostazioni Globali"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Tutorial Videos"
msgstr "Video tutorial"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:35
msgid "More Options"
msgstr "Altre opzioni"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:35
msgid "Control"
msgstr "Controllo"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
#. translators: %d is replaced with the count
msgid "Variation-%d"
msgstr "Variazione-%d"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "No Product Assigned"
msgstr "Nessun prodotto assegnato"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Store Checkout - Remove selected checkout product"
msgstr "Cassa del negozio - Rimuovi il prodotto selezionato dal carrello"

#: admin-core/assets/build/editor-app.js:1
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:1
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:54
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "PRO"
msgstr "PRO"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Accepted"
msgstr "Offerta accettata"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Offer Rejected"
msgstr "Offerta rifiutata"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Invalid Position"
msgstr "Posizione non valida"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/settings-app.js:36
msgid "Views"
msgstr "Visualizzazioni"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Conversions"
msgstr "Conversioni"

#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:53
msgid "Revenue"
msgstr "Entrate"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/editor-app.js:24
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:36
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:80
msgid "Upgrade to Pro"
msgstr "Passa a Pro"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:39
#: admin-core/assets/build/settings-app.js:42
#. translators: %s: step slug
msgid "%s Step"
msgstr "Passo %s"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid "Step Editing is Disabled"
msgstr "Modifica del passaggio disabilitata"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Open Settings"
msgstr "Apri Impostazioni"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Duplicate Step"
msgstr "Duplica passaggio"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to duplicate this step? Are you sure?"
msgstr "Vuoi duplicare questo passaggio? Sei sicuro?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Delete Step"
msgstr "Elimina passaggio"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to delete this step? Are you sure?"
msgstr "Vuoi eliminare questo passaggio? Sei sicuro?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Declare Winner"
msgstr "Dichiara vincitore"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to declare this step as winner? Are you sure?"
msgstr "Vuoi dichiarare questo passaggio come vincitore? Sei sicuro?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Archive Step"
msgstr "Passaggio di archiviazione"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to archive this step? Are you sure?"
msgstr "Vuoi archiviare questo passaggio? Sei sicuro?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Saving.."
msgstr "Salvataggio in corso.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:53
msgid "Saved"
msgstr "Salvato"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading.."
msgstr "Ricaricamento in corso.."

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Testing"
msgstr "Test di suddivisione"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stop Split Test"
msgstr "Interrompi test diviso"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Start Split Test"
msgstr "Inizia il test diviso"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test"
msgstr "Test diviso"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Stopping…"
msgstr "Interrompendo…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Starting…"
msgstr "Inizio…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Reloading…"
msgstr "Ricaricamento…"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Split Test Settings"
msgstr "Impostazioni del test diviso"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Restore Archived Variation"
msgstr "Ripristina variazione archiviata"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/settings-app.js:42
msgid "Do you want to restore this archived variation? Are you sure?"
msgstr "Vuoi ripristinare questa variazione archiviata? Sei sicuro?"

#: admin-core/assets/build/editor-app.js:30
#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:42
#: admin-core/assets/build/settings-app.js:44
msgid "Trash Archived Variation"
msgstr "Variazione Archiviata del Cestino"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will trash this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Questa azione eliminerà definitivamente questa variazione archiviata e i suoi dati analitici. %1$s Vuoi eliminare "
"questa variazione archiviata?"

#: admin-core/assets/build/editor-app.js:31
#: admin-core/assets/build/settings-app.js:43
msgid "Hide Archived Variation"
msgstr "Nascondi variazione archiviata"

#: admin-core/assets/build/editor-app.js:32
#: admin-core/assets/build/settings-app.js:44
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will hide this archived variation from the list of steps, but its analytics will be visible. %1$s Do you "
"want to hide this archived variation?"
msgstr ""
"Questa azione nasconderà questa variazione archiviata dall'elenco dei passaggi, ma le sue analisi saranno visibili. "
"%1$s Vuoi nascondere questa variazione archiviata?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
#. translators: %1$s is replaced with the HTML tag
msgid ""
"This action will delete this archived variation and its analytics data permanently. %1$s Do you want to delete this "
"archived variation?"
msgstr ""
"Questa azione eliminerà definitivamente questa variazione archiviata e i suoi dati analitici. %1$s Vuoi eliminare "
"questa variazione archiviata?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Deleted On: "
msgstr "Eliminato il:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived On: "
msgstr "Archiviato il:"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/settings-app.js:45
msgid "Archived Steps"
msgstr "Passaggi archiviati"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Step Name"
msgstr "Modifica il nome del passaggio"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:53
msgid "Save"
msgstr "Salva"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Update Template"
msgstr "Aggiorna modello"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Changing the template will permanently delete the current design in this step. Would you still like to proceed?"
msgstr "Modificare il modello eliminerà definitivamente il design attuale in questo passaggio. Vuoi comunque procedere?"

#: admin-core/assets/build/editor-app.js:33
#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:45
#: admin-core/assets/build/settings-app.js:48
msgid "Change Template"
msgstr "Cambia modello"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If you are using shortcodes, enable this design settings to apply styles."
msgstr "Se stai utilizzando i codici brevi, abilita queste impostazioni di design per applicare gli stili."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Design Settings"
msgstr "Abilita impostazioni di design"

#: admin-core/assets/build/editor-app.js:15
#: admin-core/assets/build/settings-app.js:15
#. translators: %s is replaced with plugin name
msgid "Activate %s"
msgstr "Attiva %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
#. translators: %s is replaced with plugin name
msgid "Activating %s"
msgstr "Attivazione di %s"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Activated!"
msgstr "Attivato con successo!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:23
msgid "Failed! Activation!"
msgstr "Fallito! Attivazione!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Upgrade to Cartflows Pro"
msgstr "Aggiorna a Cartflows Pro"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:16
#: admin-core/assets/build/settings-app.js:50
msgid "Activate the License"
msgstr "Attiva la licenza"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Step Type: "
msgstr "Tipo di passaggio:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "License is required!"
msgstr "È necessaria la licenza!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Activate the license to modify this offer step's settings"
msgstr "Attiva la licenza per modificare le impostazioni di questo passaggio dell'offerta"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product Selected"
msgstr "Nessun prodotto selezionato"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you select the product, they will be displayed here."
msgstr "Una volta selezionato il prodotto, verranno visualizzati qui."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Shipping Rate"
msgstr "Tariffa di spedizione"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Once you have add product, it will be displayed here."
msgstr "Una volta aggiunto il prodotto, verrà visualizzato qui."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Coupon will apply on checkout page"
msgstr "Il coupon verrà applicato nella pagina di pagamento"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "AND"
msgstr "E"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the conditional order bump feature."
msgstr "Si prega di aggiornare CartFlows Pro all'ultima versione per utilizzare la funzione di ordine condizionale."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable conditional order bump "
msgstr "Abilita l'ordine condizionale aggiuntivo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "By enabling this option, you can create the conditions to display the order bump."
msgstr "Abilitando questa opzione, puoi creare le condizioni per visualizzare l'ordine aggiuntivo."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show this order bump if following conditions are true"
msgstr "Mostra questo ordine aggiuntivo se le seguenti condizioni sono vere"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Condition"
msgstr "Aggiungi condizione"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:33
#: admin-core/assets/build/settings-app.js:48
msgid "OR"
msgstr "O"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Conditions Group"
msgstr "Aggiungi gruppo di condizioni"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Bump Product Image"
msgstr "Immagine del prodotto aggiuntivo all'ordine"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Styles"
msgstr "Stili"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Conditions"
msgstr "Condizioni"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Save Changes"
msgstr "Salva le modifiche"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "You have made changes. Do you want to save the changes?"
msgstr "Hai apportato delle modifiche. Vuoi salvare le modifiche?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "No product"
msgstr "Nessun prodotto"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Duplicate Order Bump"
msgstr "Duplicare l'ordine aggiuntivo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Edit Order Bump"
msgstr "Modifica l'Offerta Aggiuntiva"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Trash Order Bump"
msgstr "Elimina l'ordine aggiuntivo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Do you really want to trash this order bump permanently?"
msgstr "Vuoi davvero eliminare definitivamente questo ordine aggiuntivo?"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Delete Order Bump"
msgstr "Elimina l'ordine aggiuntivo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:48
msgid "Status"
msgstr "Stato"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Actions"
msgstr "Azioni"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Create an order bump."
msgstr "Crea un'offerta aggiuntiva."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enter order bump name"
msgstr "Inserisci il nome del bump dell'ordine"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please enter the order bump title"
msgstr "Per favore inserisci il titolo del supplemento all'ordine"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Preview"
msgstr "Anteprima"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "View in Full Screen"
msgstr "Visualizza a schermo intero"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Exit Full Screen"
msgstr "Esci dalla modalità a schermo intero"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Submitted"
msgstr "Ordine inviato"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Special Offer"
msgstr "Offerta Speciale"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Order Receipt"
msgstr "Ricevuta d'ordine"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Checkout Offer"
msgstr "Abilita Offerta di Checkout"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Dynamic Conditions"
msgstr "Condizioni dinamiche"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Please Update the CartFlows Pro to the latest version to use the dynamic offers feature."
msgstr "Si prega di aggiornare CartFlows Pro all'ultima versione per utilizzare la funzione di offerte dinamiche."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable Dynamic Offers"
msgstr "Abilita Offerte Dinamiche"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Redirect to: "
msgstr "Reindirizza a:"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for step…"
msgstr "Cerca il passaggio…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If the following conditions are true"
msgstr "Se le seguenti condizioni sono vere"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Dynamic Offer"
msgstr "Aggiungi Offerta Dinamica"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Search for default step…"
msgstr "Cerca il passaggio predefinito…"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "If all of the above conditions failed."
msgstr "Se tutte le condizioni sopra menzionate falliscono."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Label"
msgstr "Etichetta"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "ID"
msgstr "ID"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Enable"
msgstr "Abilita"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Add New Field"
msgstr "Aggiungi nuovo campo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:48
#: admin-core/assets/build/settings-app.js:50
msgid "Date & Time"
msgstr "Data e Ora"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add Custom Field"
msgstr "Aggiungi campo personalizzato"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "TextArea"
msgstr "Area di testo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Number"
msgstr "Numero"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Checkbox"
msgstr "Casella di controllo"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Radio"
msgstr "Radio"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Select"
msgstr "Seleziona"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Hidden"
msgstr "Nascosto"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Add To"
msgstr "Aggiungi a"

#: admin-core/assets/build/editor-app.js:37
#: admin-core/assets/build/settings-app.js:49
#. translators: %$s is replaced with the HTML tag
msgid "Label %1$s*%2$s"
msgstr "Etichetta %1$s*%2$s"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#. translators: %$s is replaced with the HTML tag
msgid ""
"Field value will store in this meta key. Add field id without prefix like \"billing_\" or \"shipping_\". %s Use \"_\" "
"instead of spaces."
msgstr ""
"Il valore del campo verrà memorizzato in questa chiave meta. Aggiungi l'ID del campo senza prefisso come \"billing_\" o "
"\"shipping_\". %s Usa \"_\" invece degli spazi."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min Value"
msgstr "Valore minimo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max Value"
msgstr "Valore massimo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Enter your options separated by (|)."
msgstr "Inserisci le tue opzioni separate da (|)."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "UnChecked"
msgstr "Non selezionato"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Input Type"
msgstr "Tipo di input del campo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:50
msgid "Date"
msgstr "Data"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Time"
msgstr "Tempo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Min "
msgstr "Min"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Max "
msgstr "Max"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Show in Email"
msgstr "Mostra nell'email"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Billing Fields"
msgstr "Campi di fatturazione"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Shipping Fields"
msgstr "Campi di spedizione"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Delete Field"
msgstr "Elimina campo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Are you really want to delete field?"
msgstr "Vuoi davvero eliminare il campo?"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Field Editor"
msgstr "Editor di campo"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Label is required field"
msgstr "Il campo etichetta è obbligatorio"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "WooCommerce is Required!"
msgstr "WooCommerce è richiesto!"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
#. translators: %s: step type
msgid "To modify the %s step options, please install and activate the WooCommerce plugin."
msgstr "Per modificare le opzioni del passaggio %s, installa e attiva il plugin WooCommerce."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activating…"
msgstr "Attivazione in corso…"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:53
#: admin-core/assets/build/settings-app.js:80
msgid "Activated"
msgstr "Attivato"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Funnel Settings"
msgstr "Impostazioni del funnel"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Save Setting"
msgstr "Salva impostazione"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Disable Store Checkout"
msgstr "Disabilita il checkout del negozio"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Enable Store Checkout"
msgstr "Abilita il checkout del negozio"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Edit Title"
msgstr "Modifica titolo"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Publish or Draft the Funnel"
msgstr "Pubblica o Bozza l'imbuto"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Publish"
msgstr "Pubblica"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to publish this funnel?"
msgstr "Vuoi davvero pubblicare questo funnel?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Draft Funnel"
msgstr "Bozza di Funnel"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Do you really want to draft this funnel?"
msgstr "Vuoi davvero creare questo funnel?"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Unique Visits"
msgstr "Visite uniche"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Conversion Rate"
msgstr "Tasso di conversione"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Total number of orders."
msgstr "Numero totale di ordini."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all orders."
msgstr "Totale complessivo di tutti gli ordini."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Average total of every order."
msgstr "Totale medio di ogni ordine."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Grand total of all order bumps."
msgstr "Totale complessivo di tutti gli aumenti d'ordine."

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "Export Flow"
msgstr "Flusso di esportazione"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:53
msgid "View Funnel"
msgstr "Visualizza imbuto"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Open Funnel Settings"
msgstr "Apri Impostazioni del Funnel"

#: admin-core/assets/build/editor-app.js:41
#: admin-core/assets/build/settings-app.js:53
msgid "Delete Store Checkout"
msgstr "Elimina il checkout del negozio"

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
#. translators: %s new line break
msgid "Do you really want to delete store checkout?%1$1sNOTE: This action cannot be reversed."
msgstr "Vuoi davvero eliminare il checkout del negozio?%1$1sNOTA: Questa azione non può essere annullata."

#: admin-core/assets/build/editor-app.js:42
#: admin-core/assets/build/settings-app.js:54
msgid "archived_date"
msgstr "archived_date"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
#. translators: %1$s: html tag, %2$s: html tag
msgid "%1$sNote:%2$s The orders which are placed by the admins are not considered while calculating the analytics."
msgstr "%1$sNota:%2$s Gli ordini effettuati dagli amministratori non vengono considerati nel calcolo delle analisi."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Reset Analytics"
msgstr "Reimposta analisi"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Are you really want to reset funnel analytics?"
msgstr "Vuoi davvero reimpostare le analisi del funnel?"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Resetting"
msgstr "Reimpostazione"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Automation for"
msgstr "Automazione per"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Create a distraction free, high-converting checkout experience without needing a page builder."
msgstr "Crea un'esperienza di pagamento senza distrazioni e ad alta conversione senza bisogno di un costruttore di pagine."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Funnel Steps"
msgstr "Fasi del funnel"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add New Step"
msgstr "Aggiungi nuovo passaggio"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "No Steps Added."
msgstr "Nessun passaggio aggiunto."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Seems like there are no steps created or added in this flow"
msgstr "Sembra che non ci siano passaggi creati o aggiunti in questo flusso"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Add new step"
msgstr "Aggiungi nuovo passaggio"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Select Step Type"
msgstr "Seleziona il tipo di passaggio"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Activating WooCommerce.."
msgstr "Attivazione di WooCommerce.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installing WooCommerce.."
msgstr "Installazione di WooCommerce.."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Successfully Installed!"
msgstr "Installato con successo!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:23
msgid "Installation Failed!"
msgstr "Installazione fallita!"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Step"
msgstr "Passaggio di importazione"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
msgid "Add multiple steps to your flows today with an upgraded CartFlows plan."
msgstr "Aggiungi più passaggi ai tuoi flussi oggi con un piano CartFlows aggiornato."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
msgid "Get CartFlows Higher Plan"
msgstr "Ottieni il piano superiore di CartFlows"

#: admin-core/assets/build/editor-app.js:44
#: admin-core/assets/build/editor-app.js:45
#: admin-core/assets/build/editor-app.js:54
#: admin-core/assets/build/editor-app.js:55
#: admin-core/assets/build/editor-app.js:59
#: admin-core/assets/build/editor-app.js:63
#: admin-core/assets/build/editor-app.js:64
#: admin-core/assets/build/settings-app.js:56
#: admin-core/assets/build/settings-app.js:57
#: admin-core/assets/build/settings-app.js:66
#: admin-core/assets/build/settings-app.js:67
#: admin-core/assets/build/settings-app.js:70
#: admin-core/assets/build/settings-app.js:74
#: admin-core/assets/build/settings-app.js:75
#. translators: %s is replaced with plugin name
msgid "Add multiple steps to your flows by activating %s."
msgstr "Aggiungi più passaggi ai tuoi flussi attivando %s."

#: admin-core/assets/build/editor-app.js:46
#: admin-core/assets/build/editor-app.js:47
#: admin-core/assets/build/editor-app.js:56
#: admin-core/assets/build/editor-app.js:60
#: admin-core/assets/build/editor-app.js:65
#: admin-core/assets/build/settings-app.js:58
#: admin-core/assets/build/settings-app.js:59
#: admin-core/assets/build/settings-app.js:68
#: admin-core/assets/build/settings-app.js:71
#: admin-core/assets/build/settings-app.js:76
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating %2$s."
msgstr "Aggiungi il passaggio %1$s ai tuoi flussi attivando %2$s."

#: admin-core/assets/build/editor-app.js:48
#: admin-core/assets/build/editor-app.js:49
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:60
#: admin-core/assets/build/settings-app.js:61
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click %s to your flows when you upgrade to our CartFlows Higher plan today."
msgstr ""
"Aggiungi %s a clic singolo per aumentare illimitatamente il reddito ai tuoi flussi quando esegui l'upgrade al nostro "
"piano CartFlows Higher oggi stesso."

#: admin-core/assets/build/editor-app.js:50
#: admin-core/assets/build/settings-app.js:62
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flow by activating CartFlows license."
msgstr "Aggiungi il passaggio %1$s al tuo flusso attivando la licenza CartFlows."

#: admin-core/assets/build/editor-app.js:51
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:63
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s."
msgstr "Accedi a tutti i nostri modelli pro attivando %s."

#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Access all of our pro templates when you upgrade your plan to CartFlows Pro today."
msgstr "Accedi a tutti i nostri modelli pro quando aggiorni il tuo piano a CartFlows Pro oggi."

#: admin-core/inc/admin-menu.php:318
#: admin-core/inc/admin-menu.php:319
#: classes/class-cartflows-admin.php:250
#: admin-core/assets/build/editor-app.js:52
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:64
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:78
#: wizard/assets/build/wizard-app.js:5
msgid "Get CartFlows Pro"
msgstr "Ottieni CartFlows Pro"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
#. translators: %1$s, %2$s are variables
msgid "Add %1$s step to your flows by activating license."
msgstr "Aggiungi il passaggio %1$s ai tuoi flussi attivando la licenza."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:72
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:78
msgid "You need WooCommerce plugin installed and activated to import this step."
msgstr "È necessario che il plugin WooCommerce sia installato e attivato per importare questo passaggio."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Imported! Redirecting…"
msgstr "Importato! Reindirizzamento…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid " Please sync the library and try importing the template again."
msgstr "Per favore, sincronizza la libreria e prova a importare di nuovo il modello."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:72
msgid "Import Failed! Try again."
msgstr "Importazione fallita! Riprova."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:73
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create Step"
msgstr "Crea passaggio"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Creating Step.."
msgstr "Creazione del passaggio.."

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/editor-app.js:62
#: admin-core/assets/build/settings-app.js:65
#: admin-core/assets/build/settings-app.js:73
msgid "Step Created! Redirecting…"
msgstr "Passo creato! Reindirizzamento…"

#: admin-core/assets/build/editor-app.js:53
#: admin-core/assets/build/settings-app.js:65
msgid "Failed to Create Step!"
msgstr "Creazione del passaggio non riuscita!"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Activate license for adding more steps and other features."
msgstr "Attiva la licenza per aggiungere più passaggi e altre funzionalità."

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:55
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Close the window"
msgstr "Chiudi la finestra"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Name Your Step"
msgstr "Nomina il tuo passo"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Step Name"
msgstr "Nome del passaggio"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
msgid "Enter Step Name"
msgstr "Inserisci il nome del passaggio"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Learn How"
msgstr "Scopri come"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:77
#: admin-core/assets/build/settings-app.js:79
msgid "Create from Scratch"
msgstr "Crea da zero"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Step thumbnail image"
msgstr "Immagine in miniatura del passaggio"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Import"
msgstr "Importa"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
#: wizard/assets/build/wizard-app.js:1
msgid "Back"
msgstr "Indietro"

#: admin-core/assets/build/editor-app.js:57
#: admin-core/assets/build/settings-app.js:34
msgid "Sync Library"
msgstr "Sincronizza libreria"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
#. translators: %d is replaced with the condition number
msgid "Importing page %d"
msgstr "Importazione della pagina %d"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Sync Complete"
msgstr "Sincronizzazione completata"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Syncing Library…"
msgstr "Sincronizzazione della libreria…"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:35
msgid "Library Synced"
msgstr "Libreria sincronizzata"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:69
#: admin-core/assets/build/settings-app.js:79
msgid "Steps Library"
msgstr "Biblioteca dei Passi"

#: admin-core/assets/build/editor-app.js:58
#: admin-core/assets/build/settings-app.js:69
msgid "Get CartFlows Higher plan"
msgstr "Ottieni il piano superiore di CartFlows"

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Activate license for adding more flows and other features."
msgstr "Attiva la licenza per aggiungere più flussi e altre funzionalità."

#: admin-core/assets/build/editor-app.js:61
#: admin-core/assets/build/settings-app.js:72
msgid "Importing Step.."
msgstr "Importazione del passaggio.."

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Close"
msgstr "Chiudi"

#: admin-core/assets/build/editor-app.js:66
#: admin-core/assets/build/settings-app.js:34
msgid "Error"
msgstr "Errore"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
#. translators: %s is replaced with plugin name
msgid "Add unlimited income boosting one-click upsells to your flows by activating %s"
msgstr "Aggiungi upsell a un clic per aumentare il reddito illimitato ai tuoi flussi attivando %s"

#: admin-core/assets/build/editor-app.js:67
#: admin-core/assets/build/settings-app.js:78
msgid "Add unlimited income boosting one-click upsells to your flows when you upgrade to our CartFlows Plus or Pro plan today."
msgstr ""
"Aggiungi upsell con un solo clic per aumentare il reddito illimitato ai tuoi flussi quando esegui l'upgrade al nostro "
"piano CartFlows Plus o Pro oggi stesso."

#: admin-core/assets/build/editor-app.js:68
#: admin-core/assets/build/settings-app.js:79
#. translators: %s is replaced with plugin name
msgid "Access all of our pro templates by activating %s"
msgstr "Accedi a tutti i nostri modelli pro attivando %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:80
#. translators: %s is replaced with the step title
msgid "Templates for %s"
msgstr "Modelli per %s"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait…"
msgstr "Attendere prego…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating funnel…"
msgstr "Attendere prego. Duplicazione del funnel in corso…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Drafting funnel…"
msgstr "Attendere prego. Creazione del funnel in corso…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting funnel…"
msgstr "Attendere prego. Eliminazione del funnel in corso…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Restoring funnel…"
msgstr "Attendere prego. Ripristino dell'imbuto in corso…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Exporting…"
msgstr "Attendere. Esportazione in corso…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Duplicating step…"
msgstr "Attendere. Duplicazione del passaggio…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Deleting step…"
msgstr "Attendere. Eliminazione del passaggio…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Creating variation…"
msgstr "Attendere. Creazione della variazione in corso…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Archiving variation…"
msgstr "Attendere. Archiviazione della variazione in corso…"

#: admin-core/assets/build/editor-app.js:69
#: admin-core/assets/build/settings-app.js:81
msgid "Please wait. Declaring winner…"
msgstr "Attendere prego. Dichiarazione del vincitore in corso…"

#: admin-core/assets/build/settings-app.js:23
msgid "Getting Started"
msgstr "Iniziare"

#: admin-core/assets/build/settings-app.js:23
msgid "Introduction to CartFlows"
msgstr "Introduzione a CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Modernizing WordPress eCommerce!"
msgstr "Modernizzare l'eCommerce di WordPress!"

#: admin-core/assets/build/settings-app.js:23
msgid "Create Your First Flow"
msgstr "Crea il tuo primo flusso"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Setup Wizard"
msgstr "Vai alla procedura guidata di configurazione"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:34
msgid "Import Funnel"
msgstr "Importa imbuto"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Click for more info"
msgstr "Fai clic per ulteriori informazioni"

#: admin-core/assets/build/settings-app.js:23
msgid "You need WooCommerce plugin installed and activated to import this funnel."
msgstr "È necessario che il plugin WooCommerce sia installato e attivato per importare questo funnel."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates by activating CartFlows Pro."
msgstr "Accedi a tutti i nostri modelli professionali attivando CartFlows Pro."

#: admin-core/assets/build/settings-app.js:23
msgid "Access all of our pro templates when you activate CartFlows Pro license."
msgstr "Accedi a tutti i nostri modelli professionali quando attivi la licenza CartFlows Pro."

#: admin-core/assets/build/settings-app.js:23
msgid "Importing Complete Funnel.."
msgstr "Importazione del Funnel Completa.."

#: admin-core/assets/build/settings-app.js:23
msgid "Design Your Funnel"
msgstr "Progetta il tuo imbuto"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Created! Redirecting…"
msgstr "Creato! Reindirizzamento in corso…"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Failed to Create Flow!"
msgstr "Creazione del flusso non riuscita!"

#: admin-core/assets/build/settings-app.js:23
#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To CartFlows Pro"
msgstr "Aggiorna a CartFlows Pro"

#: admin-core/assets/build/settings-app.js:23
msgid "Name Your Funnel"
msgstr "Assegna un nome al tuo funnel"

#: admin-core/assets/build/settings-app.js:23
msgid "Funnel Name"
msgstr "Nome del funnel"

#: admin-core/assets/build/settings-app.js:23
msgid "Enter Funnel Name"
msgstr "Inserisci il nome dell'imbuto"

#: admin-core/assets/build/settings-app.js:24
msgid "Welcome to CartFlows "
msgstr "Benvenuto su CartFlows"

#: admin-core/assets/build/settings-app.js:24
msgid "Sales funnel builder turns your WordPress website into an optimized selling machine."
msgstr "Il costruttore di funnel di vendita trasforma il tuo sito WordPress in una macchina di vendita ottimizzata."

#: admin-core/assets/build/settings-app.js:24
msgid "Create Your First Funnel"
msgstr "Crea il tuo primo funnel"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"A sales funnel is the sequence of steps a buyer takes to make a purchase. CartFlows helps optimize funnels to turn "
"visitors into customers."
msgstr ""
"Un funnel di vendita è la sequenza di passaggi che un acquirente compie per effettuare un acquisto. CartFlows aiuta a "
"ottimizzare i funnel per trasformare i visitatori in clienti."

#: admin-core/assets/build/settings-app.js:24
#: admin-core/assets/build/settings-app.js:25
msgid "Create New Funnel"
msgstr "Crea nuovo funnel"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views"
msgstr "Visualizzazioni totali della pagina"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Revenue"
msgstr "Entrate Totali"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Orders"
msgstr "Ordini totali"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue"
msgstr "Entrate dall'offerta"

#: admin-core/assets/build/settings-app.js:24
msgid "Total Views"
msgstr "Visualizzazioni totali"

#: admin-core/assets/build/settings-app.js:24
msgid "Overview"
msgstr "Panoramica"

#: admin-core/assets/build/settings-app.js:24
msgid "WooCommerce plugin is required."
msgstr "È richiesto il plugin WooCommerce."

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the overview"
msgstr "È necessario che il plugin WooCommerce sia installato e attivato per visualizzare la panoramica"

#: admin-core/assets/build/settings-app.js:24
msgid "Recent Orders"
msgstr "Ordini recenti"

#: admin-core/assets/build/settings-app.js:24
msgid "View All"
msgstr "Visualizza tutto"

#: admin-core/assets/build/settings-app.js:24
msgid "Customer"
msgstr "Cliente"

#: admin-core/assets/build/settings-app.js:24
msgid "Payment Method"
msgstr "Metodo di pagamento"

#: admin-core/assets/build/settings-app.js:24
msgid "Value"
msgstr "Valore"

#: admin-core/assets/build/settings-app.js:24
msgid "at"
msgstr "a"

#: admin-core/assets/build/settings-app.js:24
msgid "Find recent order here"
msgstr "Trova l'ordine recente qui"

#: admin-core/assets/build/settings-app.js:24
msgid "Once you have received orders, come back here to find it again easily"
msgstr "Una volta ricevuti gli ordini, torna qui per trovarli facilmente"

#: admin-core/assets/build/settings-app.js:24
msgid "You need WooCommerce plugin installed and activated to view the recent orders"
msgstr "È necessario che il plugin WooCommerce sia installato e attivato per visualizzare gli ordini recenti"

#: admin-core/assets/build/settings-app.js:24
msgid "Quick Actions"
msgstr "Azioni rapide"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Funnel"
msgstr "Crea un imbuto"

#: admin-core/assets/build/settings-app.js:80
msgid "Analytics"
msgstr "Analisi"

#: admin-core/assets/build/settings-app.js:24
msgid "Create a Product"
msgstr "Crea un prodotto"

#: admin-core/assets/build/settings-app.js:24
msgid "Create new Product"
msgstr "Crea nuovo prodotto"

#: admin-core/assets/build/settings-app.js:24
msgid "All Funnels"
msgstr "Tutti i funnel"

#: admin-core/assets/build/settings-app.js:24
msgid "View all funnels"
msgstr "Visualizza tutti i funnel"

#: admin-core/assets/build/settings-app.js:24
msgid "Previous"
msgstr "Precedente"

#: admin-core/assets/build/settings-app.js:24
#: wizard/assets/build/wizard-app.js:1
msgid "Next"
msgstr "Avanti"

#: admin-core/assets/build/settings-app.js:24
msgid "First"
msgstr "Primo"

#: admin-core/assets/build/settings-app.js:24
msgid "Last"
msgstr "Ultimo"

#: admin-core/assets/build/settings-app.js:24
msgid ""
"You can specify a file to import by either dragging it into the drag and drop area.(Maximum file size of 5MB; .json "
"file extensions only.)"
msgstr ""
"Puoi specificare un file da importare trascinandolo nell'area di trascinamento. (Dimensione massima del file di 5 MB; "
"solo estensioni di file .json.)"

#: admin-core/assets/build/settings-app.js:24
msgid "Change a file"
msgstr "Modifica un file"

#: admin-core/assets/build/settings-app.js:24
msgid "JSON file up to 5MB"
msgstr "File JSON fino a 5MB"

#: admin-core/assets/build/settings-app.js:25
#. translators: %s is replaced with the file name.
msgid "File Selected: %s"
msgstr "File selezionato: %s"

#: admin-core/assets/build/settings-app.js:25
msgid "Please select the valid json file."
msgstr "Si prega di selezionare il file json valido."

#: admin-core/assets/build/settings-app.js:25
#: wizard/assets/build/wizard-app.js:5
msgid "Importing.."
msgstr "Importazione in corso.."

#: admin-core/assets/build/settings-app.js:25
msgid "Export All"
msgstr "Esporta tutto"

#: admin-core/assets/build/settings-app.js:25
msgid "Exporting…"
msgstr "Esportazione in corso…"

#: admin-core/assets/build/settings-app.js:25
msgid "Search Funnels"
msgstr "Canali di ricerca"

#: admin-core/assets/build/settings-app.js:25
msgid "Filter Funnels by Date"
msgstr "Filtra i funnel per data"

#: admin-core/assets/build/settings-app.js:25
msgid "Publish "
msgstr "Pubblica"

#: admin-core/assets/build/settings-app.js:25
msgid "Draft "
msgstr "Bozza"

#: admin-core/assets/build/settings-app.js:25
msgid "Trash "
msgstr "Spazzatura"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:33
msgid "Mode"
msgstr "Modalità"

#: admin-core/assets/build/settings-app.js:25
msgid "All"
msgstr "Tutti"

#: admin-core/assets/build/settings-app.js:25
#: admin-core/assets/build/settings-app.js:32
msgid "Live"
msgstr "Vivi"

#: admin-core/assets/build/settings-app.js:25
msgid "SandBox"
msgstr "SandBox"

#: admin-core/assets/build/settings-app.js:25
msgid "Reset Filters"
msgstr "Reimposta filtri"

#: admin-core/assets/build/settings-app.js:28
#. translators: %s: action name.
msgid "%s This Flow"
msgstr "%s Questo flusso"

#: admin-core/assets/build/settings-app.js:31
#. translators: %s: action status name.
msgid "Do you want to %s this flow? Are you sure?"
msgstr "Vuoi %s questo flusso? Sei sicuro?"

#: admin-core/assets/build/settings-app.js:31
msgid "items selected"
msgstr "elementi selezionati"

#: admin-core/assets/build/settings-app.js:31
msgid "Applying changes…"
msgstr "Applicazione delle modifiche…"

#: admin-core/assets/build/settings-app.js:31
msgid " Published "
msgstr "Pubblicato"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate Funnel"
msgstr "Duplica Funnel"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to duplicate this funnel?"
msgstr "Vuoi davvero duplicare questo funnel?"

#: admin-core/assets/build/settings-app.js:31
msgid "Trash Funnel"
msgstr "Imbuto per rifiuti"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this funnel?"
msgstr "Vuoi davvero eliminare questo funnel?"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to trash this Funnel?"
msgstr "Vuoi davvero eliminare questo Funnel?"

#: admin-core/assets/build/settings-app.js:31
msgid "Restore Funnel"
msgstr "Ripristina il funnel"

#: admin-core/assets/build/settings-app.js:31
msgid "Do you really want to restore this funnel?"
msgstr "Vuoi davvero ripristinare questo funnel?"

#: admin-core/assets/build/settings-app.js:31
#: admin-core/assets/build/settings-app.js:32
msgid "Draft"
msgstr "Bozza"

#: admin-core/assets/build/settings-app.js:32
#. translators: %s date
msgid "Last Modified: %s"
msgstr "Ultima modifica: %s"

#: admin-core/assets/build/settings-app.js:32
msgid "Updated "
msgstr "Aggiornato"

#: admin-core/assets/build/settings-app.js:32
msgid "Sandbox"
msgstr "Sandbox"

#: admin-core/assets/build/settings-app.js:32
msgid "WooCommerce Required to display the revenue."
msgstr "WooCommerce è necessario per visualizzare il fatturato."

#: admin-core/assets/build/settings-app.js:32
msgid "Restore Flow"
msgstr "Ripristina flusso"

#: admin-core/assets/build/settings-app.js:32
msgid "Delete Flow"
msgstr "Elimina flusso"

#: admin-core/assets/build/settings-app.js:32
msgid "Upgrade to Pro for this feature."
msgstr "Passa a Pro per questa funzione."

#: admin-core/assets/build/settings-app.js:32
msgid "Duplicate (Pro)"
msgstr "Duplica (Pro)"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash Flow"
msgstr "Flusso di rifiuti"

#: admin-core/assets/build/settings-app.js:32
msgid "Trash"
msgstr "Spazzatura"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Name"
msgstr "Nome"

#: admin-core/assets/build/settings-app.js:32
#: admin-core/assets/build/settings-app.js:33
msgid "Sales"
msgstr "Vendite"

#: admin-core/assets/build/settings-app.js:32
msgid "Move to Trash"
msgstr "Sposta nel cestino"

#: admin-core/assets/build/settings-app.js:33
#. translators: %d Search term
msgid "No matching results found for the search term \"%s\"."
msgstr "Nessun risultato corrispondente trovato per il termine di ricerca \"%s\"."

#: admin-core/assets/build/settings-app.js:33
msgid "No flows found for the selected filter."
msgstr "Nessun flusso trovato per il filtro selezionato."

#: admin-core/assets/build/settings-app.js:33
msgid "Please try using different keywords, date range, or filters to refine your results."
msgstr "Per favore, prova a utilizzare parole chiave diverse, un intervallo di date o filtri per affinare i tuoi risultati."

#: admin-core/assets/build/settings-app.js:33
msgid "Create New"
msgstr "Crea nuovo"

#: admin-core/assets/build/settings-app.js:34
#. translators: %d flow count
msgid " %d items"
msgstr "%d articoli"

#: admin-core/assets/build/settings-app.js:34
msgid "Create your first funnel"
msgstr "Crea il tuo primo funnel"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Build a sales funnel with everything you need to generate leads and grow sales."
msgstr "Costruisci un funnel di vendita con tutto ciò di cui hai bisogno per generare contatti e aumentare le vendite."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "One Click Upsells"
msgstr "Upsell con un clic"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "A/B Split Testing"
msgstr "Test A/B"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Conversion Templates"
msgstr "Modelli di conversione"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Editor"
msgstr "Editor di Checkout"

#: admin-core/assets/build/settings-app.js:34
msgid "Insights"
msgstr "Approfondimenti"

#: admin-core/assets/build/settings-app.js:34
msgid "Create Funnel"
msgstr "Crea imbuto"

#: admin-core/assets/build/settings-app.js:34
msgid "Plugin Required"
msgstr "Plugin richiesto"

#: admin-core/assets/build/settings-app.js:34
msgid "You need WooCommerce plugin installed and activated to access this page."
msgstr "È necessario che il plugin WooCommerce sia installato e attivato per accedere a questa pagina."

#: admin-core/assets/build/settings-app.js:34
msgid "Installing"
msgstr "Installazione"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:34
msgid "Activating"
msgstr "Attivazione"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Failed"
msgstr "Fallito"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Redirecting"
msgstr "Reindirizzamento"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Create Store Checkout"
msgstr "Crea il checkout del negozio"

#: admin-core/assets/build/settings-app.js:34
msgid "Name Your Store Checkout"
msgstr "Nome del tuo negozio Checkout"

#: admin-core/assets/build/settings-app.js:34
msgid "You can't create more than 3 flows in free version. Upgrade to CartFlows Pro for adding more flows and other features."
msgstr ""
"Non puoi creare più di 3 flussi nella versione gratuita. Passa a CartFlows Pro per aggiungere più flussi e altre "
"funzionalità."

#: admin-core/assets/build/settings-app.js:34
msgid "Upgrade To Cartflows Pro"
msgstr "Aggiorna a Cartflows Pro"

#: admin-core/assets/build/settings-app.js:34
msgid "Store Checkout Name"
msgstr "Nome del checkout del negozio"

#: admin-core/assets/build/settings-app.js:34
msgid "Enter Store Checkout Name"
msgstr "Inserisci il nome del checkout del negozio"

#: admin-core/assets/build/settings-app.js:34
msgid "Create a global store checkout"
msgstr "Crea un checkout globale del negozio"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"A well-designed checkout page can help streamline the checkout process, reduce cart abandonment rates and increase "
"conversions."
msgstr ""
"Una pagina di pagamento ben progettata può aiutare a semplificare il processo di pagamento, ridurre i tassi di "
"abbandono del carrello e aumentare le conversioni."

#: admin-core/assets/build/settings-app.js:34
msgid "Improved user experience"
msgstr "Esperienza utente migliorata"

#: admin-core/assets/build/settings-app.js:34
msgid "Brand consistency"
msgstr "Coerenza del marchio"

#: admin-core/assets/build/settings-app.js:34
msgid "Increased trust and credibility"
msgstr "Aumento della fiducia e della credibilità"

#: admin-core/assets/build/settings-app.js:34
msgid "Flexibility and customization"
msgstr "Flessibilità e personalizzazione"

#: admin-core/assets/build/settings-app.js:34
msgid "Competitive advantage"
msgstr "Vantaggio competitivo"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"By setting up the store checkout, your default checkout page will be replaced by the CartFlows modern checkout which "
"will lead to more conversion and leads."
msgstr ""
"Impostando il checkout del negozio, la tua pagina di checkout predefinita verrà sostituita dal moderno checkout di "
"CartFlows, che porterà a più conversioni e contatti."

#: admin-core/assets/build/settings-app.js:34
msgid "Get Started"
msgstr "Inizia"

#: admin-core/assets/build/settings-app.js:34
msgid "Connect a Payment Gateway"
msgstr "Collega un gateway di pagamento"

#: admin-core/assets/build/settings-app.js:34
msgid "Stripe for WooCommerce delivers a simple, secure way to accept credit card payments in your WooCommerce store."
msgstr ""
"Stripe per WooCommerce offre un modo semplice e sicuro per accettare pagamenti con carta di credito nel tuo negozio "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:34
msgid "Connect with Stripe"
msgstr "Connettiti con Stripe"

#: admin-core/assets/build/settings-app.js:34
msgid "Setting up…"
msgstr "Impostazione in corso…"

#: admin-core/assets/build/settings-app.js:34
msgid "Recover Abandoned Carts"
msgstr "Recupera i carrelli abbandonati"

#: admin-core/assets/build/settings-app.js:34
msgid "Use our cart abandonment plugin and automatically recover your lost revenue absolutely free."
msgstr ""
"Usa il nostro plugin per il recupero dei carrelli abbandonati e recupera automaticamente il tuo fatturato perso, "
"assolutamente gratis."

#: admin-core/assets/build/settings-app.js:34
msgid "Finishing…"
msgstr "Completando…"

#: admin-core/assets/build/settings-app.js:34
msgid "Setup Email Reports"
msgstr "Configura i rapporti email"

#: admin-core/assets/build/settings-app.js:34
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send you an email report with "
"key metrics and insights."
msgstr ""
"Lascia che CartFlows elimini le congetture dai risultati del tuo checkout. Ogni settimana il tuo negozio ti invierà un "
"rapporto via email con metriche chiave e approfondimenti."

#: admin-core/assets/build/settings-app.js:34
msgid "Add Email Address"
msgstr "Aggiungi indirizzo email"

#: admin-core/assets/build/settings-app.js:34
msgid "Dismiss Setup"
msgstr "Ignora configurazione"

#: admin-core/assets/build/settings-app.js:34
msgid "Active"
msgstr "Attivo"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Activate"
msgstr "Attiva"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Install"
msgstr "Installa"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:80
msgid "Installing…"
msgstr "Installazione in corso…"

#: admin-core/assets/build/settings-app.js:34
msgid "Installed"
msgstr "Installato"

#: admin-core/assets/build/settings-app.js:34
msgid "Let's Go"
msgstr "Andiamo"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Plugins"
msgstr "Plugin consigliati"

#: admin-core/assets/build/settings-app.js:34
msgid "Recommended Themes"
msgstr "Temi consigliati"

#: admin-core/assets/build/settings-app.js:34
msgid "View All Steps"
msgstr "Visualizza tutti i passaggi"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel thumbnail image"
msgstr "Immagine di anteprima dell'imbuto"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Funnel Preview"
msgstr "Anteprima del funnel"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Live Preview"
msgstr "Anteprima dal vivo"

#: admin-core/assets/build/settings-app.js:34
msgid "Funnel Templates"
msgstr "Modelli di imbuto"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Start from scratch"
msgstr "Inizia da zero"

#: admin-core/assets/build/settings-app.js:34
msgid "It seems that the page builder you selected is inactive."
msgstr "Sembra che il costruttore di pagine che hai selezionato sia inattivo."

#: admin-core/assets/build/settings-app.js:34
msgid " to see CartFlows templates. If you prefer another page builder tool, you can "
msgstr "per vedere i modelli di CartFlows. Se preferisci un altro strumento per la creazione di pagine, puoi"

#: admin-core/assets/build/settings-app.js:34
msgid "select it here"
msgstr "selezionalo qui"

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using any other page builder? No worries. CartFlows works well with every other page builder. Right now we do "
"not have ready templates for every page builder but we are planning to add it very soon."
msgstr ""
"Stai utilizzando un altro page builder? Nessun problema. CartFlows funziona bene con ogni altro page builder. Al "
"momento non abbiamo modelli pronti per ogni page builder, ma stiamo pianificando di aggiungerli molto presto."

#: admin-core/assets/build/settings-app.js:34
#: admin-core/assets/build/settings-app.js:35
msgid "Learn How "
msgstr "Scopri come"

#: admin-core/assets/build/settings-app.js:35
msgid "No Results Found."
msgstr "Nessun risultato trovato."

#: admin-core/assets/build/settings-app.js:35
msgid "Don't see a funnel that you would like to import?"
msgstr "Non vedi un funnel che vorresti importare?"

#: admin-core/assets/build/settings-app.js:35
msgid "Please suggest us "
msgstr "Per favore suggeriscici"

#: admin-core/assets/build/settings-app.js:35
msgid "Choose a Funnel Templates"
msgstr "Scegli un modello di funnel"

#: admin-core/assets/build/settings-app.js:35
msgid "Search Templates"
msgstr "Cerca modelli"

#: admin-core/assets/build/settings-app.js:35
msgid "Start from Scratch"
msgstr "Inizia da zero"

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the Bricks Builder."
msgstr "Sembra che tu stia utilizzando Bricks Builder."

#: admin-core/assets/build/settings-app.js:35
msgid "It seems that you are using the page builder other than Elementor, Beaver Builder, Block Builder."
msgstr "Sembra che tu stia utilizzando un page builder diverso da Elementor, Beaver Builder, Block Builder."

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Are you using Bricks Builder? No worries. CartFlows works well with Bricks Builder. Right now we do not have ready "
"templates for Bricks Builder but we are planning to add it very soon."
msgstr ""
"Stai usando Bricks Builder? Nessun problema. CartFlows funziona bene con Bricks Builder. Al momento non abbiamo modelli "
"pronti per Bricks Builder, ma stiamo pianificando di aggiungerli molto presto."

#: admin-core/assets/build/settings-app.js:35
msgid "Checkout Page"
msgstr "Pagina di pagamento"

#: admin-core/assets/build/settings-app.js:35
msgid "Oops!!! No template Found."
msgstr "Ops!!! Nessun modello trovato."

#: admin-core/assets/build/settings-app.js:35
msgid "Seems like no template is available for chosen editor."
msgstr "Sembra che non ci sia alcun modello disponibile per l'editor scelto."

#: admin-core/assets/build/settings-app.js:35
msgid "Store Checkout Templates"
msgstr "Modelli di pagamento del negozio"

#: admin-core/assets/build/settings-app.js:80
msgid "No CartFlows Logs Found."
msgstr "Nessun log di CartFlows trovato."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Logs"
msgstr "Registri di CartFlows"

#: admin-core/assets/build/settings-app.js:80
msgid "Copied"
msgstr "Copiato"

#: admin-core/assets/build/settings-app.js:80
msgid "Copy"
msgstr "Copia"

#: admin-core/assets/build/settings-app.js:80
msgid "Downloading"
msgstr "Download in corso"

#: admin-core/assets/build/settings-app.js:80
msgid "Download"
msgstr "Scarica"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleting"
msgstr "Eliminazione"

#: admin-core/assets/build/settings-app.js:80
msgid "Deleted"
msgstr "Eliminato"

#: admin-core/assets/build/settings-app.js:80
msgid "Email Marketing Automation"
msgstr "Automazione del marketing via email"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate email marketing campaigns based on customer actions, such as abandoned carts or completed purchases in "
"WooCommerce."
msgstr ""
"Automatizza le campagne di email marketing in base alle azioni dei clienti, come carrelli abbandonati o acquisti "
"completati in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Customer Birthday Campaigns"
msgstr "Campagne di compleanno per i clienti"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automatically send personalized birthday offers or discounts to customers based on their birthdate stored in "
"WooCommerce."
msgstr ""
"Invia automaticamente offerte o sconti personalizzati di compleanno ai clienti in base alla loro data di nascita "
"memorizzata in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Order Notification"
msgstr "Notifica di Ordine"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Receive instant notifications via SMS, Slack, WhatsApp, or messaging apps when new orders are placed in your "
"WooCommerce store."
msgstr ""
"Ricevi notifiche istantanee tramite SMS, Slack, WhatsApp o app di messaggistica quando vengono effettuati nuovi ordini "
"nel tuo negozio WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Payment and Accounting Integration"
msgstr "Integrazione Pagamenti e Contabilità"

#: admin-core/assets/build/settings-app.js:80
msgid "Sync WooCommerce sales data with your accounting software for streamlined financial management."
msgstr ""
"Sincronizza i dati di vendita di WooCommerce con il tuo software di contabilità per una gestione finanziaria "
"semplificata."

#: admin-core/assets/build/settings-app.js:80
msgid "Coupon Code Marketing"
msgstr "Marketing dei codici sconto"

#: admin-core/assets/build/settings-app.js:80
msgid "Automate the creation and distribution of coupon codes based on specific conditions or customer actions in WooCommerce."
msgstr ""
"Automatizza la creazione e la distribuzione di codici coupon in base a condizioni specifiche o azioni dei clienti in "
"WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Upsell and Cross-sell Campaigns"
msgstr "Campagne di Upsell e Cross-sell"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Automate targeted upsell and cross-sell offers based on customers' purchase history or product interactions in "
"WooCommerce."
msgstr ""
"Automatizza le offerte di upsell e cross-sell mirate in base alla cronologia degli acquisti o alle interazioni con i "
"prodotti dei clienti in WooCommerce."

#: admin-core/assets/build/settings-app.js:80
msgid "Connect Your Website"
msgstr "Collega il tuo sito web"

#: admin-core/assets/build/settings-app.js:80
msgid "Reloading"
msgstr "Ricaricamento"

#: admin-core/assets/build/settings-app.js:80
msgid "Connecting"
msgstr "Connessione in corso"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate WooCommerce and CartFlows with Anything"
msgstr "Integra WooCommerce e CartFlows con qualsiasi cosa"

#: admin-core/assets/build/settings-app.js:80
msgid "Integrate all your apps, plugins, and services to automate repetitive tasks."
msgstr "Integra tutte le tue app, plugin e servizi per automatizzare i compiti ripetitivi."

#: admin-core/assets/build/settings-app.js:80
msgid "These are just some examples. The possibilities are truly endless!"
msgstr "Questi sono solo alcuni esempi. Le possibilità sono davvero infinite!"

#: admin-core/assets/build/settings-app.js:80
msgid "Trusted by World's Top Brands to Connect Their Apps"
msgstr "Fidato dai migliori marchi del mondo per connettere le loro app"

#: admin-core/assets/build/settings-app.js:80
msgid "Connect your apps and automate your business."
msgstr "Collega le tue app e automatizza la tua attività."

#: modules/gutenberg/build/blocks-placeholder.js:12
#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail Form"
msgstr "Modulo Dettagli Ordine"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Overview."
msgstr "Nascondi/Mostra panoramica dell'ordine."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Order Detail"
msgstr "Dettaglio dell'ordine"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Order Detail."
msgstr "Nascondi/Mostra Dettaglio Ordine."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Billing Address."
msgstr "Nascondi/Mostra indirizzo di fatturazione."

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide/Show Shipping Address."
msgstr "Nascondi/Mostra indirizzo di spedizione."

#: modules/gutenberg/build/blocks.js:7
msgid "Heading Text"
msgstr "Testo dell'intestazione"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Heading Bottom Spacing(px)"
msgstr "Spaziatura inferiore intestazione (px)"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Opacity"
msgstr "Opacità"

#: modules/gutenberg/build/blocks.js:7
msgid "Section Spacing"
msgstr "Spaziatura della sezione"

#: modules/gutenberg/build/blocks.js:7
msgid "Download Details"
msgstr "Dettagli del download"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Loading"
msgstr "Caricamento"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Order Detail Form Block"
msgstr "Blocco Modulo Dettaglio Ordine CartFlows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cartflows"
msgstr "cartflows"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/dist/blocks.build.js:1
msgid "order detail form"
msgstr "modulo dettagli ordine"

#: modules/gutenberg/build/blocks.js:7
#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "cf"
msgstr "cf"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Color"
msgstr "Colore dell'icona"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Icon Hover Color"
msgstr "Colore al passaggio del mouse sull'icona"

#: modules/gutenberg/build/blocks.js:11
msgid "Gap Between Icon And Text"
msgstr "Distanza tra icona e testo"

#: modules/gutenberg/build/blocks.js:11
msgid "Subtitle"
msgstr "Sottotitolo"

#: modules/gutenberg/build/blocks.js:11
msgid "Enable Subtitle"
msgstr "Abilita sottotitoli"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Bottom Spacing"
msgstr "Spaziatura inferiore del titolo"

#: modules/gutenberg/build/blocks.js:11
msgid "SubTitle"
msgstr "Sottotitolo"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Add text…"
msgstr "Aggiungi testo…"

#: modules/gutenberg/build/blocks.js:11
msgid "CartFlows Next Step Button Block."
msgstr "Blocco Pulsante Passo Successivo di CartFlows."

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "next step button"
msgstr "pulsante passo successivo"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Two Step ( Pro )"
msgstr "Due passi ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Multistep Checkout ( Pro )"
msgstr "Checkout multistep ( Pro )"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This feature is available in the CartFlows higher plan. Upgrade Now!."
msgstr "Nota: Questa funzione è disponibile nel piano superiore di CartFlows. Aggiorna ora!."

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Skin"
msgstr "Pelle del campo di input"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input Field"
msgstr "Campo di input"

#: modules/gutenberg/build/blocks.js:11
msgid "Field Text Color"
msgstr "Colore del testo del campo"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Field Validation"
msgstr "Validazione del campo di input"

#: modules/gutenberg/build/blocks.js:11
msgid "Note: This styling can be only seen at frontend"
msgstr "Nota: Questo stile può essere visto solo nel frontend"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Horizontal"
msgstr "Orizzontale"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Vertical"
msgstr "Verticale"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Blur"
msgstr "Sfocatura"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spread"
msgstr "Diffondere"

#: modules/gutenberg/build/blocks.js:11
msgid "Buttons Text"
msgstr "Testo dei pulsanti"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Color"
msgstr "Colore del titolo"

#: modules/gutenberg/build/blocks.js:11
msgid "Title Background Color"
msgstr "Colore di sfondo del titolo"

#: modules/gutenberg/build/blocks.js:11
msgid "Desc Background Color"
msgstr "Colore di sfondo della descrizione"

#: modules/gutenberg/build/blocks.js:11
msgid "Success/Error Message"
msgstr "Messaggio di successo/errore"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Message Color"
msgstr "Colore del messaggio"

#: modules/gutenberg/build/blocks.js:11
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label"
msgstr "Etichetta fluttuante"

#: modules/gutenberg/build/blocks.js:11
msgid "Input Skin"
msgstr "Pelle di input"

#: modules/gutenberg/build/blocks.js:11
msgid "Skin"
msgstr "Pelle"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hide Advanced"
msgstr "Nascondi Avanzate"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Line Height"
msgstr "Altezza della linea"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Color Settings"
msgstr "Impostazioni colore"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Overlay Color"
msgstr "Colore di sovrapposizione"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Inset"
msgstr "Inserto"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Outset"
msgstr "Inizio"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Spacing Between Sections(px)"
msgstr "Distanza tra le sezioni (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Type"
msgstr "Tipo di sfondo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image"
msgstr "Immagine"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Background Image"
msgstr "Immagine di sfondo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Background Image"
msgstr "Seleziona immagine di sfondo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Replace image"
msgstr "Sostituisci immagine"

#: admin-core/assets/build/editor-app.js:1
#: modules/gutenberg/dist/blocks.build.js:1
msgid "Remove Image"
msgstr "Rimuovi immagine"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Image Position"
msgstr "Posizione dell'immagine"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Left"
msgstr "In alto a sinistra"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Center"
msgstr "Centro superiore"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Top Right"
msgstr "In alto a destra"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Left"
msgstr "Centro Sinistra"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Center"
msgstr "Centro Centro"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Center Right"
msgstr "Centro Destra"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Left"
msgstr "In basso a sinistra"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Center"
msgstr "Centro inferiore"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Bottom Right"
msgstr "In basso a destra"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Attachment"
msgstr "Allegato"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Fixed"
msgstr "Fisso"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Scroll"
msgstr "Scorri"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat"
msgstr "Ripeti"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "No Repeat"
msgstr "Nessuna ripetizione"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-x"
msgstr "Ripeti-x"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Repeat-y"
msgstr "Ripeti-y"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Auto"
msgstr "Auto"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Cover"
msgstr "Copertura"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Contain"
msgstr "Contenere"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Weight"
msgstr "Spessore del carattere"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Font Subset"
msgstr "Sottoinsieme di caratteri"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "px"
msgstr "px"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "em"
msgstr "em"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Size Type"
msgstr "Tipo di dimensione"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Next Step Button Block"
msgstr "Blocco del pulsante Passo successivo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Select Icon"
msgstr "Seleziona icona"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Button Hover Color"
msgstr "Colore al passaggio del mouse sul pulsante"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Groove"
msgstr "Solco"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Ridge"
msgstr "Cresta"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Title Bottom Spacing (px)"
msgstr "Spaziatura inferiore del titolo (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Hover Color"
msgstr "Colore al passaggio del mouse"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Text Transform"
msgstr "Trasformazione del testo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Capitalize"
msgstr "Maiuscolo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Uppercase"
msgstr "Maiuscolo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Lowercase"
msgstr "Minuscolo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Letter Spacing (px)"
msgstr "Spaziatura lettere (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Checkout Block"
msgstr "Blocco di Checkout di CartFlows"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "checkout form"
msgstr "modulo di pagamento"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "MultiStep Checkout ( Pro )"
msgstr "Checkout MultiStep ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Width (px)"
msgstr "Larghezza bordo (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Note: This feature is available in the CartFlows Pro. Upgrade Now!."
msgstr "Nota: Questa funzione è disponibile in CartFlows Pro. Aggiorna ora!."

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Input/Text Placeholder Color"
msgstr "Colore segnaposto input/testo"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Border Radius (px)"
msgstr "Raggio Bordo (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Padding (px)"
msgstr "Padding della sezione (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Section Margin (px)"
msgstr "Margine sezione (px)"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Success / Error Message"
msgstr "Messaggio di successo / errore"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Form success / Error validation"
msgstr "Successo del modulo / Errore di convalida"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Error Message"
msgstr "Messaggio di errore"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "CartFlows Optin Form Block"
msgstr "Blocco Modulo di Opt-in di CartFlows"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "optin form"
msgstr "modulo di adesione"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Floating Label ( Pro )"
msgstr "Etichetta Fluttuante ( Pro )"

#: modules/gutenberg/dist/blocks.build.js:1
msgid "Submit Button Text"
msgstr "Testo del pulsante di invio"

#: wizard/assets/build/wizard-app.js:1
msgid "Let's Start"
msgstr "Iniziamo"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 1 of 6"
msgstr "Passo 1 di 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome to CartFlows"
msgstr "Benvenuto su CartFlows"

#: wizard/assets/build/wizard-app.js:1
msgid ""
"You're only minutes away from having a more profitable WooCommerce store! This short setup wizard will help you get "
"started with CartFlows."
msgstr ""
"Sei a pochi minuti dall'avere un negozio WooCommerce più redditizio! Questo breve wizard di configurazione ti aiuterà a "
"iniziare con CartFlows."

#: wizard/assets/build/wizard-app.js:1
msgid "Save & Continue"
msgstr "Salva e continua"

#: wizard/assets/build/wizard-app.js:1
msgid "Saving"
msgstr "Salvataggio"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 2 of 6"
msgstr "Passo 2 di 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Hi there! Tell us which page builder you use."
msgstr "Ciao! Dicci quale page builder usi."

#: wizard/assets/build/wizard-app.js:1
msgid "CartFlows works with all page builders, so don't worry if your page builder is not in the list. "
msgstr "CartFlows funziona con tutti i page builder, quindi non preoccuparti se il tuo page builder non è nell'elenco."

#: wizard/assets/build/wizard-app.js:1
msgid "Install & Activate"
msgstr "Installa e attiva"

#: wizard/assets/build/wizard-app.js:1
msgid "Installing Required Plugins"
msgstr "Installazione dei plugin richiesti"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 3 of 6"
msgstr "Passo 3 di 6"

#: wizard/assets/build/wizard-app.js:1
msgid "Great job!"
msgstr "Ottimo lavoro!"

#: wizard/assets/build/wizard-app.js:1
msgid "Now let's install some required plugins."
msgstr "Ora installiamo alcuni plugin richiesti."

#: wizard/assets/build/wizard-app.js:1
msgid ""
"Since CartFlows uses WooCommerce, we'll set it up for you along with Cart Abandonment and Stripe Payments so you can "
"recover abandoned orders and easily accept payments."
msgstr ""
"Poiché CartFlows utilizza WooCommerce, lo configureremo per te insieme a Cart Abandonment e Stripe Payments in modo che "
"tu possa recuperare gli ordini abbandonati e accettare facilmente i pagamenti."

#: wizard/assets/build/wizard-app.js:1
msgid "The following plugins will be installed and activated for you:"
msgstr "I seguenti plugin saranno installati e attivati per te:"

#: wizard/assets/build/wizard-app.js:1
msgid "Continuing…"
msgstr "Continuando…"

#: wizard/assets/build/wizard-app.js:1
msgid "Continue"
msgstr "Continua"

#: wizard/assets/build/wizard-app.js:1
msgid "Step 5 of 6"
msgstr "Passo 5 di 6"

#: wizard/assets/build/wizard-app.js:2
#. translators: %s: html tag
msgid "One last step. %s Let's setup email reports on how your store is doing."
msgstr "Un ultimo passo. %s Configuriamo i rapporti via email su come sta andando il tuo negozio."

#: wizard/assets/build/wizard-app.js:3
#. translators: %1$s: html tag, %2$s: html tag
msgid ""
"Let CartFlows take the guesswork out of your checkout results. Each week your store will send %1$s you an email report "
"with key metrics and insights. You also will receive emails from us to %2$s help your store sell more."
msgstr ""
"Lascia che CartFlows elimini le congetture dai risultati del tuo checkout. Ogni settimana il tuo negozio ti invierà "
"%1$s un rapporto via email con metriche chiave e approfondimenti. Riceverai anche email da noi per %2$s aiutare il tuo "
"negozio a vendere di più."

#: wizard/assets/build/wizard-app.js:3
msgid "First Name"
msgstr "Nome"

#: wizard/assets/build/wizard-app.js:3
msgid "Please enter your name"
msgstr "Per favore inserisci il tuo nome"

#: wizard/assets/build/wizard-app.js:3
msgid "Enter Your Email"
msgstr "Inserisci la tua email"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Name"
msgstr "Per favore inserisci il nome"

#: wizard/assets/build/wizard-app.js:4
msgid "Entered email address is not a valid email"
msgstr "L'indirizzo email inserito non è un'email valida"

#: wizard/assets/build/wizard-app.js:4
msgid "Please Enter Email ID"
msgstr "Per favore inserisci l'ID email"

#: wizard/assets/build/wizard-app.js:1
msgid "Welcome"
msgstr "Benvenuto"

#: wizard/assets/build/wizard-app.js:1
msgid "Page Builder"
msgstr "Costruttore di Pagine"

#: wizard/assets/build/wizard-app.js:1
msgid "Required Plugins"
msgstr "Plugin richiesti"

#: wizard/assets/build/wizard-app.js:1
msgid "Done"
msgstr "Fatto"

#: wizard/assets/build/wizard-app.js:1
msgid "Exit setup wizard"
msgstr "Esci dalla procedura guidata di configurazione"

#: wizard/assets/build/wizard-app.js:1
msgid "Redirecting.."
msgstr "Reindirizzamento.."

#: wizard/assets/build/wizard-app.js:1
msgid "Skip"
msgstr "Salta"

#: wizard/assets/build/wizard-app.js:1
#: wizard/assets/build/wizard-app.js:5
msgid "Finish Store Setup"
msgstr "Completa la configurazione del negozio"

#: wizard/assets/build/wizard-app.js:1
msgid "Select Color"
msgstr "Seleziona colore"

#: wizard/assets/build/wizard-app.js:5
msgid "Recommended"
msgstr "Consigliato"

#: wizard/assets/build/wizard-app.js:5
msgid "Upload a Logo"
msgstr "Carica un logo"

#: wizard/assets/build/wizard-app.js:5
msgid "Change a Logo"
msgstr "Cambia un logo"

#: wizard/assets/build/wizard-app.js:5
msgid "Remove logo"
msgstr "Rimuovi logo"

#: wizard/assets/build/wizard-app.js:5
msgid "Suggested Dimensions: 180x60 pixels"
msgstr "Dimensioni suggerite: 180x60 pixel"

#: wizard/assets/build/wizard-app.js:5
msgid "Oops!!! No templates found"
msgstr "Ops!!! Nessun modello trovato"

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Seems like no templates are available for chosen page editor. Don't worry, you can always import the store checkout "
"template from the CartFlows setting menu."
msgstr ""
"Sembra che non ci siano modelli disponibili per l'editor di pagina scelto. Non preoccuparti, puoi sempre importare il "
"modello di checkout del negozio dal menu delle impostazioni di CartFlows."

#: wizard/assets/build/wizard-app.js:5
msgid "Skip to Next"
msgstr "Salta al prossimo"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 4 of 6"
msgstr "Fase 4 di 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Awesome"
msgstr "Fantastico"

#: wizard/assets/build/wizard-app.js:5
msgid "Now let's setup your new store checkout."
msgstr "Ora configuriamo il checkout del tuo nuovo negozio."

#: wizard/assets/build/wizard-app.js:5
msgid ""
"Choose one of the store checkout designs below. After setup you can change the text and color or even choose an "
"entirely new store checkout design."
msgstr ""
"Scegli uno dei design di checkout del negozio qui sotto. Dopo l'installazione, puoi cambiare il testo e il colore o "
"persino scegliere un design di checkout del negozio completamente nuovo."

#: wizard/assets/build/wizard-app.js:5
msgid "Import & Continue"
msgstr "Importa e continua"

#: wizard/assets/build/wizard-app.js:5
msgid "Processing.."
msgstr "Elaborazione in corso.."

#: wizard/assets/build/wizard-app.js:5
msgid "Importing Failed.."
msgstr "Importazione fallita.."

#: wizard/assets/build/wizard-app.js:5
msgid "Selected Template:"
msgstr "Modello selezionato:"

#: wizard/assets/build/wizard-app.js:5
msgid "Change Primary Color"
msgstr "Cambia colore primario"

#: wizard/assets/build/wizard-app.js:5
msgid "Step 6 of 6"
msgstr "Passo 6 di 6"

#: wizard/assets/build/wizard-app.js:5
msgid "Congratulations, You Did It!"
msgstr "Congratulazioni, ce l'hai fatta!"

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows is set up on your website! Please watch the short video below for your next steps."
msgstr "CartFlows è configurato sul tuo sito web! Guarda il breve video qui sotto per i tuoi prossimi passi."

#: wizard/assets/build/wizard-app.js:5
msgid "CartFlows Extended Walkthrough Tutorial"
msgstr "Tutorial esteso di CartFlows"

#: wizard/assets/build/wizard-app.js:5
msgid "Finishing the Setup"
msgstr "Completamento dell'installazione"

#: admin-core/inc/admin-menu.php:1052
msgid "A simple yet powerful way to add content restriction to your website."
msgstr "Un modo semplice ma potente per aggiungere restrizioni ai contenuti del tuo sito web."

#: classes/class-cartflows-loader.php:368
msgid "Quick Feedback"
msgstr "Feedback rapido"

#: classes/class-cartflows-loader.php:370
msgid "If you have a moment, please share why you are deactivating CartFlows:"
msgstr "Se hai un momento, per favore condividi il motivo per cui stai disattivando CartFlows:"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Unlock Premium Features with CartFlows PRO!"
msgstr "Sblocca le funzionalità premium con CartFlows PRO!"

#: admin-core/assets/build/editor-app.js:16
#: admin-core/assets/build/settings-app.js:16
msgid "Get the tools you need to create powerful sales funnels, increase conversions, and grow your business with ease."
msgstr ""
"Ottieni gli strumenti necessari per creare potenti funnel di vendita, aumentare le conversioni e far crescere la tua "
"attività con facilità."

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Select a Settings Tab"
msgstr "Seleziona una scheda Impostazioni"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
#: admin-core/assets/build/settings-app.js:80
msgid "Free vs Pro"
msgstr "Gratuito vs Pro"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Increase Your Revenue with Smart Order Bumps"
msgstr "Aumenta il tuo fatturato con i Smart Order Bumps"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Boost sales with CartFlows’ Order Bump! Offer personalized add-ons at checkout to increase revenue effortlessly. Quick "
"to set up, no coding needed!"
msgstr ""
"Aumenta le vendite con l'Order Bump di CartFlows! Offri componenti aggiuntivi personalizzati al checkout per aumentare "
"le entrate senza sforzo. Facile da configurare, nessuna programmazione necessaria!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Show the Right Offer to the Right People – Automatically!"
msgstr "Mostra l'offerta giusta alle persone giuste, automaticamente!"

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid ""
"Personalize deals based on location, cart details, and more. Upgrade to CartFlows PRO and unlock this smart feature "
"today!"
msgstr ""
"Personalizza le offerte in base alla posizione, ai dettagli del carrello e altro ancora. Passa a CartFlows PRO e "
"sblocca questa funzione intelligente oggi stesso!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Boost Sales Instantly with Auto-Applied Coupons!"
msgstr "Aumenta le vendite istantaneamente con i coupon applicati automaticamente!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"No codes, no hassle—discounts apply instantly at checkout. Upgrade to CartFlows PRO and start converting more customers "
"today!"
msgstr ""
"Nessun codice, nessun problema: gli sconti si applicano istantaneamente al checkout. Passa a CartFlows PRO e inizia a "
"convertire più clienti oggi stesso!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Give Your Customers More Choices, Boost Your Sales"
msgstr "Dai ai tuoi clienti più opzioni, aumenta le tue vendite"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid ""
"Make buying easier with flexible product options. Let customers make the right choices from your checkout. Upgrade to "
"CartFlows PRO and start customizing today!"
msgstr ""
"Rendi l'acquisto più facile con opzioni di prodotto flessibili. Lascia che i clienti facciano le scelte giuste dal tuo "
"checkout. Passa a CartFlows PRO e inizia a personalizzare oggi stesso!"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Optin Product"
msgstr "Prodotto Optin"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Select free & virtual product only. Once you select a product, it will be displayed here."
msgstr "Seleziona solo prodotti gratuiti e virtuali. Una volta selezionato un prodotto, verrà visualizzato qui."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Search for a Free & Virtual Product"
msgstr "Cerca un prodotto gratuito e virtuale"

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
msgid "Please select a free & virtual product only."
msgstr "Si prega di selezionare solo un prodotto gratuito e virtuale."

#: admin-core/assets/build/editor-app.js:38
#: admin-core/assets/build/settings-app.js:50
#: admin-core/assets/build/settings-app.js:80
msgid "Free"
msgstr "Gratis"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "A/B Testing"
msgstr "Test A/B"

#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid ""
"Optimize your sales with A/B testing in CartFlows! Experiment with product pricing, page layouts, messaging, and "
"design. Create variants, analyze results, and discover new ways to boost revenue."
msgstr ""
"Ottimizza le tue vendite con i test A/B in CartFlows! Sperimenta con i prezzi dei prodotti, i layout delle pagine, i "
"messaggi e il design. Crea varianti, analizza i risultati e scopri nuovi modi per aumentare i ricavi."

#: admin-core/assets/build/settings-app.js:23
msgid "Finish Setup"
msgstr "Completa l'installazione"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup CartFlows"
msgstr "Configura CartFlows"

#: admin-core/assets/build/settings-app.js:23
msgid "Setup Store Checkout"
msgstr "Imposta il checkout del negozio"

#: admin-core/assets/build/settings-app.js:23
msgid "Create"
msgstr "Crea"

#: admin-core/assets/build/settings-app.js:23
msgid "Build Your Funnel"
msgstr "Costruisci il tuo imbuto"

#: admin-core/assets/build/settings-app.js:23
msgid "Start From Scratch"
msgstr "Inizia da zero"

#: admin-core/assets/build/settings-app.js:23
msgid "Go To Library"
msgstr "Vai in biblioteca"

#: admin-core/assets/build/settings-app.js:23
msgid "Offer add-ons with Order Bump."
msgstr "Offri componenti aggiuntivi con Order Bump."

#: admin-core/assets/build/settings-app.js:23
msgid "Increase Revenue with Upsells."
msgstr "Aumenta le entrate con le vendite aggiuntive."

#: admin-core/assets/build/settings-app.js:23
msgid "Almost There! Let's Go Live."
msgstr "Quasi arrivati! Andiamo in diretta."

#: admin-core/assets/build/settings-app.js:24
#. translators: %d is the number of completed steps.
msgid "%d out of 5 completed"
msgstr "%d su 5 completati"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade to PRO"
msgstr "Passa a PRO"

#: admin-core/assets/build/settings-app.js:24
msgid "Completed"
msgstr "Completato"

#: admin-core/assets/build/settings-app.js:24
msgid "Upgrade your plan anytime and get more detailed analytics data."
msgstr "Aggiorna il tuo piano in qualsiasi momento e ottieni dati analitici più dettagliati."

#: admin-core/assets/build/settings-app.js:24
msgid "Total Page Views is a Premium Feature"
msgstr "Il conteggio totale delle visualizzazioni delle pagine è una funzione Premium"

#: admin-core/assets/build/settings-app.js:24
msgid "Offer Revenue is a Premium Feature"
msgstr "Il ricavo dell'offerta è una funzione premium"

#: admin-core/assets/build/settings-app.js:24
msgid "Custom Filter:"
msgstr "Filtro personalizzato:"

#: admin-core/assets/build/settings-app.js:80
msgid "Checkout Features"
msgstr "Funzionalità di checkout"

#: admin-core/assets/build/settings-app.js:80
msgid "Modern Checkout Styles"
msgstr "Stili di pagamento moderni"

#: admin-core/assets/build/settings-app.js:80
msgid "Optimized replacement for the standard WooCommerce checkout page designed for higher conversion"
msgstr "Sostituzione ottimizzata per la pagina di pagamento standard di WooCommerce progettata per una maggiore conversione"

#: admin-core/assets/build/settings-app.js:80
msgid "Custom Checkout Fields"
msgstr "Campi di pagamento personalizzati"

#: admin-core/assets/build/settings-app.js:80
msgid "Have complete control over the field editor to manage the fields as required"
msgstr "Hai il controllo completo sull'editor dei campi per gestire i campi come richiesto"

#: admin-core/assets/build/settings-app.js:80
msgid "One-Click Upsells / Downsells"
msgstr "Upsell / Downsell con un clic"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic One-Click Upsells"
msgstr "Upsell dinamici con un solo clic"

#: admin-core/assets/build/settings-app.js:80
msgid "Use cart contents or customer data to display relevant upsells for maximum conversion"
msgstr "Utilizza il contenuto del carrello o i dati del cliente per mostrare upsell pertinenti per la massima conversione"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Upsell Templates"
msgstr "Modelli di Upsell Dinamici"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional templates to help you sell more even if you’re not a designer"
msgstr "Modelli professionali per aiutarti a vendere di più anche se non sei un designer"

#: admin-core/assets/build/settings-app.js:80
msgid "Order Bump Features"
msgstr "Funzionalità di Order Bump"

#: admin-core/assets/build/settings-app.js:80
msgid "Dynamic Order Bumps"
msgstr "Incrementi d'ordine dinamici"

#: admin-core/assets/build/settings-app.js:80
msgid "Smart order bumps using customer data to display most relevant products or offers"
msgstr "Incrementi intelligenti degli ordini utilizzando i dati dei clienti per mostrare i prodotti o le offerte più pertinenti"

#: admin-core/assets/build/settings-app.js:80
msgid "Advanced Funnel Features"
msgstr "Funzionalità Avanzate dell'Imbuto"

#: admin-core/assets/build/settings-app.js:80
msgid "A / B Split Testing"
msgstr "Test A / B"

#: admin-core/assets/build/settings-app.js:80
msgid "Increase conversions and sales with CartFlows A/B Testing by running simple tests"
msgstr "Aumenta le conversioni e le vendite con i test A/B di CartFlows eseguendo test semplici"

#: admin-core/assets/build/settings-app.js:80
msgid "Analyze transactions and user behavior to refine conversions and make more profit"
msgstr "Analizza le transazioni e il comportamento degli utenti per migliorare le conversioni e aumentare i profitti"

#: admin-core/assets/build/settings-app.js:80
msgid "Cloud-based automation tools that intelligently links your websites, stores, plugins and apps"
msgstr "Strumenti di automazione basati su cloud che collegano in modo intelligente i tuoi siti web, negozi, plugin e app"

#: admin-core/assets/build/settings-app.js:80
msgid "SkillJet Academy Access"
msgstr "Accesso a SkillJet Academy"

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows offers full training to help you make more profit with SkillJet academy"
msgstr "CartFlows offre una formazione completa per aiutarti a ottenere più profitto con l'accademia SkillJet"

#: admin-core/assets/build/settings-app.js:80
msgid "Others Benefits"
msgstr "Altri benefici"

#: admin-core/assets/build/settings-app.js:80
msgid "Premium Support"
msgstr "Supporto Premium"

#: admin-core/assets/build/settings-app.js:80
msgid "Professional Support, Professional Support Team or Dedicated Support Team"
msgstr "Supporto professionale, Team di supporto professionale o Team di supporto dedicato"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community"
msgstr "Incredibile comunità di utenti"

#: admin-core/assets/build/settings-app.js:80
msgid "Amazing User Community is already a great message unless you’re looking for a different meaning"
msgstr "Comunità Utente Straordinaria è già un ottimo messaggio a meno che tu non stia cercando un significato diverso"

#: admin-core/assets/build/settings-app.js:80
msgid "Great Documentation & Video Tutorials"
msgstr "Ottima documentazione e video tutorial"

#: admin-core/assets/build/settings-app.js:80
msgid "Comprehensive Documentation and Video Tutorials or Comprehensive Documentation and Video Guides"
msgstr "Documentazione completa e tutorial video o Documentazione completa e guide video"

#: admin-core/assets/build/settings-app.js:80
msgid "Free Plugins"
msgstr "Plugin gratuiti"

#: admin-core/assets/build/settings-app.js:80
msgid "Variation Swatches"
msgstr "Campioni di variazione"

#: admin-core/assets/build/settings-app.js:80
msgid "Give customers choice by including relevant product variations including size, color and more"
msgstr "Offri ai clienti una scelta includendo varianti di prodotto rilevanti come taglia, colore e altro"

#: admin-core/assets/build/settings-app.js:80
msgid "Stripe Payment Gateway"
msgstr "Gateway di pagamento Stripe"

#: admin-core/assets/build/settings-app.js:80
msgid "Accepting multiple payment methods gives customers choice and can significantly increase conversion"
msgstr ""
"Accettare più metodi di pagamento offre ai clienti la possibilità di scelta e può aumentare significativamente la "
"conversione"

#: admin-core/assets/build/settings-app.js:80
msgid "Features"
msgstr "Caratteristiche"

#: admin-core/assets/build/settings-app.js:80
msgid "See all CartFlows Pro features"
msgstr "Vedi tutte le funzionalità di CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Sell More with CartFlows Pro"
msgstr "Vendi di più con CartFlows Pro"

#: admin-core/assets/build/settings-app.js:80
msgid ""
"Get access to powerful features for painless WordPress designing, without the high costs. With all the time you will "
"save, it’s a product that pays for itself!"
msgstr ""
"Accedi a potenti funzionalità per progettare WordPress senza sforzo, senza costi elevati. Con tutto il tempo che "
"risparmierai, è un prodotto che si ripaga da solo!"

#: wizard/assets/build/wizard-app.js:1
msgid "Please complete the previous step before proceeding."
msgstr "Si prega di completare il passaggio precedente prima di procedere."

#: cartflows.php
#. Author of the plugin
msgid "Brainstorm Force"
msgstr "Brainstorm Force"

#: cartflows.php
#. Author URI of the plugin
msgid "https://www.brainstormforce.com"
msgstr "https://www.brainstormforce.com"

#: admin-core/ajax/importer.php:1017
#. translators: %1$s: html tag, %2$s: link html start %3$s: link html end
msgid ""
"Request timeout error. Please check if the firewall or any security plugin is blocking the outgoing HTTP/HTTPS requests "
"to templates.cartflows.com or not. %1$sTo resolve this issue, please check this %2$s article%3$s."
msgstr ""
"Errore di timeout della richiesta. Si prega di controllare se il firewall o qualsiasi plugin di sicurezza sta bloccando "
"le richieste HTTP/HTTPS in uscita verso templates.cartflows.com o meno. %1$sPer risolvere questo problema, si prega di "
"controllare questo %2$s articolo%3$s."

#: admin-core/inc/admin-menu.php:288
#: admin-core/inc/admin-menu.php:290
#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Modern Cart"
msgstr "Carrello moderno"

#: admin-core/inc/admin-menu.php:1038
msgid "OttoKit"
msgstr "OttoKit"

#: admin-core/inc/admin-menu.php:1039
msgid ""
"OttoKit helps people automate their work by integrating multiple apps and plugins, allowing them to share data and "
"perform tasks automatically."
msgstr ""
"OttoKit aiuta le persone ad automatizzare il loro lavoro integrando più app e plugin, permettendo loro di condividere "
"dati ed eseguire compiti automaticamente."

#: admin-core/inc/admin-menu.php:1088
msgid "Modern Cart for WooCommerce"
msgstr "Carrello Moderno per WooCommerce"

#: admin-core/inc/admin-menu.php:1089
msgid ""
"Modern Cart for WooCommerce that helps every shop owner improve their user experience, increase conversions & maximize "
"profits."
msgstr ""
"Carrello moderno per WooCommerce che aiuta ogni proprietario di negozio a migliorare l'esperienza utente, aumentare le "
"conversioni e massimizzare i profitti."

#: admin-core/inc/flow-meta.php:59
#: admin-core/assets/build/editor-app.js:43
#: admin-core/assets/build/settings-app.js:55
msgid "Enable Instant Layout"
msgstr "Abilita Layout Istantaneo"

#: admin-core/inc/flow-meta.php:64
#. translators: %1$s: Break line, %2$s: link html Start, %3$s: Link html end.
msgid ""
"This layout will replace the default page template for the Checkout, Upsell/Downsell and Thank You steps. You can "
"customize the design %1$sin the Checkout, Upsell/Downsell and Thank You step's settings, under the design tab. %2$sRead "
"More.%3$s"
msgstr ""
"Questo layout sostituirà il modello di pagina predefinito per i passaggi di Checkout, Upsell/Downsell e Grazie. Puoi "
"personalizzare il design %1$s nelle impostazioni dei passaggi di Checkout, Upsell/Downsell e Grazie, sotto la scheda "
"design. %2$sLeggi di più.%3$s"

#: admin-core/inc/flow-meta.php:87
msgid "Custom Logo"
msgstr "Logo personalizzato"

#: admin-core/inc/flow-meta.php:92
msgid "If you've added a custom logo, it will show up here. If not, a default logo from the theme will be used instead."
msgstr ""
"Se hai aggiunto un logo personalizzato, verrà visualizzato qui. In caso contrario, verrà utilizzato un logo predefinito "
"del tema."

#: admin-core/inc/flow-meta.php:103
msgid "Minimum image size should be 130 x 40 in pixes for ideal display."
msgstr "La dimensione minima dell'immagine dovrebbe essere 130 x 40 pixel per una visualizzazione ideale."

#: admin-core/inc/flow-meta.php:143
msgid "Header Color"
msgstr "Colore dell'intestazione"

#: admin-core/inc/flow-meta.php:249
msgid ""
"The Test Mode automatically adds sample products to your funnel if you haven't selected any. This helps you preview and "
"test the checkout experience easily."
msgstr ""
"La Modalità Test aggiunge automaticamente prodotti di esempio al tuo funnel se non ne hai selezionati. Questo ti aiuta "
"a visualizzare in anteprima e testare facilmente l'esperienza di checkout."

#: admin-core/inc/flow-meta.php:250
msgid ""
"The Test Mode automatically adds sample products to your store checkout funnel if you haven't selected any. This helps "
"you preview and test the experience easily on all steps except the Checkout page."
msgstr ""
"La Modalità Test aggiunge automaticamente prodotti di esempio al funnel di checkout del tuo negozio se non ne hai "
"selezionati. Questo ti aiuta a visualizzare in anteprima e testare l'esperienza facilmente in tutti i passaggi tranne "
"la pagina di Checkout."

#: admin-core/inc/flow-meta.php:256
msgid "Disallow Indexing"
msgstr "Disabilita l'indicizzazione"

#: admin-core/inc/flow-meta.php:257
msgid "Changing this will replace the default global setting. To go back to the global setting, just select Default."
msgstr ""
"Modificare questo sostituirà l'impostazione globale predefinita. Per tornare all'impostazione globale, seleziona "
"semplicemente Predefinito."

#: admin-core/inc/flow-meta.php:280
msgid "Any code you add here will work across all the pages in this funnel."
msgstr "Qualsiasi codice aggiungi qui funzionerà su tutte le pagine di questo funnel."

#: admin-core/inc/global-settings.php:50
msgid "Allow full access to all settings to customize everything."
msgstr "Consenti l'accesso completo a tutte le impostazioni per personalizzare tutto."

#: admin-core/inc/global-settings.php:55
msgid "Allow limited access to create, edit, delete, or import flows and steps."
msgstr "Consenti l'accesso limitato per creare, modificare, eliminare o importare flussi e passaggi."

#: admin-core/inc/global-settings.php:349
#: admin-core/inc/global-settings.php:539
msgid "This event will trigger when someone subscribes or signs up on the opt-in page."
msgstr "Questo evento si attiverà quando qualcuno si iscrive o si registra sulla pagina di adesione."

#: admin-core/inc/global-settings.php:930
msgid "This option is only available for products that are part of a subscription."
msgstr "Questa opzione è disponibile solo per i prodotti che fanno parte di un abbonamento."

#: admin-core/inc/global-settings.php:1415
msgid "Usage Tracking"
msgstr "Monitoraggio dell'utilizzo"

#: admin-core/inc/global-settings.php:1417
#. translators: %1$1s: link html start, %2$12: link html end
msgid "Allow CartFlows Inc products to track non-sensitive usage tracking data. %1$1s Learn More%2$2s."
msgstr "Consenti ai prodotti CartFlows Inc di tracciare dati di utilizzo non sensibili. %1$1s Scopri di più%2$2s."

#: classes/class-cartflows-admin-notices.php:183
msgid "Hi there! You recently used CartFlows to build a sales funnel &mdash; Thanks a ton!"
msgstr "Ciao! Hai recentemente utilizzato CartFlows per creare un funnel di vendita &mdash; Grazie mille!"

#: classes/class-cartflows-admin-notices.php:184
msgid ""
"It would be awesome if you give us a 5-star review and share your experience on WordPress. Your reviews pump us up and "
"also help other WordPress users make a better decision when choosing CartFlows!"
msgstr ""
"Sarebbe fantastico se ci dessi una recensione a 5 stelle e condividessi la tua esperienza su WordPress. Le tue "
"recensioni ci motivano e aiutano anche altri utenti di WordPress a prendere una decisione migliore quando scelgono "
"CartFlows!"

#: classes/class-cartflows-admin-notices.php:186
msgid "Ok, you deserve it"
msgstr "Ok, te lo meriti"

#: classes/class-cartflows-admin-notices.php:188
msgid "Nope, maybe later"
msgstr "No, forse più tardi"

#: classes/class-cartflows-admin-notices.php:189
msgid "I already did"
msgstr "L'ho già fatto"

#: classes/class-cartflows-flow-frontend.php:90
msgid ""
"Test mode is currently enabled to help you preview your funnel. You can turn it off anytime from the funnel's settings "
"in the admin dashboard."
msgstr ""
"La modalità di test è attualmente abilitata per aiutarti a visualizzare in anteprima il tuo funnel. Puoi disattivarla "
"in qualsiasi momento dalle impostazioni del funnel nella dashboard di amministrazione."

#: classes/class-cartflows-flow-frontend.php:91
msgid "Click here to disable it."
msgstr "Fai clic qui per disabilitarlo."

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:119
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:131
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:162
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:145
#: modules/gutenberg/build/blocks.js:7
msgid "Legacy"
msgstr "Eredità"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:120
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:132
#: modules/elementor/widgets/class-cartflows-el-order-details-form.php:163
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:149
#: modules/gutenberg/build/blocks.js:7
msgid "Modern"
msgstr "Moderno"

#: modules/beaver-builder/cartflows-bb-order-details/cartflows-bb-order-details.php:132
#: modules/bricks/elements/class-cartflows-bricks-order-details-form.php:146
#: modules/gutenberg/build/blocks.js:7
msgid "The Thank You Text is only applicable for the old layout."
msgstr "Il testo di ringraziamento è applicabile solo al vecchio layout."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:322
msgid "Pick a background color for the left side of your Checkout page."
msgstr "Scegli un colore di sfondo per il lato sinistro della tua pagina di Checkout."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:339
msgid "Pick a background color for the right side of your Checkout page."
msgstr "Scegli un colore di sfondo per il lato destro della tua pagina di Checkout."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:841
msgid "Change the background color of the payment description box to match your style."
msgstr "Cambia il colore di sfondo della casella di descrizione del pagamento per adattarlo al tuo stile."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1022
msgid "This is the name (slug) of the current step. Changing it will update the URL for this step, so be cautious!"
msgstr "Questo è il nome (slug) del passaggio corrente. Cambiarlo aggiornerà l'URL per questo passaggio, quindi fai attenzione!"

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1030
#: modules/landing/classes/class-cartflows-landing-meta-data.php:123
#: modules/optin/classes/class-cartflows-optin-meta-data.php:577
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:405
msgid "Add your own custom code here. If you're adding CSS, make sure to wrap it inside &lt;style&gt; tags."
msgstr ""
"Aggiungi qui il tuo codice personalizzato. Se stai aggiungendo CSS, assicurati di racchiuderlo all'interno dei tag "
"&lt;style&gt;."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1044
msgid "Turn this ON to show your product images in the order review section."
msgstr "Attiva questa opzione per mostrare le immagini dei tuoi prodotti nella sezione di revisione dell'ordine."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1052
msgid "Users can easily remove products from the checkout page if they decide not to purchase them."
msgstr "Gli utenti possono facilmente rimuovere i prodotti dalla pagina di pagamento se decidono di non acquistarli."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1216
msgid "Turn this on to show a custom message when no shipping options are available at checkout."
msgstr ""
"Attiva questa opzione per mostrare un messaggio personalizzato quando non sono disponibili opzioni di spedizione al "
"momento del pagamento."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1244
#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:449
msgid "Choose this option to adjust where the order summary appears on mobile devices."
msgstr "Scegli questa opzione per regolare dove appare il riepilogo dell'ordine sui dispositivi mobili."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1297
#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1305
msgid "This heading will only appear when you use the Modern Checkout style."
msgstr "Questo titolo apparirà solo quando utilizzi lo stile di pagamento moderno."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1313
msgid "This message will appear next to the field name to show an error if something goes wrong."
msgstr "Questo messaggio apparirà accanto al nome del campo per mostrare un errore se qualcosa va storto."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1346
msgid ""
"Customizes the text on the 'Place Order' button during checkout, allowing you to make it more relevant to your "
"customers."
msgstr ""
"Personalizza il testo sul pulsante 'Effettua ordine' durante il checkout, permettendoti di renderlo più pertinente per "
"i tuoi clienti."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1354
msgid ""
"Enabling this will add a lock icon to the 'Place Order' button on the checkout page, indicating secure payment "
"processing."
msgstr ""
"Abilitando questa opzione verrà aggiunta un'icona di lucchetto al pulsante 'Effettua Ordine' nella pagina di pagamento, "
"indicando l'elaborazione sicura del pagamento."

#: modules/checkout/classes/class-cartflows-checkout-meta-data.php:1363
msgid "This will display the total amount in the cart when you click the 'Place Order' button."
msgstr "Questo visualizzerà l'importo totale nel carrello quando clicchi sul pulsante 'Effettua ordine'."

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:408
msgid "This heading will appear only when the Instant Layout option is used."
msgstr "Questo titolo apparirà solo quando viene utilizzata l'opzione Layout istantaneo."

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:139
msgid "Thank You Skin"
msgstr "Grazie Pelle"

#: modules/thankyou/classes/class-cartflows-thankyou-meta-data.php:501
msgid "After submitting, users will be sent to this URL instead of the usual thank you page."
msgstr "Dopo l'invio, gli utenti verranno reindirizzati a questo URL invece della solita pagina di ringraziamento."

#: modules/thankyou/templates/instant-thankyou-order-details.php:35
#. Translators: Order ID.
msgid "Order #%s"
msgstr "Ordine n. %s"

#: modules/woo-dynamic-flow/classes/class-cartflows-wd-flow-product-meta.php:139
msgid "Type to search a funnel..."
msgstr "Digita per cercare un imbuto..."

#: admin-core/assets/build/editor-app.js:8
#: admin-core/assets/build/settings-app.js:8
msgid "Disabled"
msgstr "Disabilitato"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "upgrading to PRO"
msgstr "aggiornamento a PRO"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "activating CartFlows Pro"
msgstr "attivazione di CartFlows Pro"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "You're using"
msgstr "Stai usando"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "CartFlows Free"
msgstr "CartFlows Free"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "To unlock more features, consider"
msgstr "Per sbloccare più funzionalità, considera"

#: admin-core/assets/build/editor-app.js:22
#: admin-core/assets/build/settings-app.js:22
msgid "Activate CartFlows Pro"
msgstr "Attiva CartFlows Pro"

#: admin-core/assets/build/editor-app.js:23
#: admin-core/assets/build/settings-app.js:23
msgid "Activated!"
msgstr "Attivato!"

#: admin-core/assets/build/editor-app.js:27
#: admin-core/assets/build/settings-app.js:39
msgid ""
"You can't edit this step directly because Instant Layout is turned on in the funnel settings. To make design changes, "
"go to the Design tab inside this step's settings."
msgstr ""
"Non puoi modificare direttamente questo passaggio perché il Layout Istantaneo è attivato nelle impostazioni del funnel. "
"Per apportare modifiche al design, vai alla scheda Design all'interno delle impostazioni di questo passaggio."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
#. translators: %s: The current step type.
msgid "Use this setting to customize the style of the Instant %s Layout."
msgstr "Usa questa impostazione per personalizzare lo stile del Layout %s istantaneo."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Turn this on to set up rules that decide when visitors should be redirected to a special offer or the next step."
msgstr ""
"Attiva questa opzione per impostare regole che decidano quando i visitatori devono essere reindirizzati a un'offerta "
"speciale o al passaggio successivo."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "Your email address can't be edited when using the Modern Checkout Style."
msgstr "Il tuo indirizzo email non può essere modificato quando si utilizza lo stile di pagamento moderno."

#: admin-core/assets/build/editor-app.js:36
#: admin-core/assets/build/settings-app.js:48
msgid "The Company field won't be visible if you're using the Instant Layout Style."
msgstr "Il campo Azienda non sarà visibile se stai utilizzando lo stile di layout istantaneo."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from all your funnels combined."
msgstr "Questo mostra l'importo totale di denaro guadagnato da tutti i tuoi funnel combinati."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of orders placed through your CartFlows checkout pages."
msgstr "Questo mostra il numero totale di ordini effettuati tramite le tue pagine di pagamento CartFlows."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total number of times people visited any step in your funnel."
msgstr "Questo mostra il numero totale di volte in cui le persone hanno visitato qualsiasi fase del tuo funnel."

#: admin-core/assets/build/settings-app.js:24
msgid "This shows the total amount of money earned from your Upsell and Downsell offers."
msgstr "Questo mostra l'importo totale di denaro guadagnato dalle tue offerte di Upsell e Downsell."

#: admin-core/assets/build/settings-app.js:35
msgid "Set up a Store Checkout in just one click:"
msgstr "Imposta un checkout del negozio con un solo clic:"

#: admin-core/assets/build/settings-app.js:35
msgid "Thank You Page"
msgstr "Pagina di ringraziamento"

#: admin-core/assets/build/settings-app.js:35
msgid ""
"Use ready-made templates from the CartFlows Library, our custom widget, or shortcodes on each page to set this up "
"easily—no coding needed!"
msgstr ""
"Usa i modelli predefiniti dalla Libreria CartFlows, il nostro widget personalizzato o gli shortcode su ogni pagina per "
"configurarlo facilmente—senza bisogno di codifica!"

#: admin-core/assets/build/settings-app.js:80
msgid "Install OttoKit for Free"
msgstr "Installa OttoKit gratuitamente"

#: admin-core/assets/build/settings-app.js:80
msgid "Visit OttoKit Website"
msgstr "Visita il sito web di OttoKit"

#: admin-core/assets/build/settings-app.js:80
msgid "Here are a few simple examples of what OttoKit can do on your WooCommerce store:"
msgstr "Ecco alcuni semplici esempi di ciò che OttoKit può fare nel tuo negozio WooCommerce:"

#: admin-core/assets/build/settings-app.js:80
msgid "Join Thousands of Entrepreneurs Already Using OttoKit."
msgstr "Unisciti a migliaia di imprenditori che già utilizzano OttoKit."

#: admin-core/assets/build/settings-app.js:80
msgid "Bonus ($200 Value)"
msgstr "Bonus (valore di $200)"

#: admin-core/assets/build/settings-app.js:80
msgid "Access to OttoKit Pro Plan"
msgstr "Accesso al piano OttoKit Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Plus - Annual"
msgstr "Plus - Annuale"

#: admin-core/assets/build/settings-app.js:80
msgid "/ year"
msgstr "/ anno"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Annual"
msgstr "Pro - Annuale"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (One Time Pay)"
msgstr "Pro - A vita (Pagamento una tantum)"

#: admin-core/assets/build/settings-app.js:80
msgid "for Lifetime"
msgstr "per tutta la vita"

#: admin-core/assets/build/settings-app.js:80
msgid "Pro - Lifetime (11 x Split Pay)"
msgstr "Pro - A vita (11 x Pagamento rateale)"

#: admin-core/assets/build/settings-app.js:80
msgid "x 11 Months"
msgstr "x 11 mesi"

#: admin-core/assets/build/settings-app.js:80
msgid "Explore the key differences between Plus and Pro to find the perfect fit for your needs."
msgstr "Esplora le differenze chiave tra Plus e Pro per trovare la soluzione perfetta per le tue esigenze."

#: admin-core/assets/build/settings-app.js:80
msgid "CartFlows Free vs Pro Image"
msgstr "Immagine CartFlows Free vs Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Unlock Pro Features"
msgstr "Sblocca le funzionalità Pro"

#: admin-core/assets/build/settings-app.js:80
msgid "Generate More Sales With CartFlows Pro!"
msgstr "Genera più vendite con CartFlows Pro!"

#: admin-core/assets/build/settings-app.js:80
msgid "And More…"
msgstr "E altro ancora…"

#: admin-core/assets/build/settings-app.js:80
msgid "Buy Now"
msgstr "Acquista ora"

#: admin-core/assets/build/settings-app.js:80
msgid "View plans"
msgstr "Visualizza i piani"

#: admin-core/assets/build/settings-app.js:80
msgid "Get Modern Cart Now"
msgstr "Ottieni Modern Cart ora"

#: admin-core/assets/build/settings-app.js:80
msgid "Moderncart"
msgstr "Moderncart"

#: admin-core/assets/build/settings-app.js:81
#. translators: %s: line break
msgid "Your Cart Can Do More — Let’s Make It %sa Sales Machine!"
msgstr "Il tuo carrello può fare di più — trasformiamolo in %suna macchina da vendite!"

#: admin-core/assets/build/settings-app.js:81
msgid ""
"Transform your default WooCommerce cart into a high-converting, fast, and user-friendly shopping experience — designed "
"to keep customers engaged and ready to buy."
msgstr ""
"Trasforma il tuo carrello WooCommerce predefinito in un'esperienza di acquisto ad alta conversione, veloce e facile da "
"usare, progettata per mantenere i clienti coinvolti e pronti all'acquisto."

#: admin-core/assets/build/settings-app.js:81
msgid "Visit Modern Cart"
msgstr "Visita Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Why Store Owners ❤️ Modern Cart"
msgstr "Perché i proprietari dei negozi ❤️ Modern Cart"

#: admin-core/assets/build/settings-app.js:81
msgid "Trusted by Top Brands to Boost Conversions Instantly"
msgstr "Fidato dai marchi principali per aumentare le conversioni istantaneamente"

#: admin-core/assets/build/settings-app.js:81
msgid "Brand logo"
msgstr "Logo del marchio"

#: admin-core/assets/build/settings-app.js:81
msgid "Stop Losing Sales at the Cart — Fix It in Minutes!"
msgstr "Smetti di perdere vendite al carrello — Risolvilo in pochi minuti!"

#: admin-core/assets/build/settings-app.js:81
msgid "Modern Cart is your instant upgrade for more sales, bigger orders, and smoother checkouts."
msgstr ""
"Modern Cart è il tuo aggiornamento istantaneo per aumentare le vendite, incrementare gli ordini e rendere più fluidi i "
"processi di pagamento."

#: wizard/assets/build/wizard-app.js:1
msgid "Learn more about usage tracking"
msgstr "Scopri di più sul monitoraggio dell'utilizzo"

#: wizard/assets/build/wizard-app.js:3
msgid "I agree to share anonymous usage data to help improve CartFlows."
msgstr "Accetto di condividere dati di utilizzo anonimi per aiutare a migliorare CartFlows."

#: wizard/assets/build/wizard-app.js:4
#. translators: %1$s: anchor tag start, %2$s: anchor tag close
msgid ""
"We never collect personal info, Only anonymized data like PHP version, admin language, and feature usage. To learn what "
"we collect and why, see this %1$sdocument%2$s."
msgstr ""
"Non raccogliamo mai informazioni personali, solo dati anonimi come la versione di PHP, la lingua dell'amministratore e "
"l'uso delle funzionalità. Per sapere cosa raccogliamo e perché, consulta questo %1$sdocumento%2$s."

#: wizard/assets/build/wizard-app.js:5
#. translators: %1$s: Anchor tag one start, %2$s: Anchor tag one close, %3$s: Anchor tag two start, %4$s: Anchor tag two close.
msgid "By continuing you agree to our %1$sTerms%2$s and %3$sPrivacy Policy%4$s."
msgstr "Continuando accetti i nostri %1$sTermini%2$s e %3$sInformativa sulla privacy%4$s."

#: modules/beaver-builder/cartflows-bb-next-step/cartflows-bb-next-step.php:266
msgctxt "Width."
msgid "Auto"
msgstr "Auto"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping %d"
msgstr "Spedizione %d"

#: modules/checkout/classes/layouts/class-cartflows-instant-checkout.php:469
#: modules/checkout/templates/checkout/shipping-methods.php:17
#. translators: %d: shipping package number
msgctxt "shipping packages"
msgid "Shipping"
msgstr "Spedizione"

#: modules/flow/classes/class-cartflows-flow-post-type.php:103
msgctxt "flow general name"
msgid "Flows"
msgstr "Flussi"

#: modules/flow/classes/class-cartflows-flow-post-type.php:104
msgctxt "flow singular name"
msgid "Flow"
msgstr "Flusso"

#: modules/flow/classes/class-cartflows-step-post-type.php:170
msgctxt "flow step general name"
msgid "Steps"
msgstr "Passi"

#: modules/flow/classes/class-cartflows-step-post-type.php:171
msgctxt "flow step singular name"
msgid "Step"
msgstr "Passo"
