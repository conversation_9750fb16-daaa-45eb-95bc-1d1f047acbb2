<?php
/**
 * Classe de redirecionamento
 *
 * @package CF_Payment_Redirector
 */

// Sair se acessado diretamente.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Classe de redirecionador.
 */
class CFPR_Redirector {

    /**
     * Variável de instância.
     *
     * @var CFPR_Redirector
     */
    private static $instance;

    /**
     * Inicializador.
     */
    public static function get_instance() {
        if ( ! isset( self::$instance ) ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Construtor.
     */
    public function __construct() {
        // Hook principal para redirecionar após o pagamento
        // Prioridade menor (20) para garantir que execute depois de outros redirecionamentos
        add_action( 'template_redirect', array( $this, 'handle_redirection' ), 20 );
        
        // Hook para salvar o método de pagamento após o processamento do pedido
        add_action( 'woocommerce_thankyou', array( $this, 'detect_payment_method' ), 5 );
        
        // Se estiver no modo de depuração
        if ( defined('WP_DEBUG') && WP_DEBUG ) {
            add_action( 'admin_notices', array( $this, 'show_debug_notice' ) );
        }
    }

    /**
     * Mostrar aviso de depuração na administração
     */
    public function show_debug_notice() {
        // Verificar permissões
        if ( ! current_user_can( 'manage_options' ) ) {
            return;
        }
        
        // Verificar se há logs recentes
        $logs = get_option( 'cfpr_debug_logs', array() );
        
        if ( ! empty( $logs ) ) {
            // Mostrar apenas os últimos 5 logs
            $logs = array_slice( $logs, -5 );
            
            echo '<div class="notice notice-info is-dismissible">';
            echo '<p><strong>CartFlows Payment Redirector - Logs recentes:</strong></p>';
            echo '<ul>';
            
            foreach ( $logs as $log ) {
                echo '<li>' . esc_html( $log['time'] ) . ': ' . esc_html( $log['message'] ) . '</li>';
            }
            
            echo '</ul>';
            echo '<p><a href="#" class="button button-secondary" onclick="jQuery.post(ajaxurl, {action: \'cfpr_clear_logs\', nonce: \'' . wp_create_nonce( 'cfpr_clear_logs' ) . '\'}, function() { location.reload(); }); return false;">Limpar logs</a></p>';
            echo '</div>';
        }
    }
    
    /**
     * Registrar mensagem de log para depuração
     *
     * @param string $message Mensagem a ser registrada
     */
    private function log( $message ) {
        if ( defined('WP_DEBUG') && WP_DEBUG ) {
            $logs = get_option( 'cfpr_debug_logs', array() );
            
            // Limitar a 50 logs
            if ( count( $logs ) > 50 ) {
                array_shift( $logs );
            }
            
            $logs[] = array(
                'time' => current_time( 'd/m/Y H:i:s' ),
                'message' => $message
            );
            
            update_option( 'cfpr_debug_logs', $logs );
        }
    }

    /**
     * Detectar método de pagamento e salvar na sessão
     *
     * @param int $order_id ID do pedido.
     */
    public function detect_payment_method( $order_id ) {
        if ( empty( $order_id ) ) {
            return;
        }
        
        $order = wc_get_order( $order_id );
        
        if ( ! $order ) {
            return;
        }
        
        $payment_method = $order->get_payment_method();
        
        // Salvar na sessão para redirecionamento posterior
        if ( ! empty( $payment_method ) ) {
            WC()->session->set( 'cfpr_payment_method', $payment_method );
            WC()->session->set( 'cfpr_order_id', $order_id );
        }
    }

    /**
     * Lidar com o redirecionamento com base nas regras configuradas
     */
    public function handle_redirection() {
        // Verificar se estamos em uma página CartFlows
        if ( ! $this->is_cartflows_thankyou_page() ) {
            $this->log( 'Não é uma página de agradecimento - ignorando' );
            return;
        }
        
        // Prevenir loops de redirecionamento
        if ( $this->is_already_redirected() ) {
            $this->log( 'Já foi redirecionado - evitando loop' );
            return;
        }
        
        // Obter o método de pagamento do pedido ou da sessão
        $payment_method = $this->get_current_payment_method();
        $order_id = $this->get_current_order_id();
        
        if ( empty( $payment_method ) || empty( $order_id ) ) {
            $this->log( 'Método de pagamento ou ID do pedido vazio' );
            return;
        }
        
        $this->log( 'Método de pagamento: ' . $payment_method . ', ID do pedido: ' . $order_id );
        
        // Obter regras de redirecionamento para este funil
        $redirect_rules = $this->get_redirect_rules();
        
        // Verificar se há uma regra para este método de pagamento
        if ( ! empty( $redirect_rules ) && isset( $redirect_rules[ $payment_method ] ) ) {
            $step_id = $redirect_rules[ $payment_method ];
            
            $this->log( 'Regra encontrada: redirecionar para o passo ID ' . $step_id );
            
            // Se houver um passo para redirecionar
            if ( ! empty( $step_id ) ) {
                global $post;
                
                // Evitar redirecionamento para a mesma página
                if ( $post && $post->ID == $step_id ) {
                    $this->log( 'Já estamos na página de destino - ignorando' );
                    return;
                }
                
                $redirect_url = $this->get_step_url_with_params( $step_id, $order_id );
                
                if ( ! empty( $redirect_url ) ) {
                    $this->log( 'Redirecionando para: ' . $redirect_url );
                    
                    // Marcar como redirecionado para evitar loops
                    $this->mark_as_redirected();
                    
                    // Limpar sessão para evitar loops
                    WC()->session->__unset( 'cfpr_payment_method' );
                    WC()->session->__unset( 'cfpr_order_id' );
                    
                    // Redirecionar
                    wp_redirect( $redirect_url );
                    exit;
                } else {
                    $this->log( 'URL de redirecionamento vazia' );
                }
            }
        } else {
            $this->log( 'Nenhuma regra definida para o método de pagamento: ' . $payment_method );
        }
    }

    /**
     * Verificar se estamos em uma página de thank you do CartFlows
     *
     * @return bool
     */
    private function is_cartflows_thankyou_page() {
        global $post;
        
        if ( ! $post ) {
            return false;
        }
        
        // Verificar se estamos na página de checkout padrão do WooCommerce
        // Isso evita redirecionamentos na página de checkout
        if ( function_exists('is_checkout') && is_checkout() && !is_order_received_page() ) {
            return false;
        }
        
        // Se for uma página de pedido recebido no WooCommerce
        if ( function_exists('is_order_received_page') && is_order_received_page() ) {
            // Verificar o método de pagamento
            $order_id = $this->get_order_id_from_url();
            if ($order_id) {
                return true;
            }
        }
        
        // Verificar se é uma página do CartFlows
        if ( ! function_exists('wcf') ) {
            return false;
        }
        
        // Verificar se está em uma página de agradecimento do CartFlows
        $step_type = get_post_meta( $post->ID, 'wcf-step-type', true );
        
        // Verificar se é uma página CartFlows de checkout
        if ( $step_type === 'checkout' ) {
            return false;
        }
        
        // Considerar qualquer página do CartFlows com ID de pedido na URL como elegível para redirecionamento
        // desde que não seja a página de checkout
        if ( $this->get_order_id_from_url() && $step_type ) {
            // Verificar se o pedido está completo para evitar redirecionamentos durante o checkout
            $order_id = $this->get_order_id_from_url();
            $order = wc_get_order( $order_id );
            
            if ( $order ) {
                // Tentar detectar se o pagamento foi concluído
                if ( $order->is_paid() || $order->has_status( array( 'processing', 'completed', 'on-hold' ) ) ) {
                    return true;
                }
            }
        }
        
        // Página de agradecimento padrão
        return 'thankyou' === $step_type;
    }

    /**
     * Obter método de pagamento atual da sessão ou URL
     *
     * @return string Método de pagamento
     */
    private function get_current_payment_method() {
        // Primeiro tenta obter da sessão
        $payment_method = WC()->session->get( 'cfpr_payment_method' );
        
        // Se não estiver na sessão, tenta obter do pedido atual
        if ( empty( $payment_method ) ) {
            $order_id = $this->get_order_id_from_url();
            
            if ( ! empty( $order_id ) ) {
                $order = wc_get_order( $order_id );
                
                if ( $order ) {
                    $payment_method = $order->get_payment_method();
                    
                    // Salvar na sessão para futuras referências
                    WC()->session->set( 'cfpr_payment_method', $payment_method );
                    WC()->session->set( 'cfpr_order_id', $order_id );
                }
            }
        }
        
        return $payment_method;
    }

    /**
     * Obter ID do pedido atual da sessão ou URL
     *
     * @return int ID do pedido
     */
    private function get_current_order_id() {
        // Primeiro tenta obter da sessão
        $order_id = WC()->session->get( 'cfpr_order_id' );
        
        // Se não estiver na sessão, tenta obter da URL
        if ( empty( $order_id ) ) {
            $order_id = $this->get_order_id_from_url();
        }
        
        return $order_id;
    }

    /**
     * Extrair ID do pedido da URL
     *
     * @return int ID do pedido ou 0
     */
    private function get_order_id_from_url() {
        $order_id = 0;
        
        // Obter da URL usando o parâmetro wcf-order
        if ( isset( $_GET['wcf-order'] ) ) {
            $order_id = absint( $_GET['wcf-order'] );
        }
        
        // Se não encontrado, tenta o parâmetro order-received
        if ( empty( $order_id ) && isset( $_GET['order-received'] ) ) {
            $order_id = absint( $_GET['order-received'] );
        }
        
        // Se não encontrado, tenta o parâmetro order
        if ( empty( $order_id ) && isset( $_GET['order'] ) ) {
            $order_id = absint( $_GET['order'] );
        }
        
        return $order_id;
    }

    /**
     * Obter regras de redirecionamento para o funil atual
     *
     * @return array Regras de redirecionamento
     */
    private function get_redirect_rules() {
        global $post;
        
        if ( ! $post ) {
            return array();
        }
        
        $flow_id = $this->get_flow_id_from_step( $post->ID );
        
        if ( empty( $flow_id ) ) {
            return array();
        }
        
        // Obter regras de redirecionamento das opções
        $all_rules = get_option( 'cfpr_redirect_rules', array() );
        
        return isset( $all_rules[ $flow_id ] ) ? $all_rules[ $flow_id ] : array();
    }

    /**
     * Obter o ID do funil a partir do ID do passo
     *
     * @param int $step_id ID do passo.
     * @return int ID do funil ou 0.
     */
    private function get_flow_id_from_step( $step_id ) {
        return get_post_meta( $step_id, 'wcf-flow-id', true );
    }

    /**
     * Construir URL de passo com os parâmetros necessários
     *
     * @param int $step_id ID do passo.
     * @param int $order_id ID do pedido.
     * @return string URL completa.
     */
    private function get_step_url_with_params( $step_id, $order_id ) {
        $integration = CFPR_Integration::get_instance();
        $url = $integration->get_step_url( $step_id );
        
        if ( empty( $url ) ) {
            return '';
        }
        
        // Obter parâmetros da URL atual para preservar
        $params = array();
        
        // Adicionar parâmetro wcf-order
        $params['wcf-order'] = $order_id;
        
        // Adicionar parâmetro wcf-key se existir
        if ( isset( $_GET['wcf-key'] ) ) {
            $params['wcf-key'] = sanitize_text_field( $_GET['wcf-key'] );
        }
        
        // Gerar URL com parâmetros
        return add_query_arg( $params, $url );
    }

    /**
     * Verifica se já ocorreu um redirecionamento para evitar loops
     *
     * @return bool Se já ocorreu um redirecionamento
     */
    private function is_already_redirected() {
        // Verificar cookie de redirecionamento
        if ( isset( $_COOKIE['cfpr_redirected'] ) ) {
            $redirect_data = json_decode( stripslashes( $_COOKIE['cfpr_redirected'] ), true );
            
            if ( $redirect_data ) {
                $order_id = $this->get_current_order_id();
                
                // Se o mesmo pedido já foi redirecionado
                if ( isset( $redirect_data['order_id'] ) && $redirect_data['order_id'] == $order_id ) {
                    // Se o redirecionamento ocorreu há menos de 10 segundos, evitar novo redirecionamento
                    if ( isset( $redirect_data['time'] ) && ( time() - $redirect_data['time'] ) < 10 ) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Marca que um redirecionamento ocorreu para evitar loops
     */
    private function mark_as_redirected() {
        $order_id = $this->get_current_order_id();
        $redirect_data = array(
            'order_id' => $order_id,
            'time' => time()
        );
        
        // Definir cookie que dura 1 minuto
        setcookie( 'cfpr_redirected', json_encode( $redirect_data ), time() + 60, '/' );
    }
} 