!function(n){const i={init(){this._bind()},_bind(){n(document).on("click",".cartflows-dismissible-notice .notice-dismiss",i.disable_license_admin_notice)},disable_license_admin_notice(i){i.preventDefault(),n.ajax({url:ajaxurl,type:"POST",data:{action:"cartflows_disable_activate_license_notice",security:CartFlowsProAdminNoticeVars._nonce}}).done(function(){}).fail(function(){}).always(function(){})}};n(function(){i.init()})}(jQuery);