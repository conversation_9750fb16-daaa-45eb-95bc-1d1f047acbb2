!function(s){const t={init(){this._check_popup(),this._bind()},_check_popup(){const e=t;(e._getUrlParameter("cartflows-license-popup")||"")&&"Deactivated"===CartFlowsProLicenseVars.activation_status&&e._open_popup()},_bind(){s(document).on("click",".cartflows-close-popup-button",t._close_popup),s(document).on("click","#cartflows-license-popup-overlay",t._close_popup),s(document).on("click",".cartflows-activate-license",t._activate_license),s(document).on("click",".cartflows-deactivate-license",t._deactivate_license)},_log(e){const t=new Date;t.toLocaleTimeString()},_export_button_click(e){e.preventDefault(),t._open_popup()},_open_popup(){const e=s("#cartflows-license-popup-overlay, #cartflows-license-popup"),t=s("#cartflows-license-popup").attr("data-license-key")||"",a=e.find(".contents");"Activated"===t?a.html(wp.template("cartflows-deactivate-license")):a.html(wp.template("cartflows-activate-license")),e.show()},_close_popup(){const e=s("#cartflows-license-popup-overlay, #cartflows-license-popup");e.hasClass("validating")&&!confirm("WARNING! License request not complete!!\n\nPlease wait for a moment until complete the license request.")||e.hide()},_activate_license(e){e.preventDefault();const a=s("#cartflows-license-popup"),i=a.find(".license_key").val()||"";if(i.length){const n=s(this);if(!n.hasClass("disabled")&&!n.hasClass("validating")){a.addClass("validating"),n.find(".text").text("Validating..");const c=a.find(".contents");c.find(".notice").length&&c.find(".notice").remove(),n.find(".cartflows-processing").addClass("is-active");e=a.find(".license_nonce").val()||"";s.ajax({url:ajaxurl,type:"POST",data:{action:"cartflows_activate_license",license_key:i,security:e}}).done(function(e){if(a.removeClass("validating"),n.find(".cartflows-processing").removeClass("is-active"),e.success){const t=s(".cartflows-license-popup-open-button");t.removeClass("active").addClass("inactive").text("Deactivate License"),n.find(".text").text("Successfully Activated! Reloading.."),a.attr("data-license-key",i),setTimeout(function(){location.reload()},2500),a.find("input").addClass("disabled").attr("readonly","readonly")}else{e=e.data.error||e.data||"";e&&c.append('<div class="notice notice-error"><p>'+e+"</p></div>"),n.find(".text").text("Failed!")}}).fail(function(){}).always(function(){})}}},_deactivate_license(e){e.preventDefault();const t=s(this),a=s(".cartflows-license-popup-open-button"),i=s("#cartflows-license-popup"),n=i.find(".contents");e=i.find(".deactivate_license_nonce").val()||"";i.addClass("validating"),t.find(".text").text("Deactivating.."),n.find(".notice").length&&n.find(".notice").remove(),t.find(".cartflows-processing").addClass("is-active"),s.ajax({url:ajaxurl,type:"POST",data:{action:"cartflows_deactivate_license",security:e}}).done(function(e){i.removeClass("validating"),t.find(".cartflows-processing").removeClass("is-active"),e.success?(a.removeClass("inactive").addClass("active").text("Activate License"),t.find(".text").text("Successfully Deactivated! Reloading.."),i.attr("data-license-key",""),setTimeout(function(){location.reload()},2500)):((e=e.data.message||e.data||e.response||"")&&n.append('<div class="notice notice-error"><p>'+e+"</p></div>"),t.find(".text").text("Failed!"))}).fail(function(){}).always(function(){})},_getUrlParameter(e){const t=decodeURIComponent(window.location.search.substring(1)),a=t.split("&");let i,n;for(n=0;n<a.length;n++)if((i=a[n].split("="))[0]===e)return void 0===i[1]||i[1]}};s(function(){t.init()})}(jQuery);